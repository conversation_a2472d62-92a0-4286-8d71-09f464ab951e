"""
网文爽感语料库管理器

负责管理和访问网文爽感相关的语料库数据，包括：
- 打脸爽文模式
- 权力幻想模式  
- 升级进化模式
- 节奏模板
- 强化表达库

作者: 系统
创建时间: 2025-08-05
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path

from app.core.corpus_manager import create_corpus_manager, CorpusManager, CorpusEntry
from app.core.config import log_info, log_debug, log_error, log_success


@dataclass
class PleasurePatternData:
    """爽感模式数据结构"""
    content: str
    weight: float
    intensity: float
    reliability: float
    quality: float
    tags: List[str]
    examples: List[str]
    data: Dict[str, Any]


@dataclass
class RhythmTemplateData:
    """节奏模板数据结构"""
    name: str
    structure_breakdown: Dict[str, Any]
    emotion_curve: List[int]
    techniques: List[str]
    application_scenarios: List[str]


class PleasureCorpusManager:
    """
    🎯 [爽感语料库] 网文爽感语料库管理器
    
    管理各种网文爽感模式的语料库数据，提供统一的访问接口
    """
    
    def __init__(self, corpus_root: Optional[str] = None):
        """
        初始化爽感语料库管理器
        
        Args:
            corpus_root: 语料库根目录路径
        """
        log_debug("🎯爽感语料库", "初始化爽感语料库管理器", 语料库根目录=corpus_root)
        
        self.corpus_manager = create_corpus_manager(corpus_root)
        self.corpus_data = {}
        
        # 定义语料库文件映射
        self.corpus_files = {
            "face_slapping": "pleasure_patterns/face_slapping_patterns.json",
            "power_fantasy": "pleasure_patterns/power_fantasy_patterns.json", 
            "upgrade": "pleasure_patterns/upgrade_patterns.json",
            "rhythm_templates": "pleasure_patterns/rhythm_templates.json",
            "enhancement_expressions": "pleasure_patterns/enhancement_expressions.json"
        }
        
        log_info("🎯爽感语料库", "爽感语料库管理器初始化完成", 
                语料库文件数=len(self.corpus_files))
    
    async def initialize(self) -> bool:
        """
        🚀 [初始化] 初始化语料库数据
        
        Returns:
            bool: 初始化是否成功
        """
        log_debug("🎯爽感语料库", "开始初始化语料库数据")
        
        try:
            # 加载所有语料库文件
            for corpus_key, file_path in self.corpus_files.items():
                corpus_data = await self.corpus_manager.load_corpus_file(file_path)
                if corpus_data:
                    self.corpus_data[corpus_key] = corpus_data
                    log_debug("🎯爽感语料库", f"成功加载{corpus_key}语料库",
                             文件路径=file_path,
                             数据条目数=len(corpus_data.categories) if hasattr(corpus_data, 'categories') else 0)
                else:
                    log_error("🎯爽感语料库", f"加载{corpus_key}语料库失败", 文件路径=file_path)
                    return False
            
            log_success("🎯爽感语料库", "语料库数据初始化完成", 
                       成功加载数=len(self.corpus_data))
            return True
            
        except Exception as e:
            log_error("🎯爽感语料库", "语料库初始化失败", error=e)
            return False
    
    async def get_pleasure_patterns_by_type(
        self,
        pleasure_type: str,
        count: int = 5,
        min_quality: float = 0.6
    ) -> List[PleasurePatternData]:
        """
        🎯 [模式获取] 根据爽感类型获取相关的爽感模式
        
        Args:
            pleasure_type: 爽感类型
            count: 获取数量
            min_quality: 最小质量要求
            
        Returns:
            List[PleasurePatternData]: 爽感模式数据列表
        """
        log_debug("🎯爽感语料库", "获取爽感模式",
                 爽感类型=pleasure_type, 数量=count, 最小质量=min_quality)
        
        # 确保语料库已初始化
        if not self.corpus_data:
            await self.initialize()
        
        patterns = []
        
        try:
            # 根据爽感类型选择对应的语料库
            corpus_key = self._get_corpus_key_by_pleasure_type(pleasure_type)
            if corpus_key not in self.corpus_data:
                log_error("🎯爽感语料库", "未找到对应的语料库", 爽感类型=pleasure_type)
                return patterns
            
            corpus_data = self.corpus_data[corpus_key]
            
            # 从语料库中提取模式数据
            if hasattr(corpus_data, 'categories'):
                for category_name, category_data in corpus_data.categories.items():
                    if hasattr(category_data, 'items'):
                        for item in category_data.items:
                            # 检查质量要求
                            item_quality = item.metadata.get('quality', 0.5)
                            if item_quality >= min_quality:
                                pattern_data = PleasurePatternData(
                                    content=item.content,
                                    weight=getattr(item, 'weight', 1.0),
                                    intensity=item.metadata.get('intensity', 0.5),
                                    reliability=item.metadata.get('reliability', 0.5),
                                    quality=item_quality,
                                    tags=getattr(item, 'tags', []),
                                    examples=getattr(item, 'examples', []),
                                    data=getattr(item, 'data', {})
                                )
                                patterns.append(pattern_data)
                                
                                if len(patterns) >= count:
                                    break
                    
                    if len(patterns) >= count:
                        break
            
            log_info("🎯爽感语料库", "成功获取爽感模式",
                    爽感类型=pleasure_type, 获取数量=len(patterns))

        except Exception as e:
            log_error("🎯爽感语料库", "获取爽感模式失败",
                     error=e, 爽感类型=pleasure_type)
        
        return patterns[:count]
    
    async def get_rhythm_template(self, template_name: str) -> Optional[RhythmTemplateData]:
        """
        🎵 [节奏模板] 获取指定的节奏模板
        
        Args:
            template_name: 模板名称
            
        Returns:
            Optional[RhythmTemplateData]: 节奏模板数据
        """
        log_debug("🎯爽感语料库", "获取节奏模板", 模板名称=template_name)
        
        # 确保语料库已初始化
        if not self.corpus_data:
            await self.initialize()
        
        try:
            if "rhythm_templates" not in self.corpus_data:
                log_error("🎯爽感语料库", "节奏模板语料库未加载")
                return None
            
            corpus_data = self.corpus_data["rhythm_templates"]
            
            # 在语料库中查找指定模板
            if hasattr(corpus_data, 'categories'):
                for category_name, category_data in corpus_data.categories.items():
                    if category_name == template_name or template_name in category_name:
                        if hasattr(category_data, 'items') and category_data.items:
                            item = category_data.items[0]  # 取第一个项目
                            if hasattr(item, 'data'):
                                return RhythmTemplateData(
                                    name=template_name,
                                    structure_breakdown=item.data.get('structure_breakdown', {}),
                                    emotion_curve=item.data.get('emotion_curve', []),
                                    techniques=item.data.get('transition_techniques', []) or item.data.get('progression_patterns', []),
                                    application_scenarios=item.data.get('application_scenarios', [])
                                )
            
            log_error("🎯爽感语料库", "未找到指定的节奏模板", 模板名称=template_name)
            return None
            
        except Exception as e:
            log_error("🎯爽感语料库", "获取节奏模板失败", error=e, 模板名称=template_name)
            return None
    
    async def get_enhancement_expressions(
        self, 
        expression_type: str, 
        count: int = 5
    ) -> List[str]:
        """
        💫 [强化表达] 获取指定类型的强化表达
        
        Args:
            expression_type: 表达类型（如：震惊反应、情感爆发等）
            count: 获取数量
            
        Returns:
            List[str]: 强化表达列表
        """
        log_debug("🎯爽感语料库", "获取强化表达", 表达类型=expression_type, 数量=count)
        
        # 确保语料库已初始化
        if not self.corpus_data:
            await self.initialize()
        
        expressions = []
        
        try:
            if "enhancement_expressions" not in self.corpus_data:
                log_error("🎯爽感语料库", "强化表达语料库未加载")
                return expressions
            
            corpus_data = self.corpus_data["enhancement_expressions"]
            
            # 在语料库中查找指定类型的表达
            if hasattr(corpus_data, 'categories'):
                for category_name, category_data in corpus_data.categories.items():
                    if expression_type in category_name or category_name in expression_type:
                        if hasattr(category_data, 'items'):
                            for item in category_data.items:
                                if hasattr(item, 'data'):
                                    # 从数据中提取表达列表
                                    for key, value_list in item.data.items():
                                        if isinstance(value_list, list):
                                            expressions.extend(value_list[:count])
                                            if len(expressions) >= count:
                                                break
                                if len(expressions) >= count:
                                    break
                        break
            
            log_info("🎯爽感语料库", "成功获取强化表达", 
                    表达类型=expression_type, 获取数量=len(expressions))
            
        except Exception as e:
            log_error("🎯爽感语料库", "获取强化表达失败", 
                     表达类型=expression_type, 错误=str(e))
        
        return expressions[:count]
    
    def _get_corpus_key_by_pleasure_type(self, pleasure_type: str) -> str:
        """
        🔄 [类型映射] 根据爽感类型获取对应的语料库键名

        Args:
            pleasure_type: 爽感类型字符串

        Returns:
            str: 语料库键名
        """
        mapping = {
            "face_slapping": "face_slapping",
            "power_fantasy": "power_fantasy",
            "upgrade": "upgrade",
            "recognition": "upgrade",  # 认可类型也使用升级模式
            "revenge": "face_slapping",  # 复仇类型使用打脸模式
            "mystery": "face_slapping"  # 解谜类型使用打脸模式
        }

        return mapping.get(pleasure_type, "face_slapping")


# ==================== 工厂函数 ====================

def create_pleasure_corpus_manager(corpus_root: Optional[str] = None) -> PleasureCorpusManager:
    """
    🏭 [工厂] 创建爽感语料库管理器实例
    
    Args:
        corpus_root: 语料库根目录路径
        
    Returns:
        PleasureCorpusManager: 爽感语料库管理器实例
    """
    return PleasureCorpusManager(corpus_root)
