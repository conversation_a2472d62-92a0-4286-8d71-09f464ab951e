// API 适配器 - 根据环境变量选择使用 Mock API 或真实 API
import { isMode, debugLog } from '../config/env';
import { MockApiService } from '../mock/mockApi';
import { SimpleApiService } from './api/client';
import type { StoryBible, Chapter } from '../stores/storyStore';
import type {
  StoryBibleRequest,
  StoryBibleResponse,
  ChapterGenerationRequest,
  ChapterResponse,
  TaskStatusResponse,
  AIProvider,
  StoryGenre
} from '../types/backend';
import {
  convertStoryBibleResponse,
  convertChapterResponse,
  convertToStoryBibleRequest,
  convertToChapterRequest
} from '../types/story';

// 统一的 API 适配器类
export class ApiAdapter {
  
  // 健康检查
  static async healthCheck(): Promise<{ connected: boolean, latency?: number }> {
    debugLog('API', '健康检查开始', { mode: isMode.mock() ? 'Mock' : 'Real' });
    
    if (isMode.mock()) {
      return await MockApiService.healthCheck();
    } else {
      // 调用真实 API
      return await SimpleApiService.healthCheck();
    }
  }

  // 登录接口
  static async loginWithFixedToken(): Promise<{ success: boolean, token?: string, message: string }> {
    debugLog('API', '登录请求开始', { mode: isMode.mock() ? 'Mock' : 'Real' });
    
    if (isMode.mock()) {
      return await MockApiService.loginWithFixedToken();
    } else {
      return await SimpleApiService.loginWithFixedToken();
    }
  }

  // Token验证
  static async validateToken(token: string): Promise<boolean> {
    debugLog('API', 'Token验证开始', { 
      mode: isMode.mock() ? 'Mock' : 'Real',
      tokenPrefix: token.substring(0, 10) + '...'
    });
    
    if (isMode.mock()) {
      return await MockApiService.validateToken(token);
    } else {
      return await SimpleApiService.validateToken(token);
    }
  }

  // 生成故事圣经（新版本 - 返回任务）
  static async generateStoryBibleV2(
    request: StoryBibleRequest
  ): Promise<StoryBibleResponse> {
    debugLog('API', '故事圣经生成开始（V2）', {
      mode: isMode.mock() ? 'Mock' : 'Real',
      title: request.title,
      genre: request.genre,
      aiProvider: request.ai_provider
    });

    if (isMode.mock()) {
      // Mock版本暂时返回模拟数据
      throw new Error('Mock API暂不支持V2接口，请使用真实API');
    } else {
      return await SimpleApiService.generateStoryBible(request);
    }
  }

  // 生成故事圣经（兼容旧版本）
  static async generateStoryBible(
    concept: string,
    aiProvider?: string
  ): Promise<StoryBible> {
    debugLog('API', '故事圣经生成开始（兼容版本）', {
      mode: isMode.mock() ? 'Mock' : 'Real',
      concept: concept.substring(0, 50) + '...',
      provider: aiProvider
    });

    if (isMode.mock()) {
      const mockResult = await MockApiService.generateStoryBible(concept, aiProvider);
      return mockResult as StoryBible;
    } else {
      // 转换为新格式请求
      const request: StoryBibleRequest = {
        title: '基于创意的小说',
        genre: StoryGenre.FANTASY,
        theme: concept,
        protagonist: '待定主角',
        setting: '待定设定',
        plot_outline: concept,
        ai_provider: (aiProvider as AIProvider) || AIProvider.ZHIPU,
        temperature: 0.8,
        max_tokens: 3000
      };

      const response = await SimpleApiService.generateStoryBible(request);
      return convertStoryBibleResponse(response);
    }
  }

  // 查询任务状态
  static async getTaskStatus(taskId: string): Promise<TaskStatusResponse | null> {
    debugLog('API', '查询任务状态', {
      mode: isMode.mock() ? 'Mock' : 'Real',
      taskId
    });

    if (isMode.mock()) {
      // Mock版本暂时返回模拟数据
      throw new Error('Mock API暂不支持任务状态查询，请使用真实API');
    } else {
      return await SimpleApiService.getTaskStatus(taskId);
    }
  }

  // 生成故事圣经（流式）
  static async generateStoryBibleStream(
    concept: string,
    aiProvider: string | null,
    onChunk: (chunk: string) => void,
    onComplete: (data: StoryBible) => void,
    onError: (error: string) => void
  ): Promise<void> {
    debugLog('API', '流式生成故事圣经开始', { 
      mode: isMode.mock() ? 'Mock' : 'Real',
      concept: concept.substring(0, 50) + '...',
      provider: aiProvider
    });
    
    if (isMode.mock()) {
      try {
        const generator = MockApiService.generateStoryBibleStream(
          concept, 
          aiProvider || undefined, 
          onChunk, 
          (data) => onComplete(data as StoryBible), 
          onError
        );
        
        // 消费generator但不重复调用onComplete
        for await (const _ of generator) {
          // 流式数据已在 generator 中处理，onComplete会在generator内部调用
        }
      } catch (error) {
        onError(error instanceof Error ? error.message : '生成失败');
      }
    } else {
      return await SimpleApiService.generateStoryBibleStream(
        concept, 
        aiProvider, 
        onChunk, 
        onComplete, 
        onError
      );
    }
  }

  // 生成章节内容（新版本 - 返回任务）
  static async generateChapterV2(
    request: ChapterGenerationRequest
  ): Promise<ChapterResponse> {
    debugLog('API', '章节生成开始（V2）', {
      mode: isMode.mock() ? 'Mock' : 'Real',
      storyBibleId: request.story_bible_id,
      chapterNumber: request.chapter_number,
      chapterTitle: request.chapter_title,
      aiProvider: request.ai_provider
    });

    if (isMode.mock()) {
      // Mock版本暂时返回模拟数据
      throw new Error('Mock API暂不支持V2接口，请使用真实API');
    } else {
      return await SimpleApiService.generateChapter(request);
    }
  }

  // 生成章节内容（兼容旧版本）
  static async generateChapter(
    storyBibleId: number | string,
    chapterNumber: number,
    outline: string,
    aiProvider?: string
  ): Promise<Chapter> {
    debugLog('API', '生成章节内容开始（兼容版本）', {
      mode: isMode.mock() ? 'Mock' : 'Real',
      storyBibleId,
      chapterNumber,
      outline: outline.substring(0, 50) + '...',
      provider: aiProvider
    });

    if (isMode.mock()) {
      const mockResult = await MockApiService.generateChapter(
        Number(storyBibleId),
        chapterNumber,
        outline,
        aiProvider
      );
      return mockResult as Chapter;
    } else {
      // 转换为新格式请求
      const request: ChapterGenerationRequest = {
        story_bible_id: String(storyBibleId),
        chapter_number: chapterNumber,
        chapter_title: `第${chapterNumber}章`,
        chapter_outline: outline,
        target_word_count: 2000,
        ai_provider: (aiProvider as AIProvider) || AIProvider.ZHIPU,
        temperature: 0.8,
        max_tokens: 4000
      };

      const response = await SimpleApiService.generateChapter(request);
      return convertChapterResponse(response);
    }
  }

  // 获取章节列表
  static async getChapters(storyBibleId: number): Promise<Chapter[]> {
    debugLog('API', '获取章节列表开始', { 
      mode: isMode.mock() ? 'Mock' : 'Real',
      storyBibleId
    });
    
    if (isMode.mock()) {
      const mockResult = await MockApiService.getChapters(storyBibleId);
      return mockResult as Chapter[];
    } else {
      return await SimpleApiService.getChapters(storyBibleId);
    }
  }
}

// 导出当前 API 模式信息
export const getCurrentApiMode = () => {
  if (isMode.mock()) return 'Mock 模式';
  if (isMode.development()) return '开发模式';
  if (isMode.production()) return '生产模式';
  return '未知模式';
};