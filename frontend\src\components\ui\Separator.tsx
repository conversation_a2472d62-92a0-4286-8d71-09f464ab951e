/**
 * 📏 [UI组件] Separator分隔线组件
 * 用于在内容之间添加视觉分隔，支持主题系统
 */

import React from 'react';
import { useTheme } from '../ThemeProvider';

export interface SeparatorProps {
  /** 分隔线方向 */
  orientation?: 'horizontal' | 'vertical';
  /** 自定义样式类名 */
  className?: string;
  /** 分隔线粗细 */
  thickness?: 'thin' | 'medium' | 'thick';
  /** 分隔线样式 */
  variant?: 'solid' | 'dashed' | 'dotted';
}

/**
 * Separator分隔线组件
 * 
 * 提供水平或垂直的分隔线，支持不同样式和粗细
 */
export const Separator: React.FC<SeparatorProps> = ({
  orientation = 'horizontal',
  className = '',
  thickness = 'thin',
  variant = 'solid'
}) => {
  const { isDark } = useTheme();

  const thicknessMap = {
    thin: orientation === 'horizontal' ? 'h-px' : 'w-px',
    medium: orientation === 'horizontal' ? 'h-0.5' : 'w-0.5',
    thick: orientation === 'horizontal' ? 'h-1' : 'w-1'
  };

  const variantMap = {
    solid: '',
    dashed: 'border-dashed',
    dotted: 'border-dotted'
  };

  const baseClasses = `
    ${orientation === 'horizontal' ? 'w-full' : 'h-full'}
    ${thicknessMap[thickness]}
    ${isDark ? 'bg-gray-700' : 'bg-gray-200'}
    ${variant !== 'solid' ? `border-t ${variantMap[variant]} ${isDark ? 'border-gray-700' : 'border-gray-200'} bg-transparent` : ''}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  return (
    <div 
      className={baseClasses}
      role="separator"
      aria-orientation={orientation}
    />
  );
};

export default Separator;
