"""
🌐 [世界图谱] 世界知识图谱管理API路由
提供实体和关系的CRUD操作，构建动态的"世界大脑"
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, select, func

from app.core.database import get_database_session
from app.models.world_graph import Entity, EntityRelationship, EntityType, RelationshipStatus, RelationshipTypes
from app.models.story_bible import StoryBible
from app.schemas.world_graph import (
    EntityCreate, EntityUpdate, EntityResponse,
    RelationshipCreate, RelationshipUpdate, RelationshipResponse,
    WorldGraphResponse, EntityListResponse, RelationshipListResponse
)
from app.core.config import log_info, log_error, log_debug


router = APIRouter(prefix="/api/v1/world", tags=["世界知识图谱"])


@router.post("/entities", response_model=EntityResponse, status_code=status.HTTP_201_CREATED)
async def create_entity(
    entity_data: EntityCreate,
    db: AsyncSession = Depends(get_database_session)
) -> EntityResponse:
    """
    🌐 [世界图谱] 创建新实体
    
    支持创建角色、物品、场景、概念、组织等各种叙事实体
    """
    log_info("世界图谱", "开始创建新实体", 
        entity_name=entity_data.name, 
        entity_type=entity_data.type.value,
        story_id=entity_data.story_id
    )
    
    try:
        # 验证故事是否存在
        story_result = await db.execute(select(StoryBible).filter(StoryBible.id == entity_data.story_id))
        story = story_result.scalar_one_or_none()
        if not story:
            log_error("世界图谱", "故事不存在", story_id=entity_data.story_id)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"故事 {entity_data.story_id} 不存在"
            )
        
        # 检查同名实体是否已存在
        existing_entity_result = await db.execute(select(Entity).filter(
            and_(
                Entity.story_id == entity_data.story_id,
                Entity.name == entity_data.name,
                Entity.type == entity_data.type
            )
        ))
        existing_entity = existing_entity_result.scalar_one_or_none()
        
        if existing_entity:
            log_error("世界图谱", "同名实体已存在", 
                entity_name=entity_data.name,
                entity_type=entity_data.type.value,
                existing_id=existing_entity.id
            )
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"实体 '{entity_data.name}' ({entity_data.type.value}) 已存在"
            )
        
        # 创建新实体
        new_entity = Entity(
            id=entity_data.id,
            story_id=entity_data.story_id,
            name=entity_data.name,
            type=entity_data.type,
            description=entity_data.description,
            properties=entity_data.properties,
            importance_score=entity_data.importance_score,
            first_mentioned_chapter=entity_data.first_mentioned_chapter,
            last_mentioned_chapter=entity_data.last_mentioned_chapter
        )
        
        db.add(new_entity)
        await db.commit()
        await db.refresh(new_entity)
        
        log_info("世界图谱", "实体创建成功", 
            entity_id=new_entity.id,
            entity_name=new_entity.name,
            entity_type=new_entity.type.value
        )
        
        return EntityResponse.from_attributes(new_entity)
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        log_error("世界图谱", "创建实体失败", error=e, 
            entity_name=entity_data.name,
            entity_type=entity_data.type.value
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建实体时发生内部错误"
        )


@router.get("/entities/{entity_id}", response_model=EntityResponse)
async def get_entity(
    entity_id: str,
    db: AsyncSession = Depends(get_database_session)
) -> EntityResponse:
    """
    🌐 [世界图谱] 获取指定实体信息
    """
    log_debug("世界图谱", "查询实体信息", entity_id=entity_id)
    
    result = await db.execute(select(Entity).filter(Entity.id == entity_id))
    entity = result.scalar_one_or_none()
    if not entity:
        log_error("世界图谱", "实体不存在", entity_id=entity_id)
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"实体 {entity_id} 不存在"
        )
    
    log_debug("世界图谱", "实体查询成功", 
        entity_id=entity.id,
        entity_name=entity.name,
        entity_type=entity.type.value
    )
    
    return EntityResponse.from_attributes(entity)


@router.put("/entities/{entity_id}", response_model=EntityResponse)
async def update_entity(
    entity_id: str,
    entity_data: EntityUpdate,
    db: AsyncSession = Depends(get_database_session)
) -> EntityResponse:
    """
    🌐 [世界图谱] 更新实体信息
    """
    log_info("世界图谱", "开始更新实体", entity_id=entity_id)
    
    try:
        result = await db.execute(select(Entity).filter(Entity.id == entity_id))
        entity = result.scalar_one_or_none()
        if not entity:
            log_error("世界图谱", "实体不存在", entity_id=entity_id)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"实体 {entity_id} 不存在"
            )
        
        # 更新字段
        update_data = entity_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(entity, field, value)
        
        await db.commit()
        await db.refresh(entity)
        
        log_info("世界图谱", "实体更新成功", 
            entity_id=entity.id,
            entity_name=entity.name,
            updated_fields=list(update_data.keys())
        )
        
        return EntityResponse.from_attributes(entity)
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        log_error("世界图谱", "更新实体失败", error=e, entity_id=entity_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新实体时发生内部错误"
        )


@router.delete("/entities/{entity_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_entity(
    entity_id: str,
    db: AsyncSession = Depends(get_database_session)
):
    """
    🌐 [世界图谱] 删除实体
    
    注意：删除实体会级联删除所有相关的关系
    """
    log_info("世界图谱", "开始删除实体", entity_id=entity_id)
    
    try:
        result = await db.execute(select(Entity).filter(Entity.id == entity_id))
        entity = result.scalar_one_or_none()
        if not entity:
            log_error("世界图谱", "实体不存在", entity_id=entity_id)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"实体 {entity_id} 不存在"
            )
        
        entity_name = entity.name
        entity_type = entity.type.value
        
        await db.delete(entity)
        await db.commit()
        
        log_info("世界图谱", "实体删除成功", 
            entity_id=entity_id,
            entity_name=entity_name,
            entity_type=entity_type
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        log_error("世界图谱", "删除实体失败", error=e, entity_id=entity_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除实体时发生内部错误"
        )


@router.get("/stories/{story_id}/entities", response_model=EntityListResponse)
async def get_story_entities(
    story_id: str,
    entity_type: Optional[EntityType] = None,
    is_active: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_database_session)
) -> EntityListResponse:
    """
    🌐 [世界图谱] 获取故事的所有实体
    
    支持按类型、活跃状态筛选，支持分页
    """
    log_debug("世界图谱", "查询故事实体列表", 
        story_id=story_id,
        entity_type=entity_type.value if entity_type else None,
        is_active=is_active,
        skip=skip,
        limit=limit
    )
    
    try:
        # 验证故事是否存在
        story_result = await db.execute(select(StoryBible).filter(StoryBible.id == story_id))
        story = story_result.scalar_one_or_none()
        if not story:
            log_error("世界图谱", "故事不存在", story_id=story_id)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"故事 {story_id} 不存在"
            )
        
        # 构建查询条件
        query = select(Entity).filter(Entity.story_id == story_id)
        count_query = select(func.count(Entity.id)).filter(Entity.story_id == story_id)
        
        if entity_type:
            query = query.filter(Entity.type == entity_type)
            count_query = count_query.filter(Entity.type == entity_type)
        
        if is_active is not None:
            query = query.filter(Entity.is_active == is_active)
            count_query = count_query.filter(Entity.is_active == is_active)
        
        # 获取总数
        count_result = await db.execute(count_query)
        total = count_result.scalar()
        
        # 分页查询
        paginated_query = query.offset(skip).limit(limit)
        result = await db.execute(paginated_query)
        entities = result.scalars().all()
        
        log_debug("世界图谱", "故事实体查询成功", 
            story_id=story_id,
            total_entities=total,
            returned_entities=len(entities)
        )
        
        return EntityListResponse(
            entities=[EntityResponse.from_attributes(entity) for entity in entities],
            total=total,
            skip=skip,
            limit=limit
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("世界图谱", "查询故事实体失败", error=e, story_id=story_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="查询故事实体时发生内部错误"
        )


@router.post("/relationships", response_model=RelationshipResponse, status_code=status.HTTP_201_CREATED)
async def create_relationship(
    relationship_data: RelationshipCreate,
    db: AsyncSession = Depends(get_database_session)
) -> RelationshipResponse:
    """
    🌐 [世界图谱] 创建新的实体关系

    建立两个实体之间的关系，如朋友、敌人、拥有、位于等
    """
    log_info("世界图谱", "开始创建实体关系",
        source_entity_id=relationship_data.source_entity_id,
        target_entity_id=relationship_data.target_entity_id,
        relationship_type=relationship_data.relationship_type
    )

    try:
        # 验证源实体是否存在
        source_result = await db.execute(select(Entity).filter(Entity.id == relationship_data.source_entity_id))
        source_entity = source_result.scalar_one_or_none()
        if not source_entity:
            log_error("世界图谱", "源实体不存在", entity_id=relationship_data.source_entity_id)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"源实体 {relationship_data.source_entity_id} 不存在"
            )

        # 验证目标实体是否存在
        target_result = await db.execute(select(Entity).filter(Entity.id == relationship_data.target_entity_id))
        target_entity = target_result.scalar_one_or_none()
        if not target_entity:
            log_error("世界图谱", "目标实体不存在", entity_id=relationship_data.target_entity_id)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"目标实体 {relationship_data.target_entity_id} 不存在"
            )

        # 验证两个实体属于同一个故事
        if source_entity.story_id != target_entity.story_id:
            log_error("世界图谱", "实体不属于同一故事",
                source_story_id=source_entity.story_id,
                target_story_id=target_entity.story_id
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能在不同故事的实体之间创建关系"
            )

        # 检查是否已存在相同的关系
        existing_result = await db.execute(select(EntityRelationship).filter(
            and_(
                EntityRelationship.source_entity_id == relationship_data.source_entity_id,
                EntityRelationship.target_entity_id == relationship_data.target_entity_id,
                EntityRelationship.relationship_type == relationship_data.relationship_type,
                EntityRelationship.status == RelationshipStatus.ACTIVE
            )
        ))
        existing_relationship = existing_result.scalar_one_or_none()

        if existing_relationship:
            log_error("世界图谱", "相同关系已存在",
                relationship_id=existing_relationship.id,
                relationship_type=relationship_data.relationship_type
            )
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"实体间的 '{relationship_data.relationship_type}' 关系已存在"
            )

        # 创建新关系
        new_relationship = EntityRelationship(
            id=relationship_data.id,
            source_entity_id=relationship_data.source_entity_id,
            target_entity_id=relationship_data.target_entity_id,
            relationship_type=relationship_data.relationship_type,
            description=relationship_data.description,
            status=relationship_data.status or RelationshipStatus.ACTIVE,
            strength=relationship_data.strength,
            is_bidirectional=relationship_data.is_bidirectional,
            properties=relationship_data.properties,
            established_chapter=relationship_data.established_chapter,
            last_updated_chapter=relationship_data.last_updated_chapter
        )

        db.add(new_relationship)
        await db.commit()
        await db.refresh(new_relationship)

        log_info("世界图谱", "关系创建成功",
            relationship_id=new_relationship.id,
            source_entity=source_entity.name,
            target_entity=target_entity.name,
            relationship_type=new_relationship.relationship_type
        )

        return RelationshipResponse.from_attributes(new_relationship)

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        log_error("世界图谱", "创建关系失败", error=e,
            source_entity_id=relationship_data.source_entity_id,
            target_entity_id=relationship_data.target_entity_id,
            relationship_type=relationship_data.relationship_type
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建关系时发生内部错误"
        )


@router.put("/relationships/{relationship_id}", response_model=RelationshipResponse)
async def update_relationship(
    relationship_id: str,
    relationship_data: RelationshipUpdate,
    db: AsyncSession = Depends(get_database_session)
) -> RelationshipResponse:
    """
    🌐 [世界图谱] 更新关系信息

    可以更新关系类型、状态、强度等属性
    """
    log_info("世界图谱", "开始更新关系", relationship_id=relationship_id)

    try:
        result = await db.execute(select(EntityRelationship).filter(EntityRelationship.id == relationship_id))
        relationship = result.scalar_one_or_none()
        if not relationship:
            log_error("世界图谱", "关系不存在", relationship_id=relationship_id)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"关系 {relationship_id} 不存在"
            )

        # 更新字段
        update_data = relationship_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(relationship, field, value)

        await db.commit()
        await db.refresh(relationship)

        log_info("世界图谱", "关系更新成功",
            relationship_id=relationship.id,
            relationship_type=relationship.relationship_type,
            updated_fields=list(update_data.keys())
        )

        return RelationshipResponse.from_attributes(relationship)

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        log_error("世界图谱", "更新关系失败", error=e, relationship_id=relationship_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新关系时发生内部错误"
        )


@router.get("/stories/{story_id}/graph", response_model=WorldGraphResponse)
async def get_world_graph(
    story_id: str,
    include_inactive: bool = False,
    db: AsyncSession = Depends(get_database_session)
) -> WorldGraphResponse:
    """
    🌐 [世界图谱] 获取完整的世界知识图谱

    返回故事的所有实体和关系，用于前端可视化
    """
    log_info("世界图谱", "开始获取完整世界图谱",
        story_id=story_id,
        include_inactive=include_inactive
    )

    try:
        # 验证故事是否存在
        story_result = await db.execute(select(StoryBible).filter(StoryBible.id == story_id))
        story = story_result.scalar_one_or_none()
        if not story:
            log_error("世界图谱", "故事不存在", story_id=story_id)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"故事 {story_id} 不存在"
            )

        # 获取所有实体
        entities_query = select(Entity).filter(Entity.story_id == story_id)
        if not include_inactive:
            entities_query = entities_query.filter(Entity.is_active == True)

        entities_result = await db.execute(entities_query)
        entities = entities_result.scalars().all()

        # 获取所有关系
        relationships_query = select(EntityRelationship).join(
            Entity, EntityRelationship.source_entity_id == Entity.id
        ).filter(Entity.story_id == story_id)

        if not include_inactive:
            relationships_query = relationships_query.filter(
                EntityRelationship.status == RelationshipStatus.ACTIVE
            )

        relationships_result = await db.execute(relationships_query)
        relationships = relationships_result.scalars().all()

        # 统计信息
        entity_stats = {}
        for entity in entities:
            entity_type = entity.type.value
            entity_stats[entity_type] = entity_stats.get(entity_type, 0) + 1

        relationship_stats = {}
        for relationship in relationships:
            rel_type = relationship.relationship_type
            relationship_stats[rel_type] = relationship_stats.get(rel_type, 0) + 1

        log_info("世界图谱", "世界图谱获取成功",
            story_id=story_id,
            total_entities=len(entities),
            total_relationships=len(relationships),
            entity_stats=entity_stats,
            relationship_stats=relationship_stats
        )

        return WorldGraphResponse(
            story_id=story_id,
            story_title=story.title,
            entities=[EntityResponse.from_attributes(entity) for entity in entities],
            relationships=[RelationshipResponse.from_attributes(rel) for rel in relationships],
            entity_count=len(entities),
            relationship_count=len(relationships),
            entity_stats=entity_stats,
            relationship_stats=relationship_stats
        )

    except HTTPException:
        raise
    except Exception as e:
        log_error("世界图谱", "获取世界图谱失败", error=e, story_id=story_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取世界图谱时发生内部错误"
        )
