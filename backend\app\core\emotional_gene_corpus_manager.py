"""
🧬 [情感基因语料库] 情感基因语料库管理器

将语料库数据与情感基因系统集成，提供从语料库文件加载情感基因数据的功能，
支持基础情感、复杂情感、生理反应、感官触发器等多种类型的情感数据。

主要功能:
1. 从语料库文件加载情感基因数据
2. 将语料库数据转换为情感基因格式
3. 提供情感基因数据的查询和筛选
4. 支持动态加载和缓存机制

作者: 文心小说后端服务系统
创建时间: 2025-08-05
"""

import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path

from app.core.config import log_info, log_debug, log_error, log_success
from app.core.corpus_manager import create_corpus_manager, CorpusManager
from app.models.emotional_gene import EmotionalGene


@dataclass
class EmotionalGeneData:
    """情感基因数据类"""
    emotion_tag: str
    physiological_reactions: List[str]
    sensory_triggers: List[str]
    entropy_items: List[str]
    intensity_score: float
    reliability_score: float
    quality_score: float
    category: str
    subcategory: str
    source_text: str
    source_type: str = "语料库"
    purification_level: str = "basic"


class EmotionalGeneCorpusManager:
    """🧬 情感基因语料库管理器"""
    
    def __init__(self, corpus_root: Optional[str] = None):
        """初始化情感基因语料库管理器"""
        self.corpus_manager = create_corpus_manager(corpus_root)
        self.corpus_data = {}
        self._initialized = False
        
        # 语料库文件路径映射
        self.corpus_files = {
            "basic_emotions": "emotional_genes/basic_emotions.json",
            "complex_emotions": "emotional_genes/complex_emotions.json",
            "physiological_reactions": "emotional_genes/physiological_reactions.json",
            "sensory_triggers": "emotional_genes/sensory_triggers.json",
            "emotional_transitions": "emotional_genes/emotional_transitions.json"
        }
        
        log_debug("情感基因语料库", "情感基因语料库管理器初始化完成")
    
    async def initialize(self) -> None:
        """🚀 初始化语料库管理器"""
        if self._initialized:
            return
            
        log_info("情感基因语料库", "开始初始化情感基因语料库管理器")
        
        try:
            # 初始化语料库管理器
            await self.corpus_manager.initialize()
            
            # 加载各类情感基因语料库文件
            for corpus_type, file_path in self.corpus_files.items():
                try:
                    corpus_data = await self.corpus_manager.load_corpus_file(file_path)
                    self.corpus_data[corpus_type] = corpus_data
                    log_debug("情感基因语料库", f"成功加载{corpus_type}语料库",
                             文件=file_path,
                             分类数=len(corpus_data.categories))
                except Exception as e:
                    log_error("情感基因语料库", f"加载{corpus_type}语料库失败", error=e, 文件=file_path)
                    # 使用空数据作为后备
                    self.corpus_data[corpus_type] = None
            
            self._initialized = True
            log_success("情感基因语料库", "情感基因语料库管理器初始化完成",
                       成功加载=len([k for k, v in self.corpus_data.items() if v is not None]))
            
        except Exception as e:
            log_error("情感基因语料库", "情感基因语料库管理器初始化失败", error=e)
            raise
    
    async def get_emotion_genes_by_tag(
        self,
        emotion_tag: str,
        count: int = 5,
        min_quality: float = 0.6
    ) -> List[EmotionalGeneData]:
        """
        🔍 根据情感标签获取情感基因数据
        
        Args:
            emotion_tag: 情感标签
            count: 返回数量
            min_quality: 最低质量要求
            
        Returns:
            List[EmotionalGeneData]: 情感基因数据列表
        """
        await self.initialize()
        
        log_debug("情感基因语料库", "根据情感标签获取情感基因", 
                 情感标签=emotion_tag, 数量=count, 最低质量=min_quality)
        
        gene_data_list = []
        
        # 从基础情感和复杂情感中查找
        for corpus_type in ["basic_emotions", "complex_emotions"]:
            corpus_data = self.corpus_data.get(corpus_type)
            if not corpus_data:
                continue
                
            # 查找匹配的情感类别
            for category_name, category_data in corpus_data.categories.items():
                if emotion_tag in category_name or category_name in emotion_tag:
                    # 获取该类别下的所有条目
                    items = category_data.items if hasattr(category_data, 'items') else []
                    for item in items:
                        # 检查质量要求
                        item_quality = item.metadata.get("quality", 0.0) if hasattr(item, 'metadata') else 0.0
                        if item_quality < min_quality:
                            continue

                        # 转换为情感基因数据
                        gene_data = self._convert_to_gene_data(
                            item, category_name, corpus_type, emotion_tag
                        )
                        if gene_data:
                            gene_data_list.append(gene_data)
        
        # 随机选择指定数量的基因数据
        if len(gene_data_list) > count:
            gene_data_list = random.sample(gene_data_list, count)
        
        log_info("情感基因语料库", "情感基因数据获取完成", 
                情感标签=emotion_tag, 找到数量=len(gene_data_list))
        
        return gene_data_list
    
    async def get_physiological_reactions(
        self,
        system_type: Optional[str] = None,
        intensity_level: Optional[str] = None,
        count: int = 10
    ) -> List[str]:
        """
        💓 获取生理反应数据
        
        Args:
            system_type: 系统类型 (心血管系统、呼吸系统等)
            intensity_level: 强度等级 (轻微、中等、强烈、极端)
            count: 返回数量
            
        Returns:
            List[str]: 生理反应列表
        """
        await self.initialize()
        
        log_debug("情感基因语料库", "获取生理反应数据", 
                 系统类型=system_type, 强度等级=intensity_level, 数量=count)
        
        reactions = []
        corpus_data = self.corpus_data.get("physiological_reactions")
        
        if not corpus_data:
            log_error("情感基因语料库", "生理反应语料库未加载")
            return []
        
        # 遍历所有系统类别
        for category_name, category_data in corpus_data.categories.items():
            # 如果指定了系统类型，只处理匹配的类别
            if system_type and system_type not in category_name:
                continue

            items = category_data.items if hasattr(category_data, 'items') else []
            for item in items:
                item_data = item.data if hasattr(item, 'data') else {}

                if intensity_level and "intensity_levels" in item_data:
                    # 获取指定强度等级的反应
                    level_reactions = item_data["intensity_levels"].get(intensity_level, [])
                    reactions.extend(level_reactions)
                else:
                    # 获取所有反应
                    item_reactions = item_data.get("reactions", [])
                    reactions.extend(item_reactions)
        
        # 随机选择指定数量的反应
        if len(reactions) > count:
            reactions = random.sample(reactions, count)
        
        log_info("情感基因语料库", "生理反应数据获取完成", 找到数量=len(reactions))
        return reactions
    
    async def get_sensory_triggers(
        self,
        sense_type: Optional[str] = None,
        emotion_context: Optional[str] = None,
        count: int = 10
    ) -> List[str]:
        """
        👁️ 获取感官触发器数据
        
        Args:
            sense_type: 感官类型 (视觉、听觉、嗅觉、触觉、味觉)
            emotion_context: 情感上下文
            count: 返回数量
            
        Returns:
            List[str]: 感官触发器列表
        """
        await self.initialize()
        
        log_debug("情感基因语料库", "获取感官触发器数据", 
                 感官类型=sense_type, 情感上下文=emotion_context, 数量=count)
        
        triggers = []
        corpus_data = self.corpus_data.get("sensory_triggers")
        
        if not corpus_data:
            log_error("情感基因语料库", "感官触发器语料库未加载")
            return []
        
        # 遍历所有感官类别
        for category_name, category_data in corpus_data.categories.items():
            # 如果指定了感官类型，只处理匹配的类别
            if sense_type and sense_type not in category_name:
                continue

            items = category_data.items if hasattr(category_data, 'items') else []
            for item in items:
                item_data = item.data if hasattr(item, 'data') else {}

                # 获取触发器列表
                item_triggers = item_data.get("triggers", [])

                # 如果指定了情感上下文，进行过滤
                if emotion_context and "emotion_mapping" in item_data:
                    emotion_mapping = item_data["emotion_mapping"]
                    # 查找与情感上下文相关的触发器
                    for key, emotions in emotion_mapping.items():
                        if emotion_context in emotions:
                            # 添加与该键相关的触发器
                            related_triggers = [t for t in item_triggers if key in t]
                            triggers.extend(related_triggers)
                else:
                    triggers.extend(item_triggers)
        
        # 随机选择指定数量的触发器
        if len(triggers) > count:
            triggers = random.sample(triggers, count)
        
        log_info("情感基因语料库", "感官触发器数据获取完成", 找到数量=len(triggers))
        return triggers
    
    def _convert_to_gene_data(
        self,
        item: Any,
        category_name: str,
        corpus_type: str,
        emotion_tag: str
    ) -> Optional[EmotionalGeneData]:
        """将语料库条目转换为情感基因数据"""
        try:
            item_data = item.data if hasattr(item, 'data') else {}
            metadata = item.metadata if hasattr(item, 'metadata') else {}
            
            # 提取生理反应
            physiological_reactions = item_data.get("physiological_reactions", [])
            
            # 提取感官触发器
            sensory_triggers = item_data.get("sensory_triggers", [])
            
            # 提取熵增项目
            entropy_items = item_data.get("entropy_items", [])
            
            # 生成源文本
            source_text = f"来自{corpus_type}语料库的{category_name}情感数据: {item.content if hasattr(item, 'content') else ''}"
            
            # 创建情感基因数据
            gene_data = EmotionalGeneData(
                emotion_tag=emotion_tag,
                physiological_reactions=physiological_reactions,
                sensory_triggers=sensory_triggers,
                entropy_items=entropy_items,
                intensity_score=metadata.get("intensity", 0.7),
                reliability_score=metadata.get("reliability", 0.8),
                quality_score=metadata.get("quality", 0.75),
                category=category_name,
                subcategory=item.content if hasattr(item, 'content') else "",
                source_text=source_text,
                source_type="语料库",
                purification_level="corpus"
            )
            
            return gene_data
            
        except Exception as e:
            log_error("情感基因语料库", "转换情感基因数据失败", error=e, 条目=item.content if hasattr(item, 'content') else 'unknown')
            return None
    
    async def get_all_emotion_tags(self) -> List[str]:
        """📋 获取所有可用的情感标签"""
        await self.initialize()
        
        emotion_tags = set()
        
        # 从基础情感和复杂情感中提取标签
        for corpus_type in ["basic_emotions", "complex_emotions"]:
            corpus_data = self.corpus_data.get(corpus_type)
            if corpus_data:
                emotion_tags.update(corpus_data.categories.keys())
        
        return list(emotion_tags)


def create_emotional_gene_corpus_manager(corpus_root: Optional[str] = None) -> EmotionalGeneCorpusManager:
    """创建情感基因语料库管理器实例"""
    return EmotionalGeneCorpusManager(corpus_root)
