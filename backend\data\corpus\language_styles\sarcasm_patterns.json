{"name": "讽刺表达模式库", "version": "1.0.0", "description": "包含各种讽刺、反讽、夸张等表达模式，用于增加文本的幽默感和讽刺效果", "category": "language_styles", "tags": ["讽刺", "反讽", "幽默", "夸张", "阴阳怪气"], "metadata": {"source": "网络流行语和传统讽刺表达", "quality_score": 0.78, "usage_frequency": "medium", "last_updated": "2025-08-05", "contributor": "系统内置", "review_status": "approved", "language": "zh-CN", "encoding": "utf-8"}, "usage_contexts": {"幽默对话": {"description": "角色间的幽默讽刺对话", "weight": 1.0, "conditions": ["轻松氛围", "角色冲突", "喜剧情节"]}, "内心独白": {"description": "角色内心的讽刺想法", "weight": 0.9, "conditions": ["第一人称", "内心戏", "吐槽情节"]}, "叙述风格": {"description": "叙述者的讽刺语调", "weight": 0.8, "conditions": ["幽默文风", "轻小说", "网文风格"]}}, "data": {"patterns": {"反讽模式": {"description": "通过反话来表达讽刺", "weight": 1.0, "templates": [{"template": "真是{形容词}啊", "variables": {"形容词": {"type": "adjective", "description": "与实际情况相反的形容词", "examples": ["聪明", "厉害", "了不起", "有本事"]}}, "weight": 1.0, "usage_contexts": ["嘲讽", "不满"], "examples": ["真是聪明啊（讽刺愚蠢）", "真是厉害啊（讽刺无能）"]}, {"template": "可真{形容词}", "variables": {"形容词": {"type": "adjective", "description": "夸张的正面形容词", "examples": ["棒", "牛", "绝", "强"]}}, "weight": 0.9, "usage_contexts": ["讽刺", "挖苦"], "examples": ["可真棒", "可真牛", "可真绝了"]}, {"template": "这就是传说中的{名词}吧", "variables": {"名词": {"type": "noun", "description": "夸张的称谓或概念", "examples": ["天才", "高手", "大师", "专家"]}}, "weight": 0.85, "usage_contexts": ["嘲讽", "质疑"], "examples": ["这就是传说中的天才吧", "这就是传说中的高手吧"]}]}, "夸张模式": {"description": "通过夸张来制造讽刺效果", "weight": 0.9, "templates": [{"template": "简直{形容词}到了极点", "variables": {"形容词": {"type": "adjective", "description": "可以夸张的形容词", "examples": ["厉害", "聪明", "完美", "无敌"]}}, "weight": 1.0, "usage_contexts": ["夸张讽刺"], "examples": ["简直厉害到了极点", "简直聪明到了极点"]}, {"template": "这{名词}，绝了", "variables": {"名词": {"type": "noun", "description": "可以评价的事物", "examples": ["操作", "想法", "做法", "表现"]}}, "weight": 0.9, "usage_contexts": ["夸张", "感叹"], "examples": ["这操作，绝了", "这想法，绝了"]}, {"template": "真是{形容词}得不行", "variables": {"形容词": {"type": "adjective", "description": "可以程度化的形容词", "examples": ["牛", "强", "绝", "棒"]}}, "weight": 0.85, "usage_contexts": ["夸张表达"], "examples": ["真是牛得不行", "真是强得不行"]}]}, "对比模式": {"description": "通过对比来突出讽刺", "weight": 0.8, "templates": [{"template": "比{对象}还{形容词}", "variables": {"对象": {"type": "noun", "description": "用于对比的对象", "examples": ["猪", "傻子", "小学生", "新手"]}, "形容词": {"type": "adjective", "description": "贬义形容词", "examples": ["笨", "蠢", "菜", "弱"]}}, "weight": 0.8, "usage_contexts": ["贬低", "对比"], "examples": ["比猪还笨", "比小学生还菜"]}, {"template": "连{对象}都不如", "variables": {"对象": {"type": "noun", "description": "能力较低的对象", "examples": ["三岁小孩", "新手", "菜鸟"]}}, "weight": 0.75, "usage_contexts": ["贬低", "讽刺"], "examples": ["连三岁小孩都不如", "连新手都不如"]}]}}, "categories": {"网络流行讽刺": {"description": "网络上流行的讽刺表达", "weight": 0.9, "items": [{"content": "6得飞起", "weight": 1.0, "metadata": {"type": "internet_slang", "popularity": "high", "age_group": "young"}, "tags": ["网络用语", "夸张"], "examples": ["这操作6得飞起", "技术6得飞起"]}, {"content": "秀儿是你吗", "weight": 0.9, "metadata": {"type": "internet_meme", "popularity": "high", "context": "surprise"}, "tags": ["网络梗", "惊叹"], "examples": ["这想法，秀儿是你吗", "这操作，秀儿是你吗"]}, {"content": "我直接好家伙", "weight": 0.85, "metadata": {"type": "internet_expression", "popularity": "medium", "emotion": "surprise"}, "tags": ["网络用语", "惊叹"], "examples": ["看到这个，我直接好家伙", "听到消息，我直接好家伙"]}]}, "传统讽刺表达": {"description": "传统的讽刺和挖苦表达", "weight": 0.8, "items": [{"content": "真是长见识了", "weight": 0.9, "metadata": {"type": "traditional_sarcasm", "formality": "medium", "politeness": "fake_polite"}, "tags": ["传统讽刺", "假客气"], "examples": ["听你这么说，真是长见识了", "看你这操作，真是长见识了"]}, {"content": "佩服佩服", "weight": 0.85, "metadata": {"type": "traditional_sarcasm", "formality": "high", "politeness": "fake_polite"}, "tags": ["传统讽刺", "假恭维"], "examples": ["这种做法，佩服佩服", "这个想法，佩服佩服"]}]}}}, "examples": [{"input": "他的表现很好。", "output": "他的表现真是好啊。", "context": "反讽改写", "explanation": "通过反讽模式表达相反的意思"}, {"input": "这个想法不错。", "output": "这想法，绝了。", "context": "夸张讽刺", "explanation": "使用夸张模式制造讽刺效果"}], "validation": {"schema_version": "1.0", "required_fields": ["template", "variables", "weight"], "validation_rules": [{"field": "weight", "type": "float", "range": [0.0, 1.0]}]}, "statistics": {"total_entries": 5, "categories_count": 2, "patterns_count": 3, "average_quality": 0.86, "last_calculated": "2025-08-05"}}