"""
🌐 [世界图谱] 世界知识图谱相关的Pydantic模型
定义API请求和响应的数据结构
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, field_validator
import uuid

from app.models.world_graph import EntityType, RelationshipStatus


class EntityBase(BaseModel):
    """实体基础模型"""
    name: str = Field(..., description="实体名称", max_length=200)
    type: EntityType = Field(..., description="实体类型")
    description: Optional[str] = Field(None, description="实体描述")
    properties: Optional[Dict[str, Any]] = Field(default_factory=dict, description="实体属性")
    importance_score: float = Field(default=1.0, ge=0.0, le=10.0, description="重要性评分")
    first_mentioned_chapter: Optional[int] = Field(None, ge=1, description="首次提及章节")
    last_mentioned_chapter: Optional[int] = Field(None, ge=1, description="最后提及章节")


class EntityCreate(EntityBase):
    """创建实体的请求模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="实体唯一标识")
    story_id: str = Field(..., description="所属故事ID")
    
    @field_validator('last_mentioned_chapter')
    @classmethod
    def validate_chapter_order(cls, v, info):
        """验证章节顺序"""
        if v is not None and info.data and 'first_mentioned_chapter' in info.data:
            first_chapter = info.data['first_mentioned_chapter']
            if first_chapter is not None and v < first_chapter:
                raise ValueError('最后提及章节不能早于首次提及章节')
        return v


class EntityUpdate(BaseModel):
    """更新实体的请求模型"""
    name: Optional[str] = Field(None, description="实体名称", max_length=200)
    description: Optional[str] = Field(None, description="实体描述")
    properties: Optional[Dict[str, Any]] = Field(None, description="实体属性")
    is_active: Optional[bool] = Field(None, description="是否活跃")
    importance_score: Optional[float] = Field(None, ge=0.0, le=10.0, description="重要性评分")
    last_mentioned_chapter: Optional[int] = Field(None, ge=1, description="最后提及章节")


class EntityResponse(EntityBase):
    """实体响应模型"""
    id: str = Field(..., description="实体唯一标识")
    story_id: str = Field(..., description="所属故事ID")
    is_active: bool = Field(..., description="是否活跃")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class EntityListResponse(BaseModel):
    """实体列表响应模型"""
    entities: List[EntityResponse] = Field(..., description="实体列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class RelationshipBase(BaseModel):
    """关系基础模型"""
    relationship_type: str = Field(..., description="关系类型", max_length=100)
    description: Optional[str] = Field(None, description="关系描述")
    status: Optional[RelationshipStatus] = Field(default=RelationshipStatus.ACTIVE, description="关系状态")
    strength: float = Field(default=1.0, ge=0.0, le=10.0, description="关系强度")
    is_bidirectional: bool = Field(default=False, description="是否双向关系")
    properties: Optional[Dict[str, Any]] = Field(default_factory=dict, description="关系属性")
    established_chapter: Optional[int] = Field(None, ge=1, description="建立关系的章节")
    last_updated_chapter: Optional[int] = Field(None, ge=1, description="最后更新章节")


class RelationshipCreate(RelationshipBase):
    """创建关系的请求模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="关系唯一标识")
    source_entity_id: str = Field(..., description="源实体ID")
    target_entity_id: str = Field(..., description="目标实体ID")

    @field_validator('target_entity_id')
    @classmethod
    def validate_different_entities(cls, v, info):
        """验证源实体和目标实体不能相同"""
        if info.data and 'source_entity_id' in info.data and v == info.data['source_entity_id']:
            raise ValueError('源实体和目标实体不能相同')
        return v
    
    @field_validator('last_updated_chapter')
    @classmethod
    def validate_chapter_order(cls, v, info):
        """验证章节顺序"""
        if v is not None and info.data and 'established_chapter' in info.data:
            established_chapter = info.data['established_chapter']
            if established_chapter is not None and v < established_chapter:
                raise ValueError('最后更新章节不能早于建立章节')
        return v


class RelationshipUpdate(BaseModel):
    """更新关系的请求模型"""
    relationship_type: Optional[str] = Field(None, description="关系类型", max_length=100)
    description: Optional[str] = Field(None, description="关系描述")
    status: Optional[RelationshipStatus] = Field(None, description="关系状态")
    strength: Optional[float] = Field(None, ge=0.0, le=10.0, description="关系强度")
    is_bidirectional: Optional[bool] = Field(None, description="是否双向关系")
    properties: Optional[Dict[str, Any]] = Field(None, description="关系属性")
    last_updated_chapter: Optional[int] = Field(None, ge=1, description="最后更新章节")


class RelationshipResponse(RelationshipBase):
    """关系响应模型"""
    id: str = Field(..., description="关系唯一标识")
    source_entity_id: str = Field(..., description="源实体ID")
    target_entity_id: str = Field(..., description="目标实体ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class RelationshipListResponse(BaseModel):
    """关系列表响应模型"""
    relationships: List[RelationshipResponse] = Field(..., description="关系列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class WorldGraphResponse(BaseModel):
    """完整世界图谱响应模型"""
    story_id: str = Field(..., description="故事ID")
    story_title: str = Field(..., description="故事标题")
    entities: List[EntityResponse] = Field(..., description="所有实体")
    relationships: List[RelationshipResponse] = Field(..., description="所有关系")
    entity_count: int = Field(..., description="实体总数")
    relationship_count: int = Field(..., description="关系总数")
    entity_stats: Dict[str, int] = Field(..., description="实体类型统计")
    relationship_stats: Dict[str, int] = Field(..., description="关系类型统计")


class EntitySearchRequest(BaseModel):
    """实体搜索请求模型"""
    query: str = Field(..., description="搜索关键词", min_length=1)
    entity_types: Optional[List[EntityType]] = Field(None, description="筛选的实体类型")
    is_active: Optional[bool] = Field(None, description="是否只搜索活跃实体")
    min_importance: Optional[float] = Field(None, ge=0.0, le=10.0, description="最小重要性评分")


class RelationshipSearchRequest(BaseModel):
    """关系搜索请求模型"""
    entity_id: Optional[str] = Field(None, description="相关实体ID")
    relationship_types: Optional[List[str]] = Field(None, description="筛选的关系类型")
    status: Optional[RelationshipStatus] = Field(None, description="关系状态")
    min_strength: Optional[float] = Field(None, ge=0.0, le=10.0, description="最小关系强度")


class GraphAnalyticsResponse(BaseModel):
    """图谱分析响应模型"""
    story_id: str = Field(..., description="故事ID")
    total_entities: int = Field(..., description="实体总数")
    total_relationships: int = Field(..., description="关系总数")
    entity_type_distribution: Dict[str, int] = Field(..., description="实体类型分布")
    relationship_type_distribution: Dict[str, int] = Field(..., description="关系类型分布")
    most_connected_entities: List[Dict[str, Any]] = Field(..., description="连接度最高的实体")
    relationship_density: float = Field(..., description="关系密度")
    average_entity_importance: float = Field(..., description="平均实体重要性")
    chapter_coverage: Dict[str, int] = Field(..., description="章节覆盖情况")
