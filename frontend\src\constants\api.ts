// API端点常量 - 根据后端实际API端点定义
export const API_ENDPOINTS = {
  // 系统相关
  HEALTH: '/health',
  ROOT: '/',

  // 故事生成相关 (generation.py)
  STORY: {
    GENERATE_BIBLE: '/api/v1/generate-bible',
    GENERATE_CHAPTER: '/api/v1/generate-chapter',
    GET_TASK_STATUS: (taskId: string) => `/api/v1/tasks/${taskId}/status`,
    LIST_TASKS: '/api/v1/tasks',
    GET_BIBLE_CHAPTERS: (bibleId: string) => `/api/v1/bible/${bibleId}/chapters`
  },

  // 情感基因库相关 (emotion_genes.py)
  EMOTION_GENES: {
    BASE: '/api/v1/emotion-genes',
    SEARCH: '/api/v1/emotion-genes/search',
    STATS: '/api/v1/emotion-genes/stats'
  },

  // 记忆嵌入相关 (memory.py)
  MEMORY: {
    BASE: '/api/v1/memory',
    SEARCH: '/api/v1/memory/search',
    EMBED: '/api/v1/memory/embed'
  },

  // 世界知识图谱相关 (world_graph.py)
  WORLD_GRAPH: {
    ENTITIES: '/api/v1/world-graph/entities',
    RELATIONSHIPS: '/api/v1/world-graph/relationships',
    SEARCH: '/api/v1/world-graph/search'
  },

  // 剧情分析相关 (plot_analysis.py)
  PLOT_ANALYSIS: {
    BASE: '/api/v1/plot-analysis',
    RECOMMEND: '/api/v1/plot-analysis/recommend'
  },

  // 冲突检测相关 (conflict_detection.py)
  CONFLICT_DETECTION: {
    BASE: '/api/v1/conflict-detection',
    ANALYZE: '/api/v1/conflict-detection/analyze'
  },

  // 剧情规划相关 (plot_planning.py)
  PLOT_PLANNING: {
    BASE: '/api/v1/plot-planning',
    GENERATE: '/api/v1/plot-planning/generate'
  },

  // 关系分析相关 (relationship_analysis.py)
  RELATIONSHIP_ANALYSIS: {
    BASE: '/api/v1/relationship-analysis',
    ANALYZE: '/api/v1/relationship-analysis/analyze'
  },

  // AI智能体编排相关 (agent_orchestration.py)
  AGENT_ORCHESTRATION: {
    BASE: '/api/v1/agent-orchestration',
    EXECUTE: '/api/v1/agent-orchestration/execute'
  },

  // 文学风格相关 (literary_style.py)
  LITERARY_STYLE: {
    BASE: '/api/v1/literary-style',
    ENHANCE: '/api/v1/literary-style/enhance'
  },

  // 语料库管理相关 (corpus_management.py)
  CORPUS_MANAGEMENT: {
    BASE: '/api/v1/corpus-management',
    UPLOAD: '/api/v1/corpus-management/upload',
    SEARCH: '/api/v1/corpus-management/search'
  }
} as const;

// HTTP状态码常量
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
} as const;

// API配置常量
export const API_CONFIG = {
  BASE_URL: '',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000
} as const;