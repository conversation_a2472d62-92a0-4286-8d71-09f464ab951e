"""
🧪 [测试] 测试配置和共享fixture
提供测试所需的数据库会话、样本数据等fixture
"""

import pytest
import tempfile
from pathlib import Path
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from httpx import AsyncClient
import asyncio

from app.core.database import Base, get_database_session
from app.models.story_bible import StoryBible, Chapter
from app.models.world_graph import Entity, EntityRelationship, EntityType, RelationshipStatus
from app.schemas.generation import StoryGenre, AIProvider, GenerationStatus
from app.core.config import log_debug
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from main import create_app


@pytest.fixture(scope="function")
def db_session():
    """
    🧪 [测试] 创建同步数据库会话fixture
    为每个测试函数创建独立的临时数据库
    """
    # 创建临时数据库
    temp_dir = tempfile.mkdtemp()
    temp_db_path = Path(temp_dir) / "test_world_graph.db"
    db_url = f"sqlite:///{temp_db_path}"
    
    log_debug("测试", "创建临时测试数据库", 路径=str(temp_db_path))
    
    # 创建同步引擎和会话
    engine = create_engine(db_url, echo=False)
    SessionLocal = sessionmaker(bind=engine)
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    # 创建会话
    session = SessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        engine.dispose()
        
        # 清理临时文件
        try:
            if temp_db_path.exists():
                temp_db_path.unlink()
                log_debug("测试", "清理临时测试数据库", 路径=str(temp_db_path))
        except PermissionError:
            log_debug("测试", "临时数据库文件清理跳过（文件被占用）", 路径=str(temp_db_path))


@pytest.fixture(scope="function")
def sample_story_bible(db_session: Session):
    """
    🧪 [测试] 创建样本故事圣经fixture
    为测试提供一个基础的故事圣经实例
    """
    story_bible = StoryBible(
        id="test_story_001",
        title="测试武侠小说",
        genre=StoryGenre.MARTIAL_ARTS,
        theme="江湖恩仇，侠义精神",
        protagonist="张三，年轻剑客，正义感强",
        setting="古代江湖，武林门派林立",
        plot_outline="主角张三在江湖中历练成长的故事",
        writing_style="古典武侠风格",
        ai_provider=AIProvider.ZHIPU,
        status=GenerationStatus.PENDING
    )
    
    db_session.add(story_bible)
    db_session.commit()
    
    log_debug("测试", "创建样本故事圣经", 
        story_id=story_bible.id, 
        title=story_bible.title
    )
    
    return story_bible


@pytest.fixture(scope="function")
def sample_chapter(db_session: Session, sample_story_bible: StoryBible):
    """
    🧪 [测试] 创建样本章节fixture
    为测试提供一个基础的章节实例
    """
    chapter = Chapter(
        id="test_chapter_001",
        story_bible_id=sample_story_bible.id,
        chapter_number=1,
        title="初入江湖",
        content="张三踏出家门，开始了他的江湖之路...",
        word_count=1500,
        status=GenerationStatus.COMPLETED
    )
    
    db_session.add(chapter)
    db_session.commit()
    
    log_debug("测试", "创建样本章节",
        chapter_id=chapter.id,
        chapter_number=chapter.chapter_number,
        title=chapter.title
    )
    
    return chapter


@pytest.fixture(scope="function")
def sample_entities(db_session: Session, sample_story_bible: StoryBible):
    """
    🧪 [测试] 创建样本实体fixture
    为测试提供一组基础的实体实例
    """
    entities = [
        Entity(
            id="entity_zhang_san",
            story_id=sample_story_bible.id,
            name="张三",
            type=EntityType.CHARACTER,
            description="主角，年轻的剑客",
            properties={"age": 25, "weapon": "长剑", "personality": "勇敢"},
            importance_score=0.9,
            first_mentioned_chapter=1
        ),
        Entity(
            id="entity_li_si",
            story_id=sample_story_bible.id,
            name="李四",
            type=EntityType.CHARACTER,
            description="反派角色，邪恶的武林高手",
            properties={"age": 45, "weapon": "魔刀", "personality": "狡诈"},
            importance_score=0.8,
            first_mentioned_chapter=2
        ),
        Entity(
            id="entity_dragon_sword",
            story_id=sample_story_bible.id,
            name="龙泉剑",
            type=EntityType.ITEM,
            description="传说中的神剑",
            properties={"material": "玄铁", "power": "极强", "origin": "古代"},
            importance_score=0.7,
            first_mentioned_chapter=3
        ),
        Entity(
            id="entity_huashan",
            story_id=sample_story_bible.id,
            name="华山",
            type=EntityType.SCENE,
            description="武林圣地，华山派所在地",
            properties={"location": "陕西", "climate": "温带", "features": "险峻"},
            importance_score=0.6,
            first_mentioned_chapter=1
        )
    ]
    
    for entity in entities:
        db_session.add(entity)
    db_session.commit()
    
    log_debug("测试", "创建样本实体",
        total_entities=len(entities),
        entity_names=[e.name for e in entities]
    )
    
    return entities


@pytest.fixture(scope="function")
def sample_relationships(db_session: Session, sample_entities):
    """
    🧪 [测试] 创建样本关系fixture
    为测试提供一组基础的实体关系实例
    """
    relationships = [
        EntityRelationship(
            id="rel_zhang_li_enemy",
            source_entity_id="entity_zhang_san",
            target_entity_id="entity_li_si",
            relationship_type="敌人",
            description="张三与李四是宿敌",
            strength=0.9,
            status=RelationshipStatus.ACTIVE,
            established_chapter=2
        ),
        EntityRelationship(
            id="rel_zhang_sword_owns",
            source_entity_id="entity_zhang_san",
            target_entity_id="entity_dragon_sword",
            relationship_type="拥有",
            description="张三拥有龙泉剑",
            strength=0.8,
            status=RelationshipStatus.ACTIVE,
            established_chapter=3
        ),
        EntityRelationship(
            id="rel_zhang_huashan_visits",
            source_entity_id="entity_zhang_san",
            target_entity_id="entity_huashan",
            relationship_type="拜访",
            description="张三前往华山",
            strength=0.5,
            status=RelationshipStatus.ACTIVE,
            established_chapter=1
        )
    ]
    
    for relationship in relationships:
        db_session.add(relationship)
    db_session.commit()
    
    log_debug("测试", "创建样本关系",
        total_relationships=len(relationships),
        relationship_types=[r.relationship_type for r in relationships]
    )
    
    return relationships


# ===== 异步测试支持 =====

@pytest.fixture(scope="session")
def event_loop():
    """
    🧪 [测试] 创建事件循环fixture
    为异步测试提供事件循环支持
    """
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
async def async_db_session():
    """
    🧪 [测试] 创建异步数据库会话fixture
    为异步API测试提供数据库会话
    """
    # 创建临时数据库
    temp_dir = tempfile.mkdtemp()
    temp_db_path = Path(temp_dir) / "test_async_world_graph.db"
    db_url = f"sqlite+aiosqlite:///{temp_db_path}"

    log_debug("测试", "创建临时异步测试数据库", 路径=str(temp_db_path))

    # 创建异步引擎和会话，添加更多配置
    async_engine = create_async_engine(
        db_url,
        echo=False,
        pool_pre_ping=True,
        pool_recycle=300,
        connect_args={"check_same_thread": False}
    )
    AsyncSessionLocal = async_sessionmaker(
        bind=async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autoflush=True,
        autocommit=False
    )

    # 创建所有表
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # 创建会话
    session = AsyncSessionLocal()
    try:
        yield session
    finally:
        await session.close()
        await async_engine.dispose()

        # 清理临时文件
        try:
            if temp_db_path.exists():
                temp_db_path.unlink()
                log_debug("测试", "清理临时异步测试数据库", 路径=str(temp_db_path))
        except PermissionError:
            log_debug("测试", "临时异步数据库文件清理跳过（文件被占用）", 路径=str(temp_db_path))


@pytest.fixture(scope="function")
async def async_client(async_db_session: AsyncSession):
    """
    🧪 [测试] 创建异步HTTP客户端fixture
    为API测试提供HTTP客户端，并注入测试数据库会话
    """
    from fastapi.testclient import TestClient

    app = create_app()

    # 覆盖数据库依赖
    async def override_get_database_session():
        yield async_db_session

    app.dependency_overrides[get_database_session] = override_get_database_session

    # 使用TestClient而不是AsyncClient
    with TestClient(app) as client:
        log_debug("测试", "创建HTTP测试客户端")
        yield client

    # 清理依赖覆盖
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
async def sample_story_bible_async(async_db_session: AsyncSession):
    """
    🧪 [测试] 创建异步样本故事圣经fixture
    为异步测试提供一个基础的故事圣经实例
    """
    story_bible = StoryBible(
        id="test_story_async_001",
        title="测试武侠小说（异步）",
        genre=StoryGenre.MARTIAL_ARTS,
        theme="江湖恩仇，侠义精神",
        protagonist="张三，年轻剑客，正义感强",
        setting="古代江湖，武林门派林立",
        plot_outline="主角张三在江湖中历练成长的故事",
        writing_style="古典武侠风格",
        ai_provider=AIProvider.ZHIPU,
        status=GenerationStatus.PENDING
    )

    async_db_session.add(story_bible)
    await async_db_session.commit()
    await async_db_session.refresh(story_bible)

    log_debug("测试", "创建异步样本故事圣经",
        story_id=story_bible.id,
        title=story_bible.title
    )

    return story_bible


@pytest.fixture(scope="function")
async def sample_entities_async(async_db_session: AsyncSession, sample_story_bible_async: StoryBible):
    """
    🧪 [测试] 创建异步样本实体fixture
    为异步测试提供一组基础的实体实例
    """
    entities = [
        Entity(
            id="entity_zhang_san_async",
            story_id=sample_story_bible_async.id,
            name="张三",
            type=EntityType.CHARACTER,
            description="主角，年轻的剑客",
            properties={"age": 25, "weapon": "长剑", "personality": "勇敢"},
            importance_score=0.9,
            first_mentioned_chapter=1
        ),
        Entity(
            id="entity_li_si_async",
            story_id=sample_story_bible_async.id,
            name="李四",
            type=EntityType.CHARACTER,
            description="反派角色，邪恶的武林高手",
            properties={"age": 45, "weapon": "魔刀", "personality": "狡诈"},
            importance_score=0.8,
            first_mentioned_chapter=2
        ),
        Entity(
            id="entity_dragon_sword_async",
            story_id=sample_story_bible_async.id,
            name="龙泉剑",
            type=EntityType.ITEM,
            description="传说中的神剑",
            properties={"material": "玄铁", "power": "极强", "origin": "古代"},
            importance_score=0.7,
            first_mentioned_chapter=3
        )
    ]

    for entity in entities:
        async_db_session.add(entity)
    await async_db_session.commit()

    # 刷新所有实体
    for entity in entities:
        await async_db_session.refresh(entity)

    log_debug("测试", "创建异步样本实体",
        total_entities=len(entities),
        entity_names=[e.name for e in entities]
    )

    return entities


@pytest.fixture(scope="function")
async def sample_relationships_async(async_db_session: AsyncSession, sample_entities_async: list):
    """
    🧪 [测试] 创建异步样本关系fixture
    为异步测试提供一组基础的关系实例
    """
    entities = sample_entities_async
    relationships = [
        EntityRelationship(
            id="rel_zhang_li_enemy_async",
            source_entity_id=entities[0].id,  # 张三
            target_entity_id=entities[1].id,  # 李四
            relationship_type="敌人",
            description="张三与李四是宿敌",
            strength=0.9,
            status=RelationshipStatus.ACTIVE,
            established_chapter=2
        ),
        EntityRelationship(
            id="rel_zhang_sword_owns_async",
            source_entity_id=entities[0].id,  # 张三
            target_entity_id=entities[2].id,  # 龙泉剑
            relationship_type="拥有",
            description="张三拥有龙泉剑",
            strength=0.8,
            status=RelationshipStatus.ACTIVE,
            established_chapter=3
        )
    ]

    for relationship in relationships:
        async_db_session.add(relationship)
    await async_db_session.commit()

    # 刷新所有关系
    for relationship in relationships:
        await async_db_session.refresh(relationship)

    log_debug("测试", "创建异步样本关系",
        total_relationships=len(relationships),
        relationship_types=[r.relationship_type for r in relationships]
    )

    return relationships
