"""
🗂️ [语料库管理] 语料库管理器
负责语料库数据的加载、缓存、更新和验证
"""

import json
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import hashlib
import aiofiles

from app.core.config import log_info, log_debug, log_error, log_success


@dataclass
class CorpusMetadata:
    """语料库元数据"""
    name: str
    version: str
    description: str
    category: str
    tags: List[str] = field(default_factory=list)
    source: str = ""
    quality_score: float = 0.0
    usage_frequency: str = "medium"
    last_updated: str = ""
    file_hash: str = ""


@dataclass
class CorpusEntry:
    """语料库条目"""
    content: str
    weight: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    examples: List[str] = field(default_factory=list)
    data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CorpusCategory:
    """语料库分类"""
    name: str
    description: str
    items: List[CorpusEntry] = field(default_factory=list)
    weight: float = 1.0


@dataclass
class CorpusData:
    """语料库数据结构"""
    metadata: CorpusMetadata
    categories: Dict[str, CorpusCategory] = field(default_factory=dict)
    patterns: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    rules: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    examples: List[Dict[str, Any]] = field(default_factory=list)


class CorpusCache:
    """语料库缓存管理"""
    
    def __init__(self, ttl_seconds: int = 3600):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_timestamps: Dict[str, datetime] = {}
        self.ttl = timedelta(seconds=ttl_seconds)
    
    def get(self, key: str) -> Optional[Dict[str, Any]]:
        """获取缓存数据"""
        if key not in self.cache:
            return None
        
        # 检查是否过期
        if datetime.now() - self.cache_timestamps[key] > self.ttl:
            self.remove(key)
            return None
        
        return self.cache[key]
    
    def set(self, key: str, data: Dict[str, Any]) -> None:
        """设置缓存数据"""
        self.cache[key] = data
        self.cache_timestamps[key] = datetime.now()
    
    def remove(self, key: str) -> None:
        """移除缓存数据"""
        self.cache.pop(key, None)
        self.cache_timestamps.pop(key, None)
    
    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.cache_timestamps.clear()


class CorpusManager:
    """
    🗂️ [语料库管理器] 语料库数据管理器
    
    负责语料库数据的加载、缓存、更新和验证，支持热重载和动态扩展
    """
    
    def __init__(self, corpus_root: Optional[str] = None):
        """
        初始化语料库管理器
        
        Args:
            corpus_root: 语料库根目录路径
        """
        self.corpus_root = Path(corpus_root or "data/corpus")
        self.config_file = self.corpus_root / "corpus_config.json"
        self.config: Dict[str, Any] = {}
        self.cache = CorpusCache()
        self.loaded_files: Dict[str, CorpusData] = {}
        self.file_hashes: Dict[str, str] = {}
        
        log_debug("语料库", "语料库管理器初始化", 
                 根目录=str(self.corpus_root),
                 配置文件=str(self.config_file))
    
    async def initialize(self) -> None:
        """
        🚀 [初始化] 初始化语料库管理器
        """
        log_info("语料库", "开始初始化语料库管理器")
        
        try:
            # 加载配置文件
            await self._load_config()
            
            # 预加载指定文件
            if self.config.get("loading_config", {}).get("preload_files"):
                await self._preload_files()
            
            log_success("语料库", "语料库管理器初始化完成",
                       配置版本=self.config.get("corpus_info", {}).get("version"),
                       预加载文件数=len(self.loaded_files))
            
        except Exception as e:
            log_error("语料库", "语料库管理器初始化失败", error=e)
            raise
    
    async def _load_config(self) -> None:
        """加载配置文件"""
        if not self.config_file.exists():
            raise FileNotFoundError(f"语料库配置文件不存在: {self.config_file}")
        
        async with aiofiles.open(self.config_file, 'r', encoding='utf-8') as f:
            content = await f.read()
            self.config = json.loads(content)
        
        log_debug("语料库", "配置文件加载完成", 
                 配置项数=len(self.config),
                 语料库版本=self.config.get("corpus_info", {}).get("version"))
    
    async def _preload_files(self) -> None:
        """预加载指定文件"""
        preload_files = self.config.get("loading_config", {}).get("preload_files", [])
        
        for file_path in preload_files:
            try:
                await self.load_corpus_file(file_path)
                log_debug("语料库", "预加载文件成功", 文件=file_path)
            except Exception as e:
                log_error("语料库", "预加载文件失败", error=e, 文件=file_path)
    
    async def load_corpus_file(self, file_path: str) -> CorpusData:
        """
        📂 [加载文件] 加载语料库文件
        
        Args:
            file_path: 相对于语料库根目录的文件路径
            
        Returns:
            CorpusData: 语料库数据对象
        """
        full_path = self.corpus_root / file_path
        
        # 检查缓存
        if self.config.get("loading_config", {}).get("cache_enabled", True):
            cached_data = self.cache.get(file_path)
            if cached_data and not await self._file_changed(file_path):
                log_debug("语料库", "从缓存加载文件", 文件=file_path)
                return self._dict_to_corpus_data(cached_data)
        
        log_debug("语料库", "开始加载语料库文件", 文件=file_path)
        
        try:
            if not full_path.exists():
                raise FileNotFoundError(f"语料库文件不存在: {full_path}")
            
            # 读取文件内容
            async with aiofiles.open(full_path, 'r', encoding='utf-8') as f:
                content = await f.read()
            
            # 解析JSON数据
            raw_data = json.loads(content)
            
            # 验证数据格式
            await self._validate_corpus_data(raw_data, file_path)
            
            # 转换为CorpusData对象
            corpus_data = self._dict_to_corpus_data(raw_data)
            
            # 更新缓存和文件哈希
            if self.config.get("loading_config", {}).get("cache_enabled", True):
                self.cache.set(file_path, raw_data)
            
            self.file_hashes[file_path] = self._calculate_file_hash(content)
            self.loaded_files[file_path] = corpus_data
            
            log_success("语料库", "语料库文件加载成功", 
                       文件=file_path,
                       分类数=len(corpus_data.categories),
                       模式数=len(corpus_data.patterns))
            
            return corpus_data
            
        except Exception as e:
            log_error("语料库", "语料库文件加载失败", error=e, 文件=file_path)
            raise
    
    def _dict_to_corpus_data(self, raw_data: Dict[str, Any]) -> CorpusData:
        """将字典数据转换为CorpusData对象"""
        # 创建元数据
        metadata_dict = raw_data.get("metadata", {})
        metadata = CorpusMetadata(
            name=raw_data.get("name", ""),
            version=raw_data.get("version", "1.0.0"),
            description=raw_data.get("description", ""),
            category=raw_data.get("category", ""),
            tags=raw_data.get("tags", []),
            source=metadata_dict.get("source", ""),
            quality_score=metadata_dict.get("quality_score", 0.0),
            usage_frequency=metadata_dict.get("usage_frequency", "medium"),
            last_updated=metadata_dict.get("last_updated", "")
        )
        
        # 创建分类数据
        categories = {}
        data_categories = raw_data.get("data", {}).get("categories", {})
        for cat_name, cat_data in data_categories.items():
            items = []
            for item_data in cat_data.get("items", []):
                entry = CorpusEntry(
                    content=item_data.get("content", ""),
                    weight=item_data.get("weight", 1.0),
                    metadata=item_data.get("metadata", {}),
                    tags=item_data.get("tags", []),
                    examples=item_data.get("examples", []),
                    data=item_data.get("data", {})
                )
                items.append(entry)
            
            category = CorpusCategory(
                name=cat_name,
                description=cat_data.get("description", ""),
                items=items,
                weight=cat_data.get("weight", 1.0)
            )
            categories[cat_name] = category
        
        return CorpusData(
            metadata=metadata,
            categories=categories,
            patterns=raw_data.get("data", {}).get("patterns", {}),
            rules=raw_data.get("data", {}).get("rules", {}),
            examples=raw_data.get("examples", [])
        )
    
    async def _file_changed(self, file_path: str) -> bool:
        """检查文件是否已更改"""
        if file_path not in self.file_hashes:
            return True
        
        full_path = self.corpus_root / file_path
        if not full_path.exists():
            return True
        
        async with aiofiles.open(full_path, 'r', encoding='utf-8') as f:
            content = await f.read()
        
        current_hash = self._calculate_file_hash(content)
        return current_hash != self.file_hashes[file_path]
    
    def _calculate_file_hash(self, content: str) -> str:
        """计算文件内容哈希"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    async def _validate_corpus_data(self, data: Dict[str, Any], file_path: str) -> None:
        """验证语料库数据格式"""
        if not self.config.get("quality_control", {}).get("validation_enabled", True):
            return
        
        required_fields = self.config.get("data_format", {}).get("required_fields", [])
        for field in required_fields:
            if field not in data:
                raise ValueError(f"语料库文件缺少必需字段 '{field}': {file_path}")
        
        log_debug("语料库", "数据格式验证通过", 文件=file_path)


# ==================== 工厂函数 ====================

def create_corpus_manager(corpus_root: Optional[str] = None) -> CorpusManager:
    """
    🏭 [工厂] 创建语料库管理器实例
    
    Args:
        corpus_root: 语料库根目录路径
        
    Returns:
        CorpusManager: 语料库管理器实例
    """
    return CorpusManager(corpus_root)
