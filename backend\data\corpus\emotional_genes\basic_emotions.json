{"name": "基础情感基因库", "version": "1.0.0", "description": "包含人类基础情感的生理反应、感官触发器和熵增项目，用于AI小说创作中的情感细节注入", "category": "emotional_genes", "tags": ["基础情感", "生理反应", "感官触发", "情感基因"], "metadata": {"source": "心理学研究和文学作品分析", "quality_score": 0.85, "usage_frequency": "high", "last_updated": "2025-08-05", "contributor": "系统内置", "review_status": "approved", "language": "zh-CN", "encoding": "utf-8"}, "usage_contexts": {"小说创作": {"description": "为小说角色添加真实的情感细节", "weight": 1.0, "conditions": ["角色情感描写", "心理活动", "情感转折"]}, "对话生成": {"description": "在对话中体现角色的情感状态", "weight": 0.9, "conditions": ["情感对话", "冲突场景", "亲密交流"]}, "场景描写": {"description": "通过环境细节反映角色情感", "weight": 0.8, "conditions": ["情感场景", "氛围营造", "细节描写"]}}, "data": {"categories": {"喜悦": {"description": "快乐、高兴、愉悦等正面情感", "weight": 1.0, "items": [{"content": "纯真喜悦", "weight": 1.0, "metadata": {"intensity": 0.7, "reliability": 0.9, "quality": 0.8, "source": "儿童心理学研究"}, "tags": ["纯真", "自然", "无邪"], "examples": ["孩子看到礼物时的表情", "第一次成功的喜悦"], "data": {"physiological_reactions": ["嘴角不自觉上扬", "眼睛眯成月牙形", "脸颊微微发红", "心跳轻快有力", "呼吸变得轻松", "肩膀自然放松", "步伐轻盈有弹性"], "sensory_triggers": ["听到悦耳的音乐", "看到明亮的色彩", "闻到甜美的香味", "感受到温暖的阳光", "触摸到柔软的质地"], "entropy_items": ["远处传来鸟儿的啁啾声", "微风轻抚过脸颊", "阳光透过树叶洒下斑驳光影", "空气中弥漫着淡淡花香", "衣角随风轻摆"]}}, {"content": "成就喜悦", "weight": 0.9, "metadata": {"intensity": 0.8, "reliability": 0.85, "quality": 0.82, "source": "成功心理学研究"}, "tags": ["成就", "骄傲", "满足"], "examples": ["考试通过", "工作成功", "目标达成"], "data": {"physiological_reactions": ["胸膛挺得更直", "下巴微微抬起", "眼中闪烁着光芒", "握拳庆祝的动作", "深深吸一口气", "脚步更加坚定", "声音变得洪亮"], "sensory_triggers": ["听到赞美的话语", "看到成果的展示", "感受到他人的认可", "触摸到奖杯或证书", "闻到庆祝的香槟味"], "entropy_items": ["办公室里键盘敲击声", "墙上的时钟滴答作响", "窗外车辆驶过的声音", "空调的轻微嗡鸣声", "纸张翻动的沙沙声"]}}]}, "愤怒": {"description": "生气、愤慨、暴怒等负面情感", "weight": 1.0, "items": [{"content": "压抑愤怒", "weight": 0.9, "metadata": {"intensity": 0.6, "reliability": 0.88, "quality": 0.79, "source": "情绪管理研究"}, "tags": ["压抑", "克制", "内敛"], "examples": ["职场冲突", "家庭争执", "不公待遇"], "data": {"physiological_reactions": ["紧咬牙关", "拳头暗自握紧", "太阳穴突突跳动", "呼吸变得急促", "脸色涨红", "眼神变得锐利", "肌肉紧绷"], "sensory_triggers": ["听到刺耳的声音", "看到红色物体", "感受到不公正", "闻到刺鼻的气味", "触碰到粗糙表面"], "entropy_items": ["桌上的笔被握得咯吱作响", "椅子发出轻微的摩擦声", "远处传来汽车喇叭声", "空气中有种压抑的感觉", "墙上的画框微微倾斜"]}}, {"content": "爆发愤怒", "weight": 0.8, "metadata": {"intensity": 0.95, "reliability": 0.82, "quality": 0.88, "source": "愤怒心理学研究"}, "tags": ["爆发", "失控", "激烈"], "examples": ["极度愤怒", "情绪失控", "暴怒状态"], "data": {"physiological_reactions": ["双手颤抖", "声音嘶哑", "面红耳赤", "青筋暴起", "心跳如鼓", "呼吸粗重", "全身发热"], "sensory_triggers": ["听到挑衅的话语", "看到仇恨的对象", "感受到背叛", "闻到血腥味", "触碰到武器"], "entropy_items": ["杯子被重重放在桌上", "门被用力关上", "脚步声重重踏在地板上", "空气中弥漫着紧张气息", "周围的人都屏住了呼吸"]}}]}, "悲伤": {"description": "难过、忧郁、沮丧等负面情感", "weight": 1.0, "items": [{"content": "深度悲伤", "weight": 0.9, "metadata": {"intensity": 0.85, "reliability": 0.9, "quality": 0.87, "source": "抑郁症研究"}, "tags": ["深度", "持久", "沉重"], "examples": ["失去亲人", "重大挫折", "绝望时刻"], "data": {"physiological_reactions": ["眼眶湿润", "喉咙哽咽", "肩膀下垂", "步伐沉重", "呼吸浅短", "食欲不振", "睡眠不安"], "sensory_triggers": ["听到悲伤的音乐", "看到灰暗的色彩", "感受到孤独", "闻到熟悉的味道", "触摸到冰冷的物体"], "entropy_items": ["雨滴敲打着窗户", "时钟的滴答声格外清晰", "房间里弥漫着沉闷的气息", "窗外的天空阴沉沉的", "角落里积了一层灰尘"]}}]}, "恐惧": {"description": "害怕、惊恐、畏惧等负面情感", "weight": 1.0, "items": [{"content": "突发恐惧", "weight": 0.95, "metadata": {"intensity": 0.9, "reliability": 0.85, "quality": 0.87, "source": "恐惧心理学研究"}, "tags": ["突发", "强烈", "本能"], "examples": ["突然惊吓", "危险来临", "未知威胁"], "data": {"physiological_reactions": ["心跳剧烈加速", "手心冒汗", "呼吸急促", "肌肉紧绷", "瞳孔放大", "脸色苍白", "身体颤抖"], "sensory_triggers": ["听到尖锐的声音", "看到阴影移动", "感受到危险气息", "闻到异常的气味", "触碰到冰冷的东西"], "entropy_items": ["风吹动窗帘发出沙沙声", "远处传来不明的响声", "空气中有种不安的感觉", "灯光忽明忽暗", "脚下的地板发出轻微响声"]}}]}}}, "statistics": {"total_entries": 6, "categories_count": 4, "patterns_count": 0, "average_quality": 0.84, "last_calculated": "2025-08-05"}}