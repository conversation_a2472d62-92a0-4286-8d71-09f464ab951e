"""
⚔️ [冲突检测] 实时逻辑冲突检测系统
实时监测故事中的各种逻辑冲突和不一致性，提供冲突预警和修复建议
"""

from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import asyncio
import json

from app.core.config import log_info, log_debug, log_error
from app.schemas.world_graph import EntityResponse, RelationshipResponse, WorldGraphResponse
from app.models.world_graph import EntityType, RelationshipStatus, RelationshipTypes


class ConflictType(Enum):
    """冲突类型枚举"""
    RELATIONSHIP_CONTRADICTION = "relationship_contradiction"    # 关系矛盾
    TIMELINE_INCONSISTENCY = "timeline_inconsistency"          # 时间线不一致
    CHARACTER_ABILITY_CONFLICT = "character_ability_conflict"   # 角色能力冲突
    WORLD_SETTING_CONFLICT = "world_setting_conflict"          # 世界设定冲突
    PERSONALITY_INCONSISTENCY = "personality_inconsistency"     # 性格不一致
    LOCATION_CONFLICT = "location_conflict"                     # 位置冲突
    ITEM_OWNERSHIP_CONFLICT = "item_ownership_conflict"         # 物品归属冲突
    KNOWLEDGE_INCONSISTENCY = "knowledge_inconsistency"         # 知识不一致


class ConflictSeverity(Enum):
    """冲突严重程度枚举"""
    LOW = "low"         # 轻微冲突 - 细节不一致，不影响主线
    MEDIUM = "medium"   # 中等冲突 - 影响故事逻辑，需要修正
    HIGH = "high"       # 严重冲突 - 破坏故事基础，必须解决
    CRITICAL = "critical"  # 致命冲突 - 完全破坏故事连贯性


@dataclass
class ConflictDetection:
    """冲突检测结果数据类"""
    id: str
    conflict_type: ConflictType
    severity: ConflictSeverity
    title: str
    description: str
    
    # 冲突详情
    involved_entities: List[str]      # 涉及的实体ID
    involved_relationships: List[str]  # 涉及的关系ID
    conflicting_chapters: List[int]   # 发生冲突的章节
    
    # 冲突分析
    evidence: List[str]               # 冲突证据
    impact_analysis: str              # 影响分析
    confidence_score: float           # 检测置信度 (0-1)
    
    # 修复建议
    repair_suggestions: List[str]     # 修复建议
    alternative_solutions: List[str]  # 替代方案
    
    # 元数据
    detected_at: datetime
    last_updated: datetime


@dataclass
class ConflictAnalysisResult:
    """冲突分析结果数据类"""
    story_id: str
    analysis_chapter: int
    
    # 检测统计
    total_conflicts: int
    conflicts_by_type: Dict[str, int]
    conflicts_by_severity: Dict[str, int]
    
    # 冲突列表
    critical_conflicts: List[ConflictDetection]
    high_conflicts: List[ConflictDetection]
    medium_conflicts: List[ConflictDetection]
    low_conflicts: List[ConflictDetection]
    
    # 整体评估
    story_consistency_score: float    # 故事一致性评分 (0-1)
    logical_integrity_score: float    # 逻辑完整性评分 (0-1)
    overall_health_score: float       # 整体健康度评分 (0-1)
    
    # 趋势分析
    conflict_trend: str               # 冲突趋势 (improving/stable/worsening)
    risk_areas: List[str]             # 风险区域
    
    created_at: datetime


class ConflictDetectionEngine:
    """
    ⚔️ [冲突检测] 实时逻辑冲突检测引擎核心类
    
    监测和分析故事中的各种逻辑冲突，提供实时预警和修复建议
    """
    
    def __init__(self):
        """初始化冲突检测引擎"""
        self.conflict_rules = self._initialize_conflict_rules()
        self.detection_history: Dict[str, List[ConflictDetection]] = {}
        
        log_info("冲突检测", "冲突检测引擎初始化完成", 
                规则数量=len(self.conflict_rules))
    
    def _initialize_conflict_rules(self) -> Dict[ConflictType, Dict[str, Any]]:
        """
        📋 [冲突检测] 初始化冲突检测规则
        
        定义各种类型冲突的检测规则和阈值
        """
        rules = {
            ConflictType.RELATIONSHIP_CONTRADICTION: {
                "description": "检测角色关系的前后矛盾",
                "check_function": self._check_relationship_contradictions,
                "severity_threshold": {
                    "critical": 0.9,  # 完全矛盾的关系
                    "high": 0.7,      # 严重矛盾
                    "medium": 0.5,    # 中等矛盾
                    "low": 0.3        # 轻微矛盾
                }
            },
            ConflictType.TIMELINE_INCONSISTENCY: {
                "description": "检测时间线的逻辑错误",
                "check_function": self._check_timeline_inconsistencies,
                "severity_threshold": {
                    "critical": 0.9,
                    "high": 0.7,
                    "medium": 0.5,
                    "low": 0.3
                }
            },
            ConflictType.CHARACTER_ABILITY_CONFLICT: {
                "description": "检测角色能力的前后矛盾",
                "check_function": self._check_character_ability_conflicts,
                "severity_threshold": {
                    "critical": 0.8,
                    "high": 0.6,
                    "medium": 0.4,
                    "low": 0.2
                }
            },
            ConflictType.WORLD_SETTING_CONFLICT: {
                "description": "检测世界设定的不一致",
                "check_function": self._check_world_setting_conflicts,
                "severity_threshold": {
                    "critical": 0.8,
                    "high": 0.6,
                    "medium": 0.4,
                    "low": 0.2
                }
            },
            ConflictType.PERSONALITY_INCONSISTENCY: {
                "description": "检测角色性格的不一致",
                "check_function": self._check_personality_inconsistencies,
                "severity_threshold": {
                    "critical": 0.7,
                    "high": 0.5,
                    "medium": 0.3,
                    "low": 0.1
                }
            },
            ConflictType.LOCATION_CONFLICT: {
                "description": "检测位置和场景的冲突",
                "check_function": self._check_location_conflicts,
                "severity_threshold": {
                    "critical": 0.8,
                    "high": 0.6,
                    "medium": 0.4,
                    "low": 0.2
                }
            },
            ConflictType.ITEM_OWNERSHIP_CONFLICT: {
                "description": "检测物品归属的冲突",
                "check_function": self._check_item_ownership_conflicts,
                "severity_threshold": {
                    "critical": 0.9,
                    "high": 0.7,
                    "medium": 0.5,
                    "low": 0.3
                }
            },
            ConflictType.KNOWLEDGE_INCONSISTENCY: {
                "description": "检测角色知识的不一致",
                "check_function": self._check_knowledge_inconsistencies,
                "severity_threshold": {
                    "critical": 0.8,
                    "high": 0.6,
                    "medium": 0.4,
                    "low": 0.2
                }
            }
        }
        
        log_debug("冲突检测", "冲突检测规则初始化完成", 
                 规则类型=[rule_type.value for rule_type in rules.keys()])
        
        return rules
    
    async def detect_conflicts(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int,
        previous_analysis: Optional[ConflictAnalysisResult] = None
    ) -> ConflictAnalysisResult:
        """
        🔍 [冲突检测] 执行全面的冲突检测分析
        
        分析世界知识图谱中的各种逻辑冲突
        
        Args:
            world_graph: 世界知识图谱数据
            current_chapter: 当前章节号
            previous_analysis: 上次分析结果（用于趋势分析）
            
        Returns:
            ConflictAnalysisResult: 详细的冲突分析结果
        """
        log_info("冲突检测", "开始执行冲突检测分析",
                故事ID=world_graph.story_id,
                当前章节=current_chapter,
                实体数量=len(world_graph.entities),
                关系数量=len(world_graph.relationships))
        
        try:
            all_conflicts = []
            
            # 对每种冲突类型执行检测
            for conflict_type, rule_config in self.conflict_rules.items():
                log_debug("冲突检测", f"检测{conflict_type.value}类型冲突")
                
                check_function = rule_config["check_function"]
                conflicts = await check_function(world_graph, current_chapter)
                
                # 为每个冲突分配严重程度
                for conflict in conflicts:
                    severity = self._determine_severity(
                        conflict.confidence_score,
                        rule_config["severity_threshold"]
                    )
                    conflict.severity = severity
                
                all_conflicts.extend(conflicts)
                
                log_debug("冲突检测", f"{conflict_type.value}检测完成", 
                         发现冲突数=len(conflicts))
            
            # 按严重程度分类冲突
            critical_conflicts = [c for c in all_conflicts if c.severity == ConflictSeverity.CRITICAL]
            high_conflicts = [c for c in all_conflicts if c.severity == ConflictSeverity.HIGH]
            medium_conflicts = [c for c in all_conflicts if c.severity == ConflictSeverity.MEDIUM]
            low_conflicts = [c for c in all_conflicts if c.severity == ConflictSeverity.LOW]
            
            # 统计冲突类型
            conflicts_by_type = {}
            conflicts_by_severity = {}
            
            for conflict in all_conflicts:
                conflict_type_str = conflict.conflict_type.value
                severity_str = conflict.severity.value
                
                conflicts_by_type[conflict_type_str] = conflicts_by_type.get(conflict_type_str, 0) + 1
                conflicts_by_severity[severity_str] = conflicts_by_severity.get(severity_str, 0) + 1
            
            # 计算整体评分
            story_consistency_score = self._calculate_consistency_score(all_conflicts)
            logical_integrity_score = self._calculate_integrity_score(all_conflicts)
            overall_health_score = (story_consistency_score + logical_integrity_score) / 2
            
            # 分析趋势
            conflict_trend = self._analyze_conflict_trend(
                all_conflicts, previous_analysis
            )
            
            # 识别风险区域
            risk_areas = self._identify_risk_areas(all_conflicts)
            
            # 创建分析结果
            analysis_result = ConflictAnalysisResult(
                story_id=world_graph.story_id,
                analysis_chapter=current_chapter,
                total_conflicts=len(all_conflicts),
                conflicts_by_type=conflicts_by_type,
                conflicts_by_severity=conflicts_by_severity,
                critical_conflicts=critical_conflicts,
                high_conflicts=high_conflicts,
                medium_conflicts=medium_conflicts,
                low_conflicts=low_conflicts,
                story_consistency_score=story_consistency_score,
                logical_integrity_score=logical_integrity_score,
                overall_health_score=overall_health_score,
                conflict_trend=conflict_trend,
                risk_areas=risk_areas,
                created_at=datetime.now()
            )
            
            # 保存检测历史
            self.detection_history[world_graph.story_id] = all_conflicts
            
            log_info("冲突检测", "冲突检测分析完成",
                    故事ID=world_graph.story_id,
                    总冲突数=len(all_conflicts),
                    严重冲突数=len(critical_conflicts),
                    整体健康度=f"{overall_health_score:.3f}")
            
            return analysis_result
            
        except Exception as e:
            log_error("冲突检测", "冲突检测分析失败", error=e)
            raise

    async def _check_relationship_contradictions(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int
    ) -> List[ConflictDetection]:
        """
        💔 [冲突检测] 检测角色关系的前后矛盾

        检测角色关系的不合理变化，如敌人突然变朋友但缺乏铺垫
        """
        conflicts = []

        # 按实体对分组关系
        entity_pair_relationships = {}
        for rel in world_graph.relationships:
            # 创建标准化的实体对键（较小ID在前）
            entity_pair = tuple(sorted([rel.source_entity_id, rel.target_entity_id]))

            if entity_pair not in entity_pair_relationships:
                entity_pair_relationships[entity_pair] = []
            entity_pair_relationships[entity_pair].append(rel)

        # 检查每个实体对的关系变化
        for entity_pair, relationships in entity_pair_relationships.items():
            if len(relationships) < 2:
                continue  # 只有一个关系，无法检测矛盾

            # 按建立章节排序
            relationships.sort(key=lambda x: x.established_chapter or 0)

            # 检测关系类型的突然变化
            for i in range(1, len(relationships)):
                prev_rel = relationships[i-1]
                curr_rel = relationships[i]

                # 检测敌对关系突然变为友好关系
                if (prev_rel.relationship_type in [RelationshipTypes.ENEMY, RelationshipTypes.RIVAL] and
                    curr_rel.relationship_type in [RelationshipTypes.FRIEND, RelationshipTypes.ALLY]):

                    # 计算变化的突然程度
                    chapter_gap = (curr_rel.established_chapter or current_chapter) - (prev_rel.established_chapter or 0)
                    suddenness = 1.0 / max(1, chapter_gap)  # 章节间隔越小，突然程度越高

                    if suddenness > 0.3:  # 阈值：3章内的关系转变被认为是突然的
                        conflict_id = f"rel_contradiction_{entity_pair[0]}_{entity_pair[1]}_{curr_rel.established_chapter}"

                        # 获取实体名称
                        entity1 = next((e for e in world_graph.entities if e.id == entity_pair[0]), None)
                        entity2 = next((e for e in world_graph.entities if e.id == entity_pair[1]), None)

                        entity1_name = entity1.name if entity1 else "角色A"
                        entity2_name = entity2.name if entity2 else "角色B"

                        conflict = ConflictDetection(
                            id=conflict_id,
                            conflict_type=ConflictType.RELATIONSHIP_CONTRADICTION,
                            severity=ConflictSeverity.MEDIUM,  # 将在上层函数中重新计算
                            title=f"{entity1_name}与{entity2_name}的关系转变过于突然",
                            description=f"从{prev_rel.relationship_type}关系突然转变为{curr_rel.relationship_type}关系，缺乏足够的铺垫",
                            involved_entities=list(entity_pair),
                            involved_relationships=[prev_rel.id, curr_rel.id],
                            conflicting_chapters=[prev_rel.established_chapter or 0, curr_rel.established_chapter or current_chapter],
                            evidence=[
                                f"第{prev_rel.established_chapter}章建立{prev_rel.relationship_type}关系",
                                f"第{curr_rel.established_chapter}章转变为{curr_rel.relationship_type}关系",
                                f"章节间隔仅{chapter_gap}章，转变过于突然"
                            ],
                            impact_analysis="关系转变缺乏合理铺垫，可能影响读者的代入感和故事的可信度",
                            confidence_score=min(0.9, suddenness + 0.3),
                            repair_suggestions=[
                                "在关系转变前增加铺垫情节",
                                "添加促成关系转变的关键事件",
                                "展现角色内心的变化过程",
                                "通过第三方角色的视角解释关系变化"
                            ],
                            alternative_solutions=[
                                "将关系转变分解为多个阶段",
                                "增加过渡性的中性关系阶段",
                                "通过回忆或对话补充背景信息"
                            ],
                            detected_at=datetime.now(),
                            last_updated=datetime.now()
                        )

                        conflicts.append(conflict)

        return conflicts

    async def _check_timeline_inconsistencies(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int
    ) -> List[ConflictDetection]:
        """
        ⏰ [冲突检测] 检测时间线的逻辑错误

        检测事件时间顺序的不合理安排
        """
        conflicts = []

        # 收集所有有时间信息的事件
        timeline_events = []

        # 从实体中收集时间信息
        for entity in world_graph.entities:
            if entity.first_mentioned_chapter:
                timeline_events.append({
                    "type": "entity_first_mention",
                    "entity_id": entity.id,
                    "entity_name": entity.name,
                    "chapter": entity.first_mentioned_chapter,
                    "description": f"{entity.name}首次出现"
                })

            if entity.last_mentioned_chapter and entity.last_mentioned_chapter != entity.first_mentioned_chapter:
                timeline_events.append({
                    "type": "entity_last_mention",
                    "entity_id": entity.id,
                    "entity_name": entity.name,
                    "chapter": entity.last_mentioned_chapter,
                    "description": f"{entity.name}最后出现"
                })

        # 从关系中收集时间信息
        for rel in world_graph.relationships:
            if rel.established_chapter:
                timeline_events.append({
                    "type": "relationship_established",
                    "relationship_id": rel.id,
                    "chapter": rel.established_chapter,
                    "description": f"关系{rel.relationship_type}建立"
                })

            if rel.last_updated_chapter and rel.last_updated_chapter != rel.established_chapter:
                timeline_events.append({
                    "type": "relationship_updated",
                    "relationship_id": rel.id,
                    "chapter": rel.last_updated_chapter,
                    "description": f"关系{rel.relationship_type}更新"
                })

        # 按章节排序
        timeline_events.sort(key=lambda x: x["chapter"])

        # 检测时间线冲突
        for i, event in enumerate(timeline_events):
            # 检测实体在首次出现前就参与关系
            if event["type"] == "relationship_established":
                rel = next((r for r in world_graph.relationships if r.id == event["relationship_id"]), None)
                if rel:
                    # 检查关系中的实体是否已经出现
                    source_entity = next((e for e in world_graph.entities if e.id == rel.source_entity_id), None)
                    target_entity = next((e for e in world_graph.entities if e.id == rel.target_entity_id), None)

                    # 检查源实体是否在关系建立后才首次出现
                    if source_entity and source_entity.first_mentioned_chapter and source_entity.first_mentioned_chapter > event["chapter"]:
                        # 实体在关系建立后才首次出现
                        conflict_id = f"timeline_entity_relation_{rel.id}_{source_entity.id}"

                        conflict = ConflictDetection(
                            id=conflict_id,
                            conflict_type=ConflictType.TIMELINE_INCONSISTENCY,
                            severity=ConflictSeverity.HIGH,
                            title=f"{source_entity.name}在首次出现前就参与了关系",
                            description=f"{source_entity.name}在第{source_entity.first_mentioned_chapter}章首次出现，但在第{event['chapter']}章就已经与其他角色建立了关系",
                            involved_entities=[source_entity.id, rel.target_entity_id],
                            involved_relationships=[rel.id],
                            conflicting_chapters=[event["chapter"], source_entity.first_mentioned_chapter],
                            evidence=[
                                f"第{event['chapter']}章建立关系{rel.relationship_type}",
                                f"第{source_entity.first_mentioned_chapter}章{source_entity.name}首次出现",
                                "时间顺序不合理"
                            ],
                            impact_analysis="时间线错误会严重影响故事的逻辑性和可信度",
                            confidence_score=0.8,
                            repair_suggestions=[
                                f"将{source_entity.name}的首次出现提前到第{event['chapter']}章之前",
                                f"将关系建立推迟到第{source_entity.first_mentioned_chapter}章之后",
                                "通过回忆或叙述的方式解释时间差异"
                            ],
                            alternative_solutions=[
                                "修改章节标记的准确性",
                                "添加角色的背景介绍章节"
                            ],
                            detected_at=datetime.now(),
                            last_updated=datetime.now()
                        )

                        conflicts.append(conflict)

                    # 检查目标实体是否在关系建立后才首次出现
                    if target_entity and target_entity.first_mentioned_chapter and target_entity.first_mentioned_chapter > event["chapter"]:
                        # 实体在关系建立后才首次出现
                        conflict_id = f"timeline_entity_relation_{rel.id}_{target_entity.id}"

                        conflict = ConflictDetection(
                            id=conflict_id,
                            conflict_type=ConflictType.TIMELINE_INCONSISTENCY,
                            severity=ConflictSeverity.HIGH,
                            title=f"{target_entity.name}在首次出现前就参与了关系",
                            description=f"{target_entity.name}在第{target_entity.first_mentioned_chapter}章首次出现，但在第{event['chapter']}章就已经与其他角色建立了关系",
                            involved_entities=[rel.source_entity_id, target_entity.id],
                            involved_relationships=[rel.id],
                            conflicting_chapters=[event["chapter"], target_entity.first_mentioned_chapter],
                            evidence=[
                                f"第{event['chapter']}章建立关系{rel.relationship_type}",
                                f"第{target_entity.first_mentioned_chapter}章{target_entity.name}首次出现",
                                "时间顺序不合理"
                            ],
                            impact_analysis="时间线错误会严重影响故事的逻辑性和可信度",
                            confidence_score=0.8,
                            repair_suggestions=[
                                f"将{target_entity.name}的首次出现提前到第{event['chapter']}章之前",
                                f"将关系建立推迟到第{target_entity.first_mentioned_chapter}章之后",
                                "通过回忆或叙述的方式解释时间差异"
                            ],
                            alternative_solutions=[
                                "修改章节标记的准确性",
                                "添加角色的背景介绍章节"
                            ],
                            detected_at=datetime.now(),
                            last_updated=datetime.now()
                        )

                        conflicts.append(conflict)

        return conflicts

    async def _check_character_ability_conflicts(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int
    ) -> List[ConflictDetection]:
        """
        💪 [冲突检测] 检测角色能力的前后矛盾

        检测角色能力设定的不一致性
        """
        conflicts = []

        # 检查角色实体的能力属性
        for entity in world_graph.entities:
            if entity.type != EntityType.CHARACTER:
                continue

            # 检查属性中的能力相关信息
            if not entity.properties:
                continue

            # 查找能力相关的属性
            ability_keys = ['ability', 'power', 'skill', 'magic', 'strength', 'speed', 'intelligence']
            character_abilities = {}

            for key, value in entity.properties.items():
                if any(ability_key in key.lower() for ability_key in ability_keys):
                    character_abilities[key] = value

            if not character_abilities:
                continue

            # 这里可以添加更复杂的能力冲突检测逻辑
            # 目前实现基础的数值冲突检测
            for ability_name, ability_value in character_abilities.items():
                if isinstance(ability_value, (int, float)):
                    # 检测不合理的能力数值（例如负数或过大的值）
                    if ability_value < 0:
                        conflict_id = f"ability_negative_{entity.id}_{ability_name}"

                        conflict = ConflictDetection(
                            id=conflict_id,
                            conflict_type=ConflictType.CHARACTER_ABILITY_CONFLICT,
                            severity=ConflictSeverity.MEDIUM,
                            title=f"{entity.name}的{ability_name}数值异常",
                            description=f"{entity.name}的{ability_name}为负数({ability_value})，这在逻辑上不合理",
                            involved_entities=[entity.id],
                            involved_relationships=[],
                            conflicting_chapters=[entity.first_mentioned_chapter or current_chapter],
                            evidence=[
                                f"{ability_name}数值为{ability_value}",
                                "能力数值不应为负数"
                            ],
                            impact_analysis="负数能力值会影响故事的合理性和角色设定的一致性",
                            confidence_score=0.7,
                            repair_suggestions=[
                                f"将{ability_name}修改为合理的正数值",
                                "重新评估角色的能力设定",
                                "添加能力数值的解释说明"
                            ],
                            alternative_solutions=[
                                "使用描述性的能力等级而非数值",
                                "建立统一的能力评估标准"
                            ],
                            detected_at=datetime.now(),
                            last_updated=datetime.now()
                        )

                        conflicts.append(conflict)

        return conflicts

    async def _check_world_setting_conflicts(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int
    ) -> List[ConflictDetection]:
        """
        🌍 [冲突检测] 检测世界设定的不一致

        检测世界观设定的前后矛盾
        """
        conflicts = []

        # 收集所有场景和概念实体
        world_entities = [e for e in world_graph.entities
                         if e.type in [EntityType.SCENE, EntityType.CONCEPT, EntityType.ORGANIZATION]]

        # 检测场景的位置冲突
        scene_entities = [e for e in world_entities if e.type == EntityType.SCENE]

        for scene in scene_entities:
            if not scene.properties:
                continue

            # 检查场景属性中的位置信息
            location_keys = ['location', 'position', 'coordinates', 'region', 'area']
            scene_locations = {}

            for key, value in scene.properties.items():
                if any(loc_key in key.lower() for loc_key in location_keys):
                    scene_locations[key] = value

            # 检测位置信息的冲突（这里实现基础检测）
            if len(scene_locations) > 1:
                # 如果有多个位置属性，检查是否一致
                location_values = list(scene_locations.values())
                if len(set(str(v) for v in location_values)) > 1:
                    conflict_id = f"location_conflict_{scene.id}"

                    conflict = ConflictDetection(
                        id=conflict_id,
                        conflict_type=ConflictType.LOCATION_CONFLICT,
                        severity=ConflictSeverity.MEDIUM,
                        title=f"{scene.name}的位置信息不一致",
                        description=f"{scene.name}存在多个不同的位置描述：{scene_locations}",
                        involved_entities=[scene.id],
                        involved_relationships=[],
                        conflicting_chapters=[scene.first_mentioned_chapter or current_chapter],
                        evidence=[
                            f"位置属性：{scene_locations}",
                            "多个位置描述不一致"
                        ],
                        impact_analysis="位置信息不一致会影响故事的空间逻辑和读者的理解",
                        confidence_score=0.6,
                        repair_suggestions=[
                            "统一场景的位置描述",
                            "删除冗余或错误的位置信息",
                            "建立清晰的世界地图和位置关系"
                        ],
                        alternative_solutions=[
                            "使用相对位置描述而非绝对坐标",
                            "创建位置层次结构（国家-城市-街道）"
                        ],
                        detected_at=datetime.now(),
                        last_updated=datetime.now()
                    )

                    conflicts.append(conflict)

        return conflicts

    async def _check_personality_inconsistencies(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int
    ) -> List[ConflictDetection]:
        """
        🎭 [冲突检测] 检测角色性格的不一致

        检测角色性格描述的前后矛盾
        """
        conflicts = []

        # 检查角色实体的性格属性
        character_entities = [e for e in world_graph.entities if e.type == EntityType.CHARACTER]

        for character in character_entities:
            if not character.properties:
                continue

            # 查找性格相关的属性
            personality_keys = ['personality', 'character', 'trait', 'nature', 'temperament']
            personality_attrs = {}

            for key, value in character.properties.items():
                if any(p_key in key.lower() for p_key in personality_keys):
                    personality_attrs[key] = value

            if len(personality_attrs) < 2:
                continue  # 需要至少两个性格属性才能检测冲突

            # 检测矛盾的性格特征（这里实现基础的关键词冲突检测）
            contradictory_pairs = [
                (['善良', '仁慈', '友善'], ['邪恶', '残忍', '恶毒']),
                (['勇敢', '无畏', '英勇'], ['胆小', '懦弱', '害怕']),
                (['聪明', '智慧', '机智'], ['愚蠢', '笨拙', '迟钝']),
                (['冷静', '理性', '沉着'], ['冲动', '急躁', '暴躁'])
            ]

            for positive_traits, negative_traits in contradictory_pairs:
                has_positive = any(
                    any(trait in str(value).lower() for trait in positive_traits)
                    for value in personality_attrs.values()
                )
                has_negative = any(
                    any(trait in str(value).lower() for trait in negative_traits)
                    for value in personality_attrs.values()
                )

                if has_positive and has_negative:
                    conflict_id = f"personality_conflict_{character.id}_{len(conflicts)}"

                    conflict = ConflictDetection(
                        id=conflict_id,
                        conflict_type=ConflictType.PERSONALITY_INCONSISTENCY,
                        severity=ConflictSeverity.MEDIUM,
                        title=f"{character.name}的性格描述存在矛盾",
                        description=f"{character.name}同时具有相互矛盾的性格特征",
                        involved_entities=[character.id],
                        involved_relationships=[],
                        conflicting_chapters=[character.first_mentioned_chapter or current_chapter],
                        evidence=[
                            f"性格属性：{personality_attrs}",
                            f"同时包含正面特征({positive_traits})和负面特征({negative_traits})"
                        ],
                        impact_analysis="矛盾的性格设定会影响角色的一致性和可信度",
                        confidence_score=0.5,
                        repair_suggestions=[
                            "统一角色的性格设定",
                            "为性格的复杂性提供合理解释",
                            "通过角色发展弧线解释性格变化"
                        ],
                        alternative_solutions=[
                            "将矛盾特征设定为角色的内在冲突",
                            "通过不同情境展现角色的多面性"
                        ],
                        detected_at=datetime.now(),
                        last_updated=datetime.now()
                    )

                    conflicts.append(conflict)
                    break  # 避免重复检测同一角色

        return conflicts

    async def _check_location_conflicts(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int
    ) -> List[ConflictDetection]:
        """
        📍 [冲突检测] 检测位置和场景的冲突

        检测角色在同一时间出现在不同位置的冲突
        """
        conflicts = []

        # 这里实现基础的位置冲突检测
        # 在实际应用中，需要更复杂的时空分析

        # 检查角色与场景的关系
        character_location_rels = [
            rel for rel in world_graph.relationships
            if rel.relationship_type == RelationshipTypes.LOCATED_IN
        ]

        # 按角色分组位置关系
        character_locations = {}
        for rel in character_location_rels:
            char_id = rel.source_entity_id
            if char_id not in character_locations:
                character_locations[char_id] = []
            character_locations[char_id].append(rel)

        # 检测同一章节内的位置冲突
        for char_id, location_rels in character_locations.items():
            if len(location_rels) < 2:
                continue

            # 按章节分组
            chapter_locations = {}
            for rel in location_rels:
                chapter = rel.last_updated_chapter or rel.established_chapter or current_chapter
                if chapter not in chapter_locations:
                    chapter_locations[chapter] = []
                chapter_locations[chapter].append(rel)

            # 检查每个章节是否有多个位置
            for chapter, rels in chapter_locations.items():
                if len(rels) > 1:
                    # 获取不同的位置
                    locations = list(set(rel.target_entity_id for rel in rels))
                    if len(locations) > 1:
                        character = next((e for e in world_graph.entities if e.id == char_id), None)
                        char_name = character.name if character else "角色"

                        location_names = []
                        for loc_id in locations:
                            location = next((e for e in world_graph.entities if e.id == loc_id), None)
                            location_names.append(location.name if location else "位置")

                        conflict_id = f"location_conflict_{char_id}_{chapter}"

                        conflict = ConflictDetection(
                            id=conflict_id,
                            conflict_type=ConflictType.LOCATION_CONFLICT,
                            severity=ConflictSeverity.HIGH,
                            title=f"{char_name}在第{chapter}章同时出现在多个位置",
                            description=f"{char_name}在同一章节内同时位于：{', '.join(location_names)}",
                            involved_entities=[char_id] + locations,
                            involved_relationships=[rel.id for rel in rels],
                            conflicting_chapters=[chapter],
                            evidence=[
                                f"第{chapter}章的位置关系：{location_names}",
                                "同一角色不能同时出现在多个位置"
                            ],
                            impact_analysis="位置冲突会破坏故事的空间逻辑和连贯性",
                            confidence_score=0.8,
                            repair_suggestions=[
                                "确定角色在该章节的唯一位置",
                                "添加角色移动的过程描述",
                                "检查章节划分的准确性"
                            ],
                            alternative_solutions=[
                                "将多个位置设定为同一区域的不同部分",
                                "通过时间线细分解决位置变化"
                            ],
                            detected_at=datetime.now(),
                            last_updated=datetime.now()
                        )

                        conflicts.append(conflict)

        return conflicts

    async def _check_item_ownership_conflicts(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int
    ) -> List[ConflictDetection]:
        """
        🎒 [冲突检测] 检测物品归属的冲突

        检测同一物品被多个角色同时拥有的冲突
        """
        conflicts = []

        # 查找所有拥有关系
        ownership_rels = [
            rel for rel in world_graph.relationships
            if rel.relationship_type == RelationshipTypes.OWNS
        ]

        # 按物品分组拥有关系
        item_owners = {}
        for rel in ownership_rels:
            item_id = rel.target_entity_id
            if item_id not in item_owners:
                item_owners[item_id] = []
            item_owners[item_id].append(rel)

        # 检测同一物品的多重拥有
        for item_id, ownership_list in item_owners.items():
            if len(ownership_list) < 2:
                continue

            # 检查是否有同时有效的拥有关系
            active_ownerships = [
                rel for rel in ownership_list
                if rel.status == RelationshipStatus.ACTIVE
            ]

            if len(active_ownerships) > 1:
                item = next((e for e in world_graph.entities if e.id == item_id), None)
                item_name = item.name if item else "物品"

                owner_names = []
                for rel in active_ownerships:
                    owner = next((e for e in world_graph.entities if e.id == rel.source_entity_id), None)
                    owner_names.append(owner.name if owner else "角色")

                conflict_id = f"ownership_conflict_{item_id}"

                conflict = ConflictDetection(
                    id=conflict_id,
                    conflict_type=ConflictType.ITEM_OWNERSHIP_CONFLICT,
                    severity=ConflictSeverity.HIGH,
                    title=f"{item_name}被多个角色同时拥有",
                    description=f"{item_name}同时被以下角色拥有：{', '.join(owner_names)}",
                    involved_entities=[rel.source_entity_id for rel in active_ownerships] + [item_id],
                    involved_relationships=[rel.id for rel in active_ownerships],
                    conflicting_chapters=[rel.established_chapter or current_chapter for rel in active_ownerships],
                    evidence=[
                        f"活跃的拥有关系数量：{len(active_ownerships)}",
                        f"拥有者：{owner_names}"
                    ],
                    impact_analysis="物品的多重拥有会造成逻辑混乱和情节冲突",
                    confidence_score=0.9,
                    repair_suggestions=[
                        "确定物品的唯一拥有者",
                        "将其他拥有关系标记为历史状态",
                        "添加物品转移的情节说明"
                    ],
                    alternative_solutions=[
                        "将物品设定为可共享的类型",
                        "创建物品的多个副本"
                    ],
                    detected_at=datetime.now(),
                    last_updated=datetime.now()
                )

                conflicts.append(conflict)

        return conflicts

    async def _check_knowledge_inconsistencies(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int
    ) -> List[ConflictDetection]:
        """
        🧠 [冲突检测] 检测角色知识的不一致

        检测角色对信息的了解程度的前后矛盾
        """
        conflicts = []

        # 查找知识相关的关系
        knowledge_rels = [
            rel for rel in world_graph.relationships
            if rel.relationship_type == RelationshipTypes.KNOWS
        ]

        # 按角色分组知识关系
        character_knowledge = {}
        for rel in knowledge_rels:
            char_id = rel.source_entity_id
            if char_id not in character_knowledge:
                character_knowledge[char_id] = []
            character_knowledge[char_id].append(rel)

        # 检测知识的时间线冲突
        for char_id, knowledge_list in character_knowledge.items():
            for rel in knowledge_list:
                # 检查角色是否在了解某信息之前就使用了该信息
                knowledge_chapter = rel.established_chapter or current_chapter

                # 这里可以添加更复杂的知识一致性检测逻辑
                # 目前实现基础的时间线检测

                character = next((e for e in world_graph.entities if e.id == char_id), None)
                if character and character.first_mentioned_chapter:
                    if knowledge_chapter < character.first_mentioned_chapter:
                        char_name = character.name if character else "角色"
                        known_entity = next((e for e in world_graph.entities if e.id == rel.target_entity_id), None)
                        known_name = known_entity.name if known_entity else "信息"

                        conflict_id = f"knowledge_timeline_{char_id}_{rel.target_entity_id}"

                        conflict = ConflictDetection(
                            id=conflict_id,
                            conflict_type=ConflictType.KNOWLEDGE_INCONSISTENCY,
                            severity=ConflictSeverity.MEDIUM,
                            title=f"{char_name}在出现前就了解{known_name}",
                            description=f"{char_name}在第{knowledge_chapter}章就了解{known_name}，但直到第{character.first_mentioned_chapter}章才首次出现",
                            involved_entities=[char_id, rel.target_entity_id],
                            involved_relationships=[rel.id],
                            conflicting_chapters=[knowledge_chapter, character.first_mentioned_chapter],
                            evidence=[
                                f"第{knowledge_chapter}章建立知识关系",
                                f"第{character.first_mentioned_chapter}章角色首次出现"
                            ],
                            impact_analysis="知识时间线错误会影响故事的逻辑性",
                            confidence_score=0.6,
                            repair_suggestions=[
                                "调整知识获得的时间",
                                "提前角色的出现时间",
                                "通过背景设定解释知识来源"
                            ],
                            alternative_solutions=[
                                "将知识设定为角色的先天了解",
                                "通过其他角色传递知识"
                            ],
                            detected_at=datetime.now(),
                            last_updated=datetime.now()
                        )

                        conflicts.append(conflict)

        return conflicts

    def _determine_severity(
        self,
        confidence_score: float,
        severity_thresholds: Dict[str, float]
    ) -> ConflictSeverity:
        """
        📊 [冲突检测] 根据置信度确定冲突严重程度

        Args:
            confidence_score: 冲突检测的置信度 (0-1)
            severity_thresholds: 严重程度阈值配置

        Returns:
            ConflictSeverity: 冲突严重程度
        """
        if confidence_score >= severity_thresholds.get("critical", 0.9):
            return ConflictSeverity.CRITICAL
        elif confidence_score >= severity_thresholds.get("high", 0.7):
            return ConflictSeverity.HIGH
        elif confidence_score >= severity_thresholds.get("medium", 0.5):
            return ConflictSeverity.MEDIUM
        else:
            return ConflictSeverity.LOW

    def _calculate_consistency_score(self, conflicts: List[ConflictDetection]) -> float:
        """
        📈 [冲突检测] 计算故事一致性评分

        基于冲突数量和严重程度计算整体一致性
        """
        if not conflicts:
            return 1.0

        # 按严重程度加权计算
        severity_weights = {
            ConflictSeverity.CRITICAL: 1.0,
            ConflictSeverity.HIGH: 0.7,
            ConflictSeverity.MEDIUM: 0.4,
            ConflictSeverity.LOW: 0.1
        }

        total_weight = sum(severity_weights[conflict.severity] for conflict in conflicts)
        max_possible_weight = len(conflicts) * 1.0  # 假设所有冲突都是CRITICAL

        consistency_score = max(0.0, 1.0 - (total_weight / max_possible_weight))
        return consistency_score

    def _calculate_integrity_score(self, conflicts: List[ConflictDetection]) -> float:
        """
        🔍 [冲突检测] 计算逻辑完整性评分

        基于冲突类型的多样性和影响范围计算
        """
        if not conflicts:
            return 1.0

        # 统计不同类型的冲突
        conflict_types = set(conflict.conflict_type for conflict in conflicts)
        type_diversity_penalty = len(conflict_types) / len(ConflictType)

        # 计算平均置信度
        avg_confidence = sum(conflict.confidence_score for conflict in conflicts) / len(conflicts)

        integrity_score = max(0.0, 1.0 - (type_diversity_penalty * 0.5 + avg_confidence * 0.5))
        return integrity_score

    def _analyze_conflict_trend(
        self,
        current_conflicts: List[ConflictDetection],
        previous_analysis: Optional[ConflictAnalysisResult]
    ) -> str:
        """
        📊 [冲突检测] 分析冲突趋势

        比较当前和之前的冲突情况，判断趋势
        """
        if not previous_analysis:
            return "stable"  # 没有历史数据，默认稳定

        current_count = len(current_conflicts)
        previous_count = previous_analysis.total_conflicts

        if current_count > previous_count * 1.2:
            return "worsening"
        elif current_count < previous_count * 0.8:
            return "improving"
        else:
            return "stable"

    def _identify_risk_areas(self, conflicts: List[ConflictDetection]) -> List[str]:
        """
        ⚠️ [冲突检测] 识别风险区域

        基于冲突分布识别需要重点关注的风险区域
        """
        risk_areas = []

        # 统计冲突类型
        type_counts = {}
        for conflict in conflicts:
            conflict_type = conflict.conflict_type.value
            type_counts[conflict_type] = type_counts.get(conflict_type, 0) + 1

        # 识别高频冲突类型
        total_conflicts = len(conflicts)
        for conflict_type, count in type_counts.items():
            if count / total_conflicts > 0.3:  # 超过30%的冲突属于同一类型
                risk_areas.append(f"{conflict_type}冲突频发")

        # 识别严重冲突集中的章节
        chapter_severity = {}
        for conflict in conflicts:
            for chapter in conflict.conflicting_chapters:
                if chapter not in chapter_severity:
                    chapter_severity[chapter] = []
                chapter_severity[chapter].append(conflict.severity)

        for chapter, severities in chapter_severity.items():
            critical_count = sum(1 for s in severities if s == ConflictSeverity.CRITICAL)
            if critical_count > 2:  # 单章节超过2个严重冲突
                risk_areas.append(f"第{chapter}章冲突集中")

        return risk_areas


def create_conflict_detection_engine() -> ConflictDetectionEngine:
    """
    🏭 [冲突检测] 创建冲突检测引擎实例

    工厂函数，用于创建和初始化冲突检测引擎

    Returns:
        ConflictDetectionEngine: 初始化完成的冲突检测引擎实例
    """
    log_info("冲突检测", "创建冲突检测引擎实例")

    engine = ConflictDetectionEngine()

    log_info("冲突检测", "冲突检测引擎创建完成",
            支持的冲突类型=[ct.value for ct in ConflictType],
            严重程度级别=[cs.value for cs in ConflictSeverity])

    return engine
