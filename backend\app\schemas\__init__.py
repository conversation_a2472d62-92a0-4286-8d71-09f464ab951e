"""
📝 [生成] Pydantic模型包
导出所有API请求和响应的数据结构
"""

# 生成相关模型
from .generation import *

# 世界图谱相关模型
from .world_graph import (
    EntityBase, EntityCreate, EntityUpdate, EntityResponse, EntityListResponse,
    RelationshipBase, RelationshipCreate, RelationshipUpdate, RelationshipResponse, RelationshipListResponse,
    WorldGraphResponse, EntitySearchRequest, RelationshipSearchRequest, GraphAnalyticsResponse
)

__all__ = [
    # 生成相关
    "StoryBibleCreate", "StoryBibleResponse", "ChapterCreate", "ChapterResponse",
    "GenerationRequest", "GenerationResponse", "GenerationStatus", "AIProvider", "StoryGenre",

    # 世界图谱相关
    "EntityBase", "EntityCreate", "EntityUpdate", "EntityResponse", "EntityListResponse",
    "RelationshipBase", "RelationshipCreate", "RelationshipUpdate", "RelationshipResponse", "RelationshipListResponse",
    "WorldGraphResponse", "EntitySearchRequest", "RelationshipSearchRequest", "GraphAnalyticsResponse"
]