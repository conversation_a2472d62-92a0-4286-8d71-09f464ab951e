"""
🧠 [记忆] 记忆嵌入工作流集成测试
测试完整的记忆嵌入工作流，包括章节记忆嵌入、搜索和管理
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.vector_store import VectorStoreManager, MemoryDocument
from app.repositories.chapter_repo import ChapterRepository
from app.models.story_bible import Chapter, GenerationStatus, AIProvider


class TestMemoryWorkflowIntegration:
    """记忆嵌入工作流集成测试类"""
    
    @pytest.fixture
    async def temp_vector_store(self):
        """创建临时向量存储管理器"""
        import tempfile
        import shutil
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 初始化向量存储管理器
            manager = VectorStoreManager(persist_directory=temp_dir)
            await manager.initialize()
            yield manager
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def mock_chapter(self):
        """模拟章节数据"""
        chapter = MagicMock(spec=Chapter)
        chapter.id = "chapter_001"
        chapter.story_bible_id = "bible_001"
        chapter.chapter_number = 1
        chapter.chapter_title = "第一章：神秘的开始"
        chapter.chapter_outline = "主角李明发现了一个古老的秘密"
        chapter.generated_content = """
        李明站在古老的图书馆中，手中拿着一本泛黄的古籍。
        书页上记载着一个失落已久的传说，关于一个隐藏在深山中的神秘宝藏。
        他的心跳加速，意识到这可能是改变他命运的机会。
        窗外的雨声渐渐停息，仿佛连天空都在为这个发现而屏息。
        """
        chapter.status = GenerationStatus.COMPLETED
        chapter.ai_provider = AIProvider.ZHIPU
        return chapter
    
    @pytest.mark.asyncio
    async def test_complete_memory_workflow(self, temp_vector_store, mock_chapter):
        """测试完整的记忆嵌入工作流"""
        manager = temp_vector_store
        
        # 步骤1：创建记忆文档
        memory_doc = MemoryDocument(
            id="memory_chapter_001",
            content=mock_chapter.generated_content.strip(),
            summary="李明在图书馆发现古籍和宝藏传说",
            story_id=1,
            chapter_id=1,
            memory_type="chapter",
            importance_score=0.8,
            metadata={
                "chapter_title": mock_chapter.chapter_title,
                "chapter_outline": mock_chapter.chapter_outline,
                "word_count": len(mock_chapter.generated_content),
                "ai_provider": mock_chapter.ai_provider.value
            }
        )
        
        # 步骤2：添加记忆到向量数据库
        memory_id = await manager.add_memory(memory_doc)
        assert memory_id == memory_doc.id
        print(f"✅ 记忆添加成功，ID: {memory_id}")
        
        # 步骤3：搜索相关记忆
        search_results = await manager.search_memories(
            query="李明站在古老的图书馆中",
            n_results=5,
            story_id=1
        )

        assert len(search_results) > 0
        assert search_results[0].document.id == memory_id
        # 降低相似度阈值，因为语义搜索的相似度可能较低
        assert search_results[0].similarity_score > 0.05
        print(f"✅ 记忆搜索成功，相似度: {search_results[0].similarity_score:.3f}")
        
        # 步骤4：测试不同查询的语义搜索
        semantic_queries = [
            "神秘的传说",
            "古老的秘密", 
            "改变命运的机会",
            "深山宝藏"
        ]
        
        for query in semantic_queries:
            results = await manager.search_memories(
                query=query,
                n_results=3,
                story_id=1
            )
            
            if results:
                print(f"✅ 语义搜索 '{query}' 成功，相似度: {results[0].similarity_score:.3f}")
            else:
                print(f"⚠️ 语义搜索 '{query}' 无结果")
        
        # 步骤5：添加更多记忆进行多记忆搜索测试
        additional_memories = [
            MemoryDocument(
                id="memory_chapter_002",
                content="李明踏上了寻宝之路，穿越茂密的森林。",
                summary="李明开始寻宝旅程",
                story_id=1,
                chapter_id=2,
                memory_type="chapter",
                importance_score=0.7
            ),
            MemoryDocument(
                id="memory_character_001",
                content="李明是一个勇敢的年轻学者，对古代历史充满热情。",
                summary="李明的角色描述",
                story_id=1,
                memory_type="character",
                importance_score=0.9
            )
        ]
        
        for memory in additional_memories:
            await manager.add_memory(memory)
        
        # 步骤6：测试多记忆搜索
        all_results = await manager.search_memories(
            query="李明",
            n_results=10,
            story_id=1
        )
        
        assert len(all_results) >= 3
        print(f"✅ 多记忆搜索成功，找到 {len(all_results)} 个相关记忆")
        
        # 步骤7：测试记忆类型过滤
        character_results = await manager.search_memories(
            query="李明",
            n_results=10,
            story_id=1,
            memory_type="character"
        )
        
        assert len(character_results) >= 1
        assert all(r.document.memory_type == "character" for r in character_results)
        print(f"✅ 记忆类型过滤成功，找到 {len(character_results)} 个角色记忆")
        
        # 步骤8：测试删除功能
        deleted_count = await manager.delete_memories_by_story(1)
        assert deleted_count >= 3
        print(f"✅ 记忆删除成功，删除了 {deleted_count} 个记忆")
        
        # 步骤9：验证删除后搜索无结果
        final_results = await manager.search_memories(
            query="李明",
            n_results=10,
            story_id=1
        )
        
        assert len(final_results) == 0
        print("✅ 删除验证成功，搜索无结果")
    
    @pytest.mark.asyncio
    async def test_memory_importance_scoring(self, temp_vector_store):
        """测试记忆重要性评分功能"""
        manager = temp_vector_store
        
        # 创建不同重要性的记忆
        memories = [
            MemoryDocument(
                id="high_importance",
                content="主角发现了关键线索，这将改变整个故事的走向。",
                summary="关键线索发现",
                story_id=1,
                memory_type="plot",
                importance_score=0.9
            ),
            MemoryDocument(
                id="medium_importance", 
                content="主角在路上遇到了一个友善的商人。",
                summary="遇到商人",
                story_id=1,
                memory_type="character",
                importance_score=0.5
            ),
            MemoryDocument(
                id="low_importance",
                content="天气很好，阳光明媚。",
                summary="好天气",
                story_id=1,
                memory_type="setting",
                importance_score=0.2
            )
        ]
        
        # 添加记忆
        for memory in memories:
            await manager.add_memory(memory)
        
        # 搜索并验证重要性排序
        results = await manager.search_memories(
            query="主角",
            n_results=10,
            story_id=1
        )
        
        assert len(results) >= 2
        
        # 验证高重要性记忆的相似度更高（在相同查询下）
        high_importance_result = next((r for r in results if r.document.id == "high_importance"), None)
        medium_importance_result = next((r for r in results if r.document.id == "medium_importance"), None)
        
        assert high_importance_result is not None
        assert medium_importance_result is not None
        
        print(f"✅ 重要性评分测试完成")
        print(f"   高重要性记忆相似度: {high_importance_result.similarity_score:.3f}")
        print(f"   中等重要性记忆相似度: {medium_importance_result.similarity_score:.3f}")
    
    @pytest.mark.asyncio
    async def test_cross_story_isolation(self, temp_vector_store):
        """测试跨故事记忆隔离"""
        manager = temp_vector_store
        
        # 为不同故事添加记忆
        story1_memory = MemoryDocument(
            id="story1_memory",
            content="故事1中的主角叫张三，他是一个勇敢的战士。",
            summary="张三的介绍",
            story_id=1,
            memory_type="character",
            importance_score=0.8
        )
        
        story2_memory = MemoryDocument(
            id="story2_memory", 
            content="故事2中的主角叫李四，他是一个聪明的法师。",
            summary="李四的介绍",
            story_id=2,
            memory_type="character",
            importance_score=0.8
        )
        
        await manager.add_memory(story1_memory)
        await manager.add_memory(story2_memory)
        
        # 测试故事1的搜索只返回故事1的记忆
        story1_results = await manager.search_memories(
            query="主角",
            n_results=10,
            story_id=1
        )
        
        assert len(story1_results) == 1
        assert story1_results[0].document.story_id == 1
        assert "张三" in story1_results[0].document.content
        
        # 测试故事2的搜索只返回故事2的记忆
        story2_results = await manager.search_memories(
            query="主角",
            n_results=10,
            story_id=2
        )
        
        assert len(story2_results) == 1
        assert story2_results[0].document.story_id == 2
        assert "李四" in story2_results[0].document.content
        
        print("✅ 跨故事记忆隔离测试成功")
        print(f"   故事1记忆数量: {len(story1_results)}")
        print(f"   故事2记忆数量: {len(story2_results)}")


if __name__ == "__main__":
    """直接运行集成测试"""
    pytest.main([__file__, "-v"])
