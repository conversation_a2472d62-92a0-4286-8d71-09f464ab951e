"""
🧪 [测试] 时间感知扭曲器测试

测试时间感知扭曲器的各项功能，包括情绪检测、
时间扭曲、主观时间调整等。

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import pytest
from unittest.mock import patch
import random

from app.core.time_distortion import (
    TimePerceptionDistorter,
    EmotionalState,
    TimePerceptionMode,
    TimeDistortionPattern,
    TimeDistortionResult,
    create_time_perception_distorter,
    distort_time_perception,
    apply_subjective_timing,
    analyze_time_distortion_potential
)


class TestTimePerceptionDistorter:
    """测试时间感知扭曲器类"""
    
    def setup_method(self):
        """测试前设置"""
        self.distorter = TimePerceptionDistorter()
    
    def test_distorter_initialization(self):
        """测试扭曲器初始化"""
        assert self.distorter is not None
        assert len(self.distorter.distortion_patterns) > 0
        
        # 检查各种情绪的扭曲模式
        for emotion in [EmotionalState.ANGER, EmotionalState.SADNESS, EmotionalState.ANXIETY]:
            assert emotion in self.distorter.distortion_patterns
            if emotion in self.distorter.distortion_patterns:
                assert len(self.distorter.distortion_patterns[emotion]) > 0
    
    def test_detect_emotional_context_anger(self):
        """测试愤怒情绪检测"""
        text = "他非常愤怒，几分钟后就要爆发了。"
        result = self.distorter.detect_emotional_context(text)
        
        assert result["has_emotional_content"] is True
        assert result["has_time_content"] is True
        assert result["primary_emotion"] == EmotionalState.ANGER
        assert result["emotion_intensity"] > 0.5
        assert "几分钟" in result["time_references"]
    
    def test_detect_emotional_context_sadness(self):
        """测试悲伤情绪检测"""
        text = "她很难过，感觉一会儿都像过了很久。"
        result = self.distorter.detect_emotional_context(text)
        
        assert result["has_emotional_content"] is True
        assert result["has_time_content"] is True
        assert result["primary_emotion"] == EmotionalState.SADNESS
        assert "一会儿" in result["time_references"]
    
    def test_detect_emotional_context_anxiety(self):
        """测试焦虑情绪检测"""
        text = "他很焦虑，觉得时间不够了，来不及完成任务。"
        result = self.distorter.detect_emotional_context(text)
        
        assert result["has_emotional_content"] is True
        assert result["has_time_content"] is True
        assert result["primary_emotion"] == EmotionalState.ANXIETY
        assert "时间" in result["time_references"]
    
    def test_detect_emotional_context_no_time(self):
        """测试无时间内容的文本"""
        text = "他非常愤怒，想要砸东西。"
        result = self.distorter.detect_emotional_context(text)
        
        assert result["has_emotional_content"] is True
        assert result["has_time_content"] is False
        assert result["primary_emotion"] == EmotionalState.ANGER
    
    def test_detect_emotional_context_no_emotion(self):
        """测试无情绪内容的文本"""
        text = "几分钟后他就到了。"
        result = self.distorter.detect_emotional_context(text)
        
        assert result["has_emotional_content"] is False
        assert result["has_time_content"] is True
        assert result["primary_emotion"] is None
    
    def test_distort_time_perception_anger(self):
        """测试愤怒时间扭曲"""
        text = "他愤怒地等了几分钟，然后离开了。"
        result = self.distorter.distort_time_perception(text, intensity=0.8)
        
        assert isinstance(result, TimeDistortionResult)
        assert result.original_text == text
        assert result.emotional_context["primary_emotion"] == EmotionalState.ANGER
        
        # 如果成功扭曲，应该有变化
        if result.distortions_applied:
            assert result.modified_text != text
            assert len(result.distortions_applied) > 0
    
    def test_distort_time_perception_sadness(self):
        """测试悲伤时间扭曲"""
        text = "她难过地坐着，感觉几分钟都过得很慢。"
        result = self.distorter.distort_time_perception(text, intensity=0.9)
        
        assert result.emotional_context["primary_emotion"] == EmotionalState.SADNESS
        assert result.emotional_context["has_time_content"] is True
    
    def test_distort_time_perception_no_emotion(self):
        """测试无情绪内容的扭曲"""
        text = "几分钟后他就到了。"
        result = self.distorter.distort_time_perception(text, intensity=0.8)
        
        assert result.original_text == text
        assert result.modified_text == text  # 应该没有变化
        assert len(result.distortions_applied) == 0
        assert result.processing_details["skipped"] is True
        assert result.processing_details["reason"] == "无情绪内容"
    
    def test_distort_time_perception_no_time(self):
        """测试无时间内容的扭曲"""
        text = "他非常愤怒，想要砸东西。"
        result = self.distorter.distort_time_perception(text, intensity=0.8)
        
        assert result.original_text == text
        assert result.modified_text == text  # 应该没有变化
        assert len(result.distortions_applied) == 0
        assert result.processing_details["skipped"] is True
        assert result.processing_details["reason"] == "无时间内容"
    
    def test_get_applicable_patterns(self):
        """测试获取适用模式"""
        text = "他愤怒地等了几分钟。"
        patterns = self.distorter._get_applicable_patterns(
            EmotionalState.ANGER, 
            text, 
            intensity=0.8
        )
        
        # 应该有一些适用的模式
        assert isinstance(patterns, list)
        for pattern in patterns:
            assert isinstance(pattern, TimeDistortionPattern)
            assert pattern.emotion == EmotionalState.ANGER
    
    def test_apply_time_distortion(self):
        """测试应用时间扭曲"""
        if EmotionalState.ANGER in self.distorter.distortion_patterns:
            pattern = self.distorter.distortion_patterns[EmotionalState.ANGER][0]
            text = "他愤怒地等了几分钟。"
            
            result = self.distorter._apply_time_distortion(
                text, 
                pattern, 
                intensity=0.8
            )
            
            assert "modified" in result
            assert "text" in result
            assert "original_expressions" in result
            assert "distorted_expressions" in result
    
    def test_apply_subjective_timing_anger(self):
        """测试愤怒主观时间调整"""
        text = "他很愤怒。然后离开了房间。"
        result = self.distorter.apply_subjective_timing(
            text, 
            EmotionalState.ANGER, 
            intensity=0.8
        )
        
        # 应该插入了时间描述
        assert len(result) >= len(text)
    
    def test_apply_subjective_timing_sadness(self):
        """测试悲伤主观时间调整"""
        text = "她很难过。坐在那里发呆。"
        result = self.distorter.apply_subjective_timing(
            text, 
            EmotionalState.SADNESS, 
            intensity=0.7
        )
        
        # 应该插入了时间描述
        assert isinstance(result, str)
    
    def test_apply_subjective_timing_unsupported_emotion(self):
        """测试不支持的情绪"""
        text = "他很平静。"
        result = self.distorter.apply_subjective_timing(
            text, 
            EmotionalState.DISGUST,  # 可能不支持的情绪
            intensity=0.7
        )
        
        # 应该返回原文本
        assert result == text


class TestFactoryFunctions:
    """测试工厂函数"""
    
    def test_create_time_perception_distorter(self):
        """测试创建扭曲器实例"""
        distorter = create_time_perception_distorter()
        assert isinstance(distorter, TimePerceptionDistorter)
        assert len(distorter.distortion_patterns) > 0
    
    def test_distort_time_perception_function(self):
        """测试快速扭曲函数"""
        text = "他愤怒地等了几分钟。"
        result = distort_time_perception(text, intensity=0.8)
        
        assert isinstance(result, str)
        # 结果可能相同（如果没有合适的扭曲）或不同（如果成功扭曲）
    
    def test_apply_subjective_timing_function(self):
        """测试主观时间调整函数"""
        text = "他很愤怒。然后离开了。"
        result = apply_subjective_timing(text, EmotionalState.ANGER, intensity=0.8)
        
        assert isinstance(result, str)
    
    def test_analyze_time_distortion_potential(self):
        """测试时间扭曲潜力分析"""
        # 高潜力文本
        high_potential_text = "他非常愤怒，等了几分钟就爆发了。"
        high_result = analyze_time_distortion_potential(high_potential_text)
        
        assert high_result["suitable_for_distortion"] is True
        assert high_result["distortion_potential"] > 0.3
        assert high_result["time_references_found"] > 0
        assert "emotional_context" in high_result
        
        # 低潜力文本（无时间内容）
        low_potential_text = "他很愤怒。"
        low_result = analyze_time_distortion_potential(low_potential_text)
        
        assert low_result["suitable_for_distortion"] is False
        assert low_result["distortion_potential"] <= 0.3


class TestTimePerceptionModes:
    """测试不同的时间感知模式"""
    
    def setup_method(self):
        """测试前设置"""
        self.distorter = TimePerceptionDistorter()
    
    def test_accelerated_mode(self):
        """测试加速感模式"""
        anger_patterns = self.distorter.distortion_patterns.get(EmotionalState.ANGER, [])
        accelerated_patterns = [p for p in anger_patterns if p.perception_mode == TimePerceptionMode.ACCELERATED]
        
        assert len(accelerated_patterns) > 0
        for pattern in accelerated_patterns:
            assert pattern.perception_mode == TimePerceptionMode.ACCELERATED
            assert len(pattern.objective_patterns) > 0
            assert len(pattern.subjective_replacements) > 0
    
    def test_decelerated_mode(self):
        """测试缓慢感模式"""
        sadness_patterns = self.distorter.distortion_patterns.get(EmotionalState.SADNESS, [])
        decelerated_patterns = [p for p in sadness_patterns if p.perception_mode == TimePerceptionMode.DECELERATED]
        
        assert len(decelerated_patterns) > 0
        for pattern in decelerated_patterns:
            assert pattern.perception_mode == TimePerceptionMode.DECELERATED
    
    def test_urgent_mode(self):
        """测试紧迫感模式"""
        anxiety_patterns = self.distorter.distortion_patterns.get(EmotionalState.ANXIETY, [])
        urgent_patterns = [p for p in anxiety_patterns if p.perception_mode == TimePerceptionMode.URGENT]
        
        assert len(urgent_patterns) > 0
        for pattern in urgent_patterns:
            assert pattern.perception_mode == TimePerceptionMode.URGENT
    
    def test_suspended_mode(self):
        """测试悬停感模式"""
        joy_patterns = self.distorter.distortion_patterns.get(EmotionalState.JOY, [])
        suspended_patterns = [p for p in joy_patterns if p.perception_mode == TimePerceptionMode.SUSPENDED]
        
        # 可能有也可能没有，取决于实现
        for pattern in suspended_patterns:
            assert pattern.perception_mode == TimePerceptionMode.SUSPENDED


class TestEdgeCases:
    """测试边界情况"""
    
    def setup_method(self):
        """测试前设置"""
        self.distorter = TimePerceptionDistorter()
    
    def test_empty_text(self):
        """测试空文本"""
        result = self.distorter.distort_time_perception("", intensity=0.8)
        
        assert result.original_text == ""
        assert result.modified_text == ""
        assert len(result.distortions_applied) == 0
    
    def test_very_short_text(self):
        """测试极短文本"""
        text = "愤怒。"
        result = self.distorter.distort_time_perception(text, intensity=0.8)
        
        assert result.original_text == text
        # 短文本可能无法扭曲
    
    def test_zero_intensity(self):
        """测试零强度"""
        text = "他愤怒地等了几分钟。"
        result = self.distorter.distort_time_perception(text, intensity=0.0)
        
        # 零强度应该很少或不扭曲
        assert len(result.distortions_applied) <= 1
    
    def test_maximum_intensity(self):
        """测试最大强度"""
        text = "他非常愤怒，等了几分钟，感觉一会儿都很长。"
        result = self.distorter.distort_time_perception(text, intensity=1.0)
        
        # 最大强度应该尝试更多扭曲
        assert isinstance(result, TimeDistortionResult)
    
    def test_mixed_emotions_with_time(self):
        """测试混合情绪和时间的文本"""
        text = "他既愤怒又悲伤，等了几分钟，感觉很久。"
        result = self.distorter.distort_time_perception(text, intensity=0.7)
        
        assert result.emotional_context["has_emotional_content"] is True
        assert result.emotional_context["has_time_content"] is True
        # 应该选择主导情绪
        assert result.emotional_context["primary_emotion"] is not None
    
    def test_multiple_time_references(self):
        """测试多个时间引用"""
        text = "他愤怒地等了几分钟，然后又等了一会儿，最后等了半小时。"
        result = self.distorter.distort_time_perception(text, intensity=0.8)
        
        assert len(result.emotional_context["time_references"]) > 1
        # 可能会扭曲多个时间表达
