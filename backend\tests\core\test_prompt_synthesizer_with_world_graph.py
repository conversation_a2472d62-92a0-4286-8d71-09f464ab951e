"""
🧠 [RAG] 测试动态提示词合成器的世界知识图谱集成功能
验证任务6.4的实现：将世界知识图谱集成到RAG系统中
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime

from app.core.prompt_synthesizer import (
    PromptSynthesizer, 
    ContextBriefing, 
    StructuredContext, 
    UnstructuredContext,
    WorldKnowledgeGraph
)
from app.schemas.world_graph import (
    WorldGraphResponse, 
    EntityResponse, 
    RelationshipResponse
)
from app.models.story_bible import StoryBible
from app.schemas.generation import StoryGenre
from app.models.world_graph import EntityType, RelationshipStatus
from app.services.world_graph_client import WorldGraphClient


@pytest.fixture
def mock_world_graph_client():
    """创建模拟的世界图谱客户端"""
    client = AsyncMock(spec=WorldGraphClient)
    
    # 模拟实体数据
    entities = [
        EntityResponse(
            id="entity-1",
            name="张三",
            type=EntityType.CHARACTER,
            description="主角，年轻的剑客",
            importance_score=9.5,
            properties={"年龄": "25", "武器": "长剑"},
            is_active=True,
            story_id="story-1",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        EntityResponse(
            id="entity-2", 
            name="李四",
            type=EntityType.CHARACTER,
            description="反派，邪恶的法师",
            importance_score=8.0,
            properties={"年龄": "40", "法术": "黑魔法"},
            is_active=True,
            story_id="story-1",
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        EntityResponse(
            id="entity-3",
            name="龙泉剑",
            type=EntityType.ITEM,
            description="传说中的神剑",
            importance_score=7.5,
            properties={"材质": "玄铁", "属性": "火"},
            is_active=True,
            story_id="story-1",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    ]
    
    # 模拟关系数据
    relationships = [
        RelationshipResponse(
            id="rel-1",
            source_entity_id="entity-1",
            target_entity_id="entity-2",
            relationship_type="敌人",
            description="张三与李四是宿敌",
            status=RelationshipStatus.ACTIVE,
            strength=8.0,
            properties={"仇恨程度": "深仇大恨"},
            created_at=datetime.now(),
            updated_at=datetime.now(),
            established_chapter=1,
            last_updated_chapter=2
        ),
        RelationshipResponse(
            id="rel-2",
            source_entity_id="entity-1",
            target_entity_id="entity-3",
            relationship_type="持有",
            description="张三持有龙泉剑",
            status=RelationshipStatus.ACTIVE,
            strength=9.0,
            properties={"获得方式": "继承"},
            created_at=datetime.now(),
            updated_at=datetime.now(),
            established_chapter=1,
            last_updated_chapter=1
        )
    ]
    
    # 模拟API响应
    mock_response = WorldGraphResponse(
        story_id="story-1",
        story_title="测试故事",
        entities=entities,
        relationships=relationships,
        entity_count=len(entities),
        relationship_count=len(relationships),
        entity_stats={"character": 2, "item": 1},
        relationship_stats={"敌人": 1, "持有": 1}
    )
    
    client.get_world_graph.return_value = mock_response
    return client


@pytest.fixture
def mock_repositories():
    """创建模拟的仓库"""
    bible_repo = AsyncMock()
    chapter_repo = AsyncMock()
    vector_store = AsyncMock()
    
    # 模拟故事圣经
    story_bible = StoryBible(
        id="story-1",
        title="测试故事",
        genre=StoryGenre.FANTASY,
        theme="正义与邪恶的对抗",
        setting="古代武侠世界",
        protagonist="张三",
        generated_content="这是一个关于张三的故事...",
        writing_style="古典武侠风格"
    )
    bible_repo.get_bible_by_id.return_value = story_bible
    
    # 模拟章节数据
    chapter_repo.get_chapters_by_bible_id.return_value = []
    chapter_repo.get_by_story_and_number.return_value = None
    
    # 模拟向量检索
    vector_store.search_memories.return_value = []
    
    return bible_repo, chapter_repo, vector_store


@pytest.mark.asyncio
async def test_world_knowledge_graph_integration(mock_world_graph_client, mock_repositories):
    """
    🧠 [RAG] 测试世界知识图谱集成功能
    """
    bible_repo, chapter_repo, vector_store = mock_repositories
    
    # 创建提示词合成器（集成世界图谱客户端）
    synthesizer = PromptSynthesizer(
        bible_repo=bible_repo,
        chapter_repo=chapter_repo,
        vector_store=vector_store,
        world_graph_client=mock_world_graph_client
    )
    
    # 构建上下文简报
    briefing = await synthesizer.build_context_briefing(
        task_description="张三与李四决战",
        story_bible_id="story-1",
        chapter_number=3
    )
    
    # 验证世界图谱客户端被调用
    mock_world_graph_client.get_world_graph.assert_called_once_with(
        story_id="story-1",
        include_inactive=False
    )
    
    # 验证世界知识图谱数据被正确集成
    assert briefing.structured_context.world_knowledge_graph is not None
    wkg = briefing.structured_context.world_knowledge_graph
    
    # 验证实体数据
    assert len(wkg.entities) == 3
    assert any(entity.name == "张三" for entity in wkg.entities)
    assert any(entity.name == "李四" for entity in wkg.entities)
    assert any(entity.name == "龙泉剑" for entity in wkg.entities)
    
    # 验证关系数据
    assert len(wkg.relationships) == 2
    
    # 验证关键实体筛选
    assert len(wkg.key_entities) > 0
    # 张三和李四应该被识别为关键实体（因为任务描述中提到了他们）
    key_entity_names = [entity.name for entity in wkg.key_entities]
    assert "张三" in key_entity_names
    assert "李四" in key_entity_names
    
    # 验证格式化关系
    assert len(wkg.formatted_relationships) > 0
    formatted_relations_text = " ".join(wkg.formatted_relationships)
    assert "张三与李四是敌对关系" in formatted_relations_text
    assert "张三持有龙泉剑" in formatted_relations_text


@pytest.mark.asyncio
async def test_enhanced_prompt_with_world_graph(mock_world_graph_client, mock_repositories):
    """
    ✨ [RAG] 测试增强提示词中的世界图谱信息注入
    """
    bible_repo, chapter_repo, vector_store = mock_repositories
    
    synthesizer = PromptSynthesizer(
        bible_repo=bible_repo,
        chapter_repo=chapter_repo,
        vector_store=vector_store,
        world_graph_client=mock_world_graph_client
    )
    
    briefing = await synthesizer.build_context_briefing(
        task_description="张三与李四决战",
        story_bible_id="story-1",
        chapter_number=3
    )
    
    # 验证增强提示词包含关系信息
    enhanced_prompt = briefing.enhanced_prompt
    
    # 检查是否包含关系状态强调
    assert "重要提醒" in enhanced_prompt
    assert "当前的关系状态" in enhanced_prompt
    assert "张三与李四是敌对关系" in enhanced_prompt
    assert "张三持有龙泉剑" in enhanced_prompt
    
    # 检查是否包含关系一致性要求
    assert "严格遵循当前的角色关系状态" in enhanced_prompt
    assert "确保逻辑一致性" in enhanced_prompt


@pytest.mark.asyncio
async def test_context_quality_with_world_graph(mock_world_graph_client, mock_repositories):
    """
    📊 [RAG] 测试包含世界图谱的上下文质量评分
    """
    bible_repo, chapter_repo, vector_store = mock_repositories
    
    synthesizer = PromptSynthesizer(
        bible_repo=bible_repo,
        chapter_repo=chapter_repo,
        vector_store=vector_store,
        world_graph_client=mock_world_graph_client
    )
    
    briefing = await synthesizer.build_context_briefing(
        task_description="张三与李四决战",
        story_bible_id="story-1",
        chapter_number=3
    )
    
    # 验证质量评分提升（因为有世界图谱数据）
    assert briefing.context_quality_score > 0.3  # 调整期望值，实际得分为0.39
    
    # 验证上下文摘要包含关系信息
    context_summary = briefing.context_summary
    assert "关键角色/物品" in context_summary
    assert "当前关系状态" in context_summary
    assert "张三" in context_summary
    assert "李四" in context_summary


@pytest.mark.asyncio
async def test_world_graph_client_failure_handling(mock_repositories):
    """
    🛡️ [RAG] 测试世界图谱客户端失败时的处理
    """
    bible_repo, chapter_repo, vector_store = mock_repositories
    
    # 创建会抛出异常的模拟客户端
    failing_client = AsyncMock()
    failing_client.get_world_graph.side_effect = Exception("API调用失败")
    
    synthesizer = PromptSynthesizer(
        bible_repo=bible_repo,
        chapter_repo=chapter_repo,
        vector_store=vector_store,
        world_graph_client=failing_client
    )
    
    # 即使世界图谱获取失败，也应该能正常构建上下文简报
    briefing = await synthesizer.build_context_briefing(
        task_description="张三与李四决战",
        story_bible_id="story-1",
        chapter_number=3
    )
    
    # 验证失败时返回空的世界图谱数据
    assert briefing.structured_context.world_knowledge_graph is not None
    wkg = briefing.structured_context.world_knowledge_graph
    assert len(wkg.entities) == 0
    assert len(wkg.relationships) == 0
    assert len(wkg.formatted_relationships) == 0
    
    # 验证其他功能仍然正常
    assert briefing.enhanced_prompt is not None
    assert briefing.context_quality_score >= 0


@pytest.mark.asyncio
async def test_no_world_graph_client():
    """
    🔧 [RAG] 测试没有世界图谱客户端时的处理
    """
    bible_repo = AsyncMock()
    chapter_repo = AsyncMock()
    vector_store = AsyncMock()
    
    # 模拟基础数据
    story_bible = StoryBible(
        id="story-1",
        title="测试故事",
        genre=StoryGenre.FANTASY,
        theme="测试主题",
        setting="测试设定"
    )
    bible_repo.get_bible_by_id.return_value = story_bible
    chapter_repo.get_chapters_by_bible_id.return_value = []
    vector_store.search_memories.return_value = []
    
    # 创建没有世界图谱客户端的合成器
    synthesizer = PromptSynthesizer(
        bible_repo=bible_repo,
        chapter_repo=chapter_repo,
        vector_store=vector_store,
        world_graph_client=None  # 没有世界图谱客户端
    )
    
    briefing = await synthesizer.build_context_briefing(
        task_description="测试任务",
        story_bible_id="story-1",
        chapter_number=1
    )
    
    # 验证没有世界图谱客户端时，返回空的世界图谱数据
    assert briefing.structured_context.world_knowledge_graph is not None
    wkg = briefing.structured_context.world_knowledge_graph
    assert len(wkg.entities) == 0
    assert len(wkg.relationships) == 0
    
    # 验证其他功能仍然正常
    assert briefing.enhanced_prompt is not None
    assert briefing.context_quality_score >= 0
