"""
🧪 [测试] 情感表达降级器测试模块

测试情感表达降级器的各项功能，包括过度描述检测、
情感表达不充分系统、降级模式应用等。

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import pytest
from typing import List, Dict, Any

from app.core.emotional_downgrader import (
    EmotionalInarticulationSystem,
    OverDescriptionDetector,
    InarticulationMode,
    DowngradePattern,
    DowngradeResult,
    create_emotional_downgrader,
    quick_emotional_downgrade,
    analyze_text_emotional_complexity,
    detect_ai_emotional_patterns,
    batch_emotional_downgrade,
    apply_preset_downgrade,
    PRESET_CONFIGURATIONS
)


class TestOverDescriptionDetector:
    """🔍 过度描述检测器测试类"""
    
    def setup_method(self):
        """设置测试环境"""
        self.detector = OverDescriptionDetector()
    
    def test_detector_initialization(self):
        """测试检测器初始化"""
        assert self.detector is not None
        assert len(self.detector.ai_patterns) > 0
        assert "过度复杂情感" in self.detector.ai_patterns
        assert "过度精确描述" in self.detector.ai_patterns
    
    def test_detect_over_complex_emotions(self):
        """测试检测过度复杂情感"""
        text = "他心中涌起复杂的情感，愤怒、失望、不甘心交织在一起，五味杂陈。"
        result = self.detector.detect_over_description(text)
        
        assert result["needs_downgrade"] is True
        assert result["over_description_level"] > 0.3
        assert "过度复杂情感" in result["detected_patterns"]
        assert len(result["detected_patterns"]["过度复杂情感"]["matches"]) > 0
    
    def test_detect_over_precise_description(self):
        """测试检测过度精确描述"""
        text = "他精确地感受到内心深处的变化，清晰地意识到这种强烈的情感波动。"
        result = self.detector.detect_over_description(text)
        
        assert result["needs_downgrade"] is True
        assert "过度精确描述" in result["detected_patterns"]
        assert "过度心理分析" in result["detected_patterns"]
    
    def test_detect_over_literary_expression(self):
        """测试检测过度文学化表达"""
        text = "痛苦如潮水般涌来，仿佛被雷击一般，宛如置身于情感的漩涡中。"
        result = self.detector.detect_over_description(text)
        
        assert result["needs_downgrade"] is True
        assert "过度文学化" in result["detected_patterns"]
    
    def test_detect_normal_text(self):
        """测试检测正常文本"""
        text = "他有点不高兴，摇摇头就走了。"
        result = self.detector.detect_over_description(text)
        
        assert result["needs_downgrade"] is False
        assert result["over_description_level"] < 0.3
        assert len(result["detected_patterns"]) == 0
    
    def test_calculate_severity_levels(self):
        """测试严重程度计算"""
        # 测试不同严重程度
        assert self.detector._calculate_severity(5, 10) == "严重"
        assert self.detector._calculate_severity(3, 10) == "中等"
        assert self.detector._calculate_severity(1, 10) == "轻微"
        assert self.detector._calculate_severity(0, 10) == "无"


class TestEmotionalInarticulationSystem:
    """🗣️ 情感表达不充分系统测试类"""
    
    def setup_method(self):
        """设置测试环境"""
        self.system = EmotionalInarticulationSystem()
    
    def test_system_initialization(self):
        """测试系统初始化"""
        assert self.system is not None
        assert len(self.system.downgrade_patterns) == 5
        assert InarticulationMode.VOCABULARY_POOR in self.system.downgrade_patterns
        assert InarticulationMode.EMOTIONAL_AVOIDANCE in self.system.downgrade_patterns
    
    def test_vocabulary_poor_mode(self):
        """测试词汇贫乏模式"""
        text = "这种复杂的情感真的难以形容，五味杂陈。"
        result = self.system.make_expression_inadequate(
            text, 
            intensity=1.0,
            target_modes=[InarticulationMode.VOCABULARY_POOR]
        )
        
        assert result.downgraded_text != text
        assert len(result.applied_modes) > 0
        assert "词汇贫乏" in result.applied_modes
        assert len(result.original_expressions) > 0
    
    def test_emotional_avoidance_mode(self):
        """测试情感回避模式"""
        text = "我感到非常愤怒和失望，这种痛苦让我无法承受。"
        result = self.system.make_expression_inadequate(
            text,
            intensity=1.0,
            target_modes=[InarticulationMode.EMOTIONAL_AVOIDANCE]
        )
        
        assert result.downgraded_text != text
        assert "情感回避" in result.applied_modes
    
    def test_body_substitution_mode(self):
        """测试身体代替模式"""
        # 使用更明确的触发词来确保匹配
        text = "他感到内心的痛苦，意识到这种情绪的强烈。"

        # 多次尝试以处理随机性
        success = False
        for _ in range(10):  # 最多尝试10次
            result = self.system.make_expression_inadequate(
                text,
                intensity=1.0,
                target_modes=[InarticulationMode.BODY_SUBSTITUTION]
            )

            if result.downgraded_text != text and "身体代替" in result.applied_modes:
                success = True
                break

        assert success, f"身体代替模式未能成功应用，最后结果: {result.downgraded_text}"
    
    def test_vague_expression_mode(self):
        """测试模糊表达模式"""
        text = "他精确地感受到这种强烈的情感变化。"
        result = self.system.make_expression_inadequate(
            text,
            intensity=1.0,
            target_modes=[InarticulationMode.VAGUE_EXPRESSION]
        )
        
        assert result.downgraded_text != text
        assert "模糊表达" in result.applied_modes
    
    def test_topic_shifting_mode(self):
        """测试转移话题模式"""
        text = "我内心很复杂，这种感受让我想到很多。"
        result = self.system.make_expression_inadequate(
            text,
            intensity=1.0,
            target_modes=[InarticulationMode.TOPIC_SHIFTING]
        )
        
        assert result.downgraded_text != text
        assert "转移话题" in result.applied_modes
    
    def test_intensity_effect(self):
        """测试强度对降级效果的影响"""
        text = "他心中涌起复杂的情感，精确地感受到这种强烈的变化。"
        
        # 低强度
        result_low = self.system.make_expression_inadequate(text, intensity=0.2)
        
        # 高强度
        result_high = self.system.make_expression_inadequate(text, intensity=0.9)
        
        # 高强度应该有更多修改
        assert len(result_high.applied_modes) >= len(result_low.applied_modes)
    
    def test_auto_mode_selection(self):
        """测试自动模式选择"""
        text = "他精确地感受到内心深处复杂的情感，这种强烈的感觉难以言喻。"
        result = self.system.make_expression_inadequate(text, intensity=0.7)
        
        assert len(result.applied_modes) > 0
        # 应该自动选择合适的模式
        assert any(mode in ["词汇贫乏", "模糊表达"] for mode in result.applied_modes)
    
    def test_context_requirements_check(self):
        """测试上下文要求检查"""
        # 包含情感描述的文本
        emotional_text = "我感到很愤怒"
        assert self.system._check_context_requirements(emotional_text, ["情感描述"]) is True
        
        # 不包含情感描述的文本
        neutral_text = "今天天气很好"
        assert self.system._check_context_requirements(neutral_text, ["情感描述"]) is False
    
    def test_no_downgrade_needed(self):
        """测试无需降级的情况"""
        simple_text = "他走了。"
        result = self.system.make_expression_inadequate(simple_text, intensity=0.3)
        
        # 简单文本应该无需降级
        assert result.processing_details.get("skipped", False) is True
        assert result.downgraded_text == simple_text
    
    def test_analyze_emotional_expression(self):
        """测试情感表达分析"""
        text = "他心中涌起复杂的情感，精确地感受到这种强烈的痛苦。"
        analysis = self.system.analyze_emotional_expression(text)
        
        assert "over_description_analysis" in analysis
        assert "expression_patterns" in analysis
        assert "complexity_score" in analysis
        assert "recommendation" in analysis
        assert analysis["total_triggers"] > 0


class TestFactoryFunctions:
    """🏭 工厂函数测试类"""
    
    def test_create_emotional_downgrader(self):
        """测试创建情感表达降级器"""
        downgrader = create_emotional_downgrader()
        assert isinstance(downgrader, EmotionalInarticulationSystem)
    
    def test_quick_emotional_downgrade(self):
        """测试快速情感表达降级"""
        text = "他心中涌起复杂的情感，难以言喻。"
        result = quick_emotional_downgrade(text, intensity=0.8)
        
        assert isinstance(result, str)
        assert len(result) > 0
    
    def test_quick_downgrade_with_mode(self):
        """测试指定模式的快速降级"""
        text = "他精确地感受到这种强烈的情感。"
        result = quick_emotional_downgrade(text, intensity=0.8, mode="模糊表达")
        
        assert result != text
        assert isinstance(result, str)
    
    def test_analyze_text_emotional_complexity(self):
        """测试文本情感复杂度分析"""
        text = "他心中涌起复杂的情感，精确地感受到内心深处的变化。"
        analysis = analyze_text_emotional_complexity(text)
        
        assert "complexity_score" in analysis
        assert "recommendation" in analysis
        assert isinstance(analysis["complexity_score"], (int, float))
    
    def test_detect_ai_emotional_patterns(self):
        """测试AI情感模式检测"""
        text = "他精确地感受到复杂的情感交织在一起。"
        result = detect_ai_emotional_patterns(text)
        
        assert "over_description_level" in result
        assert "needs_downgrade" in result
        assert isinstance(result["needs_downgrade"], bool)
    
    def test_batch_emotional_downgrade(self):
        """测试批量情感表达降级"""
        texts = [
            "他心中涌起复杂的情感。",
            "她精确地感受到这种痛苦。",
            "简单的句子。"
        ]
        
        results = batch_emotional_downgrade(texts, intensity=0.7)
        
        assert len(results) == len(texts)
        assert all(isinstance(r, DowngradeResult) for r in results)


class TestPresetConfigurations:
    """🎯 预设配置测试类"""
    
    def test_preset_configurations_exist(self):
        """测试预设配置存在"""
        assert len(PRESET_CONFIGURATIONS) > 0
        assert "轻度自然化" in PRESET_CONFIGURATIONS
        assert "中度人性化" in PRESET_CONFIGURATIONS
        assert "重度去AI化" in PRESET_CONFIGURATIONS
    
    def test_apply_preset_downgrade(self):
        """测试应用预设降级配置"""
        text = "他心中涌起复杂的情感，精确地感受到这种强烈的变化。"
        
        for preset_name in PRESET_CONFIGURATIONS.keys():
            result = apply_preset_downgrade(text, preset_name)
            assert isinstance(result, DowngradeResult)
            assert len(result.downgraded_text) > 0
    
    def test_invalid_preset_name(self):
        """测试无效预设名称"""
        text = "测试文本"
        
        with pytest.raises(ValueError):
            apply_preset_downgrade(text, "不存在的预设")
    
    def test_preset_intensity_differences(self):
        """测试不同预设的强度差异"""
        text = "他心中涌起复杂的情感，精确地感受到内心深处强烈的痛苦。"
        
        light_result = apply_preset_downgrade(text, "轻度自然化")
        heavy_result = apply_preset_downgrade(text, "重度去AI化")
        
        # 重度去AI化应该有更多修改
        assert len(heavy_result.applied_modes) >= len(light_result.applied_modes)


class TestEdgeCases:
    """🔍 边界情况测试类"""
    
    def setup_method(self):
        """设置测试环境"""
        self.system = EmotionalInarticulationSystem()
    
    def test_empty_text(self):
        """测试空文本"""
        result = self.system.make_expression_inadequate("", intensity=0.7)
        assert result.downgraded_text == ""
        assert len(result.applied_modes) == 0
    
    def test_very_short_text(self):
        """测试极短文本"""
        result = self.system.make_expression_inadequate("好。", intensity=0.7)
        assert len(result.downgraded_text) > 0
    
    def test_zero_intensity(self):
        """测试零强度"""
        text = "他心中涌起复杂的情感。"
        result = self.system.make_expression_inadequate(text, intensity=0.0)
        # 零强度可能不会进行任何修改
        assert isinstance(result, DowngradeResult)
    
    def test_maximum_intensity(self):
        """测试最大强度"""
        text = "他心中涌起复杂的情感，精确地感受到这种强烈的痛苦。"
        result = self.system.make_expression_inadequate(text, intensity=1.0)
        assert isinstance(result, DowngradeResult)
        assert len(result.applied_modes) > 0
