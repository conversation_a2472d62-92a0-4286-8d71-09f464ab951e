"""
🎬 [编排] AI智能体编排器
支持分步骤、用户参与的小说创作流程，整合所有现有技术模块
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from uuid import uuid4

from app.core.config import log_info, log_error, log_debug, log_success
from app.schemas.agent_orchestration import (
    NovelProject, PhaseResult, ConceptExpansion, WorldSetting, Character, 
    PlotOutline, StoryContext, CreationPhase, PhaseStatus, AIAgentRole,
    PhaseStartRequest, PhaseConfirmationRequest, UserAction
)

# 导入现有的技术模块
from app.services.zhipu_client import get_zhipu_client, ChatMessage
from app.repositories.bible_repo import StoryBibleRepository
from app.repositories.chapter_repo import ChapterRepository
from app.core.prompt_synthesizer import PromptSynthesizer
from app.core.deai_processor import create_deai_processor
from app.core.plot_recommendation import create_plot_recommendation_engine
from app.core.conflict_detection import create_conflict_detection_engine
from app.services.world_graph_client import create_world_graph_client
from app.core.emotion_physical_mapper import create_emotion_mapper
from app.core.entropy_injector import create_entropy_injector
from app.core.literary_style_engine import create_literary_style_engine, LiteraryStyleConfig


class AIAgentOrchestrator:
    """
    🎬 [编排器] AI智能体编排器
    
    小说创作流水线的总导演，负责协调所有AI智能体和技术模块，
    实现分步骤、用户参与的创作流程。
    """
    
    def __init__(
        self,
        bible_repo: StoryBibleRepository,
        chapter_repo: ChapterRepository,
        world_graph_client=None,
        vector_store=None
    ):
        """初始化AI智能体编排器"""
        # 注入现有服务
        self.bible_repo = bible_repo
        self.chapter_repo = chapter_repo
        self.world_graph_client = world_graph_client
        self.vector_store = vector_store
        
        # 初始化技术模块
        self.zhipu_client = None  # 延迟初始化
        self.prompt_synthesizer = None
        self.deai_processor = create_deai_processor()
        self.plot_engine = create_plot_recommendation_engine()
        self.conflict_detector = create_conflict_detection_engine()
        self.emotion_mapper = create_emotion_mapper()
        self.entropy_injector = create_entropy_injector()
        self.literary_style_engine = create_literary_style_engine()
        
        # 项目存储（实际应用中应该使用数据库）
        self.projects: Dict[str, NovelProject] = {}
        
        log_info("编排器", "AI智能体编排器初始化完成", 
                模块数=7, 支持阶段数=len(CreationPhase))
    
    async def _ensure_clients_initialized(self):
        """确保客户端已初始化"""
        if not self.zhipu_client:
            self.zhipu_client = await get_zhipu_client()
        
        if not self.prompt_synthesizer and self.vector_store:
            self.prompt_synthesizer = PromptSynthesizer(
                self.bible_repo,
                self.chapter_repo,
                self.vector_store,
                self.world_graph_client
            )
    
    async def create_project(
        self, 
        user_id: str,
        title: str,
        core_idea: str,
        genre: str,
        target_length: int = 100000,
        style_preferences: Optional[Dict[str, Any]] = None
    ) -> NovelProject:
        """
        🎬 [编排器] 创建新的小说项目
        
        Args:
            user_id: 用户ID
            title: 小说标题
            core_idea: 核心创意（一句话）
            genre: 小说类型
            target_length: 目标字数
            style_preferences: 风格偏好
            
        Returns:
            NovelProject: 创建的项目
        """
        project_id = str(uuid4())
        
        project = NovelProject(
            project_id=project_id,
            user_id=user_id,
            title=title,
            core_idea=core_idea,
            genre=genre,
            target_length=target_length,
            style_preferences=style_preferences or {},
            current_phase=CreationPhase.CONCEPT_EXPANSION,
            phase_history=[],
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 存储项目
        self.projects[project_id] = project
        
        log_info("编排器", "创建新的小说项目",
                项目ID=project_id,
                标题=title,
                类型=genre,
                核心创意=core_idea[:50] + "..." if len(core_idea) > 50 else core_idea)
        
        return project
    
    async def start_phase(self, request: PhaseStartRequest) -> PhaseResult:
        """
        🎬 [编排器] 启动指定的创作阶段
        
        Args:
            request: 阶段启动请求
            
        Returns:
            PhaseResult: 阶段执行结果
        """
        project_id = request.project_id
        phase = request.phase
        
        log_info("编排器", f"启动创作阶段：{phase.value}",
                项目ID=project_id,
                输入数据键数=len(request.input_data),
                用户偏好数=len(request.user_preferences))
        
        # 检查项目是否存在
        if project_id not in self.projects:
            raise ValueError(f"项目 {project_id} 不存在")
        
        project = self.projects[project_id]
        start_time = asyncio.get_event_loop().time()
        
        try:
            # 确保客户端已初始化
            await self._ensure_clients_initialized()
            
            # 根据阶段调用相应的处理方法
            if phase == CreationPhase.CONCEPT_EXPANSION:
                result_data = await self._phase_concept_expansion(
                    project, request.input_data, request.user_preferences
                )
                agent_role = AIAgentRole.CONCEPT_ARCHITECT
                
            elif phase == CreationPhase.WORLD_BUILDING:
                result_data = await self._phase_world_building(
                    project, request.input_data, request.user_preferences
                )
                agent_role = AIAgentRole.WORLD_BUILDER
                
            elif phase == CreationPhase.CHARACTER_CREATION:
                result_data = await self._phase_character_creation(
                    project, request.input_data, request.user_preferences
                )
                agent_role = AIAgentRole.CHARACTER_DESIGNER
                
            elif phase == CreationPhase.PLOT_OUTLINE:
                result_data = await self._phase_plot_outline(
                    project, request.input_data, request.user_preferences
                )
                agent_role = AIAgentRole.CHIEF_WRITER
                
            elif phase == CreationPhase.CHAPTER_PRODUCTION:
                result_data = await self._phase_chapter_production(
                    project, request.input_data, request.user_preferences
                )
                agent_role = AIAgentRole.WRITER
                
            else:
                raise ValueError(f"不支持的创作阶段：{phase}")
            
            # 计算处理时间
            processing_time = asyncio.get_event_loop().time() - start_time
            
            # 创建阶段结果
            phase_result = PhaseResult(
                project_id=project_id,
                phase=phase,
                status=PhaseStatus.COMPLETED,
                agent_role=agent_role,
                result_data=result_data,
                processing_time=processing_time,
                created_at=datetime.now(),
                completed_at=datetime.now(),
                requires_user_confirmation=True
            )
            
            # 更新项目状态
            project.phase_history.append(phase_result)
            project.updated_at = datetime.now()
            
            log_success("编排器", f"阶段 {phase.value} 执行完成",
                       项目ID=project_id,
                       处理时间=f"{processing_time:.2f}秒",
                       AI角色=agent_role.value)
            
            return phase_result
            
        except Exception as e:
            processing_time = asyncio.get_event_loop().time() - start_time
            
            # 创建失败结果
            phase_result = PhaseResult(
                project_id=project_id,
                phase=phase,
                status=PhaseStatus.FAILED,
                processing_time=processing_time,
                created_at=datetime.now(),
                error_message=str(e),
                requires_user_confirmation=False
            )
            
            # 更新项目状态
            project.phase_history.append(phase_result)
            project.updated_at = datetime.now()
            
            log_error("编排器", f"阶段 {phase.value} 执行失败",
                     项目ID=project_id,
                     error=e,
                     处理时间=f"{processing_time:.2f}秒")
            
            return phase_result
    
    async def get_phase_result(self, project_id: str, phase: CreationPhase) -> Optional[PhaseResult]:
        """
        📋 [编排器] 获取指定阶段的结果
        
        Args:
            project_id: 项目ID
            phase: 阶段名称
            
        Returns:
            Optional[PhaseResult]: 阶段结果，如果不存在则返回None
        """
        if project_id not in self.projects:
            return None
        
        project = self.projects[project_id]
        
        # 查找指定阶段的最新结果
        for result in reversed(project.phase_history):
            if result.phase == phase:
                return result
        
        return None
    
    async def confirm_phase_and_proceed(
        self, 
        request: PhaseConfirmationRequest
    ) -> bool:
        """
        ✅ [编排器] 用户确认阶段结果并处理后续操作
        
        Args:
            request: 阶段确认请求
            
        Returns:
            bool: 操作是否成功
        """
        project_id = request.project_id
        phase = request.phase
        action = request.action
        
        log_info("编排器", f"用户确认阶段：{phase.value}",
                项目ID=project_id,
                操作=action.value,
                有修改意见=bool(request.modifications))
        
        if project_id not in self.projects:
            log_error("编排器", "项目不存在", 项目ID=project_id)
            return False
        
        project = self.projects[project_id]
        
        try:
            if action == UserAction.CONFIRM:
                # 用户确认，更新项目当前阶段
                next_phase = self._get_next_phase(phase)
                if next_phase:
                    project.current_phase = next_phase
                    log_info("编排器", f"阶段确认成功，进入下一阶段：{next_phase.value}")
                else:
                    log_info("编排器", "所有阶段已完成")
                
            elif action == UserAction.MODIFY:
                # 用户要求修改，应用修改意见并重新生成
                if request.modifications:
                    await self._apply_modifications(project, phase, request.modifications)
                    log_info("编排器", "应用用户修改意见完成")
                
            elif action == UserAction.REGENERATE:
                # 用户要求重新生成，清除当前阶段结果
                await self._regenerate_phase(project, phase)
                log_info("编排器", f"重新生成阶段：{phase.value}")
                
            elif action == UserAction.ROLLBACK:
                # 用户要求回退到上一阶段
                previous_phase = self._get_previous_phase(phase)
                if previous_phase:
                    project.current_phase = previous_phase
                    log_info("编排器", f"回退到上一阶段：{previous_phase.value}")
            
            project.updated_at = datetime.now()
            return True
            
        except Exception as e:
            log_error("编排器", "确认阶段操作失败", error=e, 项目ID=project_id)
            return False
    
    def _get_next_phase(self, current_phase: CreationPhase) -> Optional[CreationPhase]:
        """获取下一个阶段"""
        phase_order = [
            CreationPhase.CONCEPT_EXPANSION,
            CreationPhase.WORLD_BUILDING,
            CreationPhase.CHARACTER_CREATION,
            CreationPhase.PLOT_OUTLINE,
            CreationPhase.CHAPTER_PRODUCTION
        ]
        
        try:
            current_index = phase_order.index(current_phase)
            if current_index < len(phase_order) - 1:
                return phase_order[current_index + 1]
        except ValueError:
            pass
        
        return None
    
    def _get_previous_phase(self, current_phase: CreationPhase) -> Optional[CreationPhase]:
        """获取上一个阶段"""
        phase_order = [
            CreationPhase.CONCEPT_EXPANSION,
            CreationPhase.WORLD_BUILDING,
            CreationPhase.CHARACTER_CREATION,
            CreationPhase.PLOT_OUTLINE,
            CreationPhase.CHAPTER_PRODUCTION
        ]
        
        try:
            current_index = phase_order.index(current_phase)
            if current_index > 0:
                return phase_order[current_index - 1]
        except ValueError:
            pass
        
        return None

    # ==================== 各阶段具体实现 ====================

    async def _phase_concept_expansion(
        self,
        project: NovelProject,
        input_data: Dict[str, Any],
        user_preferences: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        💡 [概念架构师] 创意扩展阶段

        将用户的一句话创意扩展为完整的小说概念
        """
        log_info("编排器", "执行创意扩展阶段", 项目ID=project.project_id)

        # 构建创意扩展提示词
        expansion_prompt = f"""
你是一位资深的概念架构师，擅长将简单的创意扩展为完整的小说概念。

用户的核心创意：{project.core_idea}
小说类型：{project.genre}
目标字数：{project.target_length}

请基于这个核心创意，进行深度扩展，包括：

1. **扩展概念**：将一句话创意发展为完整的故事概念
2. **核心主题**：提取3-5个核心主题
3. **类型元素**：分析该类型应包含的关键元素
4. **潜在冲突**：识别故事中可能的主要冲突点
5. **目标读者**：分析最适合的读者群体

请以JSON格式返回结果，包含以下字段：
- expanded_concept: 扩展后的完整概念
- core_themes: 核心主题列表
- genre_elements: 类型元素列表
- potential_conflicts: 潜在冲突列表
- target_audience: 目标读者描述

确保扩展后的概念具有足够的深度和吸引力，能够支撑{project.target_length}字的长篇小说。
"""

        # 调用智谱AI进行创意扩展
        messages = [ChatMessage(role="user", content=expansion_prompt)]
        response = await self.zhipu_client.chat_completion(messages)

        try:
            # 解析AI返回的JSON结果
            ai_result = json.loads(response.choices[0].message.content)

            # 创建概念扩展对象
            concept_expansion = ConceptExpansion(
                original_idea=project.core_idea,
                expanded_concept=ai_result.get("expanded_concept", ""),
                core_themes=ai_result.get("core_themes", []),
                genre_elements=ai_result.get("genre_elements", []),
                potential_conflicts=ai_result.get("potential_conflicts", []),
                target_audience=ai_result.get("target_audience", "")
            )

            # 应用去AI化处理
            deai_result = await self.deai_processor.adaptive_deai_processing(
                text=concept_expansion.expanded_concept,
                processing_mode="adaptive"
            )

            if deai_result.success:
                concept_expansion.expanded_concept = deai_result.processed_text
                log_debug("编排器", "创意扩展文本去AI化处理完成")

            log_success("编排器", "创意扩展阶段完成",
                       主题数=len(concept_expansion.core_themes),
                       冲突数=len(concept_expansion.potential_conflicts))

            return concept_expansion.dict()

        except json.JSONDecodeError as e:
            log_error("编排器", "解析AI返回结果失败", error=e)
            # 返回基础扩展结果
            return {
                "original_idea": project.core_idea,
                "expanded_concept": response.choices[0].message.content,
                "core_themes": [],
                "genre_elements": [],
                "potential_conflicts": [],
                "target_audience": "通用读者"
            }

    async def _phase_world_building(
        self,
        project: NovelProject,
        input_data: Dict[str, Any],
        user_preferences: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        🌍 [世界构建师] 世界观构建阶段

        基于扩展的概念构建详细的世界观设定
        """
        log_info("编排器", "执行世界观构建阶段", 项目ID=project.project_id)

        # 获取创意扩展结果
        concept_result = await self.get_phase_result(project.project_id, CreationPhase.CONCEPT_EXPANSION)
        if not concept_result:
            raise ValueError("需要先完成创意扩展阶段")

        concept_data = concept_result.result_data

        # 构建世界观构建提示词
        world_building_prompt = f"""
你是一位专业的世界构建师，擅长为小说创造丰富详细的世界观设定。

基础信息：
- 小说类型：{project.genre}
- 扩展概念：{concept_data.get('expanded_concept', '')}
- 核心主题：{', '.join(concept_data.get('core_themes', []))}
- 类型元素：{', '.join(concept_data.get('genre_elements', []))}

请构建详细的世界观设定，包括：

1. **世界名称**：为这个世界起一个合适的名字
2. **世界类型**：现实世界/架空世界/未来世界等
3. **背景设定**：时代背景、历史脉络
4. **力量体系**：如果适用，设计独特的力量/魔法/科技体系
5. **社会结构**：政治制度、社会阶层、组织架构
6. **关键地点**：3-5个重要的地理位置或场所
7. **世界规则**：这个世界独有的规则和限制
8. **文化元素**：风俗习惯、价值观念、艺术形式

请以JSON格式返回结果，确保世界观设定与小说类型和核心概念高度契合。
"""

        # 调用智谱AI进行世界观构建
        messages = [ChatMessage(role="user", content=world_building_prompt)]
        response = await self.zhipu_client.chat_completion(messages)

        try:
            # 解析AI返回的JSON结果
            ai_result = json.loads(response.choices[0].message.content)

            # 创建世界设定对象
            world_setting = WorldSetting(
                world_name=ai_result.get("world_name", "未命名世界"),
                world_type=ai_result.get("world_type", "架空世界"),
                background_setting=ai_result.get("background_setting", ""),
                power_system=ai_result.get("power_system"),
                social_structure=ai_result.get("social_structure", ""),
                key_locations=ai_result.get("key_locations", []),
                world_rules=ai_result.get("world_rules", []),
                cultural_elements=ai_result.get("cultural_elements", [])
            )

            # 应用去AI化处理到关键文本
            deai_result = await self.deai_processor.adaptive_deai_processing(
                text=world_setting.background_setting,
                processing_mode="adaptive"
            )

            if deai_result.success:
                world_setting.background_setting = deai_result.processed_text
                log_debug("编排器", "世界观背景设定去AI化处理完成")

            log_success("编排器", "世界观构建阶段完成",
                       世界名称=world_setting.world_name,
                       关键地点数=len(world_setting.key_locations),
                       世界规则数=len(world_setting.world_rules))

            return world_setting.dict()

        except json.JSONDecodeError as e:
            log_error("编排器", "解析世界观构建结果失败", error=e)
            # 返回基础世界设定
            return {
                "world_name": f"{project.title}的世界",
                "world_type": "架空世界",
                "background_setting": response.choices[0].message.content,
                "power_system": None,
                "social_structure": "待完善",
                "key_locations": [],
                "world_rules": [],
                "cultural_elements": []
            }

    async def _phase_character_creation(
        self,
        project: NovelProject,
        input_data: Dict[str, Any],
        user_preferences: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        👥 [角色设计师] 角色创建阶段

        基于世界观设定创建主要角色
        """
        log_info("编排器", "执行角色创建阶段", 项目ID=project.project_id)

        # 获取前面阶段的结果
        concept_result = await self.get_phase_result(project.project_id, CreationPhase.CONCEPT_EXPANSION)
        world_result = await self.get_phase_result(project.project_id, CreationPhase.WORLD_BUILDING)

        if not concept_result or not world_result:
            raise ValueError("需要先完成创意扩展和世界观构建阶段")

        concept_data = concept_result.result_data
        world_data = world_result.result_data

        # 构建角色创建提示词
        character_prompt = f"""
你是一位专业的角色设计师，擅长创造立体生动的小说角色。

基础信息：
- 小说类型：{project.genre}
- 核心概念：{concept_data.get('expanded_concept', '')}
- 世界设定：{world_data.get('world_name', '')} - {world_data.get('background_setting', '')}
- 潜在冲突：{', '.join(concept_data.get('potential_conflicts', []))}

请创建3-5个主要角色，包括：

1. **主角**：故事的核心人物
2. **重要配角**：2-3个对剧情发展至关重要的角色
3. **反派角色**：如果适用，创建主要对立角色

每个角色需要包含：
- name: 角色姓名
- role: 角色定位（主角/配角/反派等）
- age: 年龄（可选）
- personality: 性格特征列表
- background: 背景故事
- abilities: 能力特长列表
- goals: 目标动机列表
- relationships: 与其他角色的关系
- appearance: 外貌描述（可选）

请以JSON格式返回结果，包含characters字段，值为角色列表。
确保角色之间有复杂的关系网络，能够产生丰富的戏剧冲突。
"""

        # 调用智谱AI进行角色创建
        messages = [ChatMessage(role="user", content=character_prompt)]
        response = await self.zhipu_client.chat_completion(messages)

        try:
            # 解析AI返回的JSON结果
            ai_result = json.loads(response.choices[0].message.content)
            characters_data = ai_result.get("characters", [])

            # 创建角色对象列表
            characters = []
            for char_data in characters_data:
                character = Character(
                    name=char_data.get("name", "未命名角色"),
                    role=char_data.get("role", "配角"),
                    age=char_data.get("age"),
                    personality=char_data.get("personality", []),
                    background=char_data.get("background", ""),
                    abilities=char_data.get("abilities", []),
                    goals=char_data.get("goals", []),
                    relationships=char_data.get("relationships", []),
                    appearance=char_data.get("appearance")
                )

                # 对角色背景应用去AI化处理
                if character.background:
                    deai_result = await self.deai_processor.adaptive_deai_processing(
                        text=character.background,
                        processing_mode="adaptive"
                    )
                    if deai_result.success:
                        character.background = deai_result.processed_text

                characters.append(character)

            log_success("编排器", "角色创建阶段完成",
                       角色数量=len(characters),
                       主角数=[c for c in characters if "主角" in c.role])

            return {
                "characters": [char.dict() for char in characters],
                "character_count": len(characters),
                "main_characters": [char.dict() for char in characters if "主角" in char.role]
            }

        except json.JSONDecodeError as e:
            log_error("编排器", "解析角色创建结果失败", error=e)
            # 返回基础角色设定
            return {
                "characters": [{
                    "name": "主角",
                    "role": "主角",
                    "background": response.choices[0].message.content,
                    "personality": [],
                    "abilities": [],
                    "goals": [],
                    "relationships": []
                }],
                "character_count": 1,
                "main_characters": []
            }

    async def _phase_plot_outline(
        self,
        project: NovelProject,
        input_data: Dict[str, Any],
        user_preferences: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        📖 [首席编剧] 情节大纲阶段

        基于角色和世界观创建详细的情节大纲
        """
        log_info("编排器", "执行情节大纲阶段", 项目ID=project.project_id)

        # 获取前面阶段的结果
        concept_result = await self.get_phase_result(project.project_id, CreationPhase.CONCEPT_EXPANSION)
        world_result = await self.get_phase_result(project.project_id, CreationPhase.WORLD_BUILDING)
        character_result = await self.get_phase_result(project.project_id, CreationPhase.CHARACTER_CREATION)

        if not all([concept_result, world_result, character_result]):
            raise ValueError("需要先完成前面的所有阶段")

        concept_data = concept_result.result_data
        world_data = world_result.result_data
        character_data = character_result.result_data

        # 使用剧情推荐引擎辅助大纲创建
        plot_recommendations = []
        if self.plot_engine:
            try:
                # 这里需要构建一个简化的世界图谱用于推荐
                # 实际应用中应该从数据库获取
                log_debug("编排器", "尝试获取剧情推荐")
            except Exception as e:
                log_debug("编排器", "剧情推荐获取失败", error=e)

        # 构建情节大纲提示词
        plot_prompt = f"""
你是一位资深的首席编剧，擅长构建引人入胜的故事结构和情节大纲。

基础信息：
- 小说类型：{project.genre}
- 目标字数：{project.target_length}
- 核心概念：{concept_data.get('expanded_concept', '')}
- 世界设定：{world_data.get('world_name', '')}
- 主要角色：{len(character_data.get('characters', []))}个角色

请创建详细的情节大纲，包括：

1. **故事结构**：采用的叙事结构（三幕式/英雄之旅等）
2. **主线剧情**：核心故事线的发展脉络
3. **支线剧情**：2-3条重要的支线故事
4. **章节大纲**：预计{max(10, project.target_length // 10000)}章的章节规划
5. **关键事件**：推动剧情发展的重要事件
6. **高潮点**：故事中的几个情感和戏剧高潮
7. **结局设定**：故事的结局安排

每个章节大纲包含：
- chapter_number: 章节号
- chapter_title: 章节标题
- main_events: 主要事件列表
- character_arcs: 角色发展
- emotional_beats: 情感节拍
- target_word_count: 目标字数
- cliffhanger: 悬念设置（可选）

请以JSON格式返回结果，确保情节紧凑有趣，能够支撑整部小说的发展。
"""

        # 调用智谱AI进行情节大纲创建
        messages = [ChatMessage(role="user", content=plot_prompt)]
        response = await self.zhipu_client.chat_completion(messages)

        try:
            # 解析AI返回的JSON结果
            ai_result = json.loads(response.choices[0].message.content)

            # 创建情节大纲对象
            plot_outline = PlotOutline(
                story_structure=ai_result.get("story_structure", "三幕式结构"),
                main_plotline=ai_result.get("main_plotline", ""),
                subplots=ai_result.get("subplots", []),
                chapter_outlines=ai_result.get("chapter_outlines", []),
                key_events=ai_result.get("key_events", []),
                climax_points=ai_result.get("climax_points", []),
                resolution=ai_result.get("resolution", "")
            )

            # 对主线剧情应用去AI化处理
            if plot_outline.main_plotline:
                deai_result = await self.deai_processor.adaptive_deai_processing(
                    text=plot_outline.main_plotline,
                    processing_mode="adaptive"
                )
                if deai_result.success:
                    plot_outline.main_plotline = deai_result.processed_text

            log_success("编排器", "情节大纲阶段完成",
                       章节数=len(plot_outline.chapter_outlines),
                       支线数=len(plot_outline.subplots),
                       高潮点数=len(plot_outline.climax_points))

            return plot_outline.dict()

        except json.JSONDecodeError as e:
            log_error("编排器", "解析情节大纲结果失败", error=e)
            # 返回基础大纲
            return {
                "story_structure": "三幕式结构",
                "main_plotline": response.choices[0].message.content,
                "subplots": [],
                "chapter_outlines": [],
                "key_events": [],
                "climax_points": [],
                "resolution": "待完善"
            }

    async def _phase_chapter_production(
        self,
        project: NovelProject,
        input_data: Dict[str, Any],
        user_preferences: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        ✍️ [作家AI] 章节生产阶段

        基于情节大纲生成具体的章节内容
        """
        log_info("编排器", "执行章节生产阶段", 项目ID=project.project_id)

        # 获取章节号（从输入数据中获取，默认为第1章）
        chapter_number = input_data.get("chapter_number", 1)

        # 获取前面阶段的结果
        plot_result = await self.get_phase_result(project.project_id, CreationPhase.PLOT_OUTLINE)
        if not plot_result:
            raise ValueError("需要先完成情节大纲阶段")

        plot_data = plot_result.result_data
        chapter_outlines = plot_data.get("chapter_outlines", [])

        # 找到对应章节的大纲
        target_outline = None
        for outline in chapter_outlines:
            if outline.get("chapter_number") == chapter_number:
                target_outline = outline
                break

        if not target_outline:
            raise ValueError(f"未找到第{chapter_number}章的大纲")

        # 使用提示词合成器构建上下文（如果可用）
        enhanced_prompt = None
        if self.prompt_synthesizer:
            try:
                # 构建任务描述
                task_description = f"生成第{chapter_number}章：{target_outline.get('chapter_title', '')}"

                # 这里需要一个故事圣经ID，实际应用中应该从项目中获取
                # 暂时跳过提示词合成器的使用
                log_debug("编排器", "跳过提示词合成器，直接生成章节")
            except Exception as e:
                log_debug("编排器", "提示词合成器调用失败", error=e)

        # 构建章节生成提示词
        chapter_prompt = f"""
你是一位专业的小说作家，擅长创作引人入胜的章节内容。

章节信息：
- 章节号：第{chapter_number}章
- 章节标题：{target_outline.get('chapter_title', '')}
- 主要事件：{', '.join(target_outline.get('main_events', []))}
- 目标字数：{target_outline.get('target_word_count', 3000)}字
- 情感节拍：{', '.join(target_outline.get('emotional_beats', []))}

请基于以上信息，创作完整的章节内容。要求：

1. **开头引人入胜**：抓住读者注意力
2. **情节推进自然**：按照大纲推进剧情
3. **人物刻画生动**：展现角色性格和成长
4. **对话真实自然**：符合角色身份和情境
5. **描写细腻丰富**：营造沉浸感
6. **节奏把控得当**：张弛有度
7. **悬念设置巧妙**：如果有悬念要求

请直接返回章节正文内容，不需要JSON格式。
"""

        # 调用智谱AI生成章节内容
        messages = [ChatMessage(role="user", content=chapter_prompt)]
        response = await self.zhipu_client.chat_completion(messages)

        chapter_content = response.choices[0].message.content

        # 应用多层处理
        processed_content = chapter_content

        # 1. 去AI化处理
        deai_result = await self.deai_processor.adaptive_deai_processing(
            text=processed_content,
            processing_mode="adaptive"
        )
        if deai_result.success:
            processed_content = deai_result.processed_text
            log_debug("编排器", "章节内容去AI化处理完成")

        # 2. 情感物理映射（如果适用）
        try:
            emotion_result = await self.emotion_mapper.enhance_emotional_expression(
                text=processed_content,
                target_emotion="mixed"  # 混合情感
            )
            if emotion_result.get("success"):
                processed_content = emotion_result.get("enhanced_text", processed_content)
                log_debug("编排器", "章节情感增强完成")
        except Exception as e:
            log_debug("编排器", "情感增强跳过", error=e)

        # 3. 熵增扰动（轻微）
        try:
            entropy_result = await self.entropy_injector.inject_entropy(
                text=processed_content,
                disruption_level="light"
            )
            if entropy_result.get("success"):
                processed_content = entropy_result.get("disrupted_text", processed_content)
                log_debug("编排器", "章节熵增扰动完成")
        except Exception as e:
            log_debug("编排器", "熵增扰动跳过", error=e)

        log_success("编排器", f"第{chapter_number}章生产完成",
                   原始字数=len(chapter_content),
                   处理后字数=len(processed_content),
                   章节标题=target_outline.get('chapter_title', ''))

        return {
            "chapter_number": chapter_number,
            "chapter_title": target_outline.get('chapter_title', ''),
            "content": processed_content,
            "word_count": len(processed_content),
            "target_word_count": target_outline.get('target_word_count', 3000),
            "main_events": target_outline.get('main_events', []),
            "processing_applied": ["deai", "emotion_mapping", "entropy_injection"]
        }

    # ==================== 辅助方法 ====================

    async def _apply_modifications(
        self,
        project: NovelProject,
        phase: CreationPhase,
        modifications: Dict[str, Any]
    ):
        """
        🔧 [编排器] 应用用户修改意见

        Args:
            project: 项目对象
            phase: 要修改的阶段
            modifications: 修改意见
        """
        log_info("编排器", f"应用用户修改意见：{phase.value}",
                项目ID=project.project_id,
                修改项数=len(modifications))

        # 获取当前阶段结果
        phase_result = await self.get_phase_result(project.project_id, phase)
        if not phase_result:
            log_error("编排器", "未找到阶段结果", 阶段=phase.value)
            return

        # 应用修改到结果数据
        for key, value in modifications.items():
            if key in phase_result.result_data:
                phase_result.result_data[key] = value
                log_debug("编排器", f"修改字段：{key}")

        # 更新时间戳
        phase_result.completed_at = datetime.now()
        project.updated_at = datetime.now()

        log_success("编排器", "用户修改意见应用完成")

    async def _regenerate_phase(self, project: NovelProject, phase: CreationPhase):
        """
        🔄 [编排器] 重新生成指定阶段

        Args:
            project: 项目对象
            phase: 要重新生成的阶段
        """
        log_info("编排器", f"重新生成阶段：{phase.value}", 项目ID=project.project_id)

        # 移除当前阶段的结果
        project.phase_history = [
            result for result in project.phase_history
            if result.phase != phase
        ]

        # 重置项目当前阶段
        project.current_phase = phase
        project.updated_at = datetime.now()

        log_success("编排器", f"阶段 {phase.value} 已重置，可重新生成")


# ==================== 工厂函数 ====================

def create_agent_orchestrator(
    bible_repo: StoryBibleRepository,
    chapter_repo: ChapterRepository,
    world_graph_client=None,
    vector_store=None
) -> AIAgentOrchestrator:
    """
    🏭 [工厂] 创建AI智能体编排器实例

    Args:
        bible_repo: 故事圣经仓库
        chapter_repo: 章节仓库
        world_graph_client: 世界图谱客户端（可选）
        vector_store: 向量存储（可选）

    Returns:
        AIAgentOrchestrator: 编排器实例
    """
    return AIAgentOrchestrator(
        bible_repo=bible_repo,
        chapter_repo=chapter_repo,
        world_graph_client=world_graph_client,
        vector_store=vector_store
    )
