"""
🎯 [注意力失焦] 注意力失焦系统模块

模拟人类在不同情绪状态下的选择性注意力，
让AI生成的场景描述具备真实的主观性和不完整性，
打破AI过度精细和全知视角的问题。

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import re
import random
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass
from enum import Enum

from app.core.config import log_info, log_debug, log_error


class EmotionalState(Enum):
    """情绪状态枚举"""
    ANGER = "愤怒"
    SADNESS = "悲伤"
    ANXIETY = "焦虑"
    JOY = "喜悦"
    FEAR = "恐惧"
    DISGUST = "厌恶"
    SURPRISE = "惊讶"
    NEUTRAL = "中性"


@dataclass
class AttentionPattern:
    """注意力模式数据类"""
    emotion_state: EmotionalState
    focus_keywords: List[str]  # 关注的关键词
    ignore_keywords: List[str]  # 忽略的关键词
    distortion_rules: Dict[str, str]  # 扭曲规则
    intensity_modifier: float  # 强度修饰符


@dataclass
class FilterResult:
    """过滤结果数据类"""
    filtered_text: str
    removed_elements: List[str]
    emphasized_elements: List[str]
    attention_focus: str
    processing_details: Dict[str, Any]


class AttentionDefocusSystem:
    """🧠 注意力失焦系统 - 模拟人类情绪下的选择性注意力"""
    
    def __init__(self):
        """初始化注意力失焦系统"""
        self.attention_patterns = self._initialize_attention_patterns()
        self.scene_elements = self._initialize_scene_elements()
        log_debug("注意力失焦", "注意力失焦系统初始化完成", 
                 情绪模式数=len(self.attention_patterns))
    
    def _initialize_attention_patterns(self) -> Dict[EmotionalState, AttentionPattern]:
        """初始化各种情绪状态下的注意力模式"""
        
        patterns = {
            EmotionalState.ANGER: AttentionPattern(
                emotion_state=EmotionalState.ANGER,
                focus_keywords=[
                    "阻碍", "障碍", "不公", "不公平", "刺激", "触发", "威胁",
                    "对抗", "冲突", "敌意", "挑衅", "侮辱", "背叛", "欺骗",
                    "红色", "尖锐", "刺耳", "刺眼", "突兀", "刺激性"
                ],
                ignore_keywords=[
                    "美景", "温柔", "和谐", "平静", "舒适", "优雅", "精致",
                    "细腻", "温暖", "柔和", "美好", "愉悦", "轻松", "悠闲",
                    "花香", "鸟鸣", "微风", "阳光", "温馨", "甜蜜"
                ],
                distortion_rules={
                    "放大威胁": "将中性描述转换为威胁性描述",
                    "简化复杂": "忽略复杂的细节，只关注核心冲突",
                    "主观化": "将客观描述转为主观感受"
                },
                intensity_modifier=1.2
            ),
            
            EmotionalState.SADNESS: AttentionPattern(
                emotion_state=EmotionalState.SADNESS,
                focus_keywords=[
                    "失去", "空虚", "孤独", "寂静", "黑暗", "阴沉", "灰色",
                    "过去", "回忆", "曾经", "不再", "消失", "离开", "告别",
                    "眼泪", "哭泣", "叹息", "沉默", "冷清", "荒凉", "破败"
                ],
                ignore_keywords=[
                    "希望", "机会", "未来", "可能", "新的", "开始", "明亮",
                    "活力", "生机", "热闹", "欢声", "笑语", "庆祝", "成功",
                    "彩色", "鲜艳", "生动", "活跃", "兴奋", "激动"
                ],
                distortion_rules={
                    "灰色滤镜": "所有颜色都变得暗淡",
                    "时间停滞": "时间感变得缓慢或停滞",
                    "空间收缩": "注意力集中在狭小空间"
                },
                intensity_modifier=0.8
            ),
            
            EmotionalState.ANXIETY: AttentionPattern(
                emotion_state=EmotionalState.ANXIETY,
                focus_keywords=[
                    "危险", "风险", "不确定", "未知", "可能", "如果", "万一",
                    "急促", "紧迫", "来不及", "时间", "deadline", "压力",
                    "心跳", "呼吸", "出汗", "颤抖", "紧张", "不安", "担心"
                ],
                ignore_keywords=[
                    "安全", "确定", "稳定", "放松", "平静", "从容", "淡定",
                    "慢慢", "悠闲", "不急", "充足", "足够", "保证", "肯定"
                ],
                distortion_rules={
                    "放大风险": "将小风险描述为大威胁",
                    "时间紧迫": "所有时间描述都带有紧迫感",
                    "细节放大": "过度关注可能出错的细节"
                },
                intensity_modifier=1.3
            ),
            
            EmotionalState.JOY: AttentionPattern(
                emotion_state=EmotionalState.JOY,
                focus_keywords=[
                    "明亮", "光芒", "闪耀", "美好", "完美", "精彩", "棒",
                    "成功", "胜利", "实现", "达成", "获得", "收获", "成就",
                    "笑声", "欢乐", "庆祝", "分享", "温暖", "甜蜜", "幸福"
                ],
                ignore_keywords=[
                    "问题", "困难", "障碍", "风险", "危险", "失败", "错误",
                    "黑暗", "阴沉", "沉重", "压抑", "痛苦", "悲伤", "担心"
                ],
                distortion_rules={
                    "美化滤镜": "所有事物都变得更美好",
                    "忽略负面": "自动忽略负面信息",
                    "放大正面": "夸大正面细节"
                },
                intensity_modifier=1.1
            )
        }
        
        log_debug("注意力失焦", "注意力模式初始化完成", 模式数量=len(patterns))
        return patterns
    
    def _initialize_scene_elements(self) -> Dict[str, List[str]]:
        """初始化场景元素分类"""
        
        elements = {
            "环境描述": [
                "阳光", "微风", "空气", "温度", "湿度", "气味", "声音",
                "光线", "阴影", "色彩", "质感", "材质", "形状", "大小"
            ],
            "人物外貌": [
                "面容", "表情", "眼神", "发型", "身材", "姿态", "服装",
                "配饰", "妆容", "气质", "神态", "动作", "手势", "步态"
            ],
            "情感表达": [
                "微笑", "皱眉", "叹息", "哭泣", "愤怒", "惊讶", "恐惧",
                "喜悦", "悲伤", "焦虑", "平静", "兴奋", "失望", "满足"
            ],
            "物品细节": [
                "材质", "颜色", "形状", "大小", "重量", "温度", "质感",
                "新旧", "完整", "破损", "清洁", "污渍", "光泽", "纹理"
            ],
            "时间感知": [
                "瞬间", "片刻", "一会儿", "很久", "永远", "刚才", "现在",
                "将来", "过去", "未来", "当时", "那时", "此时", "彼时"
            ]
        }
        
        log_debug("注意力失焦", "场景元素分类初始化完成", 分类数=len(elements))
        return elements
    
    def detect_emotional_state(self, text: str) -> EmotionalState:
        """
        🔍 从文本中检测情绪状态
        
        Args:
            text: 输入文本
            
        Returns:
            EmotionalState: 检测到的情绪状态
        """
        log_debug("注意力失焦", "开始检测情绪状态", 文本长度=len(text))
        
        text_lower = text.lower()
        emotion_scores = {}
        
        # 为每种情绪计算匹配分数
        for emotion_state, pattern in self.attention_patterns.items():
            score = 0
            
            # 计算关注关键词的匹配分数
            for keyword in pattern.focus_keywords:
                if keyword in text_lower:
                    score += 2  # 关注关键词权重更高
            
            # 计算忽略关键词的反向分数
            for keyword in pattern.ignore_keywords:
                if keyword in text_lower:
                    score -= 1  # 忽略关键词降低分数
            
            emotion_scores[emotion_state] = score
        
        # 找到得分最高的情绪
        if emotion_scores:
            detected_emotion = max(emotion_scores, key=emotion_scores.get)
            max_score = emotion_scores[detected_emotion]
            
            # 如果最高分数太低，返回中性情绪
            if max_score <= 0:
                detected_emotion = EmotionalState.NEUTRAL
        else:
            detected_emotion = EmotionalState.NEUTRAL
        
        log_info("注意力失焦", "情绪状态检测完成", 
                检测结果=detected_emotion.value, 
                得分=emotion_scores.get(detected_emotion, 0))
        
        return detected_emotion
    
    def apply_emotional_filter(
        self, 
        text: str, 
        emotion_state: Optional[EmotionalState] = None,
        intensity: float = 0.7
    ) -> FilterResult:
        """
        🎭 根据情绪状态过滤场景描述
        
        Args:
            text: 输入文本
            emotion_state: 指定的情绪状态，如果为None则自动检测
            intensity: 过滤强度 (0.0-1.0)
            
        Returns:
            FilterResult: 过滤结果
        """
        log_debug("注意力失焦", "开始应用情感过滤", 
                 文本长度=len(text), 情绪状态=emotion_state, 强度=intensity)
        
        # 如果没有指定情绪状态，自动检测
        if emotion_state is None:
            emotion_state = self.detect_emotional_state(text)
        
        # 如果是中性情绪，直接返回原文
        if emotion_state == EmotionalState.NEUTRAL:
            return FilterResult(
                filtered_text=text,
                removed_elements=[],
                emphasized_elements=[],
                attention_focus="中性",
                processing_details={"emotion_state": "中性", "modifications": 0}
            )
        
        # 获取对应的注意力模式
        pattern = self.attention_patterns.get(emotion_state)
        if not pattern:
            log_error("注意力失焦", "未找到对应的注意力模式", 情绪状态=emotion_state)
            return FilterResult(
                filtered_text=text,
                removed_elements=[],
                emphasized_elements=[],
                attention_focus="未知",
                processing_details={"error": "未找到注意力模式"}
            )
        
        # 应用过滤逻辑
        filtered_text, removed_elements, emphasized_elements = self._apply_attention_pattern(
            text, pattern, intensity
        )
        
        result = FilterResult(
            filtered_text=filtered_text,
            removed_elements=removed_elements,
            emphasized_elements=emphasized_elements,
            attention_focus=emotion_state.value,
            processing_details={
                "emotion_state": emotion_state.value,
                "intensity": intensity,
                "removed_count": len(removed_elements),
                "emphasized_count": len(emphasized_elements)
            }
        )
        
        log_info("注意力失焦", "情感过滤完成", 
                情绪状态=emotion_state.value,
                移除元素数=len(removed_elements),
                强调元素数=len(emphasized_elements))
        
        return result

    def _apply_attention_pattern(
        self,
        text: str,
        pattern: AttentionPattern,
        intensity: float
    ) -> Tuple[str, List[str], List[str]]:
        """
        🎯 应用注意力模式进行文本过滤

        Args:
            text: 输入文本
            pattern: 注意力模式
            intensity: 过滤强度

        Returns:
            Tuple[str, List[str], List[str]]: 过滤后文本、移除元素、强调元素
        """
        log_debug("注意力失焦", "开始应用注意力模式",
                 情绪=pattern.emotion_state.value, 强度=intensity)

        sentences = re.split(r'[。！？]', text)
        filtered_sentences = []
        removed_elements = []
        emphasized_elements = []

        for sentence in sentences:
            if not sentence.strip():
                continue

            # 检查句子是否包含需要忽略的关键词
            should_remove = self._should_remove_sentence(sentence, pattern, intensity)

            if should_remove:
                removed_elements.append(sentence.strip())
                log_debug("注意力失焦", "移除句子", 句子=sentence[:20])
                continue

            # 检查句子是否包含需要强调的关键词
            should_emphasize = self._should_emphasize_sentence(sentence, pattern)

            if should_emphasize:
                # 强调处理
                emphasized_sentence = self._emphasize_sentence(sentence, pattern)
                emphasized_elements.append(sentence.strip())
                filtered_sentences.append(emphasized_sentence)
                log_debug("注意力失焦", "强调句子", 原句=sentence[:20])
            else:
                # 普通处理
                processed_sentence = self._process_neutral_sentence(sentence, pattern, intensity)
                filtered_sentences.append(processed_sentence)

        filtered_text = "。".join([s for s in filtered_sentences if s.strip()])
        if filtered_text and not filtered_text.endswith(('。', '！', '？')):
            filtered_text += "。"

        log_info("注意力失焦", "注意力模式应用完成",
                移除句子数=len(removed_elements),
                强调句子数=len(emphasized_elements))

        return filtered_text, removed_elements, emphasized_elements

    def _should_remove_sentence(self, sentence: str, pattern: AttentionPattern, intensity: float) -> bool:
        """判断句子是否应该被移除"""
        sentence_lower = sentence.lower()

        # 检查是否包含忽略关键词
        ignore_count = 0
        for keyword in pattern.ignore_keywords:
            if keyword in sentence_lower:
                ignore_count += 1

        # 检查是否包含关注关键词
        focus_count = 0
        for keyword in pattern.focus_keywords:
            if keyword in sentence_lower:
                focus_count += 1

        # 如果包含关注关键词，不移除
        if focus_count > 0:
            return False

        # 根据忽略关键词数量和强度决定是否移除
        if ignore_count > 0:
            removal_probability = min(0.8, ignore_count * intensity * 0.3)
            return random.random() < removal_probability

        # 对于中性句子，根据情绪状态和强度决定是否移除
        if pattern.emotion_state in [EmotionalState.ANGER, EmotionalState.ANXIETY]:
            # 愤怒和焦虑状态下更容易忽略无关细节
            return random.random() < intensity * 0.4
        elif pattern.emotion_state == EmotionalState.SADNESS:
            # 悲伤状态下容易忽略积极内容
            return random.random() < intensity * 0.3

        return False

    def _should_emphasize_sentence(self, sentence: str, pattern: AttentionPattern) -> bool:
        """判断句子是否应该被强调"""
        sentence_lower = sentence.lower()

        # 检查是否包含关注关键词
        for keyword in pattern.focus_keywords:
            if keyword in sentence_lower:
                return True

        return False

    def _emphasize_sentence(self, sentence: str, pattern: AttentionPattern) -> str:
        """强调句子内容"""
        emphasized = sentence.strip()

        # 根据情绪状态应用不同的强调策略
        if pattern.emotion_state == EmotionalState.ANGER:
            # 愤怒状态：使用更强烈的词汇
            emphasized = self._apply_anger_emphasis(emphasized)
        elif pattern.emotion_state == EmotionalState.SADNESS:
            # 悲伤状态：添加灰色滤镜
            emphasized = self._apply_sadness_emphasis(emphasized)
        elif pattern.emotion_state == EmotionalState.ANXIETY:
            # 焦虑状态：放大威胁感
            emphasized = self._apply_anxiety_emphasis(emphasized)
        elif pattern.emotion_state == EmotionalState.JOY:
            # 喜悦状态：美化描述
            emphasized = self._apply_joy_emphasis(emphasized)

        return emphasized

    def _apply_anger_emphasis(self, sentence: str) -> str:
        """应用愤怒状态的强调"""
        # 替换中性词汇为更强烈的表达
        anger_replacements = {
            "看到": "瞪着",
            "听到": "听见刺耳的",
            "感觉": "强烈感受到",
            "有点": "非常",
            "一些": "这些该死的",
            "那个": "那个可恶的",
            "声音": "噪音"
        }

        result = sentence
        for original, replacement in anger_replacements.items():
            if original in result:
                result = result.replace(original, replacement, 1)
                break

        return result

    def _apply_sadness_emphasis(self, sentence: str) -> str:
        """应用悲伤状态的强调"""
        # 添加灰色滤镜效果
        sadness_replacements = {
            "明亮": "暗淡",
            "鲜艳": "褪色",
            "温暖": "冰冷",
            "热闹": "冷清",
            "笑声": "寂静",
            "快乐": "空虚",
            "希望": "绝望"
        }

        result = sentence
        for original, replacement in sadness_replacements.items():
            if original in result:
                result = result.replace(original, replacement, 1)
                break

        return result

    def _apply_anxiety_emphasis(self, sentence: str) -> str:
        """应用焦虑状态的强调"""
        # 放大威胁和紧迫感
        anxiety_replacements = {
            "可能": "很可能",
            "也许": "恐怕",
            "一会儿": "马上",
            "慢慢": "急急忙忙",
            "安静": "诡异的安静",
            "正常": "看似正常",
            "没事": "不对劲"
        }

        result = sentence
        for original, replacement in anxiety_replacements.items():
            if original in result:
                result = result.replace(original, replacement, 1)
                break

        return result

    def _apply_joy_emphasis(self, sentence: str) -> str:
        """应用喜悦状态的强调"""
        # 美化和放大正面感受
        joy_replacements = {
            "不错": "很棒",
            "还行": "非常好",
            "普通": "特别",
            "一般": "出色",
            "看起来": "显得格外",
            "感觉": "深深感受到",
            "有点": "特别"
        }

        result = sentence
        for original, replacement in joy_replacements.items():
            if original in result:
                result = result.replace(original, replacement, 1)
                break

        return result

    def _process_neutral_sentence(self, sentence: str, pattern: AttentionPattern, intensity: float) -> str:
        """处理中性句子"""
        # 对中性句子进行轻微的情绪化处理
        processed = sentence.strip()

        # 根据情绪状态轻微调整表达
        if random.random() < intensity * 0.2:  # 20%的概率进行调整
            if pattern.emotion_state == EmotionalState.ANGER:
                processed = self._add_anger_tint(processed)
            elif pattern.emotion_state == EmotionalState.SADNESS:
                processed = self._add_sadness_tint(processed)
            elif pattern.emotion_state == EmotionalState.ANXIETY:
                processed = self._add_anxiety_tint(processed)

        return processed

    def _add_anger_tint(self, sentence: str) -> str:
        """为句子添加愤怒色彩"""
        if "，" in sentence:
            parts = sentence.split("，", 1)
            return f"{parts[0]}，{parts[1]}"
        return sentence

    def _add_sadness_tint(self, sentence: str) -> str:
        """为句子添加悲伤色彩"""
        if "。" in sentence:
            return sentence.replace("。", "，显得有些落寞。", 1)
        return sentence

    def _add_anxiety_tint(self, sentence: str) -> str:
        """为句子添加焦虑色彩"""
        if "，" in sentence:
            parts = sentence.split("，", 1)
            return f"{parts[0]}，让人有些不安，{parts[1]}"
        return sentence

    def extract_subjective_details(
        self,
        text: str,
        emotion_state: Optional[EmotionalState] = None,
        max_details: int = 3
    ) -> List[str]:
        """
        🔍 提取符合当前情绪的主观细节

        Args:
            text: 输入文本
            emotion_state: 情绪状态
            max_details: 最大细节数量

        Returns:
            List[str]: 提取的主观细节列表
        """
        log_debug("注意力失焦", "开始提取主观细节",
                 文本长度=len(text), 情绪状态=emotion_state)

        if emotion_state is None:
            emotion_state = self.detect_emotional_state(text)

        if emotion_state == EmotionalState.NEUTRAL:
            return []

        pattern = self.attention_patterns.get(emotion_state)
        if not pattern:
            return []

        # 分析文本，提取相关细节
        sentences = re.split(r'[。！？]', text)
        relevant_details = []

        for sentence in sentences:
            if not sentence.strip():
                continue

            sentence_lower = sentence.lower()

            # 检查是否包含关注关键词
            for keyword in pattern.focus_keywords:
                if keyword in sentence_lower:
                    # 提取包含关键词的细节
                    detail = self._extract_detail_from_sentence(sentence, keyword, emotion_state)
                    if detail:
                        relevant_details.append(detail)
                    break

        # 限制数量并返回
        result = relevant_details[:max_details]

        log_info("注意力失焦", "主观细节提取完成",
                提取数量=len(result), 情绪状态=emotion_state.value)

        return result

    def _extract_detail_from_sentence(self, sentence: str, keyword: str, emotion_state: EmotionalState) -> Optional[str]:
        """从句子中提取特定的细节"""
        sentence = sentence.strip()

        # 确保关键词在句子中
        if keyword not in sentence.lower():
            return None

        # 根据情绪状态提取不同类型的细节
        if emotion_state == EmotionalState.ANGER:
            # 愤怒状态：提取刺激性细节
            if any(word in sentence for word in ["红", "尖", "刺", "突", "响"]):
                return f"注意到{sentence}"
        elif emotion_state == EmotionalState.SADNESS:
            # 悲伤状态：提取失落相关细节
            if any(word in sentence for word in ["空", "暗", "冷", "静", "旧"]):
                return f"感受到{sentence}"
        elif emotion_state == EmotionalState.ANXIETY:
            # 焦虑状态：提取威胁性细节
            if any(word in sentence for word in ["快", "急", "险", "不", "难"]):
                return f"担心{sentence}"

        # 如果包含关键词但不符合特定模式，返回通用描述
        return f"关注到{sentence}"

    def get_attention_summary(self, emotion_state: EmotionalState) -> Dict[str, Any]:
        """
        📊 获取指定情绪状态的注意力模式摘要

        Args:
            emotion_state: 情绪状态

        Returns:
            Dict[str, Any]: 注意力模式摘要
        """
        pattern = self.attention_patterns.get(emotion_state)
        if not pattern:
            return {}

        return {
            "emotion_state": emotion_state.value,
            "focus_keywords_count": len(pattern.focus_keywords),
            "ignore_keywords_count": len(pattern.ignore_keywords),
            "distortion_rules": list(pattern.distortion_rules.keys()),
            "intensity_modifier": pattern.intensity_modifier,
            "sample_focus_keywords": pattern.focus_keywords[:5],
            "sample_ignore_keywords": pattern.ignore_keywords[:5]
        }


# 工厂函数和便捷接口

def create_attention_defocus_system() -> AttentionDefocusSystem:
    """
    🏭 创建注意力失焦系统实例

    Returns:
        AttentionDefocusSystem: 注意力失焦系统实例
    """
    log_debug("注意力失焦", "创建注意力失焦系统实例")
    return AttentionDefocusSystem()


def quick_emotional_filter(
    text: str,
    emotion_state: Optional[str] = None,
    intensity: float = 0.7
) -> str:
    """
    ⚡ 快速情感过滤接口

    Args:
        text: 输入文本
        emotion_state: 情绪状态字符串
        intensity: 过滤强度

    Returns:
        str: 过滤后的文本
    """
    log_debug("注意力失焦", "快速情感过滤",
             文本长度=len(text), 情绪=emotion_state)

    system = create_attention_defocus_system()

    # 转换情绪状态
    emotion_enum = None
    if emotion_state:
        for state in EmotionalState:
            if state.value == emotion_state:
                emotion_enum = state
                break

    # 应用过滤
    result = system.apply_emotional_filter(text, emotion_enum, intensity)

    log_info("注意力失焦", "快速情感过滤完成",
            原文长度=len(text),
            过滤后长度=len(result.filtered_text),
            情绪状态=result.attention_focus)

    return result.filtered_text


def analyze_attention_patterns(text: str) -> Dict[str, Any]:
    """
    📈 分析文本的注意力模式

    Args:
        text: 输入文本

    Returns:
        Dict[str, Any]: 分析结果
    """
    log_debug("注意力失焦", "分析注意力模式", 文本长度=len(text))

    system = create_attention_defocus_system()

    # 检测情绪状态
    detected_emotion = system.detect_emotional_state(text)

    # 获取注意力摘要
    attention_summary = system.get_attention_summary(detected_emotion)

    # 提取主观细节
    subjective_details = system.extract_subjective_details(text, detected_emotion)

    result = {
        "detected_emotion": detected_emotion.value,
        "attention_pattern": attention_summary,
        "subjective_details": subjective_details,
        "text_length": len(text),
        "analysis_timestamp": "2025-08-04"
    }

    log_info("注意力失焦", "注意力模式分析完成",
            检测情绪=detected_emotion.value,
            主观细节数=len(subjective_details))

    return result
