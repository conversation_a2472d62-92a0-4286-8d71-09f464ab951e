"""
🎬 [API] AI智能体编排系统路由
支持分步骤、用户参与的小说创作流程API
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import log_info, log_error, log_success
from app.core.database import get_db, get_bible_repository, get_chapter_repository
from app.core.agent_orchestrator import create_agent_orchestrator
from app.services.world_graph_client import create_world_graph_client
from app.services.vector_store import get_vector_store
from app.schemas.agent_orchestration import (
    NovelProjectCreateRequest, NovelProject, NovelProjectResponse,
    PhaseStartRequest, PhaseStartResponse, PhaseResult, PhaseResultResponse,
    PhaseConfirmationRequest, ConfirmationResponse,
    CreationPhase, UserAction
)

# 创建路由器
router = APIRouter(
    prefix="/api/v1/orchestration",
    tags=["AI智能体编排"],
    responses={404: {"description": "Not found"}}
)


@router.post("/projects", response_model=NovelProjectResponse)
async def create_novel_project(
    request: NovelProjectCreateRequest,
    db: AsyncSession = Depends(get_db)
) -> NovelProjectResponse:
    """
    🎬 [编排] 创建新的小说项目
    
    Args:
        request: 项目创建请求
        db: 数据库会话
        
    Returns:
        NovelProjectResponse: 创建的项目信息
    """
    log_info("编排API", "创建新的小说项目",
            标题=request.title,
            类型=request.genre,
            目标字数=request.target_length)
    
    try:
        # 创建依赖服务
        bible_repo = get_bible_repository(db)
        chapter_repo = get_chapter_repository(db)
        world_graph_client = create_world_graph_client(db)
        vector_store = await get_vector_store()
        
        # 创建编排器
        orchestrator = create_agent_orchestrator(
            bible_repo=bible_repo,
            chapter_repo=chapter_repo,
            world_graph_client=world_graph_client,
            vector_store=vector_store
        )
        
        # 创建项目（这里使用固定用户ID，实际应用中应该从认证中获取）
        user_id = "default_user"  # TODO: 从认证系统获取真实用户ID
        
        project = await orchestrator.create_project(
            user_id=user_id,
            title=request.title,
            core_idea=request.core_idea,
            genre=request.genre,
            target_length=request.target_length,
            style_preferences=request.style_preferences
        )
        
        log_success("编排API", "小说项目创建成功",
                   项目ID=project.project_id,
                   当前阶段=project.current_phase.value)
        
        return NovelProjectResponse(
            project=project,
            current_phase_result=None,
            available_actions=[UserAction.CONFIRM]
        )
        
    except Exception as e:
        log_error("编排API", "创建小说项目失败", error=e)
        raise HTTPException(status_code=500, detail=f"创建项目失败: {str(e)}")


@router.post("/projects/{project_id}/phases/start", response_model=PhaseStartResponse)
async def start_creation_phase(
    project_id: str,
    request: PhaseStartRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
) -> PhaseStartResponse:
    """
    🎬 [编排] 启动指定的创作阶段
    
    Args:
        project_id: 项目ID
        request: 阶段启动请求
        background_tasks: 后台任务
        db: 数据库会话
        
    Returns:
        PhaseStartResponse: 阶段启动响应
    """
    log_info("编排API", f"启动创作阶段：{request.phase.value}",
            项目ID=project_id,
            输入数据键数=len(request.input_data))
    
    try:
        # 验证项目ID匹配
        if request.project_id != project_id:
            raise HTTPException(status_code=400, detail="项目ID不匹配")
        
        # 创建依赖服务
        bible_repo = get_bible_repository(db)
        chapter_repo = get_chapter_repository(db)
        world_graph_client = create_world_graph_client(db)
        vector_store = await get_vector_store()
        
        # 创建编排器
        orchestrator = create_agent_orchestrator(
            bible_repo=bible_repo,
            chapter_repo=chapter_repo,
            world_graph_client=world_graph_client,
            vector_store=vector_store
        )
        
        # 启动阶段（异步执行）
        def execute_phase():
            """后台执行阶段任务"""
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(orchestrator.start_phase(request))
                log_success("编排API", f"阶段 {request.phase.value} 后台执行完成",
                           项目ID=project_id)
            except Exception as e:
                log_error("编排API", f"阶段 {request.phase.value} 后台执行失败",
                         项目ID=project_id, error=e)
            finally:
                loop.close()
        
        # 添加到后台任务
        background_tasks.add_task(execute_phase)
        
        # 估算完成时间（分钟）
        estimated_times = {
            CreationPhase.CONCEPT_EXPANSION: 2,
            CreationPhase.WORLD_BUILDING: 3,
            CreationPhase.CHARACTER_CREATION: 4,
            CreationPhase.PLOT_OUTLINE: 5,
            CreationPhase.CHAPTER_PRODUCTION: 3
        }
        
        estimated_time = estimated_times.get(request.phase, 3)
        
        log_success("编排API", f"阶段 {request.phase.value} 启动成功",
                   项目ID=project_id,
                   预计完成时间=f"{estimated_time}分钟")
        
        return PhaseStartResponse(
            success=True,
            project_id=project_id,
            phase=request.phase,
            estimated_time=estimated_time,
            message=f"阶段 {request.phase.value} 已启动，预计 {estimated_time} 分钟完成"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("编排API", f"启动阶段 {request.phase.value} 失败", error=e)
        raise HTTPException(status_code=500, detail=f"启动阶段失败: {str(e)}")


@router.get("/projects/{project_id}/phases/{phase}/result", response_model=PhaseResultResponse)
async def get_phase_result(
    project_id: str,
    phase: CreationPhase,
    db: AsyncSession = Depends(get_db)
) -> PhaseResultResponse:
    """
    📋 [编排] 获取指定阶段的执行结果
    
    Args:
        project_id: 项目ID
        phase: 阶段名称
        db: 数据库会话
        
    Returns:
        PhaseResultResponse: 阶段结果响应
    """
    log_info("编排API", f"获取阶段结果：{phase.value}", 项目ID=project_id)
    
    try:
        # 创建依赖服务
        bible_repo = get_bible_repository(db)
        chapter_repo = get_chapter_repository(db)
        world_graph_client = create_world_graph_client(db)
        vector_store = await get_vector_store()
        
        # 创建编排器
        orchestrator = create_agent_orchestrator(
            bible_repo=bible_repo,
            chapter_repo=chapter_repo,
            world_graph_client=world_graph_client,
            vector_store=vector_store
        )
        
        # 获取阶段结果
        phase_result = await orchestrator.get_phase_result(project_id, phase)
        
        if not phase_result:
            raise HTTPException(
                status_code=404, 
                detail=f"未找到项目 {project_id} 的阶段 {phase.value} 结果"
            )
        
        # 确定下一个阶段
        next_phase = orchestrator._get_next_phase(phase)
        can_proceed = (
            phase_result.status.value == "completed" and 
            next_phase is not None
        )
        
        log_success("编排API", f"阶段结果获取成功：{phase.value}",
                   项目ID=project_id,
                   状态=phase_result.status.value,
                   下一阶段=next_phase.value if next_phase else "无")
        
        return PhaseResultResponse(
            phase_result=phase_result,
            next_phase=next_phase,
            can_proceed=can_proceed
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("编排API", f"获取阶段结果失败：{phase.value}", error=e)
        raise HTTPException(status_code=500, detail=f"获取阶段结果失败: {str(e)}")


@router.post("/projects/{project_id}/phases/{phase}/confirm", response_model=ConfirmationResponse)
async def confirm_phase_result(
    project_id: str,
    phase: CreationPhase,
    request: PhaseConfirmationRequest,
    db: AsyncSession = Depends(get_db)
) -> ConfirmationResponse:
    """
    ✅ [编排] 用户确认阶段结果并处理后续操作
    
    Args:
        project_id: 项目ID
        phase: 阶段名称
        request: 确认请求
        db: 数据库会话
        
    Returns:
        ConfirmationResponse: 确认响应
    """
    log_info("编排API", f"用户确认阶段：{phase.value}",
            项目ID=project_id,
            操作=request.action.value)
    
    try:
        # 验证请求参数
        if request.project_id != project_id or request.phase != phase:
            raise HTTPException(status_code=400, detail="请求参数不匹配")
        
        # 创建依赖服务
        bible_repo = get_bible_repository(db)
        chapter_repo = get_chapter_repository(db)
        world_graph_client = create_world_graph_client(db)
        vector_store = await get_vector_store()
        
        # 创建编排器
        orchestrator = create_agent_orchestrator(
            bible_repo=bible_repo,
            chapter_repo=chapter_repo,
            world_graph_client=world_graph_client,
            vector_store=vector_store
        )
        
        # 执行确认操作
        success = await orchestrator.confirm_phase_and_proceed(request)
        
        if not success:
            raise HTTPException(status_code=500, detail="确认操作执行失败")
        
        # 获取更新后的项目状态
        project = orchestrator.projects.get(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 确定下一阶段
        next_phase = None
        if request.action == UserAction.CONFIRM:
            next_phase = orchestrator._get_next_phase(phase)
        
        message_map = {
            UserAction.CONFIRM: f"阶段 {phase.value} 确认成功",
            UserAction.MODIFY: f"阶段 {phase.value} 修改完成",
            UserAction.REGENERATE: f"阶段 {phase.value} 已重置，可重新生成",
            UserAction.ROLLBACK: f"已回退到上一阶段"
        }
        
        log_success("编排API", f"阶段确认操作完成：{request.action.value}",
                   项目ID=project_id,
                   当前阶段=project.current_phase.value,
                   下一阶段=next_phase.value if next_phase else "无")
        
        return ConfirmationResponse(
            success=True,
            project_id=project_id,
            action_taken=request.action,
            current_phase=project.current_phase,
            next_phase=next_phase,
            message=message_map.get(request.action, "操作完成")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("编排API", f"确认阶段操作失败：{phase.value}", error=e)
        raise HTTPException(status_code=500, detail=f"确认操作失败: {str(e)}")


@router.get("/projects/{project_id}", response_model=NovelProjectResponse)
async def get_project_status(
    project_id: str,
    db: AsyncSession = Depends(get_db)
) -> NovelProjectResponse:
    """
    📊 [编排] 获取项目状态和进度
    
    Args:
        project_id: 项目ID
        db: 数据库会话
        
    Returns:
        NovelProjectResponse: 项目状态响应
    """
    log_info("编排API", "获取项目状态", 项目ID=project_id)
    
    try:
        # 创建依赖服务
        bible_repo = get_bible_repository(db)
        chapter_repo = get_chapter_repository(db)
        world_graph_client = create_world_graph_client(db)
        vector_store = await get_vector_store()
        
        # 创建编排器
        orchestrator = create_agent_orchestrator(
            bible_repo=bible_repo,
            chapter_repo=chapter_repo,
            world_graph_client=world_graph_client,
            vector_store=vector_store
        )
        
        # 获取项目
        project = orchestrator.projects.get(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 获取当前阶段结果
        current_phase_result = None
        if project.phase_history:
            current_phase_result = project.phase_history[-1]
        
        # 确定可用操作
        available_actions = [UserAction.CONFIRM]
        if current_phase_result and current_phase_result.status.value == "completed":
            available_actions.extend([
                UserAction.MODIFY,
                UserAction.REGENERATE,
                UserAction.ROLLBACK
            ])
        
        log_success("编排API", "项目状态获取成功",
                   项目ID=project_id,
                   当前阶段=project.current_phase.value,
                   历史阶段数=len(project.phase_history))
        
        return NovelProjectResponse(
            project=project,
            current_phase_result=current_phase_result,
            available_actions=available_actions
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("编排API", "获取项目状态失败", error=e)
        raise HTTPException(status_code=500, detail=f"获取项目状态失败: {str(e)}")
