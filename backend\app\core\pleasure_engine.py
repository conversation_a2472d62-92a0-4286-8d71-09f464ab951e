"""
🎯 [爽感] 网文爽感机制引擎
基于指导文档中的"爽感"概念，实现情感峰谷系统，为网络小说注入读者愉悦感
"""

import random
import math
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from app.core.config import log_info, log_debug, log_error, log_success
from app.core.pleasure_corpus_manager import create_pleasure_corpus_manager, PleasureCorpusManager


class PleasureType(str, Enum):
    """爽感类型枚举"""
    POWER_FANTASY = "power_fantasy"      # 力量幻想
    REVENGE = "revenge"                  # 复仇爽感
    FACE_SLAPPING = "face_slapping"      # 打脸爽感
    UPGRADE = "upgrade"                  # 升级爽感
    RECOGNITION = "recognition"          # 认可爽感
    WEALTH = "wealth"                    # 财富爽感
    ROMANCE = "romance"                  # 情感爽感
    MYSTERY_SOLVING = "mystery"          # 解谜爽感


class EmotionalIntensity(str, Enum):
    """情感强度枚举"""
    LOW = "低"           # 1-3分
    MEDIUM = "中"        # 4-6分
    HIGH = "高"          # 7-8分
    EXTREME = "极高"     # 9-10分


@dataclass
class PleasurePeak:
    """爽感峰值点"""
    position: int                    # 在文本中的位置
    pleasure_type: PleasureType      # 爽感类型
    intensity: EmotionalIntensity    # 强度等级
    trigger_event: str               # 触发事件
    buildup_length: int              # 铺垫长度
    payoff_description: str          # 爽点描述
    reader_satisfaction: float       # 读者满足度预测 (0.0-1.0)


@dataclass
class EmotionalValley:
    """情感低谷点"""
    position: int                    # 在文本中的位置
    valley_type: str                 # 低谷类型（挫折、误解、危机等）
    intensity: EmotionalIntensity    # 强度等级
    purpose: str                     # 设置目的
    recovery_potential: float        # 恢复潜力 (0.0-1.0)


@dataclass
class PleasureEngineResult:
    """爽感引擎结果"""
    success: bool
    original_text: str
    pleasure_enhanced_text: str
    pleasure_peaks: List[PleasurePeak]
    emotional_valleys: List[EmotionalValley]
    overall_pleasure_score: float
    rhythm_analysis: Dict[str, Any]
    enhancement_details: Dict[str, Any]


class PleasureEngine:
    """
    🎯 [爽感引擎] 网文爽感机制引擎
    
    基于网络小说的"爽感"理论，通过情感峰谷设计、
    节奏控制、读者期待管理等手段，提升阅读愉悦感。
    """
    
    def __init__(self, corpus_root: Optional[str] = None):
        """
        初始化爽感引擎

        Args:
            corpus_root: 语料库根目录路径
        """
        log_debug("🎯爽感", "初始化网文爽感引擎", 语料库根目录=corpus_root)

        # 初始化语料库管理器
        self.corpus_manager = create_pleasure_corpus_manager(corpus_root)

        # 爽感模式库（保留作为后备数据）
        self.pleasure_patterns = {
            PleasureType.POWER_FANTASY: {
                "触发词": ["实力", "力量", "强大", "碾压", "秒杀"],
                "铺垫模式": ["被轻视", "被嘲笑", "被质疑", "看似弱小"],
                "爽点模式": ["一招制敌", "实力暴露", "震惊全场", "逆转局面"],
                "强化词汇": ["恐怖如斯", "不可思议", "震撼", "颤栗"]
            },
            
            PleasureType.FACE_SLAPPING: {
                "触发词": ["嘲笑", "轻视", "看不起", "废物", "垃圾"],
                "铺垫模式": ["被羞辱", "被嘲讽", "被看轻", "众人嘲笑"],
                "爽点模式": ["打脸", "啪啪啪", "脸疼", "后悔莫及"],
                "强化词汇": ["狠狠打脸", "脸都肿了", "无地自容", "悔不当初"]
            },
            
            PleasureType.UPGRADE: {
                "触发词": ["突破", "升级", "进阶", "提升", "晋级"],
                "铺垫模式": ["瓶颈", "困难", "停滞", "苦修"],
                "爽点模式": ["突破成功", "实力大增", "脱胎换骨", "一飞冲天"],
                "强化词汇": ["天翻地覆", "判若两人", "实力暴涨", "质的飞跃"]
            },
            
            PleasureType.REVENGE: {
                "触发词": ["仇恨", "报仇", "复仇", "血债", "清算"],
                "铺垫模式": ["被害", "被欺", "家破人亡", "深仇大恨"],
                "爽点模式": ["报仇雪恨", "血债血偿", "以牙还牙", "大仇得报"],
                "强化词汇": ["痛快淋漓", "大快人心", "恶有恶报", "因果循环"]
            },
            
            PleasureType.RECOGNITION: {
                "触发词": ["认可", "赞赏", "佩服", "敬佩", "崇拜"],
                "铺垫模式": ["被忽视", "默默无闻", "不被理解", "孤独前行"],
                "爽点模式": ["众人敬佩", "万众瞩目", "名声大噪", "备受推崇"],
                "强化词汇": ["众星捧月", "万人敬仰", "声名远扬", "德高望重"]
            }
        }
        
        # 情感节奏模板
        self.rhythm_templates = {
            "经典三段式": {
                "结构": ["铺垫(30%)", "冲突(40%)", "爽点(30%)"],
                "情感曲线": [3, 2, 1, 8, 9, 10],
                "适用类型": [PleasureType.FACE_SLAPPING, PleasureType.POWER_FANTASY]
            },
            
            "渐进式爽感": {
                "结构": ["起始(20%)", "积累(50%)", "爆发(30%)"],
                "情感曲线": [4, 5, 6, 7, 8, 10],
                "适用类型": [PleasureType.UPGRADE, PleasureType.RECOGNITION]
            },
            
            "波浪式节奏": {
                "结构": ["小高潮(25%)", "低谷(25%)", "大高潮(50%)"],
                "情感曲线": [6, 7, 3, 2, 9, 10],
                "适用类型": [PleasureType.REVENGE, PleasureType.MYSTERY_SOLVING]
            }
        }
        
        # 强化表达库
        self.enhancement_expressions = {
            "震惊反应": [
                "所有人都震惊了", "全场寂静", "鸦雀无声", "目瞪口呆",
                "不敢置信", "简直不敢相信自己的眼睛", "这怎么可能"
            ],
            
            "情感爆发": [
                "热血沸腾", "激动不已", "心潮澎湃", "血脉贲张",
                "兴奋得无法自抑", "爽得不行", "痛快至极"
            ],
            
            "对比反差": [
                "前后判若两人", "天壤之别", "云泥之差", "截然不同",
                "简直是两个人", "完全变了一个人", "判若云泥"
            ],
            
            "威势描述": [
                "气势如虹", "威风凛凛", "霸气侧漏", "王者风范",
                "不怒自威", "气场全开", "威压四方"
            ]
        }
        
        log_info("爽感", "网文爽感引擎初始化完成",
                爽感模式数=len(self.pleasure_patterns),
                节奏模板数=len(self.rhythm_templates),
                强化表达数=sum(len(v) for v in self.enhancement_expressions.values()))
    
    async def enhance_pleasure(
        self,
        text: str,
        target_pleasure_types: List[PleasureType],
        intensity_level: EmotionalIntensity = EmotionalIntensity.MEDIUM,
        rhythm_template: Optional[str] = None
    ) -> PleasureEngineResult:
        """
        🎯 [爽感增强] 为文本增强爽感体验
        
        Args:
            text: 原始文本
            target_pleasure_types: 目标爽感类型列表
            intensity_level: 强度等级
            rhythm_template: 节奏模板（可选）
            
        Returns:
            PleasureEngineResult: 爽感增强结果
        """
        log_info("爽感", "开始爽感增强处理",
                文本长度=len(text),
                目标爽感数=len(target_pleasure_types),
                强度等级=intensity_level.value)
        
        try:
            enhanced_text = text
            pleasure_peaks = []
            emotional_valleys = []
            
            # 1. 分析现有文本的情感节奏
            rhythm_analysis = await self._analyze_emotional_rhythm(text)
            
            # 2. 识别潜在的爽点位置
            potential_peaks = await self._identify_potential_peaks(
                text, target_pleasure_types
            )
            
            # 3. 设计情感低谷来增强对比
            valleys = await self._design_emotional_valleys(
                text, potential_peaks, intensity_level
            )
            emotional_valleys.extend(valleys)
            
            # 4. 增强爽点表达
            for peak_info in potential_peaks:
                enhanced_peak = await self._enhance_pleasure_peak(
                    enhanced_text, peak_info, intensity_level
                )
                if enhanced_peak:
                    enhanced_text = enhanced_peak["enhanced_text"]
                    pleasure_peaks.append(enhanced_peak["peak"])
            
            # 5. 应用节奏模板
            if rhythm_template and rhythm_template in self.rhythm_templates:
                enhanced_text = await self._apply_rhythm_template(
                    enhanced_text, rhythm_template, pleasure_peaks
                )
            
            # 6. 计算整体爽感评分
            overall_score = await self._calculate_pleasure_score(
                pleasure_peaks, emotional_valleys, len(text)
            )
            
            log_success("爽感", "爽感增强处理完成",
                       爽点数量=len(pleasure_peaks),
                       低谷数量=len(emotional_valleys),
                       整体评分=f"{overall_score:.2f}")
            
            return PleasureEngineResult(
                success=len(pleasure_peaks) > 0,
                original_text=text,
                pleasure_enhanced_text=enhanced_text,
                pleasure_peaks=pleasure_peaks,
                emotional_valleys=emotional_valleys,
                overall_pleasure_score=overall_score,
                rhythm_analysis=rhythm_analysis,
                enhancement_details={
                    "target_types": [pt.value for pt in target_pleasure_types],
                    "intensity": intensity_level.value,
                    "peaks_enhanced": len(pleasure_peaks),
                    "valleys_created": len(emotional_valleys)
                }
            )
            
        except Exception as e:
            log_error("爽感", "爽感增强处理失败", error=e)
            return PleasureEngineResult(
                success=False,
                original_text=text,
                pleasure_enhanced_text=text,
                pleasure_peaks=[],
                emotional_valleys=[],
                overall_pleasure_score=0.0,
                rhythm_analysis={},
                enhancement_details={"error": str(e)}
            )

    async def _analyze_emotional_rhythm(self, text: str) -> Dict[str, Any]:
        """分析文本的情感节奏"""
        sentences = text.split('。')
        emotional_scores = []

        # 简单的情感评分（实际应用中可以使用更复杂的NLP模型）
        positive_words = ["好", "棒", "强", "赢", "成功", "开心", "兴奋"]
        negative_words = ["坏", "差", "弱", "输", "失败", "难过", "愤怒"]

        for sentence in sentences:
            if not sentence.strip():
                continue

            score = 5  # 中性基准分
            for word in positive_words:
                score += sentence.count(word) * 2
            for word in negative_words:
                score -= sentence.count(word) * 2

            emotional_scores.append(max(1, min(10, score)))

        # 计算节奏特征
        if emotional_scores:
            avg_score = sum(emotional_scores) / len(emotional_scores)
            variance = sum((s - avg_score) ** 2 for s in emotional_scores) / len(emotional_scores)
            rhythm_intensity = math.sqrt(variance)
        else:
            avg_score = 5.0
            variance = 0.0
            rhythm_intensity = 0.0

        return {
            "sentence_count": len(sentences),
            "emotional_scores": emotional_scores,
            "average_emotion": avg_score,
            "emotional_variance": variance,
            "rhythm_intensity": rhythm_intensity,
            "peak_count": sum(1 for s in emotional_scores if s >= 8),
            "valley_count": sum(1 for s in emotional_scores if s <= 3)
        }

    async def _identify_potential_peaks(
        self,
        text: str,
        target_types: List[PleasureType]
    ) -> List[Dict[str, Any]]:
        """识别潜在的爽点位置"""
        potential_peaks = []

        for pleasure_type in target_types:
            if pleasure_type not in self.pleasure_patterns:
                continue

            pattern = self.pleasure_patterns[pleasure_type]

            # 寻找触发词
            for trigger_word in pattern["触发词"]:
                position = text.find(trigger_word)
                if position != -1:
                    potential_peaks.append({
                        "position": position,
                        "pleasure_type": pleasure_type,
                        "trigger_word": trigger_word,
                        "context": text[max(0, position-50):position+50],
                        "pattern": pattern
                    })

        return potential_peaks

    async def _design_emotional_valleys(
        self,
        text: str,
        potential_peaks: List[Dict[str, Any]],
        intensity: EmotionalIntensity
    ) -> List[EmotionalValley]:
        """设计情感低谷来增强对比效果"""
        valleys = []

        # 在每个爽点前设计一个低谷
        for peak_info in potential_peaks:
            valley_position = max(0, peak_info["position"] - 100)

            valley_types = ["挫折", "误解", "危机", "嘲笑", "质疑"]
            valley_type = random.choice(valley_types)

            valley = EmotionalValley(
                position=valley_position,
                valley_type=valley_type,
                intensity=intensity,
                purpose=f"为{peak_info['pleasure_type'].value}爽点做铺垫",
                recovery_potential=0.8
            )
            valleys.append(valley)

        return valleys

    async def _enhance_pleasure_peak(
        self,
        text: str,
        peak_info: Dict[str, Any],
        intensity: EmotionalIntensity
    ) -> Optional[Dict[str, Any]]:
        """
        🎯 [爽点增强] 增强单个爽点的表达，优先使用语料库数据
        """
        pleasure_type = peak_info["pleasure_type"]
        position = peak_info["position"]
        pattern = peak_info["pattern"]

        log_debug("🎯爽感", "开始增强爽点表达",
                 爽感类型=pleasure_type.value, 位置=position, 强度=intensity.value)

        # 1. 尝试从语料库获取爽感模式数据
        corpus_patterns = await self.corpus_manager.get_pleasure_patterns_by_type(
            pleasure_type.value, count=3, min_quality=0.6
        )

        enhanced_text = text
        trigger_word = peak_info["trigger_word"]

        # 2. 使用语料库数据进行增强
        if corpus_patterns:
            log_debug("🎯爽感", "使用语料库数据增强爽点", 模式数量=len(corpus_patterns))

            # 选择最适合的模式
            selected_pattern = corpus_patterns[0]  # 取质量最高的

            # 从模式数据中提取强化表达
            enhancement_expressions = []
            if 'data' in selected_pattern.data:
                for key, value_list in selected_pattern.data['data'].items():
                    if isinstance(value_list, list) and '表达' in key or '描述' in key:
                        enhancement_expressions.extend(value_list[:2])  # 取前2个

            if enhancement_expressions:
                enhancement = random.choice(enhancement_expressions)
                enhanced_phrase = f"{trigger_word}，{enhancement}"
                enhanced_text = enhanced_text.replace(trigger_word, enhanced_phrase, 1)
        else:
            # 3. 语料库数据不可用时，使用后备数据
            log_debug("🎯爽感", "语料库数据不可用，使用后备数据")
            enhancement_words = pattern.get("强化词汇", [])
            if enhancement_words:
                enhancement = random.choice(enhancement_words)
                enhanced_phrase = f"{trigger_word}，{enhancement}"
                enhanced_text = enhanced_text.replace(trigger_word, enhanced_phrase, 1)
            else:
                return None

        # 4. 添加震惊反应（优先使用语料库）
        if intensity in [EmotionalIntensity.HIGH, EmotionalIntensity.EXTREME]:
            shock_expressions = await self.corpus_manager.get_enhancement_expressions(
                "震惊反应", count=3
            )

            if shock_expressions:
                shock_reaction = random.choice(shock_expressions)
            else:
                # 后备数据
                shock_reaction = random.choice(self.enhancement_expressions.get("震惊反应", ["震惊不已"]))

            enhanced_text = enhanced_text.replace(
                trigger_word,
                f"{trigger_word}！{shock_reaction}！",
                1
            )

        # 创建爽点对象
        intensity_score = {
            EmotionalIntensity.LOW: 0.3,
            EmotionalIntensity.MEDIUM: 0.6,
            EmotionalIntensity.HIGH: 0.8,
            EmotionalIntensity.EXTREME: 1.0
        }

        peak = PleasurePeak(
            position=position,
            pleasure_type=pleasure_type,
            intensity=intensity,
            trigger_event=trigger_word,
            buildup_length=100,  # 假设铺垫长度
            payoff_description=f"{pleasure_type.value}类型的爽点",
            reader_satisfaction=intensity_score[intensity]
        )

        return {
            "enhanced_text": enhanced_text,
            "peak": peak
        }

    async def _apply_rhythm_template(
        self,
        text: str,
        template_name: str,
        peaks: List[PleasurePeak]
    ) -> str:
        """应用节奏模板"""
        if template_name not in self.rhythm_templates:
            return text

        template = self.rhythm_templates[template_name]
        enhanced_text = text

        # 根据模板调整节奏（这里是简化实现）
        if template_name == "经典三段式":
            # 在文本的关键位置添加节奏控制
            sentences = enhanced_text.split('。')
            if len(sentences) >= 3:
                # 在1/3处添加铺垫强化
                third_point = len(sentences) // 3
                sentences[third_point] += "，事情开始变得有趣起来"

                # 在2/3处添加冲突强化
                two_third_point = len(sentences) * 2 // 3
                sentences[two_third_point] += "，关键时刻到了"

                enhanced_text = '。'.join(sentences)

        return enhanced_text

    async def _calculate_pleasure_score(
        self,
        peaks: List[PleasurePeak],
        valleys: List[EmotionalValley],
        text_length: int
    ) -> float:
        """计算整体爽感评分"""
        if not peaks:
            return 0.0

        # 基础分数：爽点数量和强度
        base_score = 0.0
        for peak in peaks:
            intensity_multiplier = {
                EmotionalIntensity.LOW: 1.0,
                EmotionalIntensity.MEDIUM: 2.0,
                EmotionalIntensity.HIGH: 3.0,
                EmotionalIntensity.EXTREME: 4.0
            }
            base_score += intensity_multiplier[peak.intensity] * peak.reader_satisfaction

        # 节奏加分：峰谷对比
        rhythm_bonus = min(len(valleys) * 0.5, 2.0)

        # 密度调整：避免过于密集
        density = len(peaks) / (text_length / 1000)  # 每千字的爽点数
        if density > 3:  # 过于密集，减分
            density_penalty = (density - 3) * 0.5
        else:
            density_penalty = 0

        final_score = base_score + rhythm_bonus - density_penalty
        return max(0.0, min(10.0, final_score))


# ==================== 工厂函数 ====================

def create_pleasure_engine(corpus_root: Optional[str] = None) -> PleasureEngine:
    """
    🏭 [工厂] 创建爽感引擎实例

    Args:
        corpus_root: 语料库根目录路径

    Returns:
        PleasureEngine: 爽感引擎实例
    """
    return PleasureEngine(corpus_root)


async def quick_pleasure_enhancement(
    text: str,
    pleasure_types: List[PleasureType],
    intensity: EmotionalIntensity = EmotionalIntensity.MEDIUM,
    corpus_root: Optional[str] = None
) -> PleasureEngineResult:
    """
    ⚡ [快速接口] 快速爽感增强

    Args:
        text: 原始文本
        pleasure_types: 爽感类型列表
        intensity: 强度等级
        corpus_root: 语料库根目录路径

    Returns:
        PleasureEngineResult: 增强结果
    """
    engine = create_pleasure_engine(corpus_root)
    return await engine.enhance_pleasure(text, pleasure_types, intensity)
