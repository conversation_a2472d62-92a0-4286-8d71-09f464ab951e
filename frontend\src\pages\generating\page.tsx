// 故事生成中页面组件 - 支持Markdown编辑和流式显示
import { useEffect } from 'react';
import { LoadingSpinner, BookIcon } from '../../components/BasicComponents';
import { useTheme } from '../../components/ThemeProvider';
import { useGenerating } from '../../hooks/useGenerating';
import { useStoryStore } from '../../stores';
import { debugLog } from '../../config/env';

export const GeneratingPage: React.FC = () => {
  const { isDark } = useTheme();
  const { userPrompt, selectedAIProvider } = useStoryStore();
  
  const {
    progress,
    currentText,
    status,
    isCompleted,
    error,
    isGenerating,
    currentTaskId,
    taskStatus,
    isMonitoring,
    isPanelVisible,
    setIsPanelVisible,
    handleComplete,
    handleCancel,
    scrollToTop,
    scrollToBottom,
    scrollUp,
    scrollDown
  } = useGenerating();
  
  // 页面位置日志
  useEffect(() => {
    debugLog('GeneratingPage', '当前位置: AI生成页面');
    console.log('🌙 [GeneratingPage] 夜间模式状态:', isDark);
    console.log('🌙 [GeneratingPage] useTheme hook 结果:', { isDark });
  }, [isDark]);

  // 获取AI提供商显示名称
  function getProviderDisplayName(provider: string | null | undefined): string {
    const names: Record<string, string> = {
      'zhipu': '智谱AI',
      'kimi': 'Kimi',
      'openai': 'OpenAI',
      'claude': 'Claude'
    };
    
    if (!provider) {
      return '智能选择';
    }
    
    return names[provider] || provider;
  }

  // 从内容中提取标题
  function extractTitleFromContent(content: string): string {
    const titleMatch = content.match(/^#\s+(.+)$/m);
    if (titleMatch) {
      return titleMatch[1].replace(/[《》""]/g, '').trim();
    }
    return '未命名故事';
  }

  return (
    <div className="w-full max-w-6xl mx-auto relative">
      {/* 固定的滚动控制面板 */}
      <div 
        className="fixed right-0 top-1/2 transform -translate-y-1/2 z-50"
      >
        {/* 滚动控制面板 - 可滑入滑出 */}
        <div className={`transition-transform duration-300 ease-in-out ${
          isPanelVisible ? 'translate-x-0' : 'translate-x-full'
        }`}>
          <div className={`backdrop-blur-md rounded-l-2xl shadow-2xl border-l border-t border-b p-4 space-y-3 mr-0 ${isDark ? 'bg-gray-800/95 border-gray-600/50' : 'bg-white/95 border-gray-200/50'}`}>
            {/* 面板标题 */}
            <div className="text-center">
              <div className="w-8 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mx-auto mb-2"></div>
              <span className={`text-xs font-medium ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>滚动控制</span>
            </div>

            {/* 滚动按钮组 */}
            <div className="flex flex-col space-y-2">
              {/* 滚动到顶部按钮 */}
              <button
                onClick={scrollToTop}
                className="relative w-11 h-11 bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group overflow-hidden"
                title="滚动到顶部"
              >
                <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <svg className="w-4 h-4 group-hover:scale-110 transition-transform relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M7 11l5-5m0 0l5 5m-5-5v12" />
                </svg>
              </button>

              {/* 向上滚动按钮 */}
              <button
                onClick={scrollUp}
                className="relative w-11 h-11 bg-gradient-to-br from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group overflow-hidden"
                title="向上滚动"
              >
                <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <svg className="w-4 h-4 group-hover:scale-110 transition-transform relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M8 15l4-4 4 4" />
                </svg>
              </button>

              {/* 向下滚动按钮 */}
              <button
                onClick={scrollDown}
                className="relative w-11 h-11 bg-gradient-to-br from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group overflow-hidden"
                title="向下滚动"
              >
                <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <svg className="w-4 h-4 group-hover:scale-110 transition-transform relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M16 9l-4 4-4-4" />
                </svg>
              </button>

              {/* 滚动到底部按钮 */}
              <button
                onClick={scrollToBottom}
                className="relative w-11 h-11 bg-gradient-to-br from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group overflow-hidden"
                title="滚动到底部"
              >
                <div className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <svg className="w-4 h-4 group-hover:scale-110 transition-transform relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M17 13l-5 5m0 0l-5-5m5 5V6" />
                </svg>
              </button>
            </div>

            {/* 装饰性底部线条 */}
            <div className="w-8 h-0.5 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full mx-auto"></div>
            
            {/* 关闭按钮 */}
            <button
              onClick={() => setIsPanelVisible(false)}
              className={`w-full py-2 text-xs transition-colors flex items-center justify-center space-x-1 ${isDark ? 'text-gray-500 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`}
              title="隐藏面板"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span>隐藏</span>
            </button>
          </div>
        </div>

        {/* 提示按钮 - 面板隐藏时显示 */}
        <div className={`absolute right-0 top-0 transition-all duration-300 ${
          isPanelVisible ? 'opacity-0 translate-x-4 pointer-events-none' : 'opacity-100 translate-x-0'
        }`}>
          <button
            onClick={() => setIsPanelVisible(true)}
            className={`group rounded-l-xl shadow-lg hover:shadow-xl transition-all duration-300 p-3 flex items-center space-x-2 ${isDark ? 'bg-gradient-to-br from-blue-600 to-purple-700 hover:from-blue-700 hover:to-purple-800' : 'bg-gradient-to-br from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700'} text-white`}
            title="显示滚动控制面板"
          >
            <div className="flex flex-col items-center space-y-1">
              <svg className="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M8 9l4-4 4 4M8 15l4 4 4-4" />
              </svg>
              <div className="w-0.5 h-6 bg-white/60 rounded-full"></div>
              <svg className="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M15 19l-7-7 7-7" />
              </svg>
            </div>
            <div className="text-xs font-medium">
              <div>滚动</div>
              <div>控制</div>
            </div>
          </button>
        </div>
      </div>

      {/* 页面标题 */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-4">
          <BookIcon />
          <h2 className={`text-2xl font-bold ${
            isDark ? 'text-gray-100' : 'text-gray-800'
          }`}>AI 正在创作您的故事圣经</h2>
        </div>
        <p className={`${
          isDark ? 'text-gray-300' : 'text-gray-600'
        }`}>使用 {getProviderDisplayName(selectedAIProvider)}，基于您的创意："{userPrompt}"</p>
      </div>

      {/* 进度条 */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className={`text-sm font-medium ${
            error ? 'text-red-600' : 'text-indigo-600'
          }`}>{status}</span>
          <div className="flex items-center space-x-2">
            {/* 任务状态指示器 */}
            {currentTaskId && (
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${
                  taskStatus === 'completed' ? 'bg-green-500' :
                  taskStatus === 'failed' ? 'bg-red-500' :
                  taskStatus === 'generating' ? 'bg-blue-500 animate-pulse' :
                  'bg-yellow-500'
                }`}></div>
                <span className={`text-xs ${
                  isDark ? 'text-gray-400' : 'text-gray-500'
                }`}>
                  任务: {currentTaskId.substring(0, 8)}...
                </span>
              </div>
            )}
            <span className={`text-sm ${
              isDark ? 'text-gray-400' : 'text-gray-500'
            }`}>{progress}%</span>
          </div>
        </div>
        <div className={`w-full rounded-full h-2 ${
          isDark ? 'bg-gray-700' : 'bg-gray-200'
        }`}>
          <div
            className={`h-2 rounded-full transition-all duration-500 ease-out ${
              error ? 'bg-red-500' :
              taskStatus === 'completed' ? 'bg-green-500' :
              taskStatus === 'failed' ? 'bg-red-500' :
              'bg-indigo-600'
            }`}
            style={{ width: `${progress}%` }}
          ></div>
        </div>

        {/* 任务监控状态 */}
        {isMonitoring && currentTaskId && (
          <div className={`mt-2 text-xs ${
            isDark ? 'text-gray-400' : 'text-gray-500'
          }`}>
            <div className="flex items-center space-x-2">
              <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse"></div>
              <span>正在监控任务状态...</span>
            </div>
          </div>
        )}
      </div>

      {/* 错误提示 */}
      {error && (
        <div className={`mb-6 border rounded-lg p-4 ${
          isDark 
            ? 'bg-red-900/20 border-red-800' 
            : 'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-center">
            <span className="text-red-600 text-lg mr-2">❌</span>
            <div>
              <h4 className={`font-semibold ${
                isDark ? 'text-red-400' : 'text-red-800'
              }`}>生成失败</h4>
              <p className={`text-sm mt-1 ${
                isDark ? 'text-red-300' : 'text-red-700'
              }`}>{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* 流式内容显示区域 */}
      <div className={`rounded-xl border shadow-sm ${
        isDark 
          ? 'bg-gray-800 border-gray-700' 
          : 'bg-white border-gray-200'
      }`}>
        <div className={`border-b px-4 py-3 ${
          isDark ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`text-lg font-semibold ${
                isDark ? 'text-gray-100' : 'text-gray-800'
              }`}>故事圣经内容</h3>
              <p className={`text-sm ${
                isDark ? 'text-gray-400' : 'text-gray-500'
              }`}>AI正在为您生成的故事世界设定</p>
            </div>
            {/* 生成状态指示器 */}
            {currentText && (
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full transition-colors ${
                  isGenerating ? 'bg-green-500' : 'bg-yellow-500'
                }`}></div>
                <span className={`text-xs ${
                  isDark ? 'text-gray-400' : 'text-gray-500'
                }`}>
                  {isGenerating ? '正在生成' : '生成完成'}
                </span>
              </div>
            )}
          </div>
        </div>
        <div className="p-6">
          <div className="min-h-96 max-w-none">
            {currentText ? (
              <div 
                className="markdown-content"
                dangerouslySetInnerHTML={{ 
                  __html: convertMarkdownToHtml(currentText, isDark) 
                }}
              />
            ) : (
              <div className={`flex flex-col items-center justify-center h-64 ${
                isDark ? 'text-gray-500' : 'text-gray-400'
              }`}>
                <LoadingSpinner />
                <p className="mt-4 text-sm">AI正在思考您的故事创意...</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 底部操作按钮 */}
      <div className="mt-8 flex justify-center space-x-4">
        <button
          onClick={handleCancel}
          className={`px-6 py-2 border rounded-lg transition-colors ${
            isDark 
              ? 'border-gray-600 text-gray-300 hover:bg-gray-700' 
              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
        >
          取消生成
        </button>
        
        {isCompleted && currentText && (
          <button
            onClick={() => handleComplete({
              id: Date.now(),
              title: extractTitleFromContent(currentText),
              concept: userPrompt,
              content: currentText,
              model: 'glm-4.5',
              usage: { total_tokens: Math.floor(currentText.length / 4) },
              created_at: new Date().toISOString()
            })}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            确认并使用此内容
          </button>
        )}
      </div>
    </div>
  );
};

// 修复后的Markdown到HTML转换函数
function convertMarkdownToHtml(markdown: string, isDark: boolean = false): string {
  if (!markdown) return '';
  
  // 根据主题获取文字颜色类
  const getTextColorClass = (level: 'h1' | 'h2' | 'h3' | 'h4' | 'p' | 'strong' | 'em' | 'li') => {
    const colorMap = {
      h1: isDark ? 'text-gray-100' : 'text-gray-900',
      h2: isDark ? 'text-gray-100' : 'text-gray-900', 
      h3: isDark ? 'text-gray-200' : 'text-gray-800',
      h4: isDark ? 'text-gray-200' : 'text-gray-800',
      p: isDark ? 'text-gray-300' : 'text-gray-700',
      strong: isDark ? 'text-gray-100' : 'text-gray-900',
      em: isDark ? 'text-gray-200' : 'text-gray-700',
      li: isDark ? 'text-gray-300' : 'text-gray-700'
    };
    return colorMap[level];
  };
  
  // 先转义HTML特殊字符，避免XSS
  let html = markdown
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;');
  
  // 使用split和map方法安全地处理每一行，避免正则表达式冲突
  const lines = html.split('\n');
  const processedLines = lines.map(line => {
    const trimmedLine = line.trim();
    
    // 处理标题（从4级到1级）
    if (trimmedLine.startsWith('#### ')) {
      const titleText = trimmedLine.slice(4).trim();
      return `<h4 class="text-base font-semibold mt-4 mb-2 ${getTextColorClass('h4')}">${titleText}</h4>`;
    }
    if (trimmedLine.startsWith('### ')) {
      const titleText = trimmedLine.slice(3).trim();
      return `<h3 class="text-lg font-semibold mt-4 mb-2 ${getTextColorClass('h3')}">${titleText}</h3>`;
    }
    if (trimmedLine.startsWith('## ')) {
      const titleText = trimmedLine.slice(2).trim();
      return `<h2 class="text-xl font-bold mt-6 mb-3 ${getTextColorClass('h2')}">${titleText}</h2>`;
    }
    if (trimmedLine.startsWith('# ')) {
      const titleText = trimmedLine.slice(1).trim();
      return `<h1 class="text-2xl font-bold mt-8 mb-4 ${getTextColorClass('h1')}">${titleText}</h1>`;
    }
    
    // 处理列表项
    if (trimmedLine.match(/^[-*+]\s+/)) {
      const listText = trimmedLine.replace(/^[-*+]\s+/, '');
      return `<li class="ml-4 mb-1 ${getTextColorClass('li')}">• ${listText}</li>`;
    }
    if (trimmedLine.match(/^\d+\.\s+/)) {
      const listText = trimmedLine.replace(/^\d+\.\s+/, '');
      return `<li class="ml-4 mb-1 list-decimal ${getTextColorClass('li')}">${listText}</li>`;
    }
    
    // 对于普通行，处理粗体和斜体
    let processedLine = line;
    
    // 先处理粗体和斜体，再添加颜色
    // 安全地处理粗体：使用while循环逐个替换
    while (processedLine.includes('**')) {
      const startIndex = processedLine.indexOf('**');
      if (startIndex === -1) break;
      
      const endIndex = processedLine.indexOf('**', startIndex + 2);
      if (endIndex === -1) break;
      
      const boldText = processedLine.slice(startIndex + 2, endIndex);
      processedLine = processedLine.slice(0, startIndex) + 
                    `<strong class="font-semibold ${getTextColorClass('strong')}">${boldText}</strong>` + 
                    processedLine.slice(endIndex + 2);
    }
    
    // 安全地处理斜体：避免与粗体冲突
    let tempLine = '';
    let i = 0;
    while (i < processedLine.length) {
      if (processedLine[i] === '*' && 
          (i === 0 || processedLine[i-1] !== '*') && 
          (i === processedLine.length - 1 || processedLine[i+1] !== '*')) {
        
        const nextAsterisk = processedLine.indexOf('*', i + 1);
        if (nextAsterisk !== -1 && 
            (nextAsterisk === processedLine.length - 1 || processedLine[nextAsterisk + 1] !== '*')) {
          
          const italicText = processedLine.slice(i + 1, nextAsterisk);
          tempLine += `<em class="italic ${getTextColorClass('em')}">${italicText}</em>`;
          i = nextAsterisk + 1;
          continue;
        }
      }
      tempLine += processedLine[i];
      i++;
    }
    processedLine = tempLine;
    
    // 如果是普通文本行（不是空行），确保整行有正确的文本颜色
    const isEmptyLine = trimmedLine === '';
    if (!isEmptyLine && !processedLine.includes('<h') && !processedLine.includes('<li')) {
      // 为整行添加span包装，确保颜色正确应用，但避免重复包装标题和列表项
      processedLine = `<span class="${getTextColorClass('p')}">${processedLine}</span>`;
    }
    
    return processedLine;
  });
  
  // 重新组合并处理段落
  html = processedLines.join('\n');
  
  // 处理段落：将双换行替换为段落分隔
  html = html.replace(/\n\n+/g, `</p>\n<p class="mb-4 ${getTextColorClass('p')}">`);
  
  // 处理单换行
  html = html.replace(/\n/g, '<br/>');
  
  // 包装在段落中
  html = `<p class="mb-4 ${getTextColorClass('p')}">` + html + '</p>';
  
  // 清理空段落和多余的标签
  html = html.replace(/<p class="mb-4[^"]*">\s*<\/p>/g, '');
  html = html.replace(/<p class="mb-4[^"]*">\s*(<h[1-6])/g, '$1');
  html = html.replace(/(<\/h[1-6]>)\s*<\/p>/g, '$1');
  
  return html;
}