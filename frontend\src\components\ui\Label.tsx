/**
 * 🏷️ [UI组件] Label标签组件
 * 用于表单字段的标签显示，支持主题系统
 */

import React from 'react';
import { useTheme } from '../ThemeProvider';

export interface LabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  /** 标签文本内容 */
  children: React.ReactNode;
  /** 是否为必填字段 */
  required?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 是否禁用状态 */
  disabled?: boolean;
}

/**
 * Label标签组件
 * 
 * 提供一致的标签样式，支持主题切换和必填标识
 */
export const Label: React.FC<LabelProps> = ({
  children,
  required = false,
  className = '',
  disabled = false,
  ...props
}) => {
  const { isDark } = useTheme();

  const baseClasses = `
    block text-sm font-medium mb-1 transition-colors duration-200
    ${disabled 
      ? (isDark ? 'text-gray-500' : 'text-gray-400') 
      : (isDark ? 'text-gray-200' : 'text-gray-700')
    }
    ${className}
  `.trim().replace(/\s+/g, ' ');

  return (
    <label 
      className={baseClasses}
      {...props}
    >
      {children}
      {required && (
        <span className={`ml-1 ${isDark ? 'text-red-400' : 'text-red-500'}`}>
          *
        </span>
      )}
    </label>
  );
};

export default Label;
