"""
🧪 [测试] 动态事件处理器同步测试
使用同步数据库会话测试事件处理器的核心功能
"""

import pytest
import json
from unittest.mock import patch, AsyncMock
from sqlalchemy.orm import Session

from app.core.event_processor import EventProcessor, WorldEvent, CreateEntityEventData, CreateRelationshipEventData
from app.models.story_bible import StoryBible, AIProvider
from app.models.world_graph import Entity, EntityRelationship, EntityType, RelationshipStatus
from app.services.zhipu_client import ChatCompletionResponse
from app.core.config import log_debug


class TestEventProcessorSync:
    """事件处理器同步测试类"""
    
    def test_event_data_models(self):
        """测试事件数据模型"""
        # 测试创建实体事件数据
        entity_data = CreateEntityEventData(
            name="测试角色",
            type="character",
            description="测试描述",
            properties={"年龄": 25},
            first_mentioned_chapter=1
        )
        
        assert entity_data.name == "测试角色"
        assert entity_data.type == "character"
        assert entity_data.properties["年龄"] == 25
        
        # 测试创建关系事件数据
        relationship_data = CreateRelationshipEventData(
            source_entity="张三",
            target_entity="李四",
            relationship_type="朋友",
            description="好朋友",
            properties={"友谊程度": "深厚"},
            established_chapter=1
        )
        
        assert relationship_data.source_entity == "张三"
        assert relationship_data.target_entity == "李四"
        assert relationship_data.relationship_type == "朋友"
        
        log_debug("同步测试", "事件数据模型测试通过")
    
    def test_world_event_parsing(self):
        """测试世界事件解析"""
        # 测试有效的事件数据
        event_dict = {
            "event": "create_entity",
            "data": {
                "name": "王五",
                "type": "character",
                "description": "神秘角色",
                "properties": {"年龄": 40},
                "first_mentioned_chapter": 2
            }
        }
        
        event = WorldEvent(**event_dict)
        assert event.event == "create_entity"
        assert event.data["name"] == "王五"
        assert event.data["type"] == "character"
        
        log_debug("同步测试", "世界事件解析测试通过")
    
    @pytest.mark.asyncio
    async def test_extract_events_success(self):
        """测试成功提取事件"""
        # Mock AI响应
        mock_ai_response = json.dumps([
            {
                "event": "create_entity",
                "data": {
                    "name": "李四",
                    "type": "character",
                    "description": "年轻剑客",
                    "properties": {"年龄": 25},
                    "first_mentioned_chapter": 1
                }
            },
            {
                "event": "create_relationship",
                "data": {
                    "source_entity": "张三",
                    "target_entity": "李四",
                    "relationship_type": "朋友",
                    "description": "好朋友",
                    "properties": {"友谊程度": "深厚"},
                    "established_chapter": 1
                }
            }
        ])
        
        mock_response = ChatCompletionResponse(
            id="test_extract_id",
            object="chat.completion",
            created=1234567890,
            model="glm-4.5-flash",
            choices=[{
                "message": {"content": mock_ai_response},
                "finish_reason": "stop"
            }],
            usage={"total_tokens": 150}
        )
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            processor = EventProcessor()
            events = await processor.extract_events_from_chapter(
                "张三遇到了李四，他们成为了朋友。", "test_story", 1
            )
            
            assert len(events) == 2
            assert events[0].event == "create_entity"
            assert events[0].data["name"] == "李四"
            assert events[1].event == "create_relationship"
            assert events[1].data["source_entity"] == "张三"
            
            log_debug("同步测试", "事件提取成功测试通过", events_count=len(events))
    
    @pytest.mark.asyncio
    async def test_extract_events_invalid_json(self):
        """测试AI返回无效JSON"""
        mock_response = ChatCompletionResponse(
            id="test_invalid_json",
            object="chat.completion",
            created=1234567890,
            model="glm-4.5-flash",
            choices=[{
                "message": {"content": "这不是有效的JSON格式"},
                "finish_reason": "stop"
            }],
            usage={"total_tokens": 50}
        )
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            processor = EventProcessor()
            events = await processor.extract_events_from_chapter(
                "测试文本", "test_story", 1
            )
            
            assert len(events) == 0
            log_debug("同步测试", "无效JSON处理测试通过")
    
    @pytest.mark.asyncio
    async def test_extract_events_empty_array(self):
        """测试AI返回空数组"""
        mock_response = ChatCompletionResponse(
            id="test_empty_array",
            object="chat.completion",
            created=1234567890,
            model="glm-4.5-flash",
            choices=[{
                "message": {"content": "[]"},
                "finish_reason": "stop"
            }],
            usage={"total_tokens": 30}
        )
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            processor = EventProcessor()
            events = await processor.extract_events_from_chapter(
                "没有任何事件的文本", "test_story", 1
            )
            
            assert len(events) == 0
            log_debug("同步测试", "空数组处理测试通过")
    
    def test_prompt_building(self):
        """测试提示词构建"""
        processor = EventProcessor()
        
        chapter_text = "张三遇到了李四"
        story_id = "test_story_001"
        chapter_number = 5
        
        prompt = processor._build_extraction_prompt(chapter_text, story_id, chapter_number)
        
        # 验证提示词包含必要信息
        assert story_id in prompt
        assert f"章节编号: {chapter_number}" in prompt
        assert chapter_text in prompt
        assert "JSON数组格式" in prompt
        assert "create_entity" in prompt
        assert "create_relationship" in prompt
        assert "character" in prompt
        assert "item" in prompt
        
        log_debug("同步测试", "提示词构建测试通过", prompt_length=len(prompt))
    
    def test_supported_events(self):
        """测试支持的事件类型"""
        processor = EventProcessor()
        
        expected_events = {
            "create_entity",
            "update_entity", 
            "create_relationship",
            "update_relationship"
        }
        
        assert set(processor.supported_events.keys()) == expected_events
        
        # 验证每个事件类型都有对应的数据模型
        for event_type, data_class in processor.supported_events.items():
            assert data_class is not None
            assert hasattr(data_class, '__annotations__')
            
        log_debug("同步测试", "支持的事件类型测试通过", 
                 supported_events=list(processor.supported_events.keys()))
    
    @pytest.mark.asyncio
    async def test_singleton_pattern(self):
        """测试单例模式"""
        from app.core.event_processor import get_event_processor
        
        processor1 = await get_event_processor()
        processor2 = await get_event_processor()
        
        assert processor1 is processor2
        assert isinstance(processor1, EventProcessor)
        
        log_debug("同步测试", "单例模式测试通过")
    
    def test_entity_type_validation(self):
        """测试实体类型验证"""
        # 测试有效的实体类型
        valid_types = ["character", "item", "scene", "concept", "organization"]
        
        for entity_type in valid_types:
            try:
                EntityType(entity_type)
                log_debug("同步测试", f"实体类型 {entity_type} 验证通过")
            except ValueError:
                pytest.fail(f"有效的实体类型 {entity_type} 验证失败")
        
        # 测试无效的实体类型
        invalid_type = "invalid_type"
        with pytest.raises(ValueError):
            EntityType(invalid_type)
        
        log_debug("同步测试", "实体类型验证测试通过")
    
    def test_relationship_status_validation(self):
        """测试关系状态验证"""
        # 测试有效的关系状态
        valid_statuses = ["active", "inactive", "historical"]
        
        for status in valid_statuses:
            try:
                RelationshipStatus(status)
                log_debug("同步测试", f"关系状态 {status} 验证通过")
            except ValueError:
                pytest.fail(f"有效的关系状态 {status} 验证失败")
        
        log_debug("同步测试", "关系状态验证测试通过")
