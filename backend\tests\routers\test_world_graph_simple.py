"""
🧪 [测试] 世界知识图谱API路由简化测试
测试实体和关系的CRUD操作，验证API功能正确性
"""

import pytest
from fastapi.testclient import TestClient
from app.core.config import log_debug
from main import create_app


@pytest.fixture(scope="function")
def test_client():
    """创建测试客户端"""
    app = create_app()
    with TestClient(app) as client:
        yield client


class TestWorldGraphAPI:
    """世界图谱API基础测试"""
    
    def test_api_health(self, test_client: TestClient):
        """测试API健康状态"""
        log_debug("测试", "开始测试API健康状态")

        # 测试根路径
        response = test_client.get("/")
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.text}")

        # 暂时不断言，先看看返回什么
        log_debug("测试", "API健康状态测试完成", status=response.status_code)
    
    def test_create_entity_missing_story(self, test_client: TestClient):
        """测试创建实体时故事不存在的情况"""
        log_debug("测试", "开始测试创建实体时故事不存在")
        
        entity_data = {
            "story_id": "nonexistent_story",
            "name": "测试实体",
            "type": "character",
            "description": "测试描述"
        }
        
        response = test_client.post("/api/v1/world/entities", json=entity_data)
        
        # 应该返回404，因为故事不存在
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]
        
        log_debug("测试", "创建实体时故事不存在测试通过")
    
    def test_get_nonexistent_entity(self, test_client: TestClient):
        """测试获取不存在的实体"""
        log_debug("测试", "开始测试获取不存在的实体")
        
        response = test_client.get("/api/v1/world/entities/nonexistent")
        
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]
        
        log_debug("测试", "获取不存在实体测试通过")
    
    def test_create_relationship_missing_entities(self, test_client: TestClient):
        """测试创建关系时实体不存在的情况"""
        log_debug("测试", "开始测试创建关系时实体不存在")
        
        relationship_data = {
            "source_entity_id": "nonexistent_source",
            "target_entity_id": "nonexistent_target",
            "relationship_type": "朋友"
        }
        
        response = test_client.post("/api/v1/world/relationships", json=relationship_data)
        
        # 应该返回404，因为源实体不存在
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]
        
        log_debug("测试", "创建关系时实体不存在测试通过")
    
    def test_get_world_graph_nonexistent_story(self, test_client: TestClient):
        """测试获取不存在故事的世界图谱"""
        log_debug("测试", "开始测试获取不存在故事的世界图谱")
        
        response = test_client.get("/api/v1/world/stories/nonexistent/graph")
        
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]
        
        log_debug("测试", "获取不存在故事的世界图谱测试通过")
    
    def test_create_relationship_same_entity_validation(self, test_client: TestClient):
        """测试创建自己与自己关系的验证"""
        log_debug("测试", "开始测试创建自己与自己关系的验证")
        
        relationship_data = {
            "source_entity_id": "same_entity",
            "target_entity_id": "same_entity",
            "relationship_type": "自恋"
        }
        
        response = test_client.post("/api/v1/world/relationships", json=relationship_data)
        
        # 应该返回422，因为验证失败
        assert response.status_code == 422
        
        log_debug("测试", "创建自己与自己关系的验证测试通过")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
