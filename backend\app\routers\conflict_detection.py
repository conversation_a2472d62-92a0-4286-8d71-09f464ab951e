"""
⚔️ [冲突检测] 冲突检测API路由
提供实时逻辑冲突检测和分析的REST API接口
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import log_info, log_debug, log_error
from app.core.conflict_detection import (
    ConflictDetectionEngine,
    ConflictAnalysisResult,
    ConflictDetection,
    create_conflict_detection_engine
)
from app.schemas.conflict_detection import (
    ConflictDetectionRequest,
    ConflictDetectionResponse,
    ConflictAnalysisResponse,
    ConflictSummaryResponse
)
from app.schemas.world_graph import (
    EntityResponse,
    RelationshipResponse,
    WorldGraphResponse
)
from app.core.database import get_database_session

# 创建路由器
router = APIRouter(
    prefix="/api/v1/conflict",
    tags=["conflict-detection"],
    responses={404: {"description": "Not found"}}
)

# 全局引擎实例
conflict_engine: Optional[ConflictDetectionEngine] = None


def get_conflict_engine() -> ConflictDetectionEngine:
    """获取冲突检测引擎实例"""
    global conflict_engine
    if conflict_engine is None:
        conflict_engine = create_conflict_detection_engine()
    return conflict_engine


async def get_world_graph_data(db: AsyncSession, story_id: str) -> WorldGraphResponse:
    """
    🌐 [世界图谱] 获取世界知识图谱数据

    从数据库获取指定故事的完整世界知识图谱数据

    Args:
        db: 数据库会话
        story_id: 故事ID

    Returns:
        WorldGraphResponse: 世界知识图谱响应数据
    """
    from app.models.world_graph import Entity, EntityRelationship
    from sqlalchemy import select
    from datetime import datetime

    # 获取实体数据
    entities_result = await db.execute(
        select(Entity).where(Entity.story_id == story_id)
    )
    entities = entities_result.scalars().all()

    # 获取关系数据
    relationships_result = await db.execute(
        select(EntityRelationship).where(EntityRelationship.story_id == story_id)
    )
    relationships = relationships_result.scalars().all()

    # 转换为响应模型
    entity_responses = [
        EntityResponse(
            id=entity.id,
            name=entity.name,
            type=entity.type,
            description=entity.description or "",
            properties=entity.properties or {},
            importance_score=entity.importance_score or 0.5,
            first_mentioned_chapter=entity.first_mentioned_chapter or 1,
            last_mentioned_chapter=entity.last_mentioned_chapter or 1,
            created_at=entity.created_at or datetime.now(),
            updated_at=entity.updated_at or datetime.now()
        )
        for entity in entities
    ]

    relationship_responses = [
        RelationshipResponse(
            id=rel.id,
            source_entity_id=rel.source_entity_id,
            target_entity_id=rel.target_entity_id,
            relationship_type=rel.relationship_type,
            strength=rel.strength or 0.5,
            status=rel.status,
            description=rel.description or "",
            established_chapter=rel.established_chapter or 1,
            last_updated_chapter=rel.last_updated_chapter or 1,
            created_at=rel.created_at or datetime.now(),
            updated_at=rel.updated_at or datetime.now()
        )
        for rel in relationships
    ]

    return WorldGraphResponse(
        story_id=story_id,
        entities=entity_responses,
        relationships=relationship_responses,
        total_entities=len(entity_responses),
        total_relationships=len(relationship_responses),
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@router.post("/detect", response_model=ConflictDetectionResponse)
async def detect_conflicts(
    request: ConflictDetectionRequest,
    db: AsyncSession = Depends(get_database_session),
    conflict_engine: ConflictDetectionEngine = Depends(get_conflict_engine)
):
    """
    🔍 [冲突检测] 执行故事冲突检测
    
    分析指定故事的世界知识图谱，检测各种逻辑冲突和不一致性
    
    Args:
        request: 冲突检测请求参数
        db: 数据库会话
        conflict_engine: 冲突检测引擎
        world_engine: 世界知识图谱引擎
        
    Returns:
        ConflictDetectionResponse: 详细的冲突检测结果
    """
    log_info("冲突检测", "开始执行冲突检测",
            故事ID=request.story_id,
            当前章节=request.current_chapter,
            检测类型=request.conflict_types)
    
    try:
        # 获取世界知识图谱
        world_graph = await get_world_graph_data(
            db=db,
            story_id=request.story_id
        )
        
        if not world_graph:
            log_error("冲突检测", "未找到世界知识图谱", 故事ID=request.story_id)
            raise HTTPException(
                status_code=404,
                detail=f"未找到故事 {request.story_id} 的世界知识图谱"
            )
        
        log_debug("冲突检测", "获取世界知识图谱成功",
                 实体数量=len(world_graph.entities),
                 关系数量=len(world_graph.relationships))
        
        # 执行冲突检测
        analysis_result = await conflict_engine.detect_conflicts(
            world_graph=world_graph,
            current_chapter=request.current_chapter,
            previous_analysis=None  # TODO: 从数据库获取历史分析结果
        )
        
        # 根据请求参数过滤冲突类型
        if request.conflict_types:
            # 将请求中的字符串转换为枚举值进行比较
            requested_types = [ct.value for ct in request.conflict_types]
            filtered_conflicts = []
            for conflict_list in [
                analysis_result.critical_conflicts,
                analysis_result.high_conflicts,
                analysis_result.medium_conflicts,
                analysis_result.low_conflicts
            ]:
                filtered_conflicts.extend([
                    c for c in conflict_list
                    if c.conflict_type.value in requested_types
                ])
        else:
            filtered_conflicts = (
                analysis_result.critical_conflicts +
                analysis_result.high_conflicts +
                analysis_result.medium_conflicts +
                analysis_result.low_conflicts
            )
        
        # 应用严重程度过滤
        if request.min_severity:
            severity_order = ["low", "medium", "high", "critical"]
            min_severity_index = severity_order.index(request.min_severity)
            
            filtered_conflicts = [
                c for c in filtered_conflicts
                if severity_order.index(c.severity.value) >= min_severity_index
            ]
        
        # 限制返回数量
        if request.max_results:
            filtered_conflicts = filtered_conflicts[:request.max_results]
        
        log_info("冲突检测", "冲突检测完成",
                故事ID=request.story_id,
                总冲突数=analysis_result.total_conflicts,
                过滤后冲突数=len(filtered_conflicts),
                整体健康度=f"{analysis_result.overall_health_score:.3f}")
        
        return ConflictDetectionResponse(
            story_id=request.story_id,
            analysis_chapter=request.current_chapter,
            conflicts=filtered_conflicts,
            total_conflicts=analysis_result.total_conflicts,
            filtered_conflicts=len(filtered_conflicts),
            analysis_summary=ConflictAnalysisResponse(
                story_consistency_score=analysis_result.story_consistency_score,
                logical_integrity_score=analysis_result.logical_integrity_score,
                overall_health_score=analysis_result.overall_health_score,
                conflict_trend=analysis_result.conflict_trend,
                risk_areas=analysis_result.risk_areas,
                conflicts_by_type=analysis_result.conflicts_by_type,
                conflicts_by_severity=analysis_result.conflicts_by_severity
            )
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("冲突检测", "冲突检测执行失败", error=e, 故事ID=request.story_id)
        raise HTTPException(
            status_code=500,
            detail=f"冲突检测执行失败: {str(e)}"
        )


@router.get("/summary/{story_id}", response_model=ConflictSummaryResponse)
async def get_conflict_summary(
    story_id: str,
    chapter: Optional[int] = Query(None, description="指定章节，默认为最新章节"),
    db: AsyncSession = Depends(get_database_session),
    conflict_engine: ConflictDetectionEngine = Depends(get_conflict_engine)
):
    """
    📊 [冲突检测] 获取冲突检测摘要
    
    获取指定故事的冲突检测摘要信息，包括整体健康度和主要风险区域
    
    Args:
        story_id: 故事ID
        chapter: 指定章节（可选）
        db: 数据库会话
        conflict_engine: 冲突检测引擎
        world_engine: 世界知识图谱引擎
        
    Returns:
        ConflictSummaryResponse: 冲突检测摘要
    """
    log_info("冲突检测", "获取冲突检测摘要",
            故事ID=story_id, 指定章节=chapter)
    
    try:
        # 获取世界知识图谱
        world_graph = await get_world_graph_data(
            db=db,
            story_id=story_id
        )
        
        if not world_graph:
            raise HTTPException(
                status_code=404,
                detail=f"未找到故事 {story_id} 的世界知识图谱"
            )
        
        # 确定分析章节
        analysis_chapter = chapter or max(
            (e.last_mentioned_chapter or 0 for e in world_graph.entities),
            default=1
        )
        
        # 执行快速冲突检测
        analysis_result = await conflict_engine.detect_conflicts(
            world_graph=world_graph,
            current_chapter=analysis_chapter
        )
        
        # 统计严重冲突
        critical_count = len(analysis_result.critical_conflicts)
        high_count = len(analysis_result.high_conflicts)
        medium_count = len(analysis_result.medium_conflicts)
        low_count = len(analysis_result.low_conflicts)
        
        # 确定整体状态
        if critical_count > 0:
            overall_status = "critical"
        elif high_count > 2:
            overall_status = "warning"
        elif medium_count > 5:
            overall_status = "caution"
        else:
            overall_status = "healthy"
        
        log_info("冲突检测", "冲突检测摘要生成完成",
                故事ID=story_id,
                整体状态=overall_status,
                严重冲突数=critical_count)
        
        return ConflictSummaryResponse(
            story_id=story_id,
            analysis_chapter=analysis_chapter,
            overall_status=overall_status,
            total_conflicts=analysis_result.total_conflicts,
            critical_conflicts=critical_count,
            high_conflicts=high_count,
            medium_conflicts=medium_count,
            low_conflicts=low_count,
            health_score=analysis_result.overall_health_score,
            top_risk_areas=analysis_result.risk_areas[:3],  # 只返回前3个风险区域
            conflict_trend=analysis_result.conflict_trend
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("冲突检测", "获取冲突检测摘要失败", error=e, 故事ID=story_id)
        raise HTTPException(
            status_code=500,
            detail=f"获取冲突检测摘要失败: {str(e)}"
        )


@router.get("/types", response_model=List[str])
async def get_conflict_types():
    """
    📋 [冲突检测] 获取支持的冲突类型列表
    
    返回系统支持的所有冲突检测类型
    
    Returns:
        List[str]: 冲突类型列表
    """
    log_debug("冲突检测", "获取支持的冲突类型列表")
    
    from app.core.conflict_detection import ConflictType
    
    conflict_types = [conflict_type.value for conflict_type in ConflictType]
    
    log_debug("冲突检测", "返回冲突类型列表", 类型数量=len(conflict_types))
    
    return conflict_types


@router.get("/health/{story_id}", response_model=dict)
async def get_story_health(
    story_id: str,
    db: AsyncSession = Depends(get_database_session),
    conflict_engine: ConflictDetectionEngine = Depends(get_conflict_engine)
):
    """
    🏥 [冲突检测] 获取故事健康度评估
    
    提供故事的整体健康度评估，包括一致性、完整性等多个维度
    
    Args:
        story_id: 故事ID
        db: 数据库会话
        conflict_engine: 冲突检测引擎
        world_engine: 世界知识图谱引擎
        
    Returns:
        dict: 故事健康度评估结果
    """
    log_info("冲突检测", "获取故事健康度评估", 故事ID=story_id)
    
    try:
        # 获取世界知识图谱
        world_graph = await get_world_graph_data(
            db=db,
            story_id=story_id
        )
        
        if not world_graph:
            raise HTTPException(
                status_code=404,
                detail=f"未找到故事 {story_id} 的世界知识图谱"
            )
        
        # 执行全面的冲突检测
        analysis_result = await conflict_engine.detect_conflicts(
            world_graph=world_graph,
            current_chapter=max(
                (e.last_mentioned_chapter or 0 for e in world_graph.entities),
                default=1
            )
        )
        
        # 计算各维度评分
        health_assessment = {
            "story_id": story_id,
            "overall_health_score": analysis_result.overall_health_score,
            "consistency_score": analysis_result.story_consistency_score,
            "integrity_score": analysis_result.logical_integrity_score,
            "conflict_density": analysis_result.total_conflicts / max(len(world_graph.entities), 1),
            "risk_level": "low" if analysis_result.overall_health_score > 0.8 else
                         "medium" if analysis_result.overall_health_score > 0.6 else
                         "high" if analysis_result.overall_health_score > 0.4 else "critical",
            "recommendations": [],
            "strengths": [],
            "weaknesses": []
        }
        
        # 生成建议
        if analysis_result.overall_health_score < 0.6:
            health_assessment["recommendations"].append("建议优先解决严重和高级别冲突")
        if len(analysis_result.risk_areas) > 0:
            health_assessment["recommendations"].append(f"重点关注风险区域：{', '.join(analysis_result.risk_areas[:2])}")
        
        # 识别优势
        if analysis_result.story_consistency_score > 0.8:
            health_assessment["strengths"].append("故事一致性良好")
        if analysis_result.logical_integrity_score > 0.8:
            health_assessment["strengths"].append("逻辑完整性优秀")
        
        # 识别弱点
        if len(analysis_result.critical_conflicts) > 0:
            health_assessment["weaknesses"].append(f"存在{len(analysis_result.critical_conflicts)}个严重冲突")
        if analysis_result.conflict_trend == "worsening":
            health_assessment["weaknesses"].append("冲突趋势正在恶化")
        
        log_info("冲突检测", "故事健康度评估完成",
                故事ID=story_id,
                整体健康度=f"{analysis_result.overall_health_score:.3f}",
                风险级别=health_assessment["risk_level"])
        
        return health_assessment
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("冲突检测", "获取故事健康度评估失败", error=e, 故事ID=story_id)
        raise HTTPException(
            status_code=500,
            detail=f"获取故事健康度评估失败: {str(e)}"
        )
