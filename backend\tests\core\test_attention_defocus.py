"""
🧪 [测试] 注意力失焦系统测试模块

测试注意力失焦系统的情绪检测、过滤和主观细节提取功能

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import pytest
from typing import List

from app.core.attention_defocus import (
    AttentionDefocusSystem, EmotionalState, FilterResult,
    create_attention_defocus_system, quick_emotional_filter,
    analyze_attention_patterns
)


class TestEmotionalStateDetection:
    """情绪状态检测测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.system = AttentionDefocusSystem()
    
    def test_detect_anger_emotion(self):
        """测试愤怒情绪检测"""
        anger_text = "这个该死的障碍让我非常愤怒，红色的警告灯刺眼地闪烁着。"
        detected_emotion = self.system.detect_emotional_state(anger_text)
        assert detected_emotion == EmotionalState.ANGER
    
    def test_detect_sadness_emotion(self):
        """测试悲伤情绪检测"""
        sadness_text = "失去了她之后，一切都变得空虚，灰色的天空下只有孤独的回忆。"
        detected_emotion = self.system.detect_emotional_state(sadness_text)
        assert detected_emotion == EmotionalState.SADNESS
    
    def test_detect_anxiety_emotion(self):
        """测试焦虑情绪检测"""
        anxiety_text = "时间不够了，如果出现危险怎么办？心跳加速，来不及了。"
        detected_emotion = self.system.detect_emotional_state(anxiety_text)
        assert detected_emotion == EmotionalState.ANXIETY
    
    def test_detect_joy_emotion(self):
        """测试喜悦情绪检测"""
        joy_text = "成功了！明亮的阳光照耀着，这是完美的胜利时刻。"
        detected_emotion = self.system.detect_emotional_state(joy_text)
        assert detected_emotion == EmotionalState.JOY
    
    def test_detect_neutral_emotion(self):
        """测试中性情绪检测"""
        neutral_text = "今天天气不错，我去了商店买了一些东西。"
        detected_emotion = self.system.detect_emotional_state(neutral_text)
        assert detected_emotion == EmotionalState.NEUTRAL


class TestEmotionalFilter:
    """情感过滤测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.system = AttentionDefocusSystem()
    
    def test_anger_filter_removes_peaceful_content(self):
        """测试愤怒过滤器移除平和内容"""
        text = "阳光透过窗户洒进来，微风轻抚着脸颊。突然传来刺耳的噪音，让人愤怒。花香弥漫在空气中。"
        result = self.system.apply_emotional_filter(text, EmotionalState.ANGER, intensity=0.8)
        
        # 应该移除一些平和的描述
        assert len(result.removed_elements) > 0
        # 应该强调刺激性内容
        assert len(result.emphasized_elements) > 0
        # 过滤后的文本应该更短
        assert len(result.filtered_text) < len(text)
    
    def test_sadness_filter_removes_positive_content(self):
        """测试悲伤过滤器移除积极内容"""
        text = "欢声笑语充满房间，大家都很开心。但是角落里坐着一个孤独的身影。庆祝活动继续进行。"
        result = self.system.apply_emotional_filter(text, EmotionalState.SADNESS, intensity=0.8)
        
        # 应该移除积极内容
        assert len(result.removed_elements) > 0
        # 应该强调悲伤相关内容
        assert "孤独" in result.filtered_text or len(result.emphasized_elements) > 0
    
    def test_anxiety_filter_emphasizes_threats(self):
        """测试焦虑过滤器强调威胁"""
        text = "一切都很安全，没有问题。但是如果出现危险怎么办？时间充足，不用担心。"
        result = self.system.apply_emotional_filter(text, EmotionalState.ANXIETY, intensity=0.8)
        
        # 应该强调威胁性内容
        assert "危险" in result.filtered_text
        # 可能移除安全相关内容
        assert len(result.removed_elements) >= 0
    
    def test_neutral_emotion_no_filtering(self):
        """测试中性情绪不进行过滤"""
        text = "今天天气不错，我去了商店买了一些东西。"
        result = self.system.apply_emotional_filter(text, EmotionalState.NEUTRAL, intensity=0.8)
        
        # 中性情绪应该不进行过滤
        assert result.filtered_text == text
        assert len(result.removed_elements) == 0
        assert len(result.emphasized_elements) == 0
    
    def test_auto_emotion_detection_and_filtering(self):
        """测试自动情绪检测和过滤"""
        anger_text = "这个该死的障碍让我愤怒，刺耳的声音让人烦躁。美丽的花园在阳光下闪闪发光。"
        result = self.system.apply_emotional_filter(anger_text, emotion_state=None, intensity=0.7)
        
        # 应该自动检测为愤怒并进行相应过滤
        assert result.attention_focus == "愤怒"
        # 应该有一些过滤效果
        assert len(result.removed_elements) > 0 or len(result.emphasized_elements) > 0


class TestSubjectiveDetailsExtraction:
    """主观细节提取测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.system = AttentionDefocusSystem()
    
    def test_extract_anger_details(self):
        """测试提取愤怒相关细节"""
        text = "红色的警告灯刺眼地闪烁，尖锐的警报声响起，阻碍了我的前进。"
        details = self.system.extract_subjective_details(text, EmotionalState.ANGER, max_details=3)
        
        assert len(details) > 0
        # 应该包含愤怒相关的细节
        assert any("红" in detail or "刺" in detail or "阻" in detail for detail in details)
    
    def test_extract_sadness_details(self):
        """测试提取悲伤相关细节"""
        text = "空荡荡的房间里只有黑暗，冷清的街道上没有人影，旧照片散落在地。"
        details = self.system.extract_subjective_details(text, EmotionalState.SADNESS, max_details=3)
        
        assert len(details) > 0
        # 应该包含悲伤相关的细节
        assert any("空" in detail or "暗" in detail or "冷" in detail for detail in details)
    
    def test_extract_anxiety_details(self):
        """测试提取焦虑相关细节"""
        text = "时间快不够了，急促的脚步声，危险的信号，不确定的未来。"
        details = self.system.extract_subjective_details(text, EmotionalState.ANXIETY, max_details=3)
        
        assert len(details) > 0
        # 应该包含焦虑相关的细节
        assert any("快" in detail or "急" in detail or "险" in detail for detail in details)
    
    def test_max_details_limit(self):
        """测试最大细节数量限制"""
        text = "红色刺眼，尖锐声音，突然阻碍，威胁出现，冲突爆发，敌意显现。"
        details = self.system.extract_subjective_details(text, EmotionalState.ANGER, max_details=2)
        
        # 应该限制在最大数量内
        assert len(details) <= 2


class TestAttentionPatternAnalysis:
    """注意力模式分析测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.system = AttentionDefocusSystem()
    
    def test_get_attention_summary(self):
        """测试获取注意力模式摘要"""
        summary = self.system.get_attention_summary(EmotionalState.ANGER)
        
        assert summary["emotion_state"] == "愤怒"
        assert "focus_keywords_count" in summary
        assert "ignore_keywords_count" in summary
        assert "distortion_rules" in summary
        assert "intensity_modifier" in summary
        assert len(summary["sample_focus_keywords"]) <= 5
        assert len(summary["sample_ignore_keywords"]) <= 5
    
    def test_get_attention_summary_invalid_emotion(self):
        """测试获取无效情绪的注意力模式摘要"""
        # 创建一个不存在的情绪状态
        summary = self.system.get_attention_summary(EmotionalState.NEUTRAL)
        
        # 中性情绪没有特定的注意力模式，应该返回空字典
        assert summary == {}


class TestFactoryFunctions:
    """工厂函数测试类"""
    
    def test_create_attention_defocus_system(self):
        """测试创建注意力失焦系统"""
        system = create_attention_defocus_system()
        assert isinstance(system, AttentionDefocusSystem)
        assert len(system.attention_patterns) > 0
    
    def test_quick_emotional_filter(self):
        """测试快速情感过滤"""
        text = "这个该死的障碍让我愤怒。美丽的花园在阳光下。"
        filtered_text = quick_emotional_filter(text, "愤怒", intensity=0.7)
        
        assert isinstance(filtered_text, str)
        assert len(filtered_text) > 0
        # 过滤后的文本应该不同于原文（在高强度下）
        assert filtered_text != text or "愤怒" in filtered_text
    
    def test_quick_emotional_filter_auto_detection(self):
        """测试快速情感过滤的自动检测"""
        text = "这个该死的障碍让我愤怒，刺耳的声音。"
        filtered_text = quick_emotional_filter(text, emotion_state=None, intensity=0.7)
        
        assert isinstance(filtered_text, str)
        assert len(filtered_text) > 0
    
    def test_analyze_attention_patterns(self):
        """测试分析注意力模式"""
        text = "这个该死的障碍让我愤怒，红色的警告灯刺眼。"
        analysis = analyze_attention_patterns(text)
        
        assert "detected_emotion" in analysis
        assert "attention_pattern" in analysis
        assert "subjective_details" in analysis
        assert "text_length" in analysis
        assert analysis["text_length"] == len(text)


class TestEmphasisMethods:
    """强调方法测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.system = AttentionDefocusSystem()
    
    def test_anger_emphasis(self):
        """测试愤怒强调"""
        sentence = "我看到那个人在说话"
        emphasized = self.system._apply_anger_emphasis(sentence)
        
        # 应该有所变化，使用更强烈的词汇
        assert emphasized != sentence or "瞪着" in emphasized
    
    def test_sadness_emphasis(self):
        """测试悲伤强调"""
        sentence = "明亮的房间里很温暖"
        emphasized = self.system._apply_sadness_emphasis(sentence)
        
        # 应该应用灰色滤镜
        assert emphasized != sentence or any(word in emphasized for word in ["暗淡", "冰冷"])
    
    def test_anxiety_emphasis(self):
        """测试焦虑强调"""
        sentence = "可能会有一些问题"
        emphasized = self.system._apply_anxiety_emphasis(sentence)
        
        # 应该放大威胁感
        assert emphasized != sentence or "很可能" in emphasized or "恐怕" in emphasized
    
    def test_joy_emphasis(self):
        """测试喜悦强调"""
        sentence = "这个结果还行"
        emphasized = self.system._apply_joy_emphasis(sentence)
        
        # 应该美化描述
        assert emphasized != sentence or "很棒" in emphasized or "非常好" in emphasized
