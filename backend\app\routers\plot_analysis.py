"""
🎭 [剧情分析] 剧情分析和推荐API路由
提供智能剧情推荐、冲突检测、路径规划等功能的REST API接口
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import log_info, log_debug, log_error
from app.core.database import get_db
from app.core.plot_recommendation import PlotRecommendationEngine, create_plot_recommendation_engine
from app.schemas.plot_analysis import (
    PlotRecommendationResponse,
    StoryAnalysisResponse,
    PlotRecommendationRequest,
    StoryAnalysisRequest
)
from app.routers.world_graph import get_world_graph
from app.schemas.world_graph import WorldGraphResponse


router = APIRouter(
    prefix="/api/v1/plot",
    tags=["plot-analysis"],
    responses={404: {"description": "Not found"}}
)


@router.post("/recommendations", response_model=List[PlotRecommendationResponse])
async def generate_plot_recommendations(
    request: PlotRecommendationRequest,
    db: AsyncSession = Depends(get_db)
) -> List[PlotRecommendationResponse]:
    """
    🎯 [剧情分析] 生成剧情推荐
    
    基于当前故事状态和知识图谱，生成智能的剧情发展建议
    
    Args:
        request: 剧情推荐请求参数
        db: 数据库会话
        
    Returns:
        List[PlotRecommendationResponse]: 排序后的剧情推荐列表
    """
    log_info("剧情分析", "开始生成剧情推荐",
            故事ID=request.story_id,
            当前章节=request.current_chapter,
            最大推荐数=request.max_recommendations)
    
    try:
        # 获取世界知识图谱
        world_graph = await get_world_graph(
            story_id=request.story_id,
            db=db
        )
        
        if not world_graph.entities:
            log_error("剧情分析", "故事知识图谱为空", 故事ID=request.story_id)
            raise HTTPException(
                status_code=404,
                detail=f"故事 {request.story_id} 的知识图谱为空，无法生成推荐"
            )
        
        # 创建剧情推荐引擎
        engine = create_plot_recommendation_engine()
        
        # 分析故事状态
        story_analysis = await engine.analyze_story_state(
            world_graph=world_graph,
            current_chapter=request.current_chapter
        )
        
        # 生成推荐
        recommendations = await engine.generate_recommendations(
            story_analysis=story_analysis,
            max_recommendations=request.max_recommendations
        )
        
        # 转换为响应格式
        response_recommendations = [
            PlotRecommendationResponse(
                id=rec.id,
                title=rec.title,
                description=rec.description,
                pattern_type=rec.pattern_type.value,
                priority=rec.priority.value,
                feasibility_score=rec.feasibility_score,
                dramatic_impact=rec.dramatic_impact,
                character_development=rec.character_development,
                plot_advancement=rec.plot_advancement,
                overall_score=rec.overall_score,
                involved_entities=rec.involved_entities,
                required_relationships=rec.required_relationships,
                potential_conflicts=rec.potential_conflicts,
                execution_steps=rec.execution_steps,
                expected_outcomes=rec.expected_outcomes,
                reasoning=rec.reasoning,
                created_at=rec.created_at
            )
            for rec in recommendations
        ]
        
        log_info("剧情分析", "剧情推荐生成完成",
                故事ID=request.story_id,
                推荐数量=len(response_recommendations),
                最高评分=f"{response_recommendations[0].overall_score:.3f}" if response_recommendations else "无")
        
        return response_recommendations
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("剧情分析", "生成剧情推荐失败", error=e, 故事ID=request.story_id)
        raise HTTPException(
            status_code=500,
            detail=f"生成剧情推荐时发生错误: {str(e)}"
        )


@router.post("/analysis", response_model=StoryAnalysisResponse)
async def analyze_story_state(
    request: StoryAnalysisRequest,
    db: AsyncSession = Depends(get_db)
) -> StoryAnalysisResponse:
    """
    📊 [剧情分析] 分析故事状态
    
    深度分析当前故事的状态，包括角色关系、情节发展、张力水平等
    
    Args:
        request: 故事分析请求参数
        db: 数据库会话
        
    Returns:
        StoryAnalysisResponse: 详细的故事状态分析结果
    """
    log_info("剧情分析", "开始分析故事状态",
            故事ID=request.story_id,
            当前章节=request.current_chapter)
    
    try:
        # 获取世界知识图谱
        world_graph = await get_world_graph(
            story_id=request.story_id,
            db=db
        )
        
        if not world_graph.entities:
            log_error("剧情分析", "故事知识图谱为空", 故事ID=request.story_id)
            raise HTTPException(
                status_code=404,
                detail=f"故事 {request.story_id} 的知识图谱为空，无法进行分析"
            )
        
        # 创建剧情推荐引擎
        engine = create_plot_recommendation_engine()
        
        # 分析故事状态
        analysis = await engine.analyze_story_state(
            world_graph=world_graph,
            current_chapter=request.current_chapter
        )
        
        # 转换为响应格式
        response = StoryAnalysisResponse(
            story_id=analysis.story_id,
            current_chapter=analysis.current_chapter,
            main_characters=[{
                "id": char.id,
                "name": char.name,
                "importance_score": char.importance_score
            } for char in analysis.main_characters],
            supporting_characters=[{
                "id": char.id,
                "name": char.name,
                "importance_score": char.importance_score
            } for char in analysis.supporting_characters],
            key_items=[{
                "id": item.id,
                "name": item.name,
                "importance_score": item.importance_score
            } for item in analysis.key_items],
            important_scenes=[{
                "id": scene.id,
                "name": scene.name,
                "importance_score": scene.importance_score
            } for scene in analysis.important_scenes],
            active_relationships_count=len(analysis.active_relationships),
            conflict_relationships_count=len(analysis.conflict_relationships),
            alliance_relationships_count=len(analysis.alliance_relationships),
            romantic_relationships_count=len(analysis.romantic_relationships),
            tension_level=analysis.tension_level,
            character_development_stage=analysis.character_development_stage,
            plot_complexity=analysis.plot_complexity,
            pacing_score=analysis.pacing_score,
            underutilized_characters=analysis.underutilized_characters,
            dormant_relationships=analysis.dormant_relationships,
            unresolved_conflicts=analysis.unresolved_conflicts,
            created_at=analysis.created_at
        )
        
        log_info("剧情分析", "故事状态分析完成",
                故事ID=request.story_id,
                主要角色数=len(analysis.main_characters),
                张力水平=f"{analysis.tension_level:.3f}",
                情节复杂度=f"{analysis.plot_complexity:.3f}")
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("剧情分析", "分析故事状态失败", error=e, 故事ID=request.story_id)
        raise HTTPException(
            status_code=500,
            detail=f"分析故事状态时发生错误: {str(e)}"
        )


@router.get("/recommendations/{story_id}", response_model=List[PlotRecommendationResponse])
async def get_story_recommendations(
    story_id: str,
    current_chapter: int = Query(..., description="当前章节号"),
    max_recommendations: int = Query(5, description="最大推荐数量", ge=1, le=20),
    db: AsyncSession = Depends(get_db)
) -> List[PlotRecommendationResponse]:
    """
    🎯 [剧情分析] 获取故事的剧情推荐 (GET方式)
    
    通过GET请求获取指定故事的剧情推荐，适用于简单的查询场景
    
    Args:
        story_id: 故事ID
        current_chapter: 当前章节号
        max_recommendations: 最大推荐数量
        db: 数据库会话
        
    Returns:
        List[PlotRecommendationResponse]: 排序后的剧情推荐列表
    """
    request = PlotRecommendationRequest(
        story_id=story_id,
        current_chapter=current_chapter,
        max_recommendations=max_recommendations
    )
    
    return await generate_plot_recommendations(request, db)


@router.get("/analysis/{story_id}", response_model=StoryAnalysisResponse)
async def get_story_analysis(
    story_id: str,
    current_chapter: int = Query(..., description="当前章节号"),
    db: AsyncSession = Depends(get_db)
) -> StoryAnalysisResponse:
    """
    📊 [剧情分析] 获取故事状态分析 (GET方式)
    
    通过GET请求获取指定故事的状态分析，适用于简单的查询场景
    
    Args:
        story_id: 故事ID
        current_chapter: 当前章节号
        db: 数据库会话
        
    Returns:
        StoryAnalysisResponse: 详细的故事状态分析结果
    """
    request = StoryAnalysisRequest(
        story_id=story_id,
        current_chapter=current_chapter
    )
    
    return await analyze_story_state(request, db)
