"""
🧪 [测试] 非理性行为注入器测试

测试非理性行为注入器的各项功能，包括情绪检测、
行为选择、微行为注入等。

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import pytest
from unittest.mock import patch
import random

from app.core.irrational_behavior import (
    IrrationalBehaviorInjector,
    EmotionalState,
    BehaviorType,
    IrrationalBehavior,
    BehaviorInjectionResult,
    create_irrational_behavior_injector,
    inject_irrational_behavior,
    analyze_emotional_behavior_potential
)


class TestIrrationalBehaviorInjector:
    """测试非理性行为注入器类"""
    
    def setup_method(self):
        """测试前设置"""
        self.injector = IrrationalBehaviorInjector()
    
    def test_injector_initialization(self):
        """测试注入器初始化"""
        assert self.injector is not None
        assert len(self.injector.behavior_library) > 0
        
        # 检查各种情绪的行为库
        for emotion in [EmotionalState.ANGER, EmotionalState.SADNESS, EmotionalState.ANXIETY]:
            assert emotion in self.injector.behavior_library
            assert len(self.injector.behavior_library[emotion]) > 0
    
    def test_detect_emotional_context_anger(self):
        """测试愤怒情绪检测"""
        text = "他非常愤怒，气得想要砸东西，真是受不了了。"
        result = self.injector.detect_emotional_context(text)
        
        assert result["has_emotional_content"] is True
        assert result["primary_emotion"] == EmotionalState.ANGER
        assert result["emotion_intensity"] > 0.5
        assert "愤怒" in result["all_emotions"][EmotionalState.ANGER]["keywords"]
    
    def test_detect_emotional_context_sadness(self):
        """测试悲伤情绪检测"""
        text = "她坐在房间里，看着照片，眼泪不停地流下来，心里很难过。"
        result = self.injector.detect_emotional_context(text)
        
        assert result["has_emotional_content"] is True
        assert result["primary_emotion"] == EmotionalState.SADNESS
        assert "房间" in result["context_triggers"]
        assert "照片" in result["context_triggers"]
    
    def test_detect_emotional_context_anxiety(self):
        """测试焦虑情绪检测"""
        text = "他在等待消息，不停地刷手机，心里很紧张和担心。"
        result = self.injector.detect_emotional_context(text)
        
        assert result["has_emotional_content"] is True
        assert result["primary_emotion"] == EmotionalState.ANXIETY
        assert "手机" in result["context_triggers"]
        assert "等待" in result["context_triggers"]
    
    def test_detect_emotional_context_neutral(self):
        """测试中性文本检测"""
        text = "今天天气很好，他去了超市买菜。"
        result = self.injector.detect_emotional_context(text)
        
        assert result["has_emotional_content"] is False
        assert result["primary_emotion"] is None
        assert result["emotion_intensity"] == 0
    
    def test_select_appropriate_behaviors_anger(self):
        """测试愤怒行为选择"""
        behaviors = self.injector._select_appropriate_behaviors(
            EmotionalState.ANGER,
            intensity=0.8,
            context_triggers=["门", "房间"],
            max_behaviors=2
        )
        
        assert len(behaviors) <= 2
        for behavior in behaviors:
            assert behavior.emotion == EmotionalState.ANGER
            assert 0.5 <= behavior.intensity_range[1]  # 强度范围适合
    
    def test_select_appropriate_behaviors_no_match(self):
        """测试无匹配行为的情况"""
        behaviors = self.injector._select_appropriate_behaviors(
            EmotionalState.ANGER,
            intensity=0.1,  # 强度太低
            context_triggers=[],
            max_behaviors=2
        )
        
        # 可能没有行为或很少行为匹配低强度
        assert isinstance(behaviors, list)
    
    def test_inject_micro_behavior_anger(self):
        """测试愤怒微行为注入"""
        text = "他非常愤怒，走出了房间。"
        result = self.injector.inject_micro_behavior(text, intensity=0.8, max_injections=1)
        
        assert isinstance(result, BehaviorInjectionResult)
        assert result.original_text == text
        assert result.emotional_context["primary_emotion"] == EmotionalState.ANGER
        
        # 如果成功注入，文本应该有变化
        if result.injected_behaviors:
            assert result.modified_text != text
            assert len(result.injected_behaviors) > 0
    
    def test_inject_micro_behavior_sadness(self):
        """测试悲伤微行为注入"""
        text = "她很难过，一个人坐在房间里看照片。"
        result = self.injector.inject_micro_behavior(text, intensity=0.7, max_injections=2)
        
        assert result.emotional_context["primary_emotion"] == EmotionalState.SADNESS
        assert "房间" in result.emotional_context["context_triggers"]
        assert "照片" in result.emotional_context["context_triggers"]
    
    def test_inject_micro_behavior_neutral_text(self):
        """测试中性文本的行为注入"""
        text = "今天天气很好，他去了超市。"
        result = self.injector.inject_micro_behavior(text, intensity=0.8)
        
        assert result.original_text == text
        assert result.modified_text == text  # 应该没有变化
        assert len(result.injected_behaviors) == 0
        assert result.processing_details["skipped"] is True
    
    def test_inject_micro_behavior_preserve_meaning(self):
        """测试保持原意的注入"""
        text = "他很愤怒，用力关上了门。"
        result = self.injector.inject_micro_behavior(
            text, 
            intensity=0.9, 
            max_injections=1,
            preserve_meaning=True
        )
        
        # 验证注入结果的合理性
        if result.injected_behaviors:
            assert "愤怒" in result.modified_text or "门" in result.modified_text
    
    def test_find_injection_points(self):
        """测试注入点查找"""
        text = "他很愤怒。走出了房间。然后用力关门。"
        behavior = IrrationalBehavior(
            emotion=EmotionalState.ANGER,
            behavior_type=BehaviorType.MICRO_ACTION,
            action="握紧了拳头",
            context_triggers=["愤怒", "门"],
            intensity_range=(0.5, 1.0),
            probability=0.8,
            description="测试行为"
        )
        
        points = self.injector._find_injection_points(text, behavior)
        
        assert len(points) > 0
        for point in points:
            assert "position" in point
            assert "type" in point
            assert "context" in point
    
    def test_evaluate_context_fit(self):
        """测试上下文适配性评估"""
        behavior = IrrationalBehavior(
            emotion=EmotionalState.ANGER,
            behavior_type=BehaviorType.MICRO_ACTION,
            action="握紧了拳头",
            context_triggers=["愤怒", "门"],
            intensity_range=(0.5, 1.0),
            probability=0.8,
            description="测试行为"
        )
        
        # 高匹配度上下文
        high_context = "他很愤怒，走向门口"
        high_score = self.injector._evaluate_context_fit(high_context, behavior)
        
        # 低匹配度上下文
        low_context = "天气很好，阳光明媚"
        low_score = self.injector._evaluate_context_fit(low_context, behavior)
        
        assert high_score > low_score
        assert 0.0 <= high_score <= 1.0
        assert 0.0 <= low_score <= 1.0


class TestFactoryFunctions:
    """测试工厂函数"""
    
    def test_create_irrational_behavior_injector(self):
        """测试创建注入器实例"""
        injector = create_irrational_behavior_injector()
        assert isinstance(injector, IrrationalBehaviorInjector)
        assert len(injector.behavior_library) > 0
    
    def test_inject_irrational_behavior(self):
        """测试快速注入函数"""
        text = "他非常愤怒，走出了房间。"
        result = inject_irrational_behavior(text, intensity=0.8, max_injections=1)
        
        assert isinstance(result, str)
        # 结果可能相同（如果没有合适的注入点）或不同（如果成功注入）
    
    def test_analyze_emotional_behavior_potential(self):
        """测试情绪行为潜力分析"""
        # 高潜力文本
        high_potential_text = "他非常愤怒，在房间里走来走去，握紧了拳头。"
        high_result = analyze_emotional_behavior_potential(high_potential_text)
        
        assert high_result["suitable_for_injection"] is True
        assert high_result["injection_potential"] > 0.3
        assert "emotional_context" in high_result
        
        # 低潜力文本
        low_potential_text = "今天天气很好。"
        low_result = analyze_emotional_behavior_potential(low_potential_text)
        
        assert low_result["suitable_for_injection"] is False
        assert low_result["injection_potential"] <= 0.3


class TestBehaviorTypes:
    """测试不同类型的行为"""
    
    def setup_method(self):
        """测试前设置"""
        self.injector = IrrationalBehaviorInjector()
    
    def test_micro_action_behaviors(self):
        """测试微动作行为"""
        anger_behaviors = self.injector.behavior_library[EmotionalState.ANGER]
        micro_actions = [b for b in anger_behaviors if b.behavior_type == BehaviorType.MICRO_ACTION]
        
        assert len(micro_actions) > 0
        for behavior in micro_actions:
            assert behavior.behavior_type == BehaviorType.MICRO_ACTION
            assert len(behavior.action) > 0
    
    def test_impulsive_decision_behaviors(self):
        """测试冲动决定行为"""
        anger_behaviors = self.injector.behavior_library[EmotionalState.ANGER]
        impulsive_decisions = [b for b in anger_behaviors if b.behavior_type == BehaviorType.IMPULSIVE_DECISION]
        
        assert len(impulsive_decisions) > 0
        for behavior in impulsive_decisions:
            assert behavior.behavior_type == BehaviorType.IMPULSIVE_DECISION
    
    def test_avoidance_behaviors(self):
        """测试回避行为"""
        sadness_behaviors = self.injector.behavior_library[EmotionalState.SADNESS]
        avoidance_behaviors = [b for b in sadness_behaviors if b.behavior_type == BehaviorType.AVOIDANCE_BEHAVIOR]
        
        assert len(avoidance_behaviors) > 0
        for behavior in avoidance_behaviors:
            assert behavior.behavior_type == BehaviorType.AVOIDANCE_BEHAVIOR
    
    def test_repetitive_action_behaviors(self):
        """测试重复行为"""
        anxiety_behaviors = self.injector.behavior_library[EmotionalState.ANXIETY]
        repetitive_actions = [b for b in anxiety_behaviors if b.behavior_type == BehaviorType.REPETITIVE_ACTION]
        
        assert len(repetitive_actions) > 0
        for behavior in repetitive_actions:
            assert behavior.behavior_type == BehaviorType.REPETITIVE_ACTION


class TestEdgeCases:
    """测试边界情况"""
    
    def setup_method(self):
        """测试前设置"""
        self.injector = IrrationalBehaviorInjector()
    
    def test_empty_text(self):
        """测试空文本"""
        result = self.injector.inject_micro_behavior("", intensity=0.8)
        
        assert result.original_text == ""
        assert result.modified_text == ""
        assert len(result.injected_behaviors) == 0
    
    def test_very_short_text(self):
        """测试极短文本"""
        text = "愤怒。"
        result = self.injector.inject_micro_behavior(text, intensity=0.8)
        
        assert result.original_text == text
        # 短文本可能无法注入或注入效果有限
    
    def test_zero_intensity(self):
        """测试零强度"""
        text = "他非常愤怒，走出了房间。"
        result = self.injector.inject_micro_behavior(text, intensity=0.0)
        
        # 零强度应该很少或不注入行为
        assert len(result.injected_behaviors) <= 1
    
    def test_maximum_intensity(self):
        """测试最大强度"""
        text = "他非常愤怒，在房间里走来走去，握紧了拳头，想要砸东西。"
        result = self.injector.inject_micro_behavior(text, intensity=1.0, max_injections=3)
        
        # 最大强度应该尝试注入更多行为
        assert isinstance(result, BehaviorInjectionResult)
    
    def test_mixed_emotions(self):
        """测试混合情绪文本"""
        text = "他既愤怒又悲伤，不知道该怎么办，心里很焦虑。"
        result = self.injector.inject_micro_behavior(text, intensity=0.7)
        
        assert result.emotional_context["has_emotional_content"] is True
        # 应该选择主导情绪
        assert result.emotional_context["primary_emotion"] is not None
