["tests/core/test_ai_effect_analyzer.py::TestAICharacteristicAnalyzer::test_analyze_high_ai_characteristics", "tests/core/test_ai_effect_analyzer.py::TestAICharacteristicAnalyzer::test_analyze_low_ai_characteristics", "tests/core/test_ai_effect_analyzer.py::TestAICharacteristicAnalyzer::test_detect_repetition_patterns", "tests/core/test_ai_effect_analyzer.py::TestAICharacteristicAnalyzer::test_empty_text_analysis", "tests/core/test_ai_effect_analyzer.py::TestAIEffectAnalyzer::test_analyze_effect_different_levels", "tests/core/test_ai_effect_analyzer.py::TestAIEffectAnalyzer::test_analyze_effect_original_only", "tests/core/test_ai_effect_analyzer.py::TestAIEffectAnalyzer::test_analyze_effect_with_improvement", "tests/core/test_ai_effect_analyzer.py::TestAIEffectAnalyzer::test_calculate_ai_reduction", "tests/core/test_ai_effect_analyzer.py::TestAIEffectAnalyzer::test_calculate_emotion_improvement", "tests/core/test_ai_effect_analyzer.py::TestAIEffectAnalyzer::test_generate_analysis_report", "tests/core/test_ai_effect_analyzer.py::TestConvenienceFunctions::test_create_ai_effect_analyzer", "tests/core/test_ai_effect_analyzer.py::TestEmotionAuthenticityAnalyzer::test_analyze_high_emotion_authenticity", "tests/core/test_ai_effect_analyzer.py::TestEmotionAuthenticityAnalyzer::test_analyze_low_emotion_authenticity", "tests/core/test_ai_effect_analyzer.py::TestEmotionAuthenticityAnalyzer::test_count_physical_reactions", "tests/core/test_ai_effect_analyzer.py::TestEmotionAuthenticityAnalyzer::test_count_sensory_details", "tests/core/test_async_db_simple.py::TestAsyncDatabaseSimple::test_async_query_operations", "tests/core/test_async_db_simple.py::TestAsyncDatabaseSimple::test_async_transaction_rollback", "tests/core/test_async_db_simple.py::TestAsyncDatabaseSimple::test_basic_async_db_operations", "tests/core/test_attention_defocus.py::TestAttentionPatternAnalysis::test_get_attention_summary", "tests/core/test_attention_defocus.py::TestAttentionPatternAnalysis::test_get_attention_summary_invalid_emotion", "tests/core/test_attention_defocus.py::TestEmotionalFilter::test_anger_filter_removes_peaceful_content", "tests/core/test_attention_defocus.py::TestEmotionalFilter::test_anxiety_filter_emphasizes_threats", "tests/core/test_attention_defocus.py::TestEmotionalFilter::test_auto_emotion_detection_and_filtering", "tests/core/test_attention_defocus.py::TestEmotionalFilter::test_neutral_emotion_no_filtering", "tests/core/test_attention_defocus.py::TestEmotionalFilter::test_sadness_filter_removes_positive_content", "tests/core/test_attention_defocus.py::TestEmotionalStateDetection::test_detect_anger_emotion", "tests/core/test_attention_defocus.py::TestEmotionalStateDetection::test_detect_anxiety_emotion", "tests/core/test_attention_defocus.py::TestEmotionalStateDetection::test_detect_joy_emotion", "tests/core/test_attention_defocus.py::TestEmotionalStateDetection::test_detect_neutral_emotion", "tests/core/test_attention_defocus.py::TestEmotionalStateDetection::test_detect_sadness_emotion", "tests/core/test_attention_defocus.py::TestEmphasisMethods::test_anger_emphasis", "tests/core/test_attention_defocus.py::TestEmphasisMethods::test_anxiety_emphasis", "tests/core/test_attention_defocus.py::TestEmphasisMethods::test_joy_emphasis", "tests/core/test_attention_defocus.py::TestEmphasisMethods::test_sadness_emphasis", "tests/core/test_attention_defocus.py::TestFactoryFunctions::test_analyze_attention_patterns", "tests/core/test_attention_defocus.py::TestFactoryFunctions::test_create_attention_defocus_system", "tests/core/test_attention_defocus.py::TestFactoryFunctions::test_quick_emotional_filter", "tests/core/test_attention_defocus.py::TestFactoryFunctions::test_quick_emotional_filter_auto_detection", "tests/core/test_attention_defocus.py::TestSubjectiveDetailsExtraction::test_extract_anger_details", "tests/core/test_attention_defocus.py::TestSubjectiveDetailsExtraction::test_extract_anxiety_details", "tests/core/test_attention_defocus.py::TestSubjectiveDetailsExtraction::test_extract_sadness_details", "tests/core/test_attention_defocus.py::TestSubjectiveDetailsExtraction::test_max_details_limit", "tests/core/test_deai_processor.py::TestAITraceDetector::test_ai_level_determination", "tests/core/test_deai_processor.py::TestAITraceDetector::test_detector_initialization", "tests/core/test_deai_processor.py::TestAITraceDetector::test_high_ai_trace_detection", "tests/core/test_deai_processor.py::TestAITraceDetector::test_low_ai_trace_detection", "tests/core/test_deai_processor.py::TestDeAIProcessor::test_adaptive_deai_processing", "tests/core/test_deai_processor.py::TestDeAIProcessor::test_primary_emotion_detection", "tests/core/test_deai_processor.py::TestDeAIProcessor::test_processing_intensity_determination", "tests/core/test_deai_processor.py::TestDeAIProcessor::test_processor_initialization", "tests/core/test_deai_processor.py::TestDeAIProcessor::test_quality_score_calculation", "tests/core/test_deai_processor.py::TestDeAIProcessor::test_time_content_detection", "tests/core/test_deai_processor.py::TestFactoryFunctions::test_analyze_ai_traces", "tests/core/test_deai_processor.py::TestFactoryFunctions::test_create_deai_processor", "tests/core/test_deai_processor.py::TestFactoryFunctions::test_quick_deai_processing", "tests/core/test_dna_compiler.py::TestDNACompiler::test_compilation_stats", "tests/core/test_dna_compiler.py::TestDNACompiler::test_compile_basic", "tests/core/test_dna_compiler.py::TestDNACompiler::test_init_dna_compiler", "tests/core/test_dna_compiler.py::TestDNACompilerIntegration::test_full_compilation_workflow", "tests/core/test_dna_compiler.py::TestDehydrator::test_extract_sensory_data", "tests/core/test_dna_compiler.py::TestDehydrator::test_init_dehydrator", "tests/core/test_dna_compiler.py::TestDehydrator::test_remove_lyrical_words", "tests/core/test_dna_compiler.py::TestEmotionalDNA::test_emotional_dna_creation", "tests/core/test_dna_compiler.py::TestEmotionalDNA::test_emotional_dna_to_dict", "tests/core/test_dna_compiler.py::TestEntropyGenerator::test_generate_entropy_items", "tests/core/test_dna_compiler.py::TestEntropyGenerator::test_init_entropy_generator", "tests/core/test_dna_compiler.py::TestGenePairer::test_calculate_intensity_score", "tests/core/test_dna_compiler.py::TestGenePairer::test_calculate_reliability_score", "tests/core/test_dna_compiler.py::TestGenePairer::test_init_gene_pairer", "tests/core/test_dna_compiler.py::TestGenePairer::test_pair_genes", "tests/core/test_dna_compiler.py::TestShellRemover::test_extract_physical_reactions", "tests/core/test_dna_compiler.py::TestShellRemover::test_init_shell_remover", "tests/core/test_dna_compiler.py::TestShellRemover::test_remove_metaphors", "tests/core/test_emotion_miner.py::TestEmotionKeywordDict::test_get_emotion_keywords", "tests/core/test_emotion_miner.py::TestEmotionKeywordDict::test_init_keyword_dict", "tests/core/test_emotion_miner.py::TestEmotionKeywordDict::test_physiological_keywords", "tests/core/test_emotion_miner.py::TestEmotionKeywordDict::test_sensory_keywords", "tests/core/test_emotion_miner.py::TestEmotionMiner::test_empty_statistics", "tests/core/test_emotion_miner.py::TestEmotionMiner::test_get_mining_statistics", "tests/core/test_emotion_miner.py::TestEmotionMiner::test_mine_emotions_from_file", "tests/core/test_emotion_miner.py::TestEmotionMiner::test_mine_emotions_from_text_basic", "tests/core/test_emotion_miner.py::TestEmotionMiner::test_mine_emotions_with_reality_filter", "tests/core/test_emotion_miner.py::TestEmotionMinerIntegration::test_full_mining_workflow", "tests/core/test_emotion_miner.py::TestRealityFilter::test_calculate_reality_score_high", "tests/core/test_emotion_miner.py::TestRealityFilter::test_calculate_reality_score_low", "tests/core/test_emotion_miner.py::TestRealityFilter::test_extract_physiological_indicators", "tests/core/test_emotion_miner.py::TestRealityFilter::test_extract_sensory_details", "tests/core/test_emotion_miner.py::TestTextSegmenter::test_segment_by_paragraphs", "tests/core/test_emotion_miner.py::TestTextSegmenter::test_segment_by_sentences", "tests/core/test_emotion_physical_mapper.py::TestConvenienceFunctions::test_create_emotion_mapper", "tests/core/test_emotion_physical_mapper.py::TestConvenienceFunctions::test_quick_enhance_prompt", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_auto_recognize_and_extract", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_build_enhanced_prompt", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_enhance_prompt_with_auto_detection", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_enhance_prompt_with_specified_emotions", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_extract_emotion_details_exception_handling", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_extract_emotion_details_no_genes_found", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_extract_emotion_details_success", "tests/core/test_emotion_physical_mapper.py::TestEmotionPhysicalMapper::test_generate_enhancement_instructions", "tests/core/test_emotion_physical_mapper.py::TestEmotionRecognizer::test_emotion_deduplication", "tests/core/test_emotion_physical_mapper.py::TestEmotionRecognizer::test_intensity_calculation", "tests/core/test_emotion_physical_mapper.py::TestEmotionRecognizer::test_no_emotions_found", "tests/core/test_emotion_physical_mapper.py::TestEmotionRecognizer::test_recognize_basic_emotions", "tests/core/test_emotional_downgrader.py::TestEdgeCases::test_empty_text", "tests/core/test_emotional_downgrader.py::TestEdgeCases::test_maximum_intensity", "tests/core/test_emotional_downgrader.py::TestEdgeCases::test_very_short_text", "tests/core/test_emotional_downgrader.py::TestEdgeCases::test_zero_intensity", "tests/core/test_emotional_downgrader.py::TestEmotionalInarticulationSystem::test_analyze_emotional_expression", "tests/core/test_emotional_downgrader.py::TestEmotionalInarticulationSystem::test_auto_mode_selection", "tests/core/test_emotional_downgrader.py::TestEmotionalInarticulationSystem::test_body_substitution_mode", "tests/core/test_emotional_downgrader.py::TestEmotionalInarticulationSystem::test_context_requirements_check", "tests/core/test_emotional_downgrader.py::TestEmotionalInarticulationSystem::test_emotional_avoidance_mode", "tests/core/test_emotional_downgrader.py::TestEmotionalInarticulationSystem::test_intensity_effect", "tests/core/test_emotional_downgrader.py::TestEmotionalInarticulationSystem::test_no_downgrade_needed", "tests/core/test_emotional_downgrader.py::TestEmotionalInarticulationSystem::test_system_initialization", "tests/core/test_emotional_downgrader.py::TestEmotionalInarticulationSystem::test_topic_shifting_mode", "tests/core/test_emotional_downgrader.py::TestEmotionalInarticulationSystem::test_vague_expression_mode", "tests/core/test_emotional_downgrader.py::TestEmotionalInarticulationSystem::test_vocabulary_poor_mode", "tests/core/test_emotional_downgrader.py::TestFactoryFunctions::test_analyze_text_emotional_complexity", "tests/core/test_emotional_downgrader.py::TestFactoryFunctions::test_batch_emotional_downgrade", "tests/core/test_emotional_downgrader.py::TestFactoryFunctions::test_create_emotional_downgrader", "tests/core/test_emotional_downgrader.py::TestFactoryFunctions::test_detect_ai_emotional_patterns", "tests/core/test_emotional_downgrader.py::TestFactoryFunctions::test_quick_downgrade_with_mode", "tests/core/test_emotional_downgrader.py::TestFactoryFunctions::test_quick_emotional_downgrade", "tests/core/test_emotional_downgrader.py::TestOverDescriptionDetector::test_calculate_severity_levels", "tests/core/test_emotional_downgrader.py::TestOverDescriptionDetector::test_detect_normal_text", "tests/core/test_emotional_downgrader.py::TestOverDescriptionDetector::test_detect_over_complex_emotions", "tests/core/test_emotional_downgrader.py::TestOverDescriptionDetector::test_detect_over_literary_expression", "tests/core/test_emotional_downgrader.py::TestOverDescriptionDetector::test_detect_over_precise_description", "tests/core/test_emotional_downgrader.py::TestOverDescriptionDetector::test_detector_initialization", "tests/core/test_emotional_downgrader.py::TestPresetConfigurations::test_apply_preset_downgrade", "tests/core/test_emotional_downgrader.py::TestPresetConfigurations::test_invalid_preset_name", "tests/core/test_emotional_downgrader.py::TestPresetConfigurations::test_preset_configurations_exist", "tests/core/test_emotional_downgrader.py::TestPresetConfigurations::test_preset_intensity_differences", "tests/core/test_entropy_injector.py::TestAITraceAnalyzer::test_analyze_empty_text", "tests/core/test_entropy_injector.py::TestAITraceAnalyzer::test_analyze_high_ai_traces", "tests/core/test_entropy_injector.py::TestAITraceAnalyzer::test_analyze_natural_text", "tests/core/test_entropy_injector.py::TestConvenienceFunctions::test_create_entropy_injector", "tests/core/test_entropy_injector.py::TestConvenienceFunctions::test_quick_entropy_process", "tests/core/test_entropy_injector.py::TestConvenienceFunctions::test_quick_entropy_process_different_levels", "tests/core/test_entropy_injector.py::TestDialogueImperfector::test_imperfect_dialogues", "tests/core/test_entropy_injector.py::TestDialogueImperfector::test_no_dialogues", "tests/core/test_entropy_injector.py::TestEntropyInjector::test_ai_trace_reduction_calculation", "tests/core/test_entropy_injector.py::TestEntropyInjector::test_process_empty_text", "tests/core/test_entropy_injector.py::TestEntropyInjector::test_process_text_custom_intensity", "tests/core/test_entropy_injector.py::TestEntropyInjector::test_process_text_medium_level", "tests/core/test_entropy_injector.py::TestEntropyInjector::test_process_text_with_emotion_context", "tests/core/test_entropy_injector.py::TestEnvironmentNoiseInjector::test_empty_text_injection", "tests/core/test_entropy_injector.py::TestEnvironmentNoiseInjector::test_inject_environmental_noise", "tests/core/test_entropy_injector.py::TestEnvironmentNoiseInjector::test_low_intensity_injection", "tests/core/test_entropy_injector.py::TestMicroDetailAdder::test_add_micro_details_with_emotion", "tests/core/test_entropy_injector.py::TestMicroDetailAdder::test_add_micro_details_without_emotion", "tests/core/test_entropy_injector.py::TestMicroDetailAdder::test_single_sentence_text", "tests/core/test_entropy_injector.py::TestPerfectionBreaker::test_break_perfect_metaphors", "tests/core/test_entropy_injector.py::TestPerfectionBreaker::test_no_metaphors_to_break", "tests/core/test_entropy_injector.py::TestThoughtInterruptor::test_inject_thought_interruptions", "tests/core/test_entropy_injector.py::TestThoughtInterruptor::test_no_thinking_patterns", "tests/core/test_event_processor.py::TestEventProcessor::test_build_extraction_prompt", "tests/core/test_event_processor.py::TestEventProcessor::test_create_entity_event_data_validation", "tests/core/test_event_processor.py::TestEventProcessor::test_create_relationship_event_data_validation", "tests/core/test_event_processor.py::TestEventProcessor::test_event_processor_initialization", "tests/core/test_event_processor.py::TestEventProcessor::test_extract_events_empty_response", "tests/core/test_event_processor.py::TestEventProcessor::test_extract_events_from_chapter_success", "tests/core/test_event_processor.py::TestEventProcessor::test_extract_events_invalid_json", "tests/core/test_event_processor.py::TestEventProcessor::test_get_event_processor_singleton", "tests/core/test_event_processor.py::TestEventProcessor::test_world_event_validation", "tests/core/test_event_processor_async_fixed.py::TestEventProcessorAsyncFixed::test_create_entity_async", "tests/core/test_event_processor_async_fixed.py::TestEventProcessorAsyncFixed::test_create_relationship_async", "tests/core/test_event_processor_async_fixed.py::TestEventProcessorAsyncFixed::test_find_entity_by_name_async", "tests/core/test_event_processor_async_fixed.py::TestEventProcessorAsyncFixed::test_process_chapter_events_async", "tests/core/test_event_processor_async_fixed.py::TestEventProcessorAsyncFixed::test_singleton_pattern_async", "tests/core/test_event_processor_async_simple.py::TestEventProcessorAsyncSimple::test_event_processor_create_entity", "tests/core/test_event_processor_async_simple.py::TestEventProcessorAsyncSimple::test_event_processor_create_relationship", "tests/core/test_event_processor_async_simple.py::TestEventProcessorAsyncSimple::test_event_processor_find_entity", "tests/core/test_event_processor_async_simple.py::TestEventProcessorAsyncSimple::test_event_processor_singleton", "tests/core/test_event_processor_functional.py::TestEventProcessorFunctional::test_complete_event_extraction_flow", "tests/core/test_event_processor_functional.py::TestEventProcessorFunctional::test_concurrent_event_processing", "tests/core/test_event_processor_functional.py::TestEventProcessorFunctional::test_create_story_and_entities", "tests/core/test_event_processor_functional.py::TestEventProcessorFunctional::test_error_handling_ai_failure", "tests/core/test_event_processor_functional.py::TestEventProcessorFunctional::test_event_data_validation_edge_cases", "tests/core/test_event_processor_functional.py::TestEventProcessorFunctional::test_prompt_customization", "tests/core/test_event_processor_integration.py::TestEventProcessorIntegration::test_create_new_entities", "tests/core/test_event_processor_integration.py::TestEventProcessorIntegration::test_create_relationships", "tests/core/test_event_processor_integration.py::TestEventProcessorIntegration::test_error_handling_invalid_story", "tests/core/test_event_processor_integration.py::TestEventProcessorIntegration::test_process_chapter_events_complete_flow", "tests/core/test_event_processor_integration.py::TestEventProcessorIntegration::test_singleton_pattern", "tests/core/test_event_processor_simple.py::TestEventProcessorSimple::test_create_entity_directly", "tests/core/test_event_processor_simple.py::TestEventProcessorSimple::test_create_relationship_directly", "tests/core/test_event_processor_simple.py::TestEventProcessorSimple::test_extract_events_mock", "tests/core/test_event_processor_simple.py::TestEventProcessorSimple::test_find_entity_by_name", "tests/core/test_event_processor_sync.py::TestEventProcessorSync::test_entity_type_validation", "tests/core/test_event_processor_sync.py::TestEventProcessorSync::test_event_data_models", "tests/core/test_event_processor_sync.py::TestEventProcessorSync::test_extract_events_empty_array", "tests/core/test_event_processor_sync.py::TestEventProcessorSync::test_extract_events_invalid_json", "tests/core/test_event_processor_sync.py::TestEventProcessorSync::test_extract_events_success", "tests/core/test_event_processor_sync.py::TestEventProcessorSync::test_prompt_building", "tests/core/test_event_processor_sync.py::TestEventProcessorSync::test_relationship_status_validation", "tests/core/test_event_processor_sync.py::TestEventProcessorSync::test_singleton_pattern", "tests/core/test_event_processor_sync.py::TestEventProcessorSync::test_supported_events", "tests/core/test_event_processor_sync.py::TestEventProcessorSync::test_world_event_parsing", "tests/core/test_irrational_behavior.py::TestBehaviorTypes::test_avoidance_behaviors", "tests/core/test_irrational_behavior.py::TestBehaviorTypes::test_impulsive_decision_behaviors", "tests/core/test_irrational_behavior.py::TestBehaviorTypes::test_micro_action_behaviors", "tests/core/test_irrational_behavior.py::TestBehaviorTypes::test_repetitive_action_behaviors", "tests/core/test_irrational_behavior.py::TestEdgeCases::test_empty_text", "tests/core/test_irrational_behavior.py::TestEdgeCases::test_maximum_intensity", "tests/core/test_irrational_behavior.py::TestEdgeCases::test_mixed_emotions", "tests/core/test_irrational_behavior.py::TestEdgeCases::test_very_short_text", "tests/core/test_irrational_behavior.py::TestEdgeCases::test_zero_intensity", "tests/core/test_irrational_behavior.py::TestFactoryFunctions::test_analyze_emotional_behavior_potential", "tests/core/test_irrational_behavior.py::TestFactoryFunctions::test_create_irrational_behavior_injector", "tests/core/test_irrational_behavior.py::TestFactoryFunctions::test_inject_irrational_behavior", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_detect_emotional_context_anger", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_detect_emotional_context_anxiety", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_detect_emotional_context_neutral", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_detect_emotional_context_sadness", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_evaluate_context_fit", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_find_injection_points", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_inject_micro_behavior_anger", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_inject_micro_behavior_neutral_text", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_inject_micro_behavior_preserve_meaning", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_inject_micro_behavior_sadness", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_injector_initialization", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_select_appropriate_behaviors_anger", "tests/core/test_irrational_behavior.py::TestIrrationalBehaviorInjector::test_select_appropriate_behaviors_no_match", "tests/core/test_prompt_synthesizer.py::TestPromptSynthesizer::test_build_context_briefing_success", "tests/core/test_prompt_synthesizer.py::TestPromptSynthesizer::test_context_quality_calculation", "tests/core/test_prompt_synthesizer.py::TestPromptSynthesizer::test_enhanced_prompt_generation", "tests/core/test_prompt_synthesizer.py::TestPromptSynthesizer::test_error_handling_missing_bible", "tests/core/test_prompt_synthesizer.py::TestPromptSynthesizer::test_structured_context_retrieval", "tests/core/test_prompt_synthesizer.py::TestPromptSynthesizer::test_unstructured_context_retrieval", "tests/core/test_prompt_synthesizer_with_world_graph.py::test_context_quality_with_world_graph", "tests/core/test_prompt_synthesizer_with_world_graph.py::test_enhanced_prompt_with_world_graph", "tests/core/test_prompt_synthesizer_with_world_graph.py::test_no_world_graph_client", "tests/core/test_prompt_synthesizer_with_world_graph.py::test_world_graph_client_failure_handling", "tests/core/test_prompt_synthesizer_with_world_graph.py::test_world_knowledge_graph_integration", "tests/core/test_time_distortion.py::TestEdgeCases::test_empty_text", "tests/core/test_time_distortion.py::TestEdgeCases::test_maximum_intensity", "tests/core/test_time_distortion.py::TestEdgeCases::test_mixed_emotions_with_time", "tests/core/test_time_distortion.py::TestEdgeCases::test_multiple_time_references", "tests/core/test_time_distortion.py::TestEdgeCases::test_very_short_text", "tests/core/test_time_distortion.py::TestEdgeCases::test_zero_intensity", "tests/core/test_time_distortion.py::TestFactoryFunctions::test_analyze_time_distortion_potential", "tests/core/test_time_distortion.py::TestFactoryFunctions::test_apply_subjective_timing_function", "tests/core/test_time_distortion.py::TestFactoryFunctions::test_create_time_perception_distorter", "tests/core/test_time_distortion.py::TestFactoryFunctions::test_distort_time_perception_function", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_apply_subjective_timing_anger", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_apply_subjective_timing_sadness", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_apply_subjective_timing_unsupported_emotion", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_apply_time_distortion", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_detect_emotional_context_anger", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_detect_emotional_context_anxiety", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_detect_emotional_context_no_emotion", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_detect_emotional_context_no_time", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_detect_emotional_context_sadness", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_distort_time_perception_anger", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_distort_time_perception_no_emotion", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_distort_time_perception_no_time", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_distort_time_perception_sadness", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_distorter_initialization", "tests/core/test_time_distortion.py::TestTimePerceptionDistorter::test_get_applicable_patterns", "tests/core/test_time_distortion.py::TestTimePerceptionModes::test_accelerated_mode", "tests/core/test_time_distortion.py::TestTimePerceptionModes::test_decelerated_mode", "tests/core/test_time_distortion.py::TestTimePerceptionModes::test_suspended_mode", "tests/core/test_time_distortion.py::TestTimePerceptionModes::test_urgent_mode", "tests/integration/test_deai_pipeline_effect.py::TestDeAIPipelineEffect::test_analysis_report_generation", "tests/integration/test_deai_pipeline_effect.py::TestDeAIPipelineEffect::test_complete_deai_pipeline_effect", "tests/integration/test_deai_pipeline_effect.py::TestDeAIPipelineEffect::test_different_disruption_levels_effect", "tests/integration/test_deai_pipeline_effect.py::TestDeAIPipelineEffect::test_emotion_enhancement_effect", "tests/integration/test_deai_pipeline_effect.py::TestDeAIPipelineEffect::test_entropy_injection_effect", "tests/integration/test_deai_pipeline_effect.py::TestDeAIPipelineEffect::test_pipeline_with_no_improvement_needed", "tests/integration/test_memory_workflow.py::TestMemoryWorkflowIntegration::test_complete_memory_workflow", "tests/integration/test_memory_workflow.py::TestMemoryWorkflowIntegration::test_cross_story_isolation", "tests/integration/test_memory_workflow.py::TestMemoryWorkflowIntegration::test_memory_importance_scoring", "tests/integration/test_rag_chapter_generation.py::TestRAGChapterGeneration::test_rag_enhanced_chapter_generation_success", "tests/integration/test_rag_chapter_generation.py::TestRAGChapterGeneration::test_rag_enhanced_generation_error_handling", "tests/integration/test_rag_chapter_generation.py::TestRAGChapterGeneration::test_rag_enhanced_generation_with_emotion", "tests/integration/test_rag_chapter_generation.py::TestRAGChapterGeneration::test_rag_enhanced_generation_with_entropy", "tests/integration/test_rag_workflow_e2e.py::TestRAGWorkflowE2E::test_complete_rag_workflow", "tests/integration/test_rag_workflow_e2e.py::TestRAGWorkflowE2E::test_rag_workflow_with_memory_integration", "tests/integration/test_task_6_4_end_to_end.py::test_complete_rag_with_world_graph_workflow", "tests/models/test_world_graph.py::TestEntityModel::test_create_different_entity_types", "tests/models/test_world_graph.py::TestEntityModel::test_create_entity", "tests/models/test_world_graph.py::TestEntityModel::test_entity_story_relationship", "tests/models/test_world_graph.py::TestEntityRelationshipModel::test_create_relationship", "tests/models/test_world_graph.py::TestEntityRelationshipModel::test_multiple_relationship_types", "tests/models/test_world_graph.py::TestEntityRelationshipModel::test_relationship_entity_references", "tests/models/test_world_graph.py::TestEntityRelationshipModel::test_relationship_status_changes", "tests/models/test_world_graph.py::TestRelationshipTypes::test_relationship_type_constants", "tests/repositories/test_bible_repo.py::TestChapterRepository::test_create_chapter", "tests/repositories/test_bible_repo.py::TestChapterRepository::test_delete_chapter", "tests/repositories/test_bible_repo.py::TestChapterRepository::test_get_chapter_by_id", "tests/repositories/test_bible_repo.py::TestChapterRepository::test_get_chapters_by_bible_id", "tests/repositories/test_bible_repo.py::TestChapterRepository::test_update_chapter_status", "tests/repositories/test_bible_repo.py::TestStoryBibleRepository::test_create_bible", "tests/repositories/test_bible_repo.py::TestStoryBibleRepository::test_delete_bible", "tests/repositories/test_bible_repo.py::TestStoryBibleRepository::test_get_bible_by_id", "tests/repositories/test_bible_repo.py::TestStoryBibleRepository::test_list_bibles", "tests/repositories/test_bible_repo.py::TestStoryBibleRepository::test_update_bible_status", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_create_chapter", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_delete_chapter", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_get_chapter_by_id", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_get_chapter_count_by_bible_id", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_get_chapters_by_bible_id", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_get_next_chapter_number", "tests/repositories/test_chapter_repo.py::TestChapterRepository::test_update_chapter_status", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_create_gene", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_delete_gene", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_get_gene_by_id", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_get_high_quality_genes", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_get_random_genes", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_get_statistics", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_increment_usage", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_search_genes", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneRepository::test_update_gene", "tests/repositories/test_emotional_gene_repo.py::TestEmotionalGeneSeeder::test_seed_from_dna_list", "tests/repositories/test_story_bible_repo.py::TestStoryBibleRepository::test_create_bible", "tests/repositories/test_story_bible_repo.py::TestStoryBibleRepository::test_delete_bible", "tests/repositories/test_story_bible_repo.py::TestStoryBibleRepository::test_get_bible_by_id", "tests/repositories/test_story_bible_repo.py::TestStoryBibleRepository::test_get_bible_with_chapters", "tests/repositories/test_story_bible_repo.py::TestStoryBibleRepository::test_list_bibles", "tests/repositories/test_story_bible_repo.py::TestStoryBibleRepository::test_update_bible_status", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_batch_seed_genes", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_create_emotion_gene", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_get_emotion_gene", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_get_high_quality_genes", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_get_nonexistent_gene", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_get_random_genes", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_get_statistics", "tests/routers/test_emotion_genes.py::TestEmotionGenesAPI::test_search_emotion_genes", "tests/routers/test_generation.py::TestGenerationRoutes::test_generate_chapter_bible_not_found", "tests/routers/test_generation.py::TestGenerationRoutes::test_generate_chapter_success", "tests/routers/test_generation.py::TestGenerationRoutes::test_generate_story_bible_invalid_data", "tests/routers/test_generation.py::TestGenerationRoutes::test_generate_story_bible_success", "tests/routers/test_generation.py::TestGenerationRoutes::test_get_task_list_success", "tests/routers/test_generation.py::TestGenerationRoutes::test_get_task_list_with_pagination", "tests/routers/test_generation.py::TestGenerationRoutes::test_get_task_list_with_status_filter", "tests/routers/test_generation.py::TestGenerationRoutes::test_get_task_status_not_found", "tests/routers/test_generation.py::TestGenerationRoutes::test_get_task_status_success", "tests/routers/test_generation.py::TestGenerationRoutes::test_request_data_validation", "tests/routers/test_generation_simple.py::TestPersistenceSimple::test_chapter_without_bible", "tests/routers/test_generation_simple.py::TestPersistenceSimple::test_create_and_get_bible", "tests/routers/test_generation_simple.py::TestPersistenceSimple::test_create_bible_basic", "tests/routers/test_generation_simple.py::TestPersistenceSimple::test_delete_bible", "tests/routers/test_generation_simple.py::TestPersistenceSimple::test_get_nonexistent_bible", "tests/routers/test_generation_simple.py::TestPersistenceSimple::test_list_bibles_empty", "tests/routers/test_memory.py::TestAutoSummaryAPI::test_generate_auto_summary_empty_content", "tests/routers/test_memory.py::TestAutoSummaryAPI::test_generate_auto_summary_success", "tests/routers/test_memory.py::TestMemoryDeleteAPI::test_delete_story_memories_success", "tests/routers/test_memory.py::TestMemoryEmbedAPI::test_embed_chapter_memory_not_completed", "tests/routers/test_memory.py::TestMemoryEmbedAPI::test_embed_chapter_memory_not_found", "tests/routers/test_memory.py::TestMemoryEmbedAPI::test_embed_chapter_memory_success", "tests/routers/test_memory.py::TestMemorySearchAPI::test_search_memories_empty_results", "tests/routers/test_memory.py::TestMemorySearchAPI::test_search_memories_success", "tests/routers/test_memory.py::TestMemoryStatsAPI::test_get_story_memory_stats_success", "tests/routers/test_persistence_routes.py::TestPersistenceRoutes::test_generate_story_bible", "tests/routers/test_persistence_routes.py::TestPersistenceRoutes::test_get_story_bible", "tests/routers/test_persistence_routes.py::TestPersistenceRoutes::test_list_story_bibles", "tests/routers/test_persistence_simple.py::TestPersistenceSimple::test_chapter_without_bible", "tests/routers/test_persistence_simple.py::TestPersistenceSimple::test_create_and_get_bible", "tests/routers/test_persistence_simple.py::TestPersistenceSimple::test_create_bible_basic", "tests/routers/test_persistence_simple.py::TestPersistenceSimple::test_delete_bible", "tests/routers/test_persistence_simple.py::TestPersistenceSimple::test_get_nonexistent_bible", "tests/routers/test_persistence_simple.py::TestPersistenceSimple::test_list_bibles_empty", "tests/routers/test_world_graph.py::TestEntityAPI::test_create_entity", "tests/routers/test_world_graph_debug.py::TestWorldGraphDebug::test_create_entity_debug", "tests/routers/test_world_graph_integration.py::TestWorldGraphIntegration::test_complete_entity_crud_flow", "tests/routers/test_world_graph_integration.py::TestWorldGraphIntegration::test_relationship_creation_and_retrieval", "tests/routers/test_world_graph_simple.py::TestWorldGraphAPI::test_api_health", "tests/routers/test_world_graph_simple.py::TestWorldGraphAPI::test_create_entity_missing_story", "tests/routers/test_world_graph_simple.py::TestWorldGraphAPI::test_create_relationship_missing_entities", "tests/routers/test_world_graph_simple.py::TestWorldGraphAPI::test_create_relationship_same_entity_validation", "tests/routers/test_world_graph_simple.py::TestWorldGraphAPI::test_get_nonexistent_entity", "tests/routers/test_world_graph_simple.py::TestWorldGraphAPI::test_get_world_graph_nonexistent_story", "tests/services/test_generation_integration.py::TestGenerationIntegration::test_chapter_generation_emotion_enhancement_error", "tests/services/test_generation_integration.py::TestGenerationIntegration::test_chapter_generation_entropy_injection_error", "tests/services/test_generation_integration.py::TestGenerationIntegration::test_chapter_generation_with_emotion_enhancement", "tests/services/test_generation_integration.py::TestGenerationIntegration::test_chapter_generation_without_enhancement", "tests/services/test_generation_integration.py::TestGenerationIntegration::test_chapter_request_model_validation", "tests/services/test_vector_store.py::TestVectorStoreIntegration::test_global_vector_store", "tests/services/test_vector_store.py::TestVectorStoreManager::test_add_memory", "tests/services/test_vector_store.py::TestVectorStoreManager::test_filtered_search", "tests/services/test_vector_store.py::TestVectorStoreManager::test_search_memories", "tests/services/test_vector_store.py::TestVectorStoreManager::test_semantic_similarity", "tests/services/test_vector_store.py::TestVectorStoreManager::test_text_embedding", "tests/services/test_vector_store.py::TestVectorStoreManager::test_vector_store_initialization", "tests/services/test_zhipu_client.py::TestZhipuClient::test_chat_completion_connection_error", "tests/services/test_zhipu_client.py::TestZhipuClient::test_chat_completion_http_error", "tests/services/test_zhipu_client.py::TestZhipuClient::test_chat_completion_stream_success", "tests/services/test_zhipu_client.py::TestZhipuClient::test_chat_completion_success", "tests/services/test_zhipu_client.py::TestZhipuClient::test_chat_completion_timeout", "tests/services/test_zhipu_client.py::TestZhipuClient::test_client_context_manager", "tests/services/test_zhipu_client.py::TestZhipuClient::test_client_initialization", "tests/services/test_zhipu_client.py::TestZhipuClient::test_get_headers", "tests/services/test_zhipu_client.py::TestZhipuClientSingleton::test_close_zhipu_client", "tests/services/test_zhipu_client.py::TestZhipuClientSingleton::test_get_zhipu_client_singleton", "tests/test_conflict_detection.py::TestConflictDetectionEngine::test_comprehensive_conflict_detection", "tests/test_conflict_detection.py::TestConflictDetectionEngine::test_conflict_trend_analysis", "tests/test_conflict_detection.py::TestConflictDetectionEngine::test_consistency_score_calculation", "tests/test_conflict_detection.py::TestConflictDetectionEngine::test_engine_initialization", "tests/test_conflict_detection.py::TestConflictDetectionEngine::test_item_ownership_conflict_detection", "tests/test_conflict_detection.py::TestConflictDetectionEngine::test_location_conflict_detection", "tests/test_conflict_detection.py::TestConflictDetectionEngine::test_relationship_contradiction_detection", "tests/test_conflict_detection.py::TestConflictDetectionEngine::test_risk_areas_identification", "tests/test_conflict_detection.py::TestConflictDetectionEngine::test_severity_determination", "tests/test_conflict_detection.py::TestConflictDetectionEngine::test_timeline_inconsistency_detection", "tests/test_database.py::TestAlembicMigrations::test_migration_can_be_applied", "tests/test_database.py::TestDatabaseManager::test_database_manager_initialization", "tests/test_database.py::TestDatabaseManager::test_database_model_operations", "tests/test_database.py::TestDatabaseManager::test_database_session_creation", "tests/test_database.py::TestDatabaseManager::test_database_table_creation", "tests/test_database.py::test_database_initialization_integration", "tests/test_main.py::TestMainApp::test_404_handling", "tests/test_main.py::TestMainApp::test_cors_headers", "tests/test_main.py::TestMainApp::test_health_check_endpoint", "tests/test_main.py::TestMainApp::test_openapi_docs_available", "tests/test_main.py::TestMainApp::test_root_endpoint", "tests/test_plot_path_planner.py::TestPlotPathPlanner::test_analyze_character_motivations", "tests/test_plot_path_planner.py::TestPlotPathPlanner::test_analyze_current_state", "tests/test_plot_path_planner.py::TestPlotPathPlanner::test_confidence_calculation", "tests/test_plot_path_planner.py::TestPlotPathPlanner::test_generate_focused_paths", "tests/test_plot_path_planner.py::TestPlotPathPlanner::test_generate_plot_paths", "tests/test_plot_path_planner.py::TestPlotPathPlanner::test_path_evaluation_metrics", "tests/test_plot_path_planner.py::TestPlotPathPlanner::test_planner_initialization", "tests/test_plot_path_planner.py::TestPlotPathPlanner::test_risk_factor_identification", "tests/test_plot_path_planner.py::test_create_plot_path_planner", "tests/test_relationship_analyzer.py::TestRelationshipNetworkAnalyzer::test_analyzer_initialization", "tests/test_relationship_analyzer.py::TestRelationshipNetworkAnalyzer::test_betweenness_centrality_calculation", "tests/test_relationship_analyzer.py::TestRelationshipNetworkAnalyzer::test_build_network_graph", "tests/test_relationship_analyzer.py::TestRelationshipNetworkAnalyzer::test_calculate_edge_metrics", "tests/test_relationship_analyzer.py::TestRelationshipNetworkAnalyzer::test_calculate_network_metrics", "tests/test_relationship_analyzer.py::TestRelationshipNetworkAnalyzer::test_calculate_node_metrics", "tests/test_relationship_analyzer.py::TestRelationshipNetworkAnalyzer::test_closeness_centrality_calculation", "tests/test_relationship_analyzer.py::TestRelationshipNetworkAnalyzer::test_create_relationship_analyzer", "tests/test_relationship_analyzer.py::TestRelationshipNetworkAnalyzer::test_degree_centrality_calculation", "tests/test_relationship_analyzer.py::TestRelationshipNetworkAnalyzer::test_detect_communities", "tests/test_relationship_analyzer.py::TestRelationshipNetworkAnalyzer::test_full_analysis"]