/**
 * 🔧 [类型定义] 故事相关类型定义
 * 统一前后端的故事数据类型，支持新旧格式兼容
 */

import {
  StoryBibleResponse,
  ChapterResponse,
  StoryGenre,
  AIProvider,
  GenerationStatus
} from './backend';

// ===== 前端兼容类型 =====

/**
 * 故事圣经类型 - 兼容旧格式
 */
export interface StoryBible {
  id: string;                    // 改为string以匹配后端
  title: string;
  concept?: string;              // 兼容旧字段
  content: string;
  model: string;                 // 兼容旧字段
  usage?: any;                   // 兼容旧字段
  created_at: string;
  
  // 新增字段以匹配后端
  genre?: StoryGenre;
  theme?: string;
  protagonist?: string;
  setting?: string;
  plot_outline?: string;
  character_profiles?: string;
  world_building?: string;
  plot_structure?: string;
  writing_guidelines?: string;
  updated_at?: string;
}

/**
 * 章节类型 - 兼容旧格式
 */
export interface Chapter {
  id: string;                    // 改为string以匹配后端
  story_bible_id: string;        // 改为string以匹配后端
  chapter_number: number;
  title?: string;
  outline?: string;              // 兼容旧字段
  content: string;
  model?: string;                // 兼容旧字段
  usage?: any;                   // 兼容旧字段
  created_at: string;
  
  // 新增字段以匹配后端
  summary?: string;
  updated_at?: string;
}

// ===== 转换工具函数 =====

/**
 * 将后端StoryBibleResponse转换为前端StoryBible格式
 */
export function convertStoryBibleResponse(response: StoryBibleResponse): StoryBible {
  return {
    id: response.id,
    title: response.request_data.title,
    concept: response.request_data.theme, // 映射主题到概念
    content: response.generated_content || '',
    model: response.ai_provider,
    usage: response.generation_stats,
    created_at: response.created_at,
    
    // 新字段
    genre: response.request_data.genre,
    theme: response.request_data.theme,
    protagonist: response.request_data.protagonist,
    setting: response.request_data.setting,
    plot_outline: response.request_data.plot_outline,
    character_profiles: response.character_profiles ? JSON.stringify(response.character_profiles) : undefined,
    world_building: response.world_building,
    plot_structure: response.plot_structure,
    writing_guidelines: response.writing_guidelines,
    updated_at: response.updated_at
  };
}

/**
 * 将后端ChapterResponse转换为前端Chapter格式
 */
export function convertChapterResponse(response: ChapterResponse): Chapter {
  return {
    id: response.id,
    story_bible_id: response.request_data.story_bible_id,
    chapter_number: response.request_data.chapter_number,
    title: response.request_data.chapter_title,
    outline: response.request_data.chapter_outline,
    content: response.generated_content || '',
    model: response.ai_provider,
    usage: response.generation_stats,
    created_at: response.created_at,
    
    // 新字段
    summary: response.chapter_summary,
    updated_at: response.updated_at
  };
}

/**
 * 将前端StoryBible转换为后端请求格式
 */
export function convertToStoryBibleRequest(story: Partial<StoryBible> & {
  title: string;
  theme: string;
  protagonist: string;
  setting: string;
  plot_outline: string;
}): import('./backend').StoryBibleRequest {
  return {
    title: story.title,
    genre: story.genre || StoryGenre.FANTASY,
    theme: story.theme,
    protagonist: story.protagonist,
    setting: story.setting,
    plot_outline: story.plot_outline,
    target_audience: undefined,
    writing_style: undefined,
    ai_provider: (story.model as AIProvider) || AIProvider.ZHIPU,
    temperature: 0.8,
    max_tokens: 3000
  };
}

/**
 * 将前端Chapter转换为后端请求格式
 */
export function convertToChapterRequest(chapter: Partial<Chapter> & {
  story_bible_id: string;
  chapter_number: number;
  title: string;
  outline: string;
}): import('./backend').ChapterGenerationRequest {
  return {
    story_bible_id: chapter.story_bible_id,
    chapter_number: chapter.chapter_number,
    chapter_title: chapter.title,
    chapter_outline: chapter.outline,
    target_word_count: 2000,
    ai_provider: (chapter.model as AIProvider) || AIProvider.ZHIPU,
    temperature: 0.8,
    max_tokens: 4000
  };
}

// ===== 扩展类型 =====

/**
 * 创作请求类型
 */
export interface CreationRequest {
  userPrompt: string;
  selectedAIProvider: AIProvider;
  temperature?: number;
  maxTokens?: number;
}

/**
 * 生成状态类型
 */
export interface GenerationState {
  isGenerating: boolean;
  currentStep?: string;
  progress?: number;
  error?: string;
  taskId?: string;
  status?: GenerationStatus;
}

/**
 * 编辑状态类型
 */
export interface EditingState {
  currentEditingChapter: string | null;
  hasUnsavedChanges: boolean;
  lastSavedAt: string | null;
  editingSection?: string;
}

/**
 * 历史记录类型
 */
export interface HistoryRecord {
  action: string;
  timestamp: string;
  data?: any;
  taskId?: string;
  status?: GenerationStatus;
}

/**
 * 故事统计类型
 */
export interface StoryStats {
  totalWords: number;
  totalChapters: number;
  averageChapterLength: number;
  lastUpdated: string;
  generationTime?: number;
  aiUsage?: {
    totalTokens: number;
    totalCost?: number;
    providerBreakdown: Record<string, number>;
  };
}

// ===== 验证函数 =====

/**
 * 验证故事圣经数据完整性
 */
export function validateStoryBible(story: Partial<StoryBible>): story is StoryBible {
  return !!(
    story.id &&
    story.title &&
    story.content &&
    story.created_at
  );
}

/**
 * 验证章节数据完整性
 */
export function validateChapter(chapter: Partial<Chapter>): chapter is Chapter {
  return !!(
    chapter.id &&
    chapter.story_bible_id &&
    typeof chapter.chapter_number === 'number' &&
    chapter.content &&
    chapter.created_at
  );
}

/**
 * 获取故事类型的中文名称
 */
export function getGenreDisplayName(genre: StoryGenre): string {
  const genreNames: Record<StoryGenre, string> = {
    [StoryGenre.ROMANCE]: '言情',
    [StoryGenre.FANTASY]: '奇幻',
    [StoryGenre.URBAN]: '都市',
    [StoryGenre.HISTORICAL]: '历史',
    [StoryGenre.MYSTERY]: '悬疑',
    [StoryGenre.SCIFI]: '科幻',
    [StoryGenre.MARTIAL_ARTS]: '武侠',
    [StoryGenre.THRILLER]: '惊悚'
  };
  
  return genreNames[genre] || '未知';
}

/**
 * 获取AI提供商的中文名称
 */
export function getAIProviderDisplayName(provider: AIProvider): string {
  const providerNames: Record<AIProvider, string> = {
    [AIProvider.ZHIPU]: '智谱AI',
    [AIProvider.KIMI]: 'Kimi'
  };
  
  return providerNames[provider] || '未知';
}

/**
 * 获取生成状态的中文名称
 */
export function getStatusDisplayName(status: GenerationStatus): string {
  const statusNames: Record<GenerationStatus, string> = {
    [GenerationStatus.PENDING]: '等待中',
    [GenerationStatus.GENERATING]: '生成中',
    [GenerationStatus.COMPLETED]: '已完成',
    [GenerationStatus.FAILED]: '失败'
  };
  
  return statusNames[status] || '未知';
}
