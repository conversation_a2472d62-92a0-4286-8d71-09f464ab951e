"""
🎨 [文学风格] 文学风格增强引擎主控制器
整合语言风格注入器、性格驱动系统和爽感机制，提供统一的文学风格增强服务
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from app.core.config import log_info, log_debug, log_error, log_success
from app.core.style_injector import (
    LanguageStyleInjector, StyleType, StyleInjectionResult, create_style_injector
)
from app.core.personality_driver import (
    PersonalityDriverSystem, ZodiacSign, PersonalityTrait, PersonalityDrivenResult, 
    create_personality_driver
)
from app.core.pleasure_engine import (
    PleasureEngine, PleasureType, EmotionalIntensity, PleasureEngineResult,
    create_pleasure_engine
)


class StyleEnhancementMode(str, Enum):
    """风格增强模式"""
    LIGHT = "轻度增强"      # 保持原文风格，轻微增强
    MODERATE = "中度增强"   # 明显的风格改变
    HEAVY = "重度增强"      # 大幅度风格转换
    EXTREME = "极致增强"    # 完全风格化改造


@dataclass
class LiteraryStyleConfig:
    """文学风格配置"""
    # 语言风格配置
    style_types: List[StyleType]
    style_intensity: float
    
    # 性格驱动配置
    zodiac_sign: Optional[ZodiacSign]
    personality_traits: Optional[List[PersonalityTrait]]
    personality_intensity: float
    
    # 爽感机制配置
    pleasure_types: List[PleasureType]
    pleasure_intensity: EmotionalIntensity
    rhythm_template: Optional[str]
    
    # 整体配置
    enhancement_mode: StyleEnhancementMode
    preserve_meaning: bool
    target_audience: str  # 目标读者群体


@dataclass
class LiteraryStyleResult:
    """文学风格增强结果"""
    success: bool
    original_text: str
    enhanced_text: str
    
    # 各组件结果
    style_injection_result: Optional[StyleInjectionResult]
    personality_driven_result: Optional[PersonalityDrivenResult]
    pleasure_engine_result: Optional[PleasureEngineResult]
    
    # 综合评估
    overall_enhancement_score: float
    style_consistency_score: float
    readability_score: float
    
    # 处理详情
    processing_pipeline: List[str]
    enhancement_details: Dict[str, Any]
    performance_metrics: Dict[str, Any]


class LiteraryStyleEngine:
    """
    🎨 [文学风格引擎] 文学风格增强引擎主控制器
    
    整合语言风格注入、性格驱动和爽感机制三大组件，
    提供统一的文学风格增强服务，支持多种增强模式和配置。
    """
    
    def __init__(self):
        """初始化文学风格引擎"""
        # 初始化各个组件
        self.style_injector = create_style_injector()
        self.personality_driver = create_personality_driver()
        self.pleasure_engine = create_pleasure_engine()
        
        # 预设配置模板
        self.preset_configs = {
            "网文爽文": LiteraryStyleConfig(
                style_types=[StyleType.CREATIVE_INSULT, StyleType.SARCASM],
                style_intensity=0.6,
                zodiac_sign=ZodiacSign.LEO,  # 狮子座：自信张扬
                personality_traits=None,
                personality_intensity=0.7,
                pleasure_types=[PleasureType.FACE_SLAPPING, PleasureType.POWER_FANTASY],
                pleasure_intensity=EmotionalIntensity.HIGH,
                rhythm_template="经典三段式",
                enhancement_mode=StyleEnhancementMode.HEAVY,
                preserve_meaning=True,
                target_audience="网文读者"
            ),
            
            "古风仙侠": LiteraryStyleConfig(
                style_types=[StyleType.ANCIENT_CHINESE, StyleType.POETRY_ADAPTATION],
                style_intensity=0.8,
                zodiac_sign=ZodiacSign.VIRGO,  # 处女座：追求完美
                personality_traits=[PersonalityTrait.RATIONAL, PersonalityTrait.CONSERVATIVE],
                personality_intensity=0.5,
                pleasure_types=[PleasureType.UPGRADE, PleasureType.RECOGNITION],
                pleasure_intensity=EmotionalIntensity.MEDIUM,
                rhythm_template="渐进式爽感",
                enhancement_mode=StyleEnhancementMode.MODERATE,
                preserve_meaning=True,
                target_audience="仙侠小说读者"
            ),
            
            "都市言情": LiteraryStyleConfig(
                style_types=[StyleType.REGIONAL_FLAVOR, StyleType.DIALECT],
                style_intensity=0.4,
                zodiac_sign=ZodiacSign.CANCER,  # 巨蟹座：情感丰富
                personality_traits=[PersonalityTrait.EMOTIONAL, PersonalityTrait.OPTIMISTIC],
                personality_intensity=0.6,
                pleasure_types=[PleasureType.ROMANCE, PleasureType.WEALTH],
                pleasure_intensity=EmotionalIntensity.MEDIUM,
                rhythm_template="波浪式节奏",
                enhancement_mode=StyleEnhancementMode.LIGHT,
                preserve_meaning=True,
                target_audience="言情小说读者"
            ),
            
            "悬疑推理": LiteraryStyleConfig(
                style_types=[StyleType.ANCIENT_CHINESE],
                style_intensity=0.3,
                zodiac_sign=ZodiacSign.SCORPIO,  # 天蝎座：神秘深沉
                personality_traits=[PersonalityTrait.RATIONAL, PersonalityTrait.INTROVERT],
                personality_intensity=0.4,
                pleasure_types=[PleasureType.MYSTERY_SOLVING],
                pleasure_intensity=EmotionalIntensity.MEDIUM,
                rhythm_template="波浪式节奏",
                enhancement_mode=StyleEnhancementMode.LIGHT,
                preserve_meaning=True,
                target_audience="推理小说读者"
            )
        }
        
        log_info("文学风格", "文学风格增强引擎初始化完成",
                预设配置数=len(self.preset_configs),
                组件状态="已加载")
    
    async def enhance_literary_style(
        self,
        text: str,
        config: LiteraryStyleConfig
    ) -> LiteraryStyleResult:
        """
        🎨 [风格增强] 执行文学风格增强
        
        Args:
            text: 原始文本
            config: 风格配置
            
        Returns:
            LiteraryStyleResult: 增强结果
        """
        log_info("文学风格", "开始文学风格增强",
                文本长度=len(text),
                增强模式=config.enhancement_mode.value,
                目标读者=config.target_audience)
        
        try:
            enhanced_text = text
            processing_pipeline = []
            enhancement_details = {}
            performance_metrics = {}
            
            # 记录开始时间
            import time
            start_time = time.time()
            
            # 1. 语言风格注入
            style_result = None
            if config.style_types:
                log_debug("文学风格", "执行语言风格注入", 风格类型数=len(config.style_types))
                style_result = await self.style_injector.inject_style(
                    enhanced_text,
                    config.style_types,
                    config.style_intensity,
                    config.preserve_meaning
                )
                if style_result.success:
                    enhanced_text = style_result.styled_text
                    processing_pipeline.append("语言风格注入")
                    enhancement_details["style_injection"] = style_result.processing_details
            
            # 2. 性格驱动处理
            personality_result = None
            if config.zodiac_sign or config.personality_traits:
                log_debug("文学风格", "执行性格驱动处理",
                         星座=config.zodiac_sign.value if config.zodiac_sign else "未指定")
                personality_result = await self.personality_driver.apply_personality_drive(
                    enhanced_text,
                    config.zodiac_sign,
                    config.personality_traits,
                    config.personality_intensity
                )
                if personality_result.success:
                    enhanced_text = personality_result.personality_driven_text
                    processing_pipeline.append("性格驱动处理")
                    enhancement_details["personality_drive"] = personality_result.processing_details
            
            # 3. 爽感机制增强
            pleasure_result = None
            if config.pleasure_types:
                log_debug("文学风格", "执行爽感机制增强", 爽感类型数=len(config.pleasure_types))
                pleasure_result = await self.pleasure_engine.enhance_pleasure(
                    enhanced_text,
                    config.pleasure_types,
                    config.pleasure_intensity,
                    config.rhythm_template
                )
                if pleasure_result.success:
                    enhanced_text = pleasure_result.pleasure_enhanced_text
                    processing_pipeline.append("爽感机制增强")
                    enhancement_details["pleasure_enhancement"] = pleasure_result.enhancement_details
            
            # 4. 计算综合评估分数
            overall_score = await self._calculate_overall_score(
                style_result, personality_result, pleasure_result
            )
            
            consistency_score = await self._calculate_consistency_score(
                text, enhanced_text, config
            )
            
            readability_score = await self._calculate_readability_score(
                enhanced_text
            )
            
            # 记录性能指标
            end_time = time.time()
            performance_metrics = {
                "processing_time": end_time - start_time,
                "text_length_change": len(enhanced_text) - len(text),
                "enhancement_ratio": len(enhanced_text) / len(text) if text else 1.0,
                "pipeline_steps": len(processing_pipeline)
            }
            
            log_success("文学风格", "文学风格增强完成",
                       处理步骤数=len(processing_pipeline),
                       综合评分=f"{overall_score:.2f}",
                       处理时间=f"{performance_metrics['processing_time']:.2f}秒")
            
            return LiteraryStyleResult(
                success=True,  # 修复：只要没有异常就算成功，即使没有找到增强点
                original_text=text,
                enhanced_text=enhanced_text,
                style_injection_result=style_result,
                personality_driven_result=personality_result,
                pleasure_engine_result=pleasure_result,
                overall_enhancement_score=overall_score,
                style_consistency_score=consistency_score,
                readability_score=readability_score,
                processing_pipeline=processing_pipeline,
                enhancement_details=enhancement_details,
                performance_metrics=performance_metrics
            )
            
        except Exception as e:
            log_error("文学风格", "文学风格增强失败", error=e)
            return LiteraryStyleResult(
                success=False,
                original_text=text,
                enhanced_text=text,
                style_injection_result=None,
                personality_driven_result=None,
                pleasure_engine_result=None,
                overall_enhancement_score=0.0,
                style_consistency_score=0.0,
                readability_score=0.0,
                processing_pipeline=[],
                enhancement_details={"error": str(e)},
                performance_metrics={}
            )
    
    async def enhance_with_preset(
        self,
        text: str,
        preset_name: str,
        custom_overrides: Optional[Dict[str, Any]] = None
    ) -> LiteraryStyleResult:
        """
        🎯 [预设增强] 使用预设配置进行风格增强
        
        Args:
            text: 原始文本
            preset_name: 预设配置名称
            custom_overrides: 自定义覆盖参数
            
        Returns:
            LiteraryStyleResult: 增强结果
        """
        if preset_name not in self.preset_configs:
            raise ValueError(f"未找到预设配置: {preset_name}")
        
        config = self.preset_configs[preset_name]
        
        # 应用自定义覆盖
        if custom_overrides:
            for key, value in custom_overrides.items():
                if hasattr(config, key):
                    setattr(config, key, value)
        
        log_info("文学风格", f"使用预设配置进行增强: {preset_name}",
                自定义覆盖数=len(custom_overrides) if custom_overrides else 0)
        
        return await self.enhance_literary_style(text, config)

    async def _calculate_overall_score(
        self,
        style_result: Optional[StyleInjectionResult],
        personality_result: Optional[PersonalityDrivenResult],
        pleasure_result: Optional[PleasureEngineResult]
    ) -> float:
        """计算综合增强评分"""
        scores = []

        if style_result and style_result.success:
            scores.append(style_result.style_intensity * 10)

        if personality_result and personality_result.success:
            scores.append(personality_result.personality_intensity * 10)

        if pleasure_result and pleasure_result.success:
            scores.append(pleasure_result.overall_pleasure_score)

        return sum(scores) / len(scores) if scores else 0.0

    async def _calculate_consistency_score(
        self,
        original_text: str,
        enhanced_text: str,
        config: LiteraryStyleConfig
    ) -> float:
        """计算风格一致性评分"""
        # 简化的一致性评分算法
        length_ratio = len(enhanced_text) / len(original_text) if original_text else 1.0

        # 理想的长度增长比例（根据增强模式）
        ideal_ratios = {
            StyleEnhancementMode.LIGHT: 1.1,
            StyleEnhancementMode.MODERATE: 1.3,
            StyleEnhancementMode.HEAVY: 1.5,
            StyleEnhancementMode.EXTREME: 2.0
        }

        ideal_ratio = ideal_ratios[config.enhancement_mode]
        ratio_deviation = abs(length_ratio - ideal_ratio) / ideal_ratio

        # 一致性分数：偏差越小分数越高
        consistency_score = max(0.0, 10.0 - ratio_deviation * 10)

        return consistency_score

    async def _calculate_readability_score(self, text: str) -> float:
        """计算可读性评分"""
        if not text:
            return 0.0

        # 简化的可读性评分
        sentences = text.split('。')
        if not sentences:
            return 0.0

        # 平均句长
        avg_sentence_length = len(text) / len(sentences)

        # 理想句长范围：15-30字
        if 15 <= avg_sentence_length <= 30:
            length_score = 10.0
        elif avg_sentence_length < 15:
            length_score = 5.0 + (avg_sentence_length / 15) * 5
        else:
            length_score = max(0.0, 10.0 - (avg_sentence_length - 30) * 0.2)

        # 标点符号多样性
        punctuation_variety = len(set(c for c in text if c in '。！？，；：'))
        variety_score = min(10.0, punctuation_variety * 2)

        # 综合可读性分数
        readability_score = (length_score + variety_score) / 2

        return readability_score

    def get_available_presets(self) -> List[str]:
        """获取可用的预设配置列表"""
        return list(self.preset_configs.keys())

    def get_preset_description(self, preset_name: str) -> Dict[str, Any]:
        """获取预设配置的详细描述"""
        if preset_name not in self.preset_configs:
            return {}

        config = self.preset_configs[preset_name]
        return {
            "name": preset_name,
            "target_audience": config.target_audience,
            "enhancement_mode": config.enhancement_mode.value,
            "style_types": [st.value for st in config.style_types],
            "zodiac_sign": config.zodiac_sign.value if config.zodiac_sign else None,
            "pleasure_types": [pt.value for pt in config.pleasure_types],
            "description": self._get_preset_description_text(preset_name)
        }

    def _get_preset_description_text(self, preset_name: str) -> str:
        """获取预设配置的文字描述"""
        descriptions = {
            "网文爽文": "适合网络爽文，强调打脸、装逼、升级等爽点，语言直接有力，节奏紧凑",
            "古风仙侠": "适合仙侠修真类小说，融入古文韵味和诗词元素，追求意境和格调",
            "都市言情": "适合现代都市言情小说，语言贴近生活，情感细腻，注重人物关系",
            "悬疑推理": "适合悬疑推理类小说，语言简洁理性，注重逻辑和氛围营造"
        }
        return descriptions.get(preset_name, "")


# ==================== 工厂函数 ====================

def create_literary_style_engine() -> LiteraryStyleEngine:
    """
    🏭 [工厂] 创建文学风格增强引擎实例

    Returns:
        LiteraryStyleEngine: 文学风格引擎实例
    """
    return LiteraryStyleEngine()


async def quick_style_enhancement(
    text: str,
    preset_name: str = "网文爽文",
    custom_overrides: Optional[Dict[str, Any]] = None
) -> LiteraryStyleResult:
    """
    ⚡ [快速接口] 快速文学风格增强

    Args:
        text: 原始文本
        preset_name: 预设配置名称
        custom_overrides: 自定义覆盖参数

    Returns:
        LiteraryStyleResult: 增强结果
    """
    engine = create_literary_style_engine()
    return await engine.enhance_with_preset(text, preset_name, custom_overrides)


# ==================== 配置构建器 ====================

class StyleConfigBuilder:
    """
    🔧 [配置构建器] 文学风格配置构建器

    提供链式调用接口，方便构建自定义的风格配置
    """

    def __init__(self):
        self.config = LiteraryStyleConfig(
            style_types=[],
            style_intensity=0.5,
            zodiac_sign=None,
            personality_traits=None,
            personality_intensity=0.5,
            pleasure_types=[],
            pleasure_intensity=EmotionalIntensity.MEDIUM,
            rhythm_template=None,
            enhancement_mode=StyleEnhancementMode.MODERATE,
            preserve_meaning=True,
            target_audience="通用读者"
        )

    def with_style_types(self, *style_types: StyleType) -> 'StyleConfigBuilder':
        """设置语言风格类型"""
        self.config.style_types = list(style_types)
        return self

    def with_style_intensity(self, intensity: float) -> 'StyleConfigBuilder':
        """设置语言风格强度"""
        self.config.style_intensity = intensity
        return self

    def with_zodiac_sign(self, zodiac: ZodiacSign) -> 'StyleConfigBuilder':
        """设置星座"""
        self.config.zodiac_sign = zodiac
        return self

    def with_personality_traits(self, *traits: PersonalityTrait) -> 'StyleConfigBuilder':
        """设置性格特征"""
        self.config.personality_traits = list(traits)
        return self

    def with_pleasure_types(self, *pleasure_types: PleasureType) -> 'StyleConfigBuilder':
        """设置爽感类型"""
        self.config.pleasure_types = list(pleasure_types)
        return self

    def with_enhancement_mode(self, mode: StyleEnhancementMode) -> 'StyleConfigBuilder':
        """设置增强模式"""
        self.config.enhancement_mode = mode
        return self

    def with_target_audience(self, audience: str) -> 'StyleConfigBuilder':
        """设置目标读者"""
        self.config.target_audience = audience
        return self

    def build(self) -> LiteraryStyleConfig:
        """构建配置对象"""
        return self.config
