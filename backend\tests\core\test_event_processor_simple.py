"""
🧪 [测试] 动态事件处理器简化测试
测试基本的数据库操作功能
"""

import pytest
import json
from unittest.mock import patch, AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.event_processor import EventProcessor
from app.models.story_bible import StoryBible, AIProvider
from app.models.world_graph import Entity, EntityType
from app.services.zhipu_client import ChatCompletionResponse
from app.core.config import log_debug


class TestEventProcessorSimple:
    """事件处理器简化测试类"""
    
    @pytest.mark.asyncio
    async def test_find_entity_by_name(self, async_db_session: AsyncSession):
        """测试根据名称查找实体"""
        # 创建测试故事
        story = StoryBible(
            id="test_story_simple",
            title="简化测试小说",
            genre="fantasy",
            theme="测试主题",
            protagonist="张三",
            setting="测试背景",
            plot_outline="测试大纲",
            target_audience="adult",
            ai_provider=AIProvider.ZHIPU
        )
        
        async_db_session.add(story)
        await async_db_session.commit()
        
        # 创建测试实体
        entity = Entity(
            id="test_entity_001",
            story_id=story.id,
            name="张三",
            type=EntityType.CHARACTER,
            description="测试角色",
            properties={"年龄": 25},
            first_mentioned_chapter=1,
            is_active=True
        )
        
        async_db_session.add(entity)
        await async_db_session.commit()
        
        # 测试查找实体
        processor = EventProcessor()
        found_entity_id = await processor._find_entity_by_name(
            async_db_session, story.id, "张三"
        )
        
        assert found_entity_id == entity.id
        log_debug("简化测试", "实体查找测试通过", entity_id=found_entity_id)
    
    @pytest.mark.asyncio
    async def test_create_entity_directly(self, async_db_session: AsyncSession):
        """测试直接创建实体"""
        # 创建测试故事
        story = StoryBible(
            id="test_story_create",
            title="创建测试小说",
            genre="fantasy",
            theme="测试主题",
            protagonist="主角",
            setting="测试背景",
            plot_outline="测试大纲",
            target_audience="adult",
            ai_provider=AIProvider.ZHIPU
        )
        
        async_db_session.add(story)
        await async_db_session.commit()
        
        # 测试创建实体的事件数据
        from app.core.event_processor import CreateEntityEventData
        
        event_data = CreateEntityEventData(
            name="李四",
            type="character",
            description="测试角色",
            properties={"年龄": 30},
            first_mentioned_chapter=1
        )
        
        # 测试创建实体
        processor = EventProcessor()
        success = await processor._process_create_entity_event(
            event_data, story.id, async_db_session
        )
        
        assert success is True
        
        # 验证实体是否创建成功
        found_entity_id = await processor._find_entity_by_name(
            async_db_session, story.id, "李四"
        )
        
        assert found_entity_id is not None
        log_debug("简化测试", "实体创建测试通过", entity_id=found_entity_id)
    
    @pytest.mark.asyncio
    async def test_create_relationship_directly(self, async_db_session: AsyncSession):
        """测试直接创建关系"""
        # 创建测试故事
        story = StoryBible(
            id="test_story_relationship",
            title="关系测试小说",
            genre="fantasy",
            theme="测试主题",
            protagonist="主角",
            setting="测试背景",
            plot_outline="测试大纲",
            target_audience="adult",
            ai_provider=AIProvider.ZHIPU
        )
        
        async_db_session.add(story)
        await async_db_session.commit()
        
        # 创建两个测试实体
        entity1 = Entity(
            id="test_entity_zhang",
            story_id=story.id,
            name="张三",
            type=EntityType.CHARACTER,
            description="测试角色1",
            properties={"年龄": 25},
            first_mentioned_chapter=1,
            is_active=True
        )
        
        entity2 = Entity(
            id="test_entity_li",
            story_id=story.id,
            name="李四",
            type=EntityType.CHARACTER,
            description="测试角色2",
            properties={"年龄": 30},
            first_mentioned_chapter=1,
            is_active=True
        )
        
        async_db_session.add(entity1)
        async_db_session.add(entity2)
        await async_db_session.commit()
        
        # 测试创建关系的事件数据
        from app.core.event_processor import CreateRelationshipEventData
        
        event_data = CreateRelationshipEventData(
            source_entity="张三",
            target_entity="李四",
            relationship_type="朋友",
            description="测试关系",
            properties={"友谊程度": "深厚"},
            established_chapter=1
        )
        
        # 测试创建关系
        processor = EventProcessor()
        success = await processor._process_create_relationship_event(
            event_data, story.id, async_db_session
        )
        
        assert success is True
        log_debug("简化测试", "关系创建测试通过")
    
    @pytest.mark.asyncio
    async def test_extract_events_mock(self):
        """测试事件提取（使用Mock）"""
        # Mock AI响应
        mock_ai_response = json.dumps([
            {
                "event": "create_entity",
                "data": {
                    "name": "王五",
                    "type": "character",
                    "description": "测试角色",
                    "properties": {"年龄": 40},
                    "first_mentioned_chapter": 1
                }
            }
        ])
        
        mock_response = ChatCompletionResponse(
            id="test_extract_id",
            object="chat.completion",
            created=1234567890,
            model="glm-4.5-flash",
            choices=[{
                "message": {"content": mock_ai_response},
                "finish_reason": "stop"
            }],
            usage={"total_tokens": 100}
        )
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            processor = EventProcessor()
            events = await processor.extract_events_from_chapter(
                "测试章节文本", "test_story", 1
            )
            
            assert len(events) == 1
            assert events[0].event == "create_entity"
            assert events[0].data["name"] == "王五"
            log_debug("简化测试", "事件提取测试通过", events_count=len(events))
