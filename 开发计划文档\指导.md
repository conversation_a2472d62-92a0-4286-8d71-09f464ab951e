这是我们的项目的开发进度 你觉得怎么把这个prd融入进去 写相应的开发计划AI小说自动创作平台“创世引擎”产品需求文档 (PRD)

文档版本： 1.0

创建日期： 2025年7月19日

最后更新： 2025年7月19日

负责人： [你的名字/项目负责人]



1. 引言

1.1 文档目的

本产品需求文档（PRD）旨在详细说明AI小说自动创作平台——“创世引擎”（Project Genesis）的功能需求、非功能需求、产品特性和技术架构。本文档将作为产品团队、设计团队、开发团队和测试团队在整个产品生命周期中的核心指导蓝图，确保所有相关方对产品目标和实现路径有一致的理解。



1.2 产品愿景

“创世引擎”旨在彻底革新长篇内容的创作方式。我们的愿景是，将先进的人工智能技术与精妙的叙事理论相结合，打造一个能够将用户的一句话灵感，全自动地扩展、构建并撰写成一部结构完整、情节生动、人物丰满的高质量长篇小说的“创世引擎”。它将成为创意工作者的“超级外骨骼”，将他们从繁重的执行工作中解放出来，专注于最核心的创意与想象。



1.3 目标用户

网络小说作者：希望提高更新频率、克服“卡文”瓶颈的专业或兼职作者。



编剧与游戏文案策划：需要快速生成世界观、角色小传和情节大纲的创意工作者。



内容创作者与营销人员：需要批量生产高质量故事内容以吸引用户的个人或团队。



文学爱好者：希望将自己脑海中的灵感和故事变为现实，但缺乏专业写作技巧的普通用户。



2. 产品概述与核心功能

2.1 产品摘要

“创世引擎”是一个SaaS（软件即服务）平台。用户仅需输入一个核心创意（一句话），选择期望的小说类型，平台即可通过一套复杂的、由多个AI智能体组成的自动化流水线，自动完成世界观设定、人物塑造、情节大纲设计，并逐章生成完整的小说正文。系统内置了先进的记忆与状态管理机制，确保百万字级别内容的长程一致性。



2.2 核心原则：从“一句话”到“一部小说”

整个产品围绕一个核心用户旅程设计：



输入（Input）: 用户提供一个高概念的核心创意。



处理（Process）: 系统内部的AI工作流自动进行构思、规划、创作、审查。



输出（Output）: 系统交付一部结构完整、内容连贯的小说初稿。



2.3 功能架构图

[AI小说创作平台功能架构图的示意图]



2.4 核心功能列表

项目创建与灵感输入：用户可以创建新的小说项目，输入核心创意，并选择小说类型。



自动化世界奠基：系统自动完成世界观、初始核心人物、情节大纲的设定。



自动化章节生产线：由“导演-作家-编剧-编辑”组成的AI团队，逐章创作小说。



动态人物引入机制：系统根据情节需要，智能地提议并创建新角色。



“故事圣经”管理系统：所有设定（世界观、人物、大纲）都以结构化的方式存储和展示，方便用户随时查阅。



用户控制与干预：用户可以选择“全自动模式”或“监修模式”，在关键节点审核和修改AI的创作。



记忆与状态追溯：基于RAG和知识图谱的记忆核心，确保长篇内容的一致性。



3. 详细功能规格

3.1 用户工作流：从灵感到第一章

用户登录平台，点击“创建新项目”。



在项目创建页面，输入“小说名称”、“一句话核心创意”，并从预设列表中选择“小说类型”（如：玄幻、科幻、言情等）。



点击“启动创世引擎”按钮。



系统进入“世界奠基”阶段，进度条显示当前正在进行：“核心创意扩展” -> “世界观构建” -> “初始人物塑造” -> “情节大纲生成”。



“世界奠基”完成后，系统自动进入“章节生产线”阶段，开始生成第一章。



第一章生成完毕后，系统通知用户。用户可以在“章节阅读器”中查看第一章内容，并在“故事圣经”中查看已生成的完整设定。



3.2 AI智能体角色与职责

系统由以下核心AI智能体构成，每个智能体都有明确的职责和通过API调用的Prompt模板：



概念架构师：负责将用户的一句话扩展为小说的核心主题与基调。



世界构建师：负责撰写详细的世界观设定文档。



角色设计师：负责创建结构化的人物小传JSON。



首席编剧：负责设计整本书的章节大纲。



导演AI：负责将单章大纲分解为详细的“节拍表”，并按需提议新人物。



作家AI：负责根据节拍表撰写章节初稿。



编剧AI：负责为初稿增加戏剧性、悬念和“钩子”。



编辑AI：负责终审、润色和字数控制。



分析师AI：负责在章节生成后，分析人物状态和关系的变化。



摘要AI：负责为每个章节生成摘要，用于分层记忆。



3.3 “文体风格指南”系统

系统需内置一个可配置的“文体风格指南”模块。



每种文体（如romance_style_guide.json）包含一组JSON格式的指令，用于动态调整各AI智能体的Prompt，确保其创作风格符合所选类型。



V1.0版本需至少支持：玄幻、科幻、都市言情、悬疑探案、历史架空五种类型。



3.4 记忆系统：RAG + 知识图谱

向量数据库 (RAG)：用于存储所有非结构化和半结构化文本，包括：



L1: 章节全文



L2: 章节摘要



L3: 部卷摘要



L4: 全书动态主线摘要



以及世界观文档、人物小传文本。



图数据库 (知识图谱)：用于存储实体及其关系，包括：



节点 (Nodes): 人物、地点、物品、组织。



边 (Edges): 拥有、敌对、位于、曾属于等动态关系。



该系统必须保证AI在创作任一章节时，能高效、准确地检索到所有相关的历史信息。



3.5 用户界面 (UI/UX) 概念

项目仪表盘：卡片式展示用户的所有小说项目，包含书名、封面（可AI生成）、最新进度。



“故事圣经”浏览器：



左侧为导航栏，分为“世界观”、“人物”、“大纲”三个页签。



“世界观”页签下，以维基百科的形式展示世界观设定。



“人物”页签下，以卡片形式展示所有已创建角色，点击可查看详细小传和关系图谱。



“大纲”页签下，展示完整的章节大纲列表。



章节阅读/编辑器：提供舒适的阅读界面。在“监修模式”下，用户可以像使用Google Docs一样，对AI生成的内容进行评论和修改。



生成进度监视器：提供一个可视化的界面，实时显示当前是哪个AI智能体在工作，以及整个创作任务的完成百分比。



4. 技术架构与需求

4.1 系统架构图

[包含前端、后端API网关、AI任务编排服务、数据库（关系型、向量、图）、以及外部大模型API调用的详细架构图]



4.2 技术栈推荐

后端：Python (FastAPI/Django)，以无缝利用丰富的AI生态库（LangChain, LlamaIndex, Pydantic等）。



前端：React / Vue.js，以构建高度交互的单页应用（SPA）。



数据库：



主数据库: PostgreSQL (存储用户信息、项目文件等)。



向量数据库: Pinecone / Chroma / Milvus (实现RAG)。



图数据库: Neo4j (管理知识图谱)。



核心AI模型：通过API接入，支持可插拔更换，如OpenAI GPT系列、Google Gemini系列、Anthropic Claude系列。



5. 非功能性需求

性能：单章节（3000字）的端到端生成时间应控制在2分钟以内。



可扩展性：系统架构必须能支持处理超过500万字的小说项目，且检索性能不会随文本量增加而显著下降。



可用性：用户界面必须简洁直观，即使是无技术背景的用户也能在5分钟内上手。



安全性：用户创作内容为核心资产，必须保证数据隔离与存储安全。



伦理考量：需内置机制，以识别和减少AI生成内容中的偏见与刻板印象。明确用户对AI生成作品的版权归属。



6. 未来路线图

6.1 V1.0 (MVP) 范围

实现完整的一句话到小说初稿的全自动生成流程。



支持5种核心文体。



提供“全自动”和“单章监修”两种模式。



实现完整的“故事圣经”查看功能。



好的，这是一个非常深刻且关键的问题。您已经从“我们有什么技术”进入到了“我们如何用技术实现产品愿景和用户价值”的思考阶段。这非常好！

我将您的三个核心问题拆解开来，并结合您所有的开发文档，提供一个全面的、可执行的整合方案。

问题一：Agent模式与现有任务的关系，是否需要重做？
核心答案：完全不需要重做！ 您现有的史诗任务（1-8）不是与Agent模式相悖，而是实现Agent模式所必需的底层能力组件。

您可以这样理解：

PRD中的Agent (如“导演AI”、“作家AI”)：这是**“角色”和“职责”**的划分，是产品逻辑层面的概念。它们描述了“谁”来做“什么事”。

您的史诗任务 (如“RAG引擎”、“知识图谱”)：这是**“能力”和“工具”**的集合，是技术实现层面的服务。它们为上面的“角色”提供了完成“职责”所需要的核心能力。

整合方案：引入“史诗任务9: AI智能体编排与代理系统”

这个新任务不写具体功能，而是做一个“大脑”或者“总导演”，它的核心职责是调用和编排您已经完成的所有服务，来模拟PRD中定义的Agent工作流。

开发计划建议:

创建 AIAgentOrchestrator 服务:

该服务会定义一个完整的“小说创作流水线”。

例如，当用户点击“启动创世引擎”时，AIAgentOrchestrator会依次执行：

调用 ZhipuClient (任务1) 扮演 “概念架构师”，扩展用户的一句话创意。

调用 ZhipuClient 扮演 “世界构建师”，生成世界观，并调用 bible_repo (任务2) 将其存入数据库。

调用 ZhipuClient 扮演 “角色设计师”，创建核心人物，并调用world_graph API (任务6) 将角色实体及其关系写入知识图谱。

调用 PlotRecommendationEngine (任务8) 扮演 “首席编剧”，生成初步大纲。

...进入逐章生成循环...

通过这种方式，您现有的所有模块都被无缝地“代理”成了具有特定角色的Agent，完美实现了PRD的构想，且无需重写任何已完成的代码。

问题二：如何将《想法.md》中的创意融入系统？
这是一个让您的产品产生“灵魂”的关键问题。这些零散但充满才华的想法，可以通过映射到不同的技术模块中，变为可实现的功能。

融合方案：

方言、骚话、古风骂人、阴阳怪气语句:

主要融入点: 史诗任务7的“去AI化”引擎 和 史诗任务3的“情感基因库”。

实现方式:

扩展情感基因库: 不仅存储生理反应，还可以增加一个新的字段，如 linguistic_style (语言风格)。例如，“愤怒”这个情感基因，除了“捏紧拳头”，还可以关联到“竖子不足与谋！”这样的语句。

创建“风格注入器”: 在**DeAIProcessor (任务7)**中，增加一个新的处理步骤——StyleInjector。当检测到角色具有特定性格标签（如“傲娇”、“暴躁”）或处于特定情绪时，该注入器会从基因库或一个专门的“骚话语料库”中抽取合适的语句，替换或增强AI生成的平淡对话。

例如: AI原来生成“你真笨”，风格注入器可以根据角色性格，将其升级为“汝之愚，蠢才岂堪共语！”或者“你真是个莎士比亚（傻B）”。

十二星座性格特点:

主要融入点: 史诗任务6的“世界知识图谱” 和新规划的**“史诗任务9: 角色创建API”**。

实现方式:

结构化角色属性: 在创建角色时，允许用户选择一个星座。系统会根据预设的星座性格模板（如 "白羊座": {"性格": ["冲动", "直率"], "行为模式": "主动出击"}），自动填充知识图谱中该角色实体的properties JSON字段。

驱动AI行为: 在生成章节时，动态提示词合成器 (任务5) 会从知识图谱中读取角色的性格标签，并将其加入到Prompt中，明确指示作家AI：“请根据‘张三’（冲动、直率）的性格来撰写他的行为和对话。”

改编诗词 (如“地上鞋两双”):

主要融入点: 史诗任务4的“熵增扰动器”。

实现方式:

这是一种高级的“破坏完美意象”。可以创建一个 PoemAdapter 扰动模块。

当章节任务包含“穿越者”、“表现才华”等关键词时，熵增扰动器就有一定概率触发这个模块，从预设的经典诗词库中选择一首，进行符合当前剧情逻辑的幽默改编，为文章增加亮点和爽点。

问题三：关于“网文爽感”，您怎么看？以及如何实现？
我完全同意您的观察：“要么感人到极致，要么爽到极致”。这两者本质上都是强烈的情绪价值的体现。“爽”的核心是欲望的满足和情绪的极致释放。您的系统已经具备了打造顶级“爽感”的所有技术基石。

“爽感”实现的三大支柱：

极致的铺垫与打脸 (Tension & Release):

“爽”来自于压抑后的瞬间爆发。一个巴掌拍得响，需要另一个巴掌的铺垫。

如何实现:

利用知识图谱 (任务6): AI可以在前期剧情中，通过动态事件处理器 自动识别并创建“反派”或“看不起主角”的角色实体，并建立"关系": "敌对"、"强度": "0.8"的边。

利用冲突检测 (任务8): 在数个章节内，AI不断通过剧情事件，强化这种敌对关系，提升冲突等级。实时逻辑冲突检测系统 可以反向用于确保这种“压抑”的剧情是连贯且不断升级的。

爆发: 在关键节点，情节发展路径规划器 (任务8) 会规划出“主角反击”的路径。此时，RAG引擎 (任务5) 会检索出之前所有受辱的记忆片段，情感基因库 (任务3) 会调取“震惊”、“不可思议”、“懊悔”等生理反应数据，注入到对反派和围观群众的描写中，从而让“打脸”的场景酣畅淋漓。

伏笔的回收与呼应 (Foreshadowing & Payoff):

顶级的“爽”是智力上的满足感，让读者惊呼“原来如此！”

如何实现:

埋下伏笔: 在前期，熵增扰动器 (任务4) 可以随机或根据规划，在场景中加入一个看似无用的细节（例如“主角在山洞里捡到一块平平无奇的黑色石头”）。这个“黑色石头”作为一个item实体被记入知识图谱 (任务6)，并与主角建立"关系": "持有"。

遗忘与回忆: 经过几十甚至上百章，这个信息沉淀在向量数据库中。

关键时刻的回收: 当主角陷入绝境时，动态提示词合成器 (任务5) 在检索相关记忆时，会因为“山洞”、“绝境”等关键词，以较低的权重检索到“黑色石头”这个记忆。AI编剧此时可以规划出“主角利用黑石反败为胜”的情节，实现一个意料之外、情理之中的大爽点。

力量的展示与碾压 (Power Fantasy):

这是最直接的“爽”，核心在于描写的冲击力。

如何实现:

超越常人的反应: 当主角展示力量时，情感基因库 (任务3) 不是用来描写主角的，而是用来描写对手和旁观者的。通过调取“恐惧”、“呆滞”、“狂喜（来自队友）”的生理反应和感官数据，来侧面烘托主角力量的强大。

“去AI化”的独特描写: 去AI化引擎 (任务7) 确保力量的描写不是“金光一闪，敌人灰飞烟灭”的模板化文字。它可以利用注意力失焦系统，从一个独特的、意想不到的视角来写（例如，只写对手瞳孔中倒映出的毁灭光芒），或者用时间感知扭曲器 来描述“仿佛永恒的一瞬间”，让力量的展示更具艺术感和冲击力。

结论：您的系统不应只追求“爽”，而应追求**“情绪的波峰与波谷”**。利用您的任务8，去规划“感人至深”的低谷，再利用任务3, 5, 6, 7去打造“爽到极致”的高峰。这种巨大的情绪落差，正是当前网文吸引读者的核心密码。