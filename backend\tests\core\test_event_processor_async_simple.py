"""
🧪 [异步测试] 事件处理器异步操作简化测试
专门测试事件处理器的异步数据库操作
"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker

from app.core.base import Base
from app.models.story_bible import StoryBible, AIProvider
from app.models.world_graph import Entity, EntityType
from app.core.event_processor import EventProcessor, CreateEntityEventData, CreateRelationshipEventData
from app.core.config import log_info, log_debug


@pytest.mark.asyncio
class TestEventProcessorAsyncSimple:
    """事件处理器异步操作简化测试"""
    
    async def create_simple_test_session(self):
        """创建简单的测试数据库会话"""
        db_url = "sqlite+aiosqlite:///:memory:"
        
        engine = create_async_engine(
            db_url,
            echo=False,
            connect_args={"check_same_thread": False}
        )
        
        async_session_factory = async_sessionmaker(
            bind=engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # 创建表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        session = async_session_factory()
        return session, engine
    
    async def test_event_processor_find_entity(self):
        """测试事件处理器的查找实体功能"""
        log_info("异步测试", "开始测试事件处理器查找实体功能")
        
        session, engine = await self.create_simple_test_session()
        
        try:
            # 创建测试故事
            story = StoryBible(
                id="find_entity_story",
                title="查找实体测试小说",
                genre="fantasy",
                theme="测试主题",
                protagonist="张三",
                setting="测试背景",
                plot_outline="测试大纲",
                target_audience="adult",
                ai_provider=AIProvider.ZHIPU
            )
            
            session.add(story)
            await session.commit()
            
            # 创建测试实体
            entity = Entity(
                id="find_test_entity",
                story_id=story.id,
                name="张三",
                type=EntityType.CHARACTER,
                description="主角",
                properties={"年龄": 25},
                first_mentioned_chapter=1,
                is_active=True
            )
            
            session.add(entity)
            await session.commit()
            
            # 测试事件处理器的查找功能
            processor = EventProcessor()
            found_entity_id = await processor._find_entity_by_name(
                session, story.id, "张三"
            )
            
            assert found_entity_id == entity.id
            log_info("异步测试", "事件处理器查找实体测试通过", entity_id=found_entity_id)
            
            # 测试查找不存在的实体
            not_found_id = await processor._find_entity_by_name(
                session, story.id, "不存在的角色"
            )
            
            assert not_found_id is None
            log_info("异步测试", "查找不存在实体测试通过")
            
        finally:
            await session.close()
            await engine.dispose()
    
    async def test_event_processor_create_entity(self):
        """测试事件处理器的创建实体功能"""
        log_info("异步测试", "开始测试事件处理器创建实体功能")
        
        session, engine = await self.create_simple_test_session()
        
        try:
            # 创建测试故事
            story = StoryBible(
                id="create_entity_story",
                title="创建实体测试小说",
                genre="fantasy",
                theme="测试主题",
                protagonist="主角",
                setting="测试背景",
                plot_outline="测试大纲",
                target_audience="adult",
                ai_provider=AIProvider.ZHIPU
            )
            
            session.add(story)
            await session.commit()
            
            # 创建实体事件数据
            event_data = CreateEntityEventData(
                name="李四",
                type="character",
                description="新角色",
                properties={"年龄": 30},
                first_mentioned_chapter=2
            )
            
            # 测试创建实体
            processor = EventProcessor()
            success = await processor._process_create_entity_event(
                event_data, story.id, session
            )
            
            assert success is True
            log_info("异步测试", "事件处理器创建实体成功")
            
            # 验证实体是否真的创建了
            found_entity_id = await processor._find_entity_by_name(
                session, story.id, "李四"
            )
            
            assert found_entity_id is not None
            log_info("异步测试", "创建的实体验证成功", entity_id=found_entity_id)
            
        finally:
            await session.close()
            await engine.dispose()
    
    async def test_event_processor_create_relationship(self):
        """测试事件处理器的创建关系功能"""
        log_info("异步测试", "开始测试事件处理器创建关系功能")
        
        session, engine = await self.create_simple_test_session()
        
        try:
            # 创建测试故事
            story = StoryBible(
                id="create_relationship_story",
                title="创建关系测试小说",
                genre="fantasy",
                theme="测试主题",
                protagonist="张三",
                setting="测试背景",
                plot_outline="测试大纲",
                target_audience="adult",
                ai_provider=AIProvider.ZHIPU
            )
            
            session.add(story)
            await session.commit()
            
            # 创建两个测试实体
            entity1 = Entity(
                id="relationship_entity_1",
                story_id=story.id,
                name="张三",
                type=EntityType.CHARACTER,
                description="角色1",
                properties={"年龄": 25},
                first_mentioned_chapter=1,
                is_active=True
            )
            
            entity2 = Entity(
                id="relationship_entity_2",
                story_id=story.id,
                name="李四",
                type=EntityType.CHARACTER,
                description="角色2",
                properties={"年龄": 30},
                first_mentioned_chapter=1,
                is_active=True
            )
            
            session.add(entity1)
            session.add(entity2)
            await session.commit()
            
            # 创建关系事件数据
            event_data = CreateRelationshipEventData(
                source_entity="张三",
                target_entity="李四",
                relationship_type="朋友",
                description="友谊关系",
                properties={"友谊程度": "深厚"},
                established_chapter=2
            )
            
            # 先验证实体是否存在
            processor = EventProcessor()
            zhang_id = await processor._find_entity_by_name(session, story.id, "张三")
            li_id = await processor._find_entity_by_name(session, story.id, "李四")

            log_debug("异步测试", "创建关系前的实体查找结果",
                     zhang_id=zhang_id, li_id=li_id)

            assert zhang_id is not None, "张三实体应该存在"
            assert li_id is not None, "李四实体应该存在"

            # 测试创建关系
            success = await processor._process_create_relationship_event(
                event_data, story.id, session
            )

            log_debug("异步测试", "关系创建结果", success=success)

            if not success:
                log_debug("异步测试", "关系创建失败，需要进一步调试")

            assert success is True, "关系创建应该成功"
            log_info("异步测试", "事件处理器创建关系成功")
            
        finally:
            await session.close()
            await engine.dispose()
    
    async def test_event_processor_singleton(self):
        """测试事件处理器单例模式"""
        from app.core.event_processor import get_event_processor
        
        processor1 = await get_event_processor()
        processor2 = await get_event_processor()
        
        assert processor1 is processor2
        assert isinstance(processor1, EventProcessor)
        
        log_info("异步测试", "事件处理器单例模式测试通过")
