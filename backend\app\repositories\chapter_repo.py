"""
💾 [数据库] 章节数据仓库
提供章节的数据访问功能
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_

from app.models.story_bible import Chapter
from app.schemas.generation import GenerationStatus, AIProvider
from app.core.config import log_info, log_error, log_debug


class ChapterRepository:
    """
    💾 [数据库] 章节数据仓库
    
    提供章节的CRUD操作和业务查询功能
    """
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create_chapter(self, chapter_data: Dict[str, Any]) -> Chapter:
        """
        💾 [数据库] 创建新章节
        
        Args:
            chapter_data: 章节数据字典
            
        Returns:
            Chapter: 创建的章节对象
        """
        log_debug("数据库", "开始创建章节", 数据=chapter_data)
        
        try:
            chapter = Chapter(**chapter_data)
            self.session.add(chapter)
            await self.session.commit()
            await self.session.refresh(chapter)
            
            log_info("数据库", "章节创建成功", 
                    章节ID=chapter.id, 
                    章节号=chapter.chapter_number,
                    标题=chapter.chapter_title)
            
            return chapter
            
        except Exception as e:
            await self.session.rollback()
            log_error("数据库", "章节创建失败", error=e, 数据=chapter_data)
            raise
    
    async def get_chapter_by_id(self, chapter_id: str) -> Optional[Chapter]:
        """
        💾 [数据库] 根据ID获取章节
        
        Args:
            chapter_id: 章节ID
            
        Returns:
            Optional[Chapter]: 章节对象，如果不存在则返回None
        """
        log_debug("数据库", "查询章节", 章节ID=chapter_id)
        
        try:
            stmt = select(Chapter).where(Chapter.id == chapter_id)
            result = await self.session.execute(stmt)
            chapter = result.scalar_one_or_none()
            
            if chapter:
                log_debug("数据库", "章节查询成功", 
                         章节ID=chapter.id, 
                         章节号=chapter.chapter_number)
            else:
                log_debug("数据库", "章节不存在", 章节ID=chapter_id)
            
            return chapter
            
        except Exception as e:
            log_error("数据库", "章节查询失败", error=e, 章节ID=chapter_id)
            raise
    
    async def get_chapters_by_bible_id(self, bible_id: str) -> List[Chapter]:
        """
        💾 [数据库] 获取指定故事圣经的所有章节
        
        Args:
            bible_id: 故事圣经ID
            
        Returns:
            List[Chapter]: 章节列表，按章节号排序
        """
        log_debug("数据库", "查询故事圣经的章节", 故事圣经ID=bible_id)
        
        try:
            stmt = (
                select(Chapter)
                .where(Chapter.story_bible_id == bible_id)
                .order_by(Chapter.chapter_number)
            )
            result = await self.session.execute(stmt)
            chapters = result.scalars().all()
            
            log_debug("数据库", "故事圣经章节查询成功", 
                     故事圣经ID=bible_id, 
                     章节数量=len(chapters))
            
            return list(chapters)
            
        except Exception as e:
            log_error("数据库", "故事圣经章节查询失败", error=e, 故事圣经ID=bible_id)
            raise
    
    async def update_chapter_status(
        self, 
        chapter_id: str, 
        status: GenerationStatus,
        generated_content: Optional[str] = None,
        error_message: Optional[str] = None,
        generation_time: Optional[float] = None,
        token_usage: Optional[int] = None,
        actual_word_count: Optional[int] = None
    ) -> bool:
        """
        💾 [数据库] 更新章节状态
        
        Args:
            chapter_id: 章节ID
            status: 新状态
            generated_content: 生成的内容
            error_message: 错误信息
            generation_time: 生成耗时
            token_usage: token使用量
            actual_word_count: 实际字数
            
        Returns:
            bool: 更新是否成功
        """
        log_debug("数据库", "更新章节状态", 
                 章节ID=chapter_id, 
                 新状态=status.value)
        
        try:
            update_data = {
                "status": status,
                "updated_at": datetime.utcnow()
            }
            
            if generated_content is not None:
                update_data["generated_content"] = generated_content
                update_data["content_length"] = len(generated_content)
            
            if error_message is not None:
                update_data["error_message"] = error_message
            
            if generation_time is not None:
                update_data["generation_time"] = generation_time
            
            if token_usage is not None:
                update_data["token_usage"] = token_usage
            
            if actual_word_count is not None:
                update_data["actual_word_count"] = actual_word_count
            
            if status == GenerationStatus.COMPLETED:
                update_data["completed_at"] = datetime.utcnow()
            
            stmt = (
                update(Chapter)
                .where(Chapter.id == chapter_id)
                .values(**update_data)
            )
            
            result = await self.session.execute(stmt)
            await self.session.commit()

            success = result.rowcount > 0
            
            if success:
                log_info("数据库", "章节状态更新成功", 
                        章节ID=chapter_id, 
                        新状态=status.value)
            else:
                log_error("数据库", "章节状态更新失败 - 记录不存在", 
                         章节ID=chapter_id)
            
            return success
            
        except Exception as e:
            await self.session.rollback()
            log_error("数据库", "章节状态更新失败", 
                     error=e, 
                     章节ID=chapter_id, 
                     新状态=status.value)
            raise
    
    async def delete_chapter(self, chapter_id: str) -> bool:
        """
        💾 [数据库] 删除章节
        
        Args:
            chapter_id: 章节ID
            
        Returns:
            bool: 删除是否成功
        """
        log_debug("数据库", "删除章节", 章节ID=chapter_id)
        
        try:
            stmt = delete(Chapter).where(Chapter.id == chapter_id)
            result = await self.session.execute(stmt)
            await self.session.commit()
            
            success = result.rowcount > 0
            
            if success:
                log_info("数据库", "章节删除成功", 章节ID=chapter_id)
            else:
                log_error("数据库", "章节删除失败 - 记录不存在", 章节ID=chapter_id)
            
            return success
            
        except Exception as e:
            await self.session.rollback()
            log_error("数据库", "章节删除失败", error=e, 章节ID=chapter_id)
            raise
    
    async def get_chapter_count_by_bible_id(self, bible_id: str) -> int:
        """
        💾 [数据库] 获取指定故事圣经的章节数量
        
        Args:
            bible_id: 故事圣经ID
            
        Returns:
            int: 章节数量
        """
        log_debug("数据库", "查询故事圣经章节数量", 故事圣经ID=bible_id)
        
        try:
            stmt = select(func.count(Chapter.id)).where(Chapter.story_bible_id == bible_id)
            result = await self.session.execute(stmt)
            count = result.scalar()
            
            log_debug("数据库", "故事圣经章节数量查询成功", 
                     故事圣经ID=bible_id, 
                     章节数量=count)
            
            return count or 0
            
        except Exception as e:
            log_error("数据库", "故事圣经章节数量查询失败", error=e, 故事圣经ID=bible_id)
            raise
    
    async def get_next_chapter_number(self, bible_id: str) -> int:
        """
        💾 [数据库] 获取指定故事圣经的下一个章节号

        Args:
            bible_id: 故事圣经ID

        Returns:
            int: 下一个章节号
        """
        log_debug("数据库", "查询下一个章节号", 故事圣经ID=bible_id)

        try:
            stmt = (
                select(func.max(Chapter.chapter_number))
                .where(Chapter.story_bible_id == bible_id)
            )
            result = await self.session.execute(stmt)
            max_number = result.scalar()

            next_number = (max_number or 0) + 1

            log_debug("数据库", "下一个章节号查询成功",
                     故事圣经ID=bible_id,
                     下一个章节号=next_number)

            return next_number

        except Exception as e:
            log_error("数据库", "下一个章节号查询失败", error=e, 故事圣经ID=bible_id)
            raise

    async def get_by_story_and_number(self, story_bible_id: str, chapter_number: int) -> Optional[Chapter]:
        """
        💾 [数据库] 根据故事ID和章节号获取章节

        Args:
            story_bible_id: 故事圣经ID
            chapter_number: 章节号

        Returns:
            Optional[Chapter]: 章节对象，如果不存在则返回None
        """
        log_debug("数据库", "根据故事ID和章节号查询章节",
                 故事圣经ID=story_bible_id,
                 章节号=chapter_number)

        try:
            stmt = select(Chapter).where(
                and_(
                    Chapter.story_bible_id == story_bible_id,
                    Chapter.chapter_number == chapter_number
                )
            )
            result = await self.session.execute(stmt)
            chapter = result.scalar_one_or_none()

            if chapter:
                log_debug("数据库", "章节查询成功",
                         章节ID=chapter.id,
                         章节号=chapter.chapter_number,
                         章节标题=chapter.chapter_title)
            else:
                log_debug("数据库", "章节不存在",
                         故事圣经ID=story_bible_id,
                         章节号=chapter_number)

            return chapter

        except Exception as e:
            log_error("数据库", "章节查询失败", error=e,
                     故事圣经ID=story_bible_id,
                     章节号=chapter_number)
            raise
