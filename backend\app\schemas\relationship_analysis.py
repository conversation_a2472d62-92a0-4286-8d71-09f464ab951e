"""
🕸️ [关系分析] 角色关系网络分析API数据模式

定义关系分析相关的请求和响应数据模式，包括：
1. 关系网络分析请求/响应
2. 可视化数据请求/响应
3. 社区分析响应
4. 影响力分析响应
"""

from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from pydantic import BaseModel, Field
from enum import Enum

from app.core.relationship_analyzer import (
    RelationshipAnalysisResult,
    NetworkVisualizationData,
    NodeMetrics,
    EdgeMetrics,
    Community,
    CentralityType,
    CommunityAlgorithm
)


class CentralityTypeEnum(str, Enum):
    """中心性类型枚举"""
    DEGREE = "degree"
    BETWEENNESS = "betweenness"
    CLOSENESS = "closeness"
    EIGENVECTOR = "eigenvector"
    PAGERANK = "pagerank"
    ALL = "all"


class CommunityAlgorithmEnum(str, Enum):
    """社区检测算法枚举"""
    LOUVAIN = "louvain"
    LABEL_PROPAGATION = "label_propagation"
    MODULARITY = "modularity"
    CONNECTED_COMPONENTS = "connected_components"


class LayoutAlgorithmEnum(str, Enum):
    """布局算法枚举"""
    FORCE_DIRECTED = "force_directed"
    CIRCULAR = "circular"
    HIERARCHICAL = "hierarchical"
    GRID = "grid"


class RelationshipAnalysisRequest(BaseModel):
    """关系网络分析请求模式"""
    story_id: str = Field(..., description="故事ID")
    analysis_options: Optional[Dict[str, Any]] = Field(
        default=None,
        description="分析选项配置"
    )
    
    # 分析配置
    include_centrality: bool = Field(default=True, description="是否包含中心性分析")
    include_communities: bool = Field(default=True, description="是否包含社区检测")
    include_visualization: bool = Field(default=True, description="是否生成可视化数据")
    
    # 算法选择
    centrality_types: List[CentralityTypeEnum] = Field(
        default=[CentralityTypeEnum.DEGREE, CentralityTypeEnum.BETWEENNESS],
        description="要计算的中心性类型"
    )
    community_algorithm: CommunityAlgorithmEnum = Field(
        default=CommunityAlgorithmEnum.CONNECTED_COMPONENTS,
        description="社区检测算法"
    )
    
    # 过滤条件
    min_relationship_strength: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="最小关系强度阈值"
    )
    character_types_filter: Optional[List[str]] = Field(
        default=None,
        description="角色类型过滤器"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "story_id": "story-123",
                "analysis_options": {
                    "detailed_analysis": True,
                    "include_weak_ties": False
                },
                "include_centrality": True,
                "include_communities": True,
                "include_visualization": True,
                "centrality_types": ["degree", "betweenness"],
                "community_algorithm": "connected_components",
                "min_relationship_strength": 0.1
            }
        }


class RelationshipAnalysisResponse(BaseModel):
    """关系网络分析响应模式"""
    story_id: str = Field(..., description="故事ID")
    analysis_result: RelationshipAnalysisResult = Field(..., description="分析结果")
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "story_id": "story-123",
                "analysis_result": {
                    "total_nodes": 8,
                    "total_edges": 12,
                    "network_density": 0.43,
                    "community_count": 2,
                    "analysis_confidence": 0.85
                },
                "success": True,
                "message": "关系网络分析完成"
            }
        }


class NetworkVisualizationRequest(BaseModel):
    """网络可视化请求模式"""
    story_id: str = Field(..., description="故事ID")
    layout_algorithm: LayoutAlgorithmEnum = Field(
        default=LayoutAlgorithmEnum.FORCE_DIRECTED,
        description="布局算法"
    )
    
    # 可视化配置
    node_size_range: Tuple[float, float] = Field(
        default=(10, 30),
        description="节点大小范围"
    )
    edge_width_range: Tuple[float, float] = Field(
        default=(1, 6),
        description="边宽度范围"
    )
    
    # 显示选项
    show_labels: bool = Field(default=True, description="是否显示标签")
    show_communities: bool = Field(default=True, description="是否显示社区")
    show_metrics: bool = Field(default=False, description="是否显示指标")
    
    # 过滤选项
    min_influence_score: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="最小影响力评分"
    )
    relationship_types_filter: Optional[List[str]] = Field(
        default=None,
        description="关系类型过滤器"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "story_id": "story-123",
                "layout_algorithm": "force_directed",
                "node_size_range": [10, 30],
                "edge_width_range": [1, 6],
                "show_labels": True,
                "show_communities": True,
                "min_influence_score": 0.1
            }
        }


class NetworkVisualizationResponse(BaseModel):
    """网络可视化响应模式"""
    story_id: str = Field(..., description="故事ID")
    visualization_data: NetworkVisualizationData = Field(..., description="可视化数据")
    network_metrics: Dict[str, Any] = Field(..., description="网络指标摘要")
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "story_id": "story-123",
                "visualization_data": {
                    "nodes": [],
                    "edges": [],
                    "communities": []
                },
                "network_metrics": {
                    "total_nodes": 8,
                    "total_edges": 12,
                    "network_density": 0.43,
                    "community_count": 2
                },
                "success": True,
                "message": "网络可视化数据生成完成"
            }
        }


class CommunityAnalysisResponse(BaseModel):
    """社区分析响应模式"""
    story_id: str = Field(..., description="故事ID")
    communities: List[Community] = Field(..., description="检测到的社区列表")
    community_count: int = Field(..., description="社区数量")
    modularity_score: float = Field(..., description="模块度评分")
    algorithm_used: str = Field(..., description="使用的算法")
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "story_id": "story-123",
                "communities": [
                    {
                        "id": "community-0",
                        "name": "社区 1",
                        "members": ["char-001", "char-002"],
                        "size": 2,
                        "density": 0.8
                    }
                ],
                "community_count": 2,
                "modularity_score": 0.65,
                "algorithm_used": "connected_components",
                "success": True,
                "message": "社区结构分析完成"
            }
        }


class InfluenceRanking(BaseModel):
    """影响力排名模式"""
    rank: int = Field(..., description="排名")
    character_name: str = Field(..., description="角色名称")
    influence_score: float = Field(..., description="影响力评分")
    degree_centrality: float = Field(..., description="度中心性")
    betweenness_centrality: float = Field(..., description="介数中心性")
    closeness_centrality: float = Field(..., description="接近中心性")
    
    class Config:
        json_schema_extra = {
            "example": {
                "rank": 1,
                "character_name": "张三",
                "influence_score": 0.85,
                "degree_centrality": 0.75,
                "betweenness_centrality": 0.60,
                "closeness_centrality": 0.80
            }
        }


class InfluenceAnalysisResponse(BaseModel):
    """影响力分析响应模式"""
    story_id: str = Field(..., description="故事ID")
    top_influential_characters: List[NodeMetrics] = Field(..., description="最具影响力角色列表")
    influence_rankings: List[InfluenceRanking] = Field(..., description="影响力排名")
    centrality_type_used: str = Field(..., description="使用的中心性类型")
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "story_id": "story-123",
                "top_influential_characters": [],
                "influence_rankings": [
                    {
                        "rank": 1,
                        "character_name": "张三",
                        "influence_score": 0.85,
                        "degree_centrality": 0.75,
                        "betweenness_centrality": 0.60,
                        "closeness_centrality": 0.80
                    }
                ],
                "centrality_type_used": "all",
                "success": True,
                "message": "角色影响力分析完成"
            }
        }


class RelationshipEvolutionRequest(BaseModel):
    """关系演化分析请求模式"""
    story_id: str = Field(..., description="故事ID")
    start_chapter: int = Field(default=1, description="开始章节")
    end_chapter: Optional[int] = Field(default=None, description="结束章节")
    
    # 分析选项
    track_relationship_changes: bool = Field(default=True, description="是否跟踪关系变化")
    analyze_influence_shifts: bool = Field(default=True, description="是否分析影响力变化")
    detect_community_evolution: bool = Field(default=True, description="是否检测社区演化")
    
    class Config:
        json_schema_extra = {
            "example": {
                "story_id": "story-123",
                "start_chapter": 1,
                "end_chapter": 10,
                "track_relationship_changes": True,
                "analyze_influence_shifts": True,
                "detect_community_evolution": True
            }
        }


class RelationshipEvolutionResponse(BaseModel):
    """关系演化分析响应模式"""
    story_id: str = Field(..., description="故事ID")
    chapter_range: Tuple[int, int] = Field(..., description="分析的章节范围")
    
    # 演化数据
    relationship_changes: List[Dict[str, Any]] = Field(..., description="关系变化记录")
    influence_shifts: List[Dict[str, Any]] = Field(..., description="影响力变化记录")
    community_evolution: List[Dict[str, Any]] = Field(..., description="社区演化记录")
    
    # 趋势分析
    evolution_trends: List[str] = Field(..., description="演化趋势")
    key_turning_points: List[Dict[str, Any]] = Field(..., description="关键转折点")
    
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "story_id": "story-123",
                "chapter_range": [1, 10],
                "relationship_changes": [],
                "influence_shifts": [],
                "community_evolution": [],
                "evolution_trends": ["关系网络逐渐复杂化", "主角影响力持续上升"],
                "key_turning_points": [],
                "success": True,
                "message": "关系演化分析完成"
            }
        }
