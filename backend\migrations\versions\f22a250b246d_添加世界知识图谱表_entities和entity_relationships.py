"""添加世界知识图谱表：entities和entity_relationships

Revision ID: f22a250b246d
Revises: 59076c7d892c
Create Date: 2025-08-04 22:14:28.737215

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f22a250b246d'
down_revision: Union[str, Sequence[str], None] = '59076c7d892c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('entities',
    sa.Column('id', sa.String(length=50), nullable=False, comment='实体唯一标识'),
    sa.Column('story_id', sa.String(length=50), nullable=False, comment='关联的故事ID'),
    sa.Column('name', sa.String(length=200), nullable=False, comment='实体名称'),
    sa.Column('type', sa.Enum('CHARACTER', 'ITEM', 'SCENE', 'CONCEPT', 'ORGANIZATION', name='entitytype'), nullable=False, comment='实体类型'),
    sa.Column('description', sa.Text(), nullable=True, comment='实体描述'),
    sa.Column('properties', sa.JSON(), nullable=True, comment='实体自定义属性'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否活跃'),
    sa.Column('importance_score', sa.Float(), nullable=False, comment='重要性评分 0-1'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.Column('first_mentioned_chapter', sa.Integer(), nullable=True, comment='首次提及章节'),
    sa.Column('last_mentioned_chapter', sa.Integer(), nullable=True, comment='最后提及章节'),
    sa.ForeignKeyConstraint(['story_id'], ['story_bibles.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('entity_relationships',
    sa.Column('id', sa.String(length=50), nullable=False, comment='关系唯一标识'),
    sa.Column('source_entity_id', sa.String(length=50), nullable=False, comment='源实体ID'),
    sa.Column('target_entity_id', sa.String(length=50), nullable=False, comment='目标实体ID'),
    sa.Column('relationship_type', sa.String(length=100), nullable=False, comment='关系类型'),
    sa.Column('description', sa.Text(), nullable=True, comment='关系描述'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'HISTORICAL', name='relationshipstatus'), nullable=False, comment='关系状态'),
    sa.Column('strength', sa.Float(), nullable=False, comment='关系强度 0-1'),
    sa.Column('is_bidirectional', sa.Boolean(), nullable=False, comment='是否双向关系'),
    sa.Column('properties', sa.JSON(), nullable=True, comment='关系自定义属性'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.Column('established_chapter', sa.Integer(), nullable=True, comment='关系建立章节'),
    sa.Column('last_updated_chapter', sa.Integer(), nullable=True, comment='关系最后更新章节'),
    sa.ForeignKeyConstraint(['source_entity_id'], ['entities.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['target_entity_id'], ['entities.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('entity_relationships')
    op.drop_table('entities')
    # ### end Alembic commands ###
