"""
🤖 [去AI化] AI内容去AI化处理器

统一管理所有去AI化模块，提供完整的去AI化处理流水线，
将AI生成的内容转换为更具人性化特征的真实文本。

主要功能:
1. AI痕迹智能检测和评估
2. 自适应去AI化处理强度调节
3. 多模块协同去AI化处理
4. 处理效果评估和反馈

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from app.core.config import log_info, log_debug, log_error
from app.core.attention_defocus import (
    AttentionDefocusSystem, 
    EmotionalState as AttentionEmotionalState,
    create_attention_defocus_system
)
from app.core.emotional_downgrader import (
    EmotionalInarticulationSystem,
    create_emotional_downgrader
)
from app.core.irrational_behavior import (
    IrrationalBehaviorInjector,
    EmotionalState as BehaviorEmotionalState,
    create_irrational_behavior_injector
)
from app.core.time_distortion import (
    TimePerceptionDistorter,
    EmotionalState as TimeEmotionalState,
    create_time_perception_distorter
)
from app.core.entropy_injector import (
    EntropyInjector,
    DisruptionLevel,
    create_entropy_injector
)


class AITraceLevel(Enum):
    """AI痕迹等级枚举"""
    VERY_LOW = "very_low"      # 0-20%
    LOW = "low"                # 20-40%
    MEDIUM = "medium"          # 40-60%
    HIGH = "high"              # 60-80%
    VERY_HIGH = "very_high"    # 80-100%


class ProcessingMode(Enum):
    """处理模式枚举"""
    CONSERVATIVE = "conservative"  # 保守模式：轻度处理
    BALANCED = "balanced"         # 平衡模式：中度处理
    AGGRESSIVE = "aggressive"     # 激进模式：重度处理
    ADAPTIVE = "adaptive"         # 自适应模式：根据AI痕迹自动调节


@dataclass
class DeAIProcessingResult:
    """去AI化处理结果"""
    original_text: str
    processed_text: str
    ai_trace_before: float
    ai_trace_after: float
    ai_reduction_percentage: float
    modules_applied: List[str]
    processing_details: Dict[str, Any]
    quality_score: float
    processing_mode: ProcessingMode


class AITraceDetector:
    """🔍 AI痕迹检测器 - 智能检测文本中的AI特征"""
    
    def __init__(self):
        """初始化AI痕迹检测器"""
        # AI文本特征模式（更全面的检测）
        self.ai_patterns = {
            "完美句式": [
                r"不仅.*而且.*",
                r"既.*又.*",
                r"一方面.*另一方面.*",
                r"首先.*其次.*最后.*",
                r"总的来说.*",
                r"综上所述.*"
            ],
            "过度修饰": [
                r"[的地得]{3,}",
                r"非常.*非常.*",
                r"十分.*十分.*",
                r"极其.*极其.*",
                r"相当.*相当.*",
                r"格外.*格外.*"
            ],
            "机械对话": [
                r'".*".*说道',
                r'".*".*回答',
                r'".*".*表示',
                r'".*".*解释',
                r'".*".*补充',
                r'".*".*强调'
            ],
            "完美描写": [
                r"完美的.*",
                r"无瑕的.*",
                r"绝对的.*",
                r"毫无疑问.*",
                r"毫不犹豫.*",
                r"完全.*"
            ],
            "逻辑连接": [
                r"因此.*",
                r"所以.*",
                r"于是.*",
                r"然后.*",
                r"接着.*",
                r"随后.*"
            ],
            "情感过度": [
                r"心中.*涌起.*",
                r"内心.*充满.*",
                r"眼中.*闪烁.*",
                r"脸上.*浮现.*",
                r"心情.*复杂.*"
            ]
        }
        
        # 人性化特征模式（正向指标）
        self.human_patterns = {
            "不确定表达": [
                r"似乎.*", r"好像.*", r"仿佛.*", r"大概.*", r"可能.*", r"或许.*"
            ],
            "口语化表达": [
                r"嗯.*", r"呃.*", r"这个.*", r"那个.*", r"怎么说呢.*"
            ],
            "情感不充分": [
                r"有点.*", r"稍微.*", r"略微.*", r"还行.*", r"还好.*"
            ]
        }
        
        log_debug("去AI化", "AI痕迹检测器初始化完成", 
                 AI模式数=len(self.ai_patterns),
                 人性化模式数=len(self.human_patterns))
    
    def analyze_ai_traces(self, text: str) -> Dict[str, Any]:
        """分析文本中的AI痕迹
        
        Args:
            text: 输入文本
            
        Returns:
            AI痕迹分析结果
        """
        log_debug("去AI化", "开始AI痕迹分析", 文本长度=len(text))
        
        # 计算AI特征
        ai_score = 0
        ai_matches = {}
        total_ai_matches = 0
        
        for category, patterns in self.ai_patterns.items():
            matches = 0
            for pattern in patterns:
                import re
                matches += len(re.findall(pattern, text))
            ai_matches[category] = matches
            total_ai_matches += matches
        
        # 计算人性化特征
        human_score = 0
        human_matches = {}
        total_human_matches = 0
        
        for category, patterns in self.human_patterns.items():
            matches = 0
            for pattern in patterns:
                import re
                matches += len(re.findall(pattern, text))
            human_matches[category] = matches
            total_human_matches += matches
        
        # 计算AI痕迹百分比
        text_length = max(len(text), 1)
        
        # AI特征权重
        ai_weight = min(100.0, (total_ai_matches / text_length) * 100 * 15)
        
        # 人性化特征权重（负向影响AI分数）
        human_weight = min(50.0, (total_human_matches / text_length) * 100 * 10)
        
        # 最终AI痕迹百分比
        ai_percentage = max(0.0, min(100.0, ai_weight - human_weight))
        
        # 确定AI痕迹等级
        ai_level = self._determine_ai_level(ai_percentage)
        
        result = {
            "ai_percentage": ai_percentage,
            "ai_level": ai_level,
            "total_ai_matches": total_ai_matches,
            "total_human_matches": total_human_matches,
            "ai_matches_by_category": ai_matches,
            "human_matches_by_category": human_matches,
            "text_length": text_length,
            "assessment": self._assess_ai_level(ai_percentage)
        }
        
        log_info("去AI化", "AI痕迹分析完成",
                AI百分比=f"{ai_percentage:.1f}%",
                AI等级=ai_level.value,
                AI特征数=total_ai_matches,
                人性化特征数=total_human_matches)
        
        return result
    
    def _determine_ai_level(self, ai_percentage: float) -> AITraceLevel:
        """确定AI痕迹等级"""
        if ai_percentage >= 80:
            return AITraceLevel.VERY_HIGH
        elif ai_percentage >= 60:
            return AITraceLevel.HIGH
        elif ai_percentage >= 40:
            return AITraceLevel.MEDIUM
        elif ai_percentage >= 20:
            return AITraceLevel.LOW
        else:
            return AITraceLevel.VERY_LOW
    
    def _assess_ai_level(self, ai_percentage: float) -> str:
        """评估AI痕迹等级描述"""
        if ai_percentage >= 80:
            return "极高AI痕迹"
        elif ai_percentage >= 60:
            return "高AI痕迹"
        elif ai_percentage >= 40:
            return "中等AI痕迹"
        elif ai_percentage >= 20:
            return "低AI痕迹"
        else:
            return "自然文本"


class DeAIProcessor:
    """🤖 去AI化处理器主类
    
    统一管理所有去AI化模块，提供完整的处理流水线
    """
    
    def __init__(self):
        """初始化去AI化处理器"""
        # 初始化各个去AI化模块
        self.ai_detector = AITraceDetector()
        self.attention_defocus = create_attention_defocus_system()
        self.emotional_downgrader = create_emotional_downgrader()
        self.behavior_injector = create_irrational_behavior_injector()
        self.time_distorter = create_time_perception_distorter()
        self.entropy_injector = create_entropy_injector()
        
        log_info("去AI化", "去AI化处理器初始化完成", 模块数=6)
    
    def adaptive_deai_processing(
        self,
        text: str,
        processing_mode: ProcessingMode = ProcessingMode.ADAPTIVE,
        custom_intensity: Optional[float] = None,
        preserve_meaning: bool = True
    ) -> DeAIProcessingResult:
        """自适应去AI化处理
        
        Args:
            text: 输入文本
            processing_mode: 处理模式
            custom_intensity: 自定义强度
            preserve_meaning: 是否保持原意
            
        Returns:
            去AI化处理结果
        """
        log_info("去AI化", "开始自适应去AI化处理",
                文本长度=len(text),
                处理模式=processing_mode.value)
        
        # 第一步：分析AI痕迹
        ai_analysis = self.ai_detector.analyze_ai_traces(text)
        ai_trace_before = ai_analysis["ai_percentage"]
        ai_level = ai_analysis["ai_level"]
        
        # 第二步：确定处理强度
        intensity = self._determine_processing_intensity(
            processing_mode, 
            ai_level, 
            custom_intensity
        )
        
        # 第三步：应用去AI化模块
        processed_text, modules_applied, processing_details = self._apply_deai_modules(
            text, 
            intensity, 
            ai_analysis,
            preserve_meaning
        )
        
        # 第四步：评估处理效果
        final_analysis = self.ai_detector.analyze_ai_traces(processed_text)
        ai_trace_after = final_analysis["ai_percentage"]
        ai_reduction = max(0, ai_trace_before - ai_trace_after)
        ai_reduction_percentage = (ai_reduction / max(ai_trace_before, 1)) * 100
        
        # 第五步：计算质量评分
        quality_score = self._calculate_quality_score(
            ai_reduction_percentage,
            len(modules_applied),
            processing_details
        )
        
        result = DeAIProcessingResult(
            original_text=text,
            processed_text=processed_text,
            ai_trace_before=ai_trace_before,
            ai_trace_after=ai_trace_after,
            ai_reduction_percentage=ai_reduction_percentage,
            modules_applied=modules_applied,
            processing_details=processing_details,
            quality_score=quality_score,
            processing_mode=processing_mode
        )
        
        log_info("去AI化", "自适应去AI化处理完成",
                AI痕迹降低=f"{ai_reduction:.1f}%",
                降低比例=f"{ai_reduction_percentage:.1f}%",
                应用模块数=len(modules_applied),
                质量评分=f"{quality_score:.2f}")
        
        return result

    def _determine_processing_intensity(
        self,
        processing_mode: ProcessingMode,
        ai_level: AITraceLevel,
        custom_intensity: Optional[float] = None
    ) -> float:
        """确定处理强度

        Args:
            processing_mode: 处理模式
            ai_level: AI痕迹等级
            custom_intensity: 自定义强度

        Returns:
            处理强度 (0.0-1.0)
        """
        if custom_intensity is not None:
            return max(0.0, min(1.0, custom_intensity))

        # 基于处理模式的基础强度
        mode_intensity = {
            ProcessingMode.CONSERVATIVE: 0.3,
            ProcessingMode.BALANCED: 0.5,
            ProcessingMode.AGGRESSIVE: 0.8,
            ProcessingMode.ADAPTIVE: 0.5  # 默认值，会根据AI等级调整
        }

        base_intensity = mode_intensity[processing_mode]

        # 自适应模式：根据AI痕迹等级调整强度
        if processing_mode == ProcessingMode.ADAPTIVE:
            ai_level_multiplier = {
                AITraceLevel.VERY_LOW: 0.2,
                AITraceLevel.LOW: 0.4,
                AITraceLevel.MEDIUM: 0.6,
                AITraceLevel.HIGH: 0.8,
                AITraceLevel.VERY_HIGH: 1.0
            }
            base_intensity = ai_level_multiplier[ai_level]

        log_debug("去AI化", "处理强度确定",
                 处理模式=processing_mode.value,
                 AI等级=ai_level.value,
                 最终强度=base_intensity)

        return base_intensity

    def _apply_deai_modules(
        self,
        text: str,
        intensity: float,
        ai_analysis: Dict[str, Any],
        preserve_meaning: bool = True
    ) -> Tuple[str, List[str], Dict[str, Any]]:
        """应用去AI化模块

        Args:
            text: 输入文本
            intensity: 处理强度
            ai_analysis: AI分析结果
            preserve_meaning: 是否保持原意

        Returns:
            (处理后文本, 应用的模块列表, 处理详情)
        """
        log_debug("去AI化", "开始应用去AI化模块", 强度=intensity)

        current_text = text
        modules_applied = []
        processing_details = {}

        # 检测主导情绪（用于情绪相关模块）
        primary_emotion = self._detect_primary_emotion(text)

        # 模块应用顺序和条件
        module_pipeline = [
            {
                "name": "注意力失焦",
                "condition": lambda: intensity > 0.2 and ai_analysis["total_ai_matches"] > 2,
                "processor": self._apply_attention_defocus,
                "priority": 1
            },
            {
                "name": "情感表达降级",
                "condition": lambda: intensity > 0.3 and ai_analysis["ai_matches_by_category"].get("情感过度", 0) > 0,
                "processor": self._apply_emotional_downgrade,
                "priority": 2
            },
            {
                "name": "非理性行为注入",
                "condition": lambda: intensity > 0.4 and primary_emotion is not None,
                "processor": self._apply_irrational_behavior,
                "priority": 3
            },
            {
                "name": "时间感知扭曲",
                "condition": lambda: intensity > 0.3 and self._has_time_content(current_text),
                "processor": self._apply_time_distortion,
                "priority": 4
            },
            {
                "name": "熵增扰动",
                "condition": lambda: intensity > 0.5,
                "processor": self._apply_entropy_injection,
                "priority": 5
            }
        ]

        # 按优先级排序并应用模块
        module_pipeline.sort(key=lambda x: x["priority"])

        for module in module_pipeline:
            if module["condition"]():
                try:
                    result = module["processor"](
                        current_text,
                        intensity,
                        primary_emotion,
                        preserve_meaning
                    )

                    if result["success"]:
                        current_text = result["text"]
                        modules_applied.append(module["name"])
                        processing_details[module["name"]] = result["details"]

                        log_debug("去AI化", f"成功应用{module['name']}模块",
                                 文本变化=len(result["text"]) - len(text))

                except Exception as e:
                    log_error("去AI化", f"{module['name']}模块处理失败", error=e)
                    continue

        log_info("去AI化", "去AI化模块应用完成",
                应用模块数=len(modules_applied),
                最终文本长度=len(current_text))

        return current_text, modules_applied, processing_details

    def _detect_primary_emotion(self, text: str) -> Optional[str]:
        """检测文本中的主导情绪

        Args:
            text: 输入文本

        Returns:
            主导情绪类型或None
        """
        emotion_keywords = {
            "愤怒": ["愤怒", "生气", "恼火", "暴怒", "气愤", "恼怒"],
            "悲伤": ["悲伤", "难过", "伤心", "痛苦", "沮丧", "失落"],
            "焦虑": ["焦虑", "紧张", "担心", "不安", "恐慌", "忧虑"],
            "喜悦": ["开心", "高兴", "快乐", "兴奋", "喜悦", "愉快"],
            "恐惧": ["害怕", "恐惧", "惊恐", "恐慌", "畏惧", "胆怯"],
            "惊讶": ["惊讶", "震惊", "意外", "吃惊", "惊奇", "诧异"]
        }

        emotion_scores = {}
        text_lower = text.lower()

        for emotion, keywords in emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > 0:
                emotion_scores[emotion] = score

        if emotion_scores:
            primary_emotion = max(emotion_scores, key=emotion_scores.get)
            log_debug("去AI化", "检测到主导情绪", 情绪=primary_emotion, 分数=emotion_scores[primary_emotion])
            return primary_emotion

        return None

    def _has_time_content(self, text: str) -> bool:
        """检查文本是否包含时间内容"""
        time_keywords = [
            "几分钟", "一会儿", "片刻", "不久", "很快", "半小时", "一小时",
            "时间", "瞬间", "刹那", "突然", "忽然", "立刻", "马上"
        ]

        return any(keyword in text for keyword in time_keywords)

    def _apply_attention_defocus(
        self,
        text: str,
        intensity: float,
        emotion: Optional[str],
        preserve_meaning: bool
    ) -> Dict[str, Any]:
        """应用注意力失焦模块"""
        try:
            # 映射情绪到注意力模块的情绪状态
            emotion_mapping = {
                "愤怒": AttentionEmotionalState.ANGER,
                "悲伤": AttentionEmotionalState.SADNESS,
                "焦虑": AttentionEmotionalState.ANXIETY,
                "喜悦": AttentionEmotionalState.JOY,
                "恐惧": AttentionEmotionalState.FEAR,
                "惊讶": AttentionEmotionalState.SURPRISE
            }

            emotional_state = emotion_mapping.get(emotion, AttentionEmotionalState.NEUTRAL)

            result = self.attention_defocus.apply_emotional_filter(
                text,
                emotional_state,
                intensity
            )

            return {
                "success": True,
                "text": result.filtered_text,
                "details": {
                    "removed_elements": len(result.removed_elements),
                    "emphasized_elements": len(result.emphasized_elements),
                    "attention_focus": result.attention_focus,
                    "emotional_state": emotional_state.value
                }
            }

        except Exception as e:
            log_error("去AI化", "注意力失焦处理失败", error=e)
            return {"success": False, "text": text, "details": {"error": str(e)}}

    def _apply_emotional_downgrade(
        self,
        text: str,
        intensity: float,
        emotion: Optional[str],
        preserve_meaning: bool
    ) -> Dict[str, Any]:
        """应用情感表达降级模块"""
        try:
            result = self.emotional_downgrader.make_expression_inadequate(
                text,
                intensity
            )

            return {
                "success": True,
                "text": result.downgraded_text,
                "details": {
                    "original_expressions": len(result.original_expressions),
                    "simplified_expressions": len(result.simplified_expressions),
                    "applied_modes": result.applied_modes
                }
            }

        except Exception as e:
            log_error("去AI化", "情感表达降级处理失败", error=e)
            return {"success": False, "text": text, "details": {"error": str(e)}}

    def _apply_irrational_behavior(
        self,
        text: str,
        intensity: float,
        emotion: Optional[str],
        preserve_meaning: bool
    ) -> Dict[str, Any]:
        """应用非理性行为注入模块"""
        try:
            # 映射情绪到行为模块的情绪状态
            emotion_mapping = {
                "愤怒": BehaviorEmotionalState.ANGER,
                "悲伤": BehaviorEmotionalState.SADNESS,
                "焦虑": BehaviorEmotionalState.ANXIETY,
                "喜悦": BehaviorEmotionalState.JOY,
                "恐惧": BehaviorEmotionalState.FEAR,
                "惊讶": BehaviorEmotionalState.SURPRISE
            }

            emotional_state = emotion_mapping.get(emotion, BehaviorEmotionalState.NEUTRAL)

            result = self.behavior_injector.inject_micro_behavior(
                text,
                intensity,
                max_injections=max(1, int(intensity * 3)),
                preserve_meaning=preserve_meaning
            )

            return {
                "success": True,
                "text": result.modified_text,
                "details": {
                    "behaviors_injected": len(result.injected_behaviors),
                    "injection_points": result.injection_points,
                    "emotional_state": emotional_state.value
                }
            }

        except Exception as e:
            log_error("去AI化", "非理性行为注入处理失败", error=e)
            return {"success": False, "text": text, "details": {"error": str(e)}}

    def _apply_time_distortion(
        self,
        text: str,
        intensity: float,
        emotion: Optional[str],
        preserve_meaning: bool
    ) -> Dict[str, Any]:
        """应用时间感知扭曲模块"""
        try:
            # 映射情绪到时间模块的情绪状态
            emotion_mapping = {
                "愤怒": TimeEmotionalState.ANGER,
                "悲伤": TimeEmotionalState.SADNESS,
                "焦虑": TimeEmotionalState.ANXIETY,
                "喜悦": TimeEmotionalState.JOY,
                "恐惧": TimeEmotionalState.FEAR,
                "惊讶": TimeEmotionalState.SURPRISE
            }

            emotional_state = emotion_mapping.get(emotion, TimeEmotionalState.NEUTRAL)

            result = self.time_distorter.distort_time_perception(
                text,
                intensity,
                preserve_meaning=preserve_meaning
            )

            return {
                "success": True,
                "text": result.modified_text,
                "details": {
                    "distortions_applied": len(result.distortions_applied),
                    "emotional_context": result.emotional_context,
                    "emotional_state": emotional_state.value
                }
            }

        except Exception as e:
            log_error("去AI化", "时间感知扭曲处理失败", error=e)
            return {"success": False, "text": text, "details": {"error": str(e)}}

    def _apply_entropy_injection(
        self,
        text: str,
        intensity: float,
        emotion: Optional[str],
        preserve_meaning: bool
    ) -> Dict[str, Any]:
        """应用熵增扰动模块"""
        try:
            # 根据强度确定扰动级别
            if intensity >= 0.8:
                disruption_level = DisruptionLevel.HIGH
            elif intensity >= 0.6:
                disruption_level = DisruptionLevel.MEDIUM
            else:
                disruption_level = DisruptionLevel.LOW

            result = self.entropy_injector.process_text(
                text,
                disruption_level=disruption_level,
                emotion_context=emotion,
                custom_intensity=intensity
            )

            return {
                "success": True,
                "text": result.processed_text,
                "details": {
                    "modifications_applied": result.modifications_applied,
                    "disruption_techniques": result.disruption_techniques,
                    "ai_trace_reduction": result.ai_trace_reduction
                }
            }

        except Exception as e:
            log_error("去AI化", "熵增扰动处理失败", error=e)
            return {"success": False, "text": text, "details": {"error": str(e)}}

    def _calculate_quality_score(
        self,
        ai_reduction_percentage: float,
        modules_count: int,
        processing_details: Dict[str, Any]
    ) -> float:
        """计算处理质量评分

        Args:
            ai_reduction_percentage: AI痕迹降低百分比
            modules_count: 应用的模块数量
            processing_details: 处理详情

        Returns:
            质量评分 (0.0-1.0)
        """
        # 基础分数：AI痕迹降低效果
        base_score = min(1.0, ai_reduction_percentage / 100.0)

        # 模块协同加分：多模块协同工作
        module_bonus = min(0.2, modules_count * 0.05)

        # 处理效果加分：基于各模块的具体效果
        effect_bonus = 0.0
        for module_name, details in processing_details.items():
            if "error" not in details:
                effect_bonus += 0.05

        # 最终评分
        final_score = min(1.0, base_score + module_bonus + effect_bonus)

        log_debug("去AI化", "质量评分计算",
                 基础分数=base_score,
                 模块加分=module_bonus,
                 效果加分=effect_bonus,
                 最终评分=final_score)

        return final_score


# 工厂函数
def create_deai_processor() -> DeAIProcessor:
    """创建去AI化处理器实例"""
    return DeAIProcessor()


def quick_deai_processing(
    text: str,
    intensity: float = 0.7,
    processing_mode: ProcessingMode = ProcessingMode.ADAPTIVE
) -> DeAIProcessingResult:
    """快速去AI化处理

    Args:
        text: 输入文本
        intensity: 处理强度
        processing_mode: 处理模式

    Returns:
        去AI化处理结果
    """
    processor = create_deai_processor()
    return processor.adaptive_deai_processing(
        text=text,
        processing_mode=processing_mode,
        custom_intensity=intensity
    )


def analyze_ai_traces(text: str) -> Dict[str, Any]:
    """分析文本AI痕迹

    Args:
        text: 输入文本

    Returns:
        AI痕迹分析结果
    """
    detector = AITraceDetector()
    return detector.analyze_ai_traces(text)
