"""
📚 [语料库管理] 语料库管理API路由模块

提供完整的语料库管理服务API端点：
1. 语料库信息查询和统计
2. 语料库文件管理操作
3. 语料库数据动态加载和更新
4. 语料库质量控制和验证
5. 爽感语料库专用接口

作者: 文心小说后端服务系统
创建时间: 2025-08-05
"""

from typing import Dict, List, Optional, Any, Union
from fastapi import APIRouter, HTTPException, Query, status, UploadFile, File, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from pathlib import Path
import json

from app.core.config import log_info, log_debug, log_error, log_success
from app.core.corpus_manager import create_corpus_manager, CorpusManager
from app.core.pleasure_corpus_manager import create_pleasure_corpus_manager, PleasureCorpusManager
from app.core.pleasure_engine import PleasureType, EmotionalIntensity


# 创建路由器
router = APIRouter(prefix="/api/v1/corpus", tags=["语料库管理"])


# ==================== 数据模型定义 ====================

class CorpusInfoResponse(BaseModel):
    """语料库信息响应模型"""
    name: str = Field(..., description="语料库名称")
    version: str = Field(..., description="版本号")
    description: str = Field(..., description="描述信息")
    category: str = Field(..., description="分类")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    file_count: int = Field(..., description="文件数量")
    total_entries: int = Field(..., description="总条目数")
    last_updated: str = Field(..., description="最后更新时间")


class CorpusStatsResponse(BaseModel):
    """语料库统计响应模型"""
    total_files: int = Field(..., description="总文件数")
    total_categories: int = Field(..., description="总分类数")
    total_entries: int = Field(..., description="总条目数")
    file_types: Dict[str, int] = Field(default_factory=dict, description="文件类型统计")
    category_distribution: Dict[str, int] = Field(default_factory=dict, description="分类分布")
    quality_distribution: Dict[str, int] = Field(default_factory=dict, description="质量分布")


class PleasurePatternResponse(BaseModel):
    """爽感模式响应模型"""
    content: str = Field(..., description="模式内容")
    weight: float = Field(..., description="权重")
    intensity: float = Field(..., description="强度")
    reliability: float = Field(..., description="可靠性")
    quality: float = Field(..., description="质量评分")
    tags: List[str] = Field(default_factory=list, description="标签")
    examples: List[str] = Field(default_factory=list, description="示例")
    data: Dict[str, Any] = Field(default_factory=dict, description="扩展数据")


class RhythmTemplateResponse(BaseModel):
    """节奏模板响应模型"""
    name: str = Field(..., description="模板名称")
    description: str = Field(..., description="模板描述")
    phases: List[Dict[str, Any]] = Field(default_factory=list, description="阶段列表")
    emotional_curve: List[float] = Field(default_factory=list, description="情感曲线")
    duration_range: Dict[str, int] = Field(default_factory=dict, description="时长范围")


class CorpusValidationResponse(BaseModel):
    """语料库验证响应模型"""
    is_valid: bool = Field(..., description="是否有效")
    file_path: str = Field(..., description="文件路径")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
    suggestions: List[str] = Field(default_factory=list, description="建议列表")


# ==================== 依赖注入 ====================

async def get_corpus_manager() -> CorpusManager:
    """获取语料库管理器实例"""
    try:
        corpus_root = str(Path(__file__).parent.parent.parent / "data" / "corpus")
        manager = create_corpus_manager(corpus_root)
        log_debug("语料库管理", "创建语料库管理器实例", 根目录=corpus_root)
        return manager
    except Exception as e:
        log_error("语料库管理", "创建语料库管理器失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="语料库管理器初始化失败"
        )


async def get_pleasure_corpus_manager() -> PleasureCorpusManager:
    """获取爽感语料库管理器实例"""
    try:
        corpus_root = str(Path(__file__).parent.parent.parent / "data" / "corpus")
        manager = create_pleasure_corpus_manager(corpus_root)
        
        # 初始化语料库数据
        success = await manager.initialize()
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="爽感语料库初始化失败"
            )
        
        log_debug("语料库管理", "创建爽感语料库管理器实例", 根目录=corpus_root)
        return manager
    except Exception as e:
        log_error("语料库管理", "创建爽感语料库管理器失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="爽感语料库管理器初始化失败"
        )


# ==================== 基础语料库管理接口 ====================

@router.get("/info", response_model=CorpusInfoResponse, summary="获取语料库基本信息")
async def get_corpus_info(
    corpus_manager: CorpusManager = Depends(get_corpus_manager)
) -> CorpusInfoResponse:
    """
    📚 [语料库管理] 获取语料库基本信息
    
    返回语料库的基本信息，包括名称、版本、描述等
    """
    log_info("语料库管理", "获取语料库基本信息")
    
    try:
        # 获取配置信息
        config = corpus_manager.config
        
        # 统计文件数量和条目数量
        file_count = len(corpus_manager.loaded_files)
        total_entries = 0
        
        for corpus_data in corpus_manager.loaded_files.values():
            if hasattr(corpus_data, 'categories'):
                for category in corpus_data.categories.values():
                    if hasattr(category, 'items'):
                        total_entries += len(category.items)
        
        response = CorpusInfoResponse(
            name=config.get("name", "文心小说语料库"),
            version=config.get("version", "1.0.0"),
            description=config.get("description", "AI小说生成系统语料库"),
            category=config.get("category", "综合语料库"),
            tags=config.get("tags", []),
            file_count=file_count,
            total_entries=total_entries,
            last_updated=config.get("last_updated", "2025-08-05")
        )
        
        log_success("语料库管理", "成功获取语料库基本信息", 
                   文件数=file_count, 条目数=total_entries)
        
        return response
        
    except Exception as e:
        log_error("语料库管理", "获取语料库基本信息失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取语料库信息失败: {str(e)}"
        )


@router.get("/stats", response_model=CorpusStatsResponse, summary="获取语料库统计信息")
async def get_corpus_stats(
    corpus_manager: CorpusManager = Depends(get_corpus_manager)
) -> CorpusStatsResponse:
    """
    📊 [语料库管理] 获取语料库详细统计信息
    
    返回语料库的详细统计数据，包括文件分布、分类统计等
    """
    log_info("语料库管理", "获取语料库统计信息")
    
    try:
        total_files = len(corpus_manager.loaded_files)
        total_categories = 0
        total_entries = 0
        file_types = {}
        category_distribution = {}
        quality_distribution = {"高质量(>0.8)": 0, "中等质量(0.6-0.8)": 0, "低质量(<0.6)": 0}
        
        # 统计各项数据
        for file_path, corpus_data in corpus_manager.loaded_files.items():
            # 文件类型统计
            file_ext = Path(file_path).suffix
            file_types[file_ext] = file_types.get(file_ext, 0) + 1
            
            # 分类和条目统计
            if hasattr(corpus_data, 'categories'):
                total_categories += len(corpus_data.categories)
                
                for category_name, category in corpus_data.categories.items():
                    category_distribution[category_name] = category_distribution.get(category_name, 0) + 1
                    
                    if hasattr(category, 'items'):
                        total_entries += len(category.items)
                        
                        # 质量分布统计
                        for item in category.items:
                            if hasattr(item, 'metadata') and isinstance(item.metadata, dict):
                                quality = item.metadata.get('quality', 0.5)
                                if quality > 0.8:
                                    quality_distribution["高质量(>0.8)"] += 1
                                elif quality >= 0.6:
                                    quality_distribution["中等质量(0.6-0.8)"] += 1
                                else:
                                    quality_distribution["低质量(<0.6)"] += 1
        
        response = CorpusStatsResponse(
            total_files=total_files,
            total_categories=total_categories,
            total_entries=total_entries,
            file_types=file_types,
            category_distribution=category_distribution,
            quality_distribution=quality_distribution
        )
        
        log_success("语料库管理", "成功获取语料库统计信息", 
                   总文件数=total_files, 总分类数=total_categories, 总条目数=total_entries)
        
        return response
        
    except Exception as e:
        log_error("语料库管理", "获取语料库统计信息失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取语料库统计失败: {str(e)}"
        )


@router.get("/files", summary="获取语料库文件列表")
async def get_corpus_files(
    corpus_manager: CorpusManager = Depends(get_corpus_manager)
) -> JSONResponse:
    """
    📁 [语料库管理] 获取语料库文件列表
    
    返回所有已加载的语料库文件信息
    """
    log_info("语料库管理", "获取语料库文件列表")
    
    try:
        files_info = []
        
        for file_path, corpus_data in corpus_manager.loaded_files.items():
            file_info = {
                "file_path": file_path,
                "name": corpus_data.metadata.name if hasattr(corpus_data, 'metadata') else "未知",
                "version": corpus_data.metadata.version if hasattr(corpus_data, 'metadata') else "1.0.0",
                "description": corpus_data.metadata.description if hasattr(corpus_data, 'metadata') else "",
                "category": corpus_data.metadata.category if hasattr(corpus_data, 'metadata') else "",
                "categories_count": len(corpus_data.categories) if hasattr(corpus_data, 'categories') else 0,
                "entries_count": sum(
                    len(cat.items) for cat in corpus_data.categories.values() 
                    if hasattr(cat, 'items')
                ) if hasattr(corpus_data, 'categories') else 0
            }
            files_info.append(file_info)
        
        log_success("语料库管理", "成功获取语料库文件列表", 文件数=len(files_info))
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "成功获取语料库文件列表",
                "data": {
                    "total_files": len(files_info),
                    "files": files_info
                }
            }
        )
        
    except Exception as e:
        log_error("语料库管理", "获取语料库文件列表失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文件列表失败: {str(e)}"
        )


# ==================== 爽感语料库专用接口 ====================

@router.get("/pleasure/patterns", response_model=List[PleasurePatternResponse], summary="获取爽感模式")
async def get_pleasure_patterns(
    pleasure_type: str = Query(..., description="爽感类型 (face_slapping, power_fantasy, upgrade, etc.)"),
    count: int = Query(5, ge=1, le=20, description="获取数量"),
    min_quality: float = Query(0.6, ge=0.0, le=1.0, description="最小质量要求"),
    pleasure_manager: PleasureCorpusManager = Depends(get_pleasure_corpus_manager)
) -> List[PleasurePatternResponse]:
    """
    🎯 [爽感语料库] 获取指定类型的爽感模式

    根据爽感类型、数量和质量要求获取爽感模式数据
    """
    log_info("语料库管理", "获取爽感模式",
             爽感类型=pleasure_type, 数量=count, 最小质量=min_quality)

    try:
        # 获取爽感模式数据
        patterns = await pleasure_manager.get_pleasure_patterns_by_type(
            pleasure_type=pleasure_type,
            count=count,
            min_quality=min_quality
        )

        # 转换为响应模型
        response_patterns = []
        for pattern in patterns:
            response_pattern = PleasurePatternResponse(
                content=pattern.content,
                weight=pattern.weight,
                intensity=pattern.intensity,
                reliability=pattern.reliability,
                quality=pattern.quality,
                tags=pattern.tags,
                examples=pattern.examples,
                data=pattern.data
            )
            response_patterns.append(response_pattern)

        log_success("语料库管理", "成功获取爽感模式",
                   爽感类型=pleasure_type, 获取数量=len(response_patterns))

        return response_patterns

    except Exception as e:
        log_error("语料库管理", "获取爽感模式失败", error=e, 爽感类型=pleasure_type)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取爽感模式失败: {str(e)}"
        )


@router.get("/pleasure/rhythm-templates", response_model=List[RhythmTemplateResponse], summary="获取节奏模板")
async def get_rhythm_templates(
    template_name: Optional[str] = Query(None, description="模板名称（可选）"),
    pleasure_manager: PleasureCorpusManager = Depends(get_pleasure_corpus_manager)
) -> List[RhythmTemplateResponse]:
    """
    🎵 [爽感语料库] 获取节奏模板

    获取网文节奏模板，用于控制情节节奏和情感曲线
    """
    log_info("语料库管理", "获取节奏模板", 模板名称=template_name)

    try:
        templates = []

        if template_name:
            # 获取指定模板
            template = await pleasure_manager.get_rhythm_template(template_name)
            if template:
                response_template = RhythmTemplateResponse(
                    name=template.name,
                    description=template.description,
                    phases=template.phases,
                    emotional_curve=template.emotional_curve,
                    duration_range=template.duration_range
                )
                templates.append(response_template)
        else:
            # 获取所有可用模板名称
            available_templates = ["经典三段式", "五幕剧结构", "英雄之旅", "悬疑推理", "爽文节奏"]

            for tmpl_name in available_templates:
                template = await pleasure_manager.get_rhythm_template(tmpl_name)
                if template:
                    response_template = RhythmTemplateResponse(
                        name=template.name,
                        description=template.description,
                        phases=template.phases,
                        emotional_curve=template.emotional_curve,
                        duration_range=template.duration_range
                    )
                    templates.append(response_template)

        log_success("语料库管理", "成功获取节奏模板",
                   模板数量=len(templates), 指定模板=template_name)

        return templates

    except Exception as e:
        log_error("语料库管理", "获取节奏模板失败", error=e, 模板名称=template_name)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取节奏模板失败: {str(e)}"
        )


@router.get("/pleasure/enhancement-expressions", summary="获取强化表达")
async def get_enhancement_expressions(
    expression_type: str = Query(..., description="表达类型 (震惊反应, 愤怒爆发, 喜悦狂欢, etc.)"),
    count: int = Query(3, ge=1, le=10, description="获取数量"),
    pleasure_manager: PleasureCorpusManager = Depends(get_pleasure_corpus_manager)
) -> JSONResponse:
    """
    💥 [爽感语料库] 获取强化表达

    获取用于增强爽感效果的表达方式和描述
    """
    log_info("语料库管理", "获取强化表达",
             表达类型=expression_type, 数量=count)

    try:
        # 获取强化表达
        expressions = await pleasure_manager.get_enhancement_expressions(
            expression_type=expression_type,
            count=count
        )

        log_success("语料库管理", "成功获取强化表达",
                   表达类型=expression_type, 获取数量=len(expressions))

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "成功获取强化表达",
                "data": {
                    "expression_type": expression_type,
                    "count": len(expressions),
                    "expressions": expressions
                }
            }
        )

    except Exception as e:
        log_error("语料库管理", "获取强化表达失败", error=e, 表达类型=expression_type)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取强化表达失败: {str(e)}"
        )


@router.get("/pleasure/types", summary="获取支持的爽感类型")
async def get_pleasure_types() -> JSONResponse:
    """
    📋 [爽感语料库] 获取支持的爽感类型列表

    返回系统支持的所有爽感类型及其描述
    """
    log_info("语料库管理", "获取支持的爽感类型")

    try:
        pleasure_types = [
            {
                "type": "face_slapping",
                "name": "打脸爽感",
                "description": "通过身份反转、实力展现等方式产生的打脸效果",
                "keywords": ["反转", "逆袭", "装逼", "打脸", "震惊"]
            },
            {
                "type": "power_fantasy",
                "name": "权力幻想",
                "description": "展现绝对权威和不可挑战地位的权力幻想",
                "keywords": ["权威", "统治", "威压", "服从", "王者"]
            },
            {
                "type": "upgrade",
                "name": "升级进化",
                "description": "通过修炼、突破等方式实现实力提升的爽感",
                "keywords": ["突破", "升级", "进化", "修炼", "境界"]
            },
            {
                "type": "recognition",
                "name": "认可赞赏",
                "description": "获得他人认可、赞赏和尊重的满足感",
                "keywords": ["认可", "赞赏", "尊重", "崇拜", "仰慕"]
            },
            {
                "type": "revenge",
                "name": "复仇快感",
                "description": "对敌人进行报复和惩罚的快感",
                "keywords": ["复仇", "报复", "惩罚", "清算", "血债"]
            },
            {
                "type": "mystery_solving",
                "name": "解谜成就",
                "description": "解开谜题、发现真相的成就感",
                "keywords": ["解谜", "真相", "发现", "推理", "揭秘"]
            }
        ]

        log_success("语料库管理", "成功获取爽感类型列表", 类型数量=len(pleasure_types))

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "成功获取爽感类型列表",
                "data": {
                    "total_types": len(pleasure_types),
                    "pleasure_types": pleasure_types
                }
            }
        )

    except Exception as e:
        log_error("语料库管理", "获取爽感类型列表失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取爽感类型失败: {str(e)}"
        )


# ==================== 语料库管理操作接口 ====================

@router.post("/reload", summary="重新加载语料库")
async def reload_corpus(
    file_path: Optional[str] = Query(None, description="指定文件路径（可选，为空则重载所有）"),
    corpus_manager: CorpusManager = Depends(get_corpus_manager)
) -> JSONResponse:
    """
    🔄 [语料库管理] 重新加载语料库文件

    重新加载指定的语料库文件或所有文件，用于更新语料库数据
    """
    log_info("语料库管理", "重新加载语料库", 文件路径=file_path)

    try:
        if file_path:
            # 重新加载指定文件
            corpus_data = await corpus_manager.load_corpus_file(file_path)

            log_success("语料库管理", "成功重新加载指定文件", 文件路径=file_path)

            return JSONResponse(
                status_code=200,
                content={
                    "success": True,
                    "message": f"成功重新加载文件: {file_path}",
                    "data": {
                        "file_path": file_path,
                        "categories_count": len(corpus_data.categories) if hasattr(corpus_data, 'categories') else 0
                    }
                }
            )
        else:
            # 重新加载所有文件
            reload_count = 0
            errors = []

            # 获取所有已加载的文件路径
            file_paths = list(corpus_manager.loaded_files.keys())

            for fp in file_paths:
                try:
                    await corpus_manager.load_corpus_file(fp)
                    reload_count += 1
                except Exception as e:
                    errors.append(f"{fp}: {str(e)}")

            log_success("语料库管理", "完成批量重新加载",
                       成功数量=reload_count, 错误数量=len(errors))

            return JSONResponse(
                status_code=200,
                content={
                    "success": True,
                    "message": f"成功重新加载 {reload_count} 个文件",
                    "data": {
                        "reloaded_count": reload_count,
                        "total_files": len(file_paths),
                        "errors": errors
                    }
                }
            )

    except Exception as e:
        log_error("语料库管理", "重新加载语料库失败", error=e, 文件路径=file_path)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重新加载失败: {str(e)}"
        )


@router.post("/validate", response_model=CorpusValidationResponse, summary="验证语料库文件")
async def validate_corpus_file(
    file_path: str = Query(..., description="要验证的文件路径"),
    corpus_manager: CorpusManager = Depends(get_corpus_manager)
) -> CorpusValidationResponse:
    """
    ✅ [语料库管理] 验证语料库文件格式和内容

    检查语料库文件的格式是否正确，内容是否符合规范
    """
    log_info("语料库管理", "验证语料库文件", 文件路径=file_path)

    try:
        errors = []
        warnings = []
        suggestions = []

        # 检查文件是否存在
        full_path = corpus_manager.corpus_root / file_path
        if not full_path.exists():
            errors.append(f"文件不存在: {full_path}")
            return CorpusValidationResponse(
                is_valid=False,
                file_path=file_path,
                errors=errors,
                warnings=warnings,
                suggestions=suggestions
            )

        # 尝试加载和验证文件
        try:
            # 读取文件内容
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析JSON
            try:
                data = json.loads(content)
            except json.JSONDecodeError as e:
                errors.append(f"JSON格式错误: {str(e)}")
                return CorpusValidationResponse(
                    is_valid=False,
                    file_path=file_path,
                    errors=errors,
                    warnings=warnings,
                    suggestions=suggestions
                )

            # 验证必需字段
            required_fields = ["name", "version", "description", "category", "data"]
            for field in required_fields:
                if field not in data:
                    errors.append(f"缺少必需字段: {field}")

            # 验证数据结构
            if "data" in data:
                data_section = data["data"]
                if "categories" not in data_section:
                    warnings.append("data部分缺少categories字段")
                else:
                    categories = data_section["categories"]
                    if not isinstance(categories, dict):
                        errors.append("categories必须是字典类型")
                    else:
                        # 验证每个分类
                        for cat_name, cat_data in categories.items():
                            if not isinstance(cat_data, dict):
                                errors.append(f"分类 {cat_name} 必须是字典类型")
                                continue

                            if "items" not in cat_data:
                                warnings.append(f"分类 {cat_name} 缺少items字段")
                                continue

                            items = cat_data["items"]
                            if not isinstance(items, list):
                                errors.append(f"分类 {cat_name} 的items必须是列表类型")
                                continue

                            # 验证条目
                            for i, item in enumerate(items):
                                if not isinstance(item, dict):
                                    errors.append(f"分类 {cat_name} 的第 {i+1} 个条目必须是字典类型")
                                    continue

                                if "content" not in item:
                                    warnings.append(f"分类 {cat_name} 的第 {i+1} 个条目缺少content字段")

                                if "metadata" in item and isinstance(item["metadata"], dict):
                                    metadata = item["metadata"]
                                    if "quality" in metadata:
                                        quality = metadata["quality"]
                                        if not isinstance(quality, (int, float)) or not (0 <= quality <= 1):
                                            warnings.append(f"分类 {cat_name} 的第 {i+1} 个条目的quality值应在0-1之间")

            # 生成建议
            if not errors:
                suggestions.append("文件格式正确，建议定期更新内容以保持语料库的时效性")
                if warnings:
                    suggestions.append("建议修复警告中提到的问题以提高语料库质量")

            is_valid = len(errors) == 0

            log_success("语料库管理", "文件验证完成",
                       文件路径=file_path, 是否有效=is_valid,
                       错误数=len(errors), 警告数=len(warnings))

            return CorpusValidationResponse(
                is_valid=is_valid,
                file_path=file_path,
                errors=errors,
                warnings=warnings,
                suggestions=suggestions
            )

        except Exception as e:
            errors.append(f"文件处理错误: {str(e)}")
            return CorpusValidationResponse(
                is_valid=False,
                file_path=file_path,
                errors=errors,
                warnings=warnings,
                suggestions=suggestions
            )

    except Exception as e:
        log_error("语料库管理", "验证语料库文件失败", error=e, 文件路径=file_path)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证文件失败: {str(e)}"
        )


# ==================== 搜索和查询接口 ====================

@router.get("/search", summary="搜索语料库内容")
async def search_corpus(
    query: str = Query(..., description="搜索关键词"),
    category: Optional[str] = Query(None, description="限定分类（可选）"),
    min_quality: float = Query(0.0, ge=0.0, le=1.0, description="最小质量要求"),
    limit: int = Query(10, ge=1, le=50, description="返回结果数量限制"),
    corpus_manager: CorpusManager = Depends(get_corpus_manager)
) -> JSONResponse:
    """
    🔍 [语料库管理] 搜索语料库内容

    在语料库中搜索包含指定关键词的内容
    """
    log_info("语料库管理", "搜索语料库内容",
             查询词=query, 限定分类=category, 最小质量=min_quality, 限制数量=limit)

    try:
        results = []

        # 遍历所有已加载的文件
        for file_path, corpus_data in corpus_manager.loaded_files.items():
            if not hasattr(corpus_data, 'categories'):
                continue

            # 遍历所有分类
            for cat_name, cat_data in corpus_data.categories.items():
                # 如果指定了分类，则跳过不匹配的分类
                if category and cat_name != category:
                    continue

                if not hasattr(cat_data, 'items'):
                    continue

                # 遍历所有条目
                for item in cat_data.items:
                    # 检查质量要求
                    if hasattr(item, 'metadata') and isinstance(item.metadata, dict):
                        quality = item.metadata.get('quality', 0.5)
                        if quality < min_quality:
                            continue

                    # 检查内容是否包含查询词
                    content = getattr(item, 'content', '')
                    if query.lower() in content.lower():
                        result_item = {
                            "file_path": file_path,
                            "category": cat_name,
                            "content": content,
                            "metadata": item.metadata if hasattr(item, 'metadata') else {},
                            "quality": item.metadata.get('quality', 0.5) if hasattr(item, 'metadata') and isinstance(item.metadata, dict) else 0.5
                        }
                        results.append(result_item)

                        # 检查是否达到限制数量
                        if len(results) >= limit:
                            break

                if len(results) >= limit:
                    break

            if len(results) >= limit:
                break

        # 按质量排序
        results.sort(key=lambda x: x['quality'], reverse=True)

        log_success("语料库管理", "搜索完成",
                   查询词=query, 找到结果=len(results))

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"搜索完成，找到 {len(results)} 个结果",
                "data": {
                    "query": query,
                    "category": category,
                    "min_quality": min_quality,
                    "total_results": len(results),
                    "results": results
                }
            }
        )

    except Exception as e:
        log_error("语料库管理", "搜索语料库内容失败", error=e, 查询词=query)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索失败: {str(e)}"
        )


@router.get("/categories", summary="获取所有分类")
async def get_all_categories(
    corpus_manager: CorpusManager = Depends(get_corpus_manager)
) -> JSONResponse:
    """
    📂 [语料库管理] 获取所有可用的分类

    返回语料库中所有可用的分类及其统计信息
    """
    log_info("语料库管理", "获取所有分类")

    try:
        categories_info = {}

        # 遍历所有已加载的文件
        for file_path, corpus_data in corpus_manager.loaded_files.items():
            if not hasattr(corpus_data, 'categories'):
                continue

            # 遍历所有分类
            for cat_name, cat_data in corpus_data.categories.items():
                if cat_name not in categories_info:
                    categories_info[cat_name] = {
                        "name": cat_name,
                        "files": [],
                        "total_items": 0,
                        "description": getattr(cat_data, 'description', '') if hasattr(cat_data, 'description') else ''
                    }

                categories_info[cat_name]["files"].append(file_path)

                if hasattr(cat_data, 'items'):
                    categories_info[cat_name]["total_items"] += len(cat_data.items)

        # 转换为列表格式
        categories_list = list(categories_info.values())

        log_success("语料库管理", "成功获取所有分类", 分类数量=len(categories_list))

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"成功获取 {len(categories_list)} 个分类",
                "data": {
                    "total_categories": len(categories_list),
                    "categories": categories_list
                }
            }
        )

    except Exception as e:
        log_error("语料库管理", "获取所有分类失败", error=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分类失败: {str(e)}"
        )


# ==================== 健康检查接口 ====================

@router.get("/health", summary="语料库系统健康检查")
async def corpus_health_check() -> JSONResponse:
    """
    🔧 [语料库管理] 语料库系统健康检查

    检查语料库系统的运行状态和数据完整性
    """
    log_info("语料库管理", "执行语料库系统健康检查")

    try:
        health_status = {
            "status": "healthy",
            "timestamp": "2025-08-05T12:00:00Z",
            "components": {}
        }

        # 检查基础语料库管理器
        try:
            corpus_manager = await get_corpus_manager()
            health_status["components"]["corpus_manager"] = {
                "status": "healthy",
                "loaded_files": len(corpus_manager.loaded_files),
                "message": "基础语料库管理器运行正常"
            }
        except Exception as e:
            health_status["components"]["corpus_manager"] = {
                "status": "unhealthy",
                "error": str(e),
                "message": "基础语料库管理器异常"
            }
            health_status["status"] = "degraded"

        # 检查爽感语料库管理器
        try:
            pleasure_manager = await get_pleasure_corpus_manager()
            health_status["components"]["pleasure_corpus_manager"] = {
                "status": "healthy",
                "message": "爽感语料库管理器运行正常"
            }
        except Exception as e:
            health_status["components"]["pleasure_corpus_manager"] = {
                "status": "unhealthy",
                "error": str(e),
                "message": "爽感语料库管理器异常"
            }
            health_status["status"] = "degraded"

        # 检查语料库文件完整性
        corpus_root = Path(__file__).parent.parent.parent / "data" / "corpus"
        if corpus_root.exists():
            health_status["components"]["corpus_files"] = {
                "status": "healthy",
                "corpus_root": str(corpus_root),
                "message": "语料库目录存在"
            }
        else:
            health_status["components"]["corpus_files"] = {
                "status": "unhealthy",
                "corpus_root": str(corpus_root),
                "message": "语料库目录不存在"
            }
            health_status["status"] = "unhealthy"

        log_success("语料库管理", "健康检查完成", 系统状态=health_status["status"])

        return JSONResponse(
            status_code=200 if health_status["status"] == "healthy" else 503,
            content={
                "success": health_status["status"] in ["healthy", "degraded"],
                "message": f"语料库系统状态: {health_status['status']}",
                "data": health_status
            }
        )

    except Exception as e:
        log_error("语料库管理", "健康检查失败", error=e)
        return JSONResponse(
            status_code=503,
            content={
                "success": False,
                "message": "健康检查失败",
                "error": str(e)
            }
        )
