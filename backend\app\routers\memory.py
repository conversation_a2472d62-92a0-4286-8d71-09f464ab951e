"""
🧠 [记忆] 记忆嵌入API路由
提供章节记忆的嵌入、搜索、统计和管理功能
"""

import time
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Path, Query
from fastapi.responses import JSONResponse

from app.schemas.memory import (
    MemoryEmbedRequest, MemoryEmbedResponse,
    MemorySearchRequest, MemorySearchResponse,
    MemoryStatsResponse, MemoryDeleteResponse,
    AutoSummaryRequest, AutoSummaryResponse,
    MemoryDocument as MemoryDocumentSchema,
    MemorySearchResult as MemorySearchResultSchema,
    MemoryType
)
from app.core.config import log_info, log_error, log_debug, log_success
from app.core.database import get_chapter_repository, get_bible_repository
from app.repositories.chapter_repo import ChapterRepository
from app.repositories.bible_repo import StoryBibleRepository
from app.services.vector_store import (
    VectorStoreManager, MemoryDocument, SearchResult, get_vector_store
)
from app.models.story_bible import GenerationStatus


# 创建路由器
router = APIRouter(prefix="/api/v1/memory", tags=["记忆嵌入"])


@router.post("/embed", response_model=MemoryEmbedResponse, summary="嵌入章节记忆")
async def embed_chapter_memory(
    request: MemoryEmbedRequest,
    chapter_repo: ChapterRepository = Depends(get_chapter_repository),
    vector_store: VectorStoreManager = Depends(get_vector_store)
) -> MemoryEmbedResponse:
    """
    🧠 [记忆] 嵌入章节记忆
    
    将指定章节的内容转换为向量并存储到记忆数据库中，支持自动摘要生成。
    
    Args:
        request: 记忆嵌入请求
        chapter_repo: 章节仓库
        vector_store: 向量存储管理器
        
    Returns:
        MemoryEmbedResponse: 嵌入结果响应
    """
    log_info("记忆", "开始嵌入章节记忆", 
             故事圣经ID=request.story_bible_id, 
             章节号=request.chapter_number)
    
    try:
        # 获取章节信息
        chapter = await chapter_repo.get_by_story_and_number(
            request.story_bible_id, 
            request.chapter_number
        )
        
        if not chapter:
            raise HTTPException(
                status_code=404,
                detail=f"章节不存在: 故事ID={request.story_bible_id}, 章节号={request.chapter_number}"
            )
        
        # 检查章节是否已完成生成
        if chapter.status != GenerationStatus.COMPLETED:
            raise HTTPException(
                status_code=400,
                detail=f"章节尚未完成生成，当前状态: {chapter.status.value}"
            )
        
        # 确定要嵌入的内容
        content = request.content or chapter.generated_content
        if not content:
            raise HTTPException(
                status_code=400,
                detail="章节内容为空，无法进行记忆嵌入"
            )
        
        # 生成摘要（如果未提供）
        summary = request.summary
        if not summary:
            summary = await _generate_auto_summary(content)
        
        # 创建记忆文档
        memory_id = f"chapter_{chapter.story_bible_id}_{chapter.chapter_number}_{int(time.time())}"
        memory_doc = MemoryDocument(
            id=memory_id,
            content=content,
            summary=summary,
            story_id=int(chapter.story_bible_id.split('_')[-1]) if '_' in chapter.story_bible_id else hash(chapter.story_bible_id) % 1000000,
            chapter_id=chapter.chapter_number,
            memory_type=request.memory_type.value,
            importance_score=request.importance_score,
            metadata={
                "chapter_title": chapter.chapter_title,
                "chapter_outline": chapter.chapter_outline,
                "word_count": len(content),
                "ai_provider": chapter.ai_provider.value,
                **(request.metadata or {})
            }
        )
        
        # 存储到向量数据库
        stored_id = await vector_store.add_memory(memory_doc)
        
        log_success("记忆", "章节记忆嵌入成功", 
                   记忆ID=stored_id,
                   章节ID=chapter.id,
                   摘要长度=len(summary))
        
        return MemoryEmbedResponse(
            success=True,
            memory_id=stored_id,
            story_id=memory_doc.story_id,
            chapter_id=memory_doc.chapter_id,
            summary=summary,
            embedding_dimension=384,  # sentence-transformers模型维度
            message=f"章节记忆嵌入成功，记忆ID: {stored_id}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("记忆", "章节记忆嵌入失败", error=e, 
                 故事圣经ID=request.story_bible_id, 
                 章节号=request.chapter_number)
        raise HTTPException(
            status_code=500,
            detail=f"记忆嵌入失败: {str(e)}"
        )


@router.post("/search", response_model=MemorySearchResponse, summary="搜索记忆")
async def search_memories(
    request: MemorySearchRequest,
    vector_store: VectorStoreManager = Depends(get_vector_store)
) -> MemorySearchResponse:
    """
    🔍 [搜索] 基于语义相似度搜索记忆
    
    根据查询文本在记忆数据库中搜索最相关的记忆片段。
    
    Args:
        request: 记忆搜索请求
        vector_store: 向量存储管理器
        
    Returns:
        MemorySearchResponse: 搜索结果响应
    """
    log_info("记忆", "开始搜索记忆", 
             查询文本=request.query, 
             故事ID=request.story_id,
             记忆类型=request.memory_type)
    
    start_time = time.time()
    
    try:
        # 执行向量搜索
        search_results = await vector_store.search_memories(
            query=request.query,
            n_results=request.n_results,
            story_id=request.story_id,
            memory_type=request.memory_type.value if request.memory_type else None,
            min_similarity=request.min_similarity
        )
        
        # 转换为响应格式
        results = []
        total_similarity = 0.0
        
        for result in search_results:
            # 转换MemoryDocument为Schema格式
            doc_schema = MemoryDocumentSchema(
                id=result.document.id,
                content=result.document.content,
                summary=result.document.summary,
                story_id=result.document.story_id,
                chapter_id=result.document.chapter_id,
                memory_type=MemoryType(result.document.memory_type),
                importance_score=result.document.importance_score,
                created_at=result.document.created_at,
                metadata=result.document.metadata
            )
            
            result_schema = MemorySearchResultSchema(
                document=doc_schema,
                similarity_score=result.similarity_score,
                distance=result.distance
            )
            
            results.append(result_schema)
            total_similarity += result.similarity_score
        
        # 计算平均相似度
        avg_similarity = total_similarity / len(results) if results else 0.0
        search_time = (time.time() - start_time) * 1000  # 转换为毫秒
        
        log_success("记忆", "记忆搜索完成", 
                   查询文本=request.query,
                   结果数量=len(results),
                   平均相似度=f"{avg_similarity:.3f}",
                   搜索耗时=f"{search_time:.2f}ms")
        
        return MemorySearchResponse(
            success=True,
            query=request.query,
            total_results=len(results),
            results=results,
            average_similarity=avg_similarity,
            search_time_ms=search_time
        )
        
    except Exception as e:
        log_error("记忆", "记忆搜索失败", error=e, 查询文本=request.query)
        raise HTTPException(
            status_code=500,
            detail=f"记忆搜索失败: {str(e)}"
        )


@router.get("/stats/{story_id}", response_model=MemoryStatsResponse, summary="获取故事记忆统计")
async def get_story_memory_stats(
    story_id: int = Path(..., description="故事ID"),
    vector_store: VectorStoreManager = Depends(get_vector_store)
) -> MemoryStatsResponse:
    """
    📊 [统计] 获取指定故事的记忆统计信息

    Args:
        story_id: 故事ID
        vector_store: 向量存储管理器

    Returns:
        MemoryStatsResponse: 记忆统计响应
    """
    log_info("记忆", "获取故事记忆统计", 故事ID=story_id)

    try:
        # 搜索该故事的所有记忆
        all_memories = await vector_store.search_memories(
            query="",  # 空查询获取所有记忆
            n_results=1000,  # 设置较大数量获取所有记忆
            story_id=story_id
        )

        # 统计各类型记忆数量
        memory_types = {}
        total_importance = 0.0
        latest_time = None

        for result in all_memories:
            memory_type = result.document.memory_type
            memory_types[memory_type] = memory_types.get(memory_type, 0) + 1
            total_importance += result.document.importance_score

            # 更新最新时间
            if latest_time is None or result.document.created_at > latest_time:
                latest_time = result.document.created_at

        # 计算平均重要性
        avg_importance = total_importance / len(all_memories) if all_memories else 0.0

        log_success("记忆", "故事记忆统计完成",
                   故事ID=story_id,
                   总记忆数=len(all_memories),
                   平均重要性=f"{avg_importance:.3f}")

        return MemoryStatsResponse(
            success=True,
            story_id=story_id,
            total_memories=len(all_memories),
            memory_types=memory_types,
            average_importance=avg_importance,
            latest_memory_time=latest_time,
            embedding_dimension=384
        )

    except Exception as e:
        log_error("记忆", "获取故事记忆统计失败", error=e, 故事ID=story_id)
        raise HTTPException(
            status_code=500,
            detail=f"获取记忆统计失败: {str(e)}"
        )


@router.delete("/story/{story_id}", response_model=MemoryDeleteResponse, summary="删除故事记忆")
async def delete_story_memories(
    story_id: int = Path(..., description="故事ID"),
    vector_store: VectorStoreManager = Depends(get_vector_store)
) -> MemoryDeleteResponse:
    """
    🗑️ [删除] 删除指定故事的所有记忆

    Args:
        story_id: 故事ID
        vector_store: 向量存储管理器

    Returns:
        MemoryDeleteResponse: 删除结果响应
    """
    log_info("记忆", "开始删除故事记忆", 故事ID=story_id)

    try:
        # 删除故事的所有记忆
        deleted_count = await vector_store.delete_memories_by_story(story_id)

        log_success("记忆", "故事记忆删除完成",
                   故事ID=story_id,
                   删除数量=deleted_count)

        return MemoryDeleteResponse(
            success=True,
            story_id=story_id,
            deleted_count=deleted_count,
            message=f"成功删除故事 {story_id} 的 {deleted_count} 个记忆"
        )

    except Exception as e:
        log_error("记忆", "删除故事记忆失败", error=e, 故事ID=story_id)
        raise HTTPException(
            status_code=500,
            detail=f"删除故事记忆失败: {str(e)}"
        )


@router.post("/summary", response_model=AutoSummaryResponse, summary="生成自动摘要")
async def generate_auto_summary(
    request: AutoSummaryRequest
) -> AutoSummaryResponse:
    """
    📝 [摘要] 生成内容的自动摘要

    Args:
        request: 自动摘要请求

    Returns:
        AutoSummaryResponse: 摘要生成响应
    """
    log_info("记忆", "开始生成自动摘要", 内容长度=len(request.content))

    try:
        summary = await _generate_auto_summary(request.content, request.max_length)

        # 提取关键词（简单实现）
        keywords = _extract_keywords(request.content, request.focus_keywords)

        compression_ratio = len(summary) / len(request.content) if request.content else 0.0

        log_success("记忆", "自动摘要生成完成",
                   原文长度=len(request.content),
                   摘要长度=len(summary),
                   压缩比=f"{compression_ratio:.3f}")

        return AutoSummaryResponse(
            success=True,
            summary=summary,
            original_length=len(request.content),
            summary_length=len(summary),
            compression_ratio=compression_ratio,
            keywords=keywords
        )

    except Exception as e:
        log_error("记忆", "自动摘要生成失败", error=e)
        raise HTTPException(
            status_code=500,
            detail=f"摘要生成失败: {str(e)}"
        )


async def _generate_auto_summary(content: str, max_length: int = 100) -> str:
    """
    📝 [摘要] 生成内容的自动摘要

    Args:
        content: 待摘要的内容
        max_length: 摘要最大长度

    Returns:
        str: 生成的摘要
    """
    # 简单的摘要生成逻辑（后续可以集成AI摘要服务）
    sentences = content.replace('。', '。\n').replace('！', '！\n').replace('？', '？\n').split('\n')
    sentences = [s.strip() for s in sentences if s.strip()]

    if not sentences:
        return "无内容摘要"

    # 取前几句作为摘要
    summary = ""
    for sentence in sentences:
        if len(summary + sentence) <= max_length:
            summary += sentence
        else:
            break

    return summary or sentences[0][:max_length]


def _extract_keywords(content: str, focus_keywords: Optional[List[str]] = None) -> List[str]:
    """
    🔍 [关键词] 提取内容关键词

    Args:
        content: 内容文本
        focus_keywords: 重点关键词

    Returns:
        List[str]: 提取的关键词列表
    """
    # 简单的关键词提取逻辑
    keywords = []

    # 添加重点关键词
    if focus_keywords:
        keywords.extend(focus_keywords)

    # 简单的词频统计（可以后续优化）
    common_words = ['的', '了', '在', '是', '有', '和', '就', '不', '人', '都', '一', '个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这样']
    words = []
    for char in content:
        if char.isalpha() or char in '一二三四五六七八九十':
            words.append(char)

    # 简单返回前几个字符作为关键词
    extracted = [content[i:i+2] for i in range(0, min(len(content), 20), 3) if content[i:i+2] not in common_words]
    keywords.extend(extracted[:5])

    return list(set(keywords))[:10]  # 去重并限制数量
