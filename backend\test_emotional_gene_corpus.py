#!/usr/bin/env python3
"""
🧬 [测试脚本] 情感基因语料库系统测试

测试情感基因语料库管理器的各项功能，包括：
1. 语料库文件加载
2. 情感基因数据提取
3. 生理反应数据获取
4. 感官触发器数据获取
5. 情感标签列表获取

作者: 文心小说后端服务系统
创建时间: 2025-08-05
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import log_info, log_debug, log_error, log_success
from app.core.emotional_gene_corpus_manager import create_emotional_gene_corpus_manager


async def test_corpus_initialization():
    """测试语料库初始化"""
    log_info("测试", "🚀 开始测试情感基因语料库初始化")
    
    try:
        # 创建语料库管理器
        corpus_manager = create_emotional_gene_corpus_manager()
        
        # 初始化
        await corpus_manager.initialize()
        
        log_success("测试", "✅ 语料库初始化测试通过")
        return corpus_manager
        
    except Exception as e:
        log_error("测试", "❌ 语料库初始化测试失败", error=e)
        return None


async def test_emotion_gene_extraction(corpus_manager):
    """测试情感基因数据提取"""
    log_info("测试", "🔍 开始测试情感基因数据提取")
    
    test_emotions = ["喜悦", "愤怒", "悲伤", "恐惧", "怀旧", "忧郁"]
    
    for emotion in test_emotions:
        try:
            gene_data_list = await corpus_manager.get_emotion_genes_by_tag(
                emotion_tag=emotion,
                count=3,
                min_quality=0.5
            )
            
            log_info("测试", f"情感 '{emotion}' 提取结果", 数量=len(gene_data_list))
            
            for i, gene_data in enumerate(gene_data_list):
                log_debug("测试", f"基因数据 {i+1}",
                         情感标签=gene_data.emotion_tag,
                         生理反应数=len(gene_data.physiological_reactions),
                         感官触发器数=len(gene_data.sensory_triggers),
                         熵增项目数=len(gene_data.entropy_items),
                         质量评分=gene_data.quality_score)
                
                # 显示部分数据内容
                if gene_data.physiological_reactions:
                    log_debug("测试", f"生理反应示例", 
                             示例=gene_data.physiological_reactions[:2])
                
                if gene_data.sensory_triggers:
                    log_debug("测试", f"感官触发器示例", 
                             示例=gene_data.sensory_triggers[:2])
            
        except Exception as e:
            log_error("测试", f"情感 '{emotion}' 提取失败", error=e)
    
    log_success("测试", "✅ 情感基因数据提取测试完成")


async def test_physiological_reactions(corpus_manager):
    """测试生理反应数据获取"""
    log_info("测试", "💓 开始测试生理反应数据获取")
    
    try:
        # 测试不同系统类型
        system_types = ["心血管系统", "呼吸系统", "肌肉系统", "神经系统"]
        
        for system_type in system_types:
            reactions = await corpus_manager.get_physiological_reactions(
                system_type=system_type,
                count=5
            )
            
            log_info("测试", f"系统类型 '{system_type}' 反应数据", 数量=len(reactions))
            if reactions:
                log_debug("测试", f"反应示例", 示例=reactions[:3])
        
        # 测试不同强度等级
        intensity_levels = ["轻微", "中等", "强烈", "极端"]
        
        for level in intensity_levels:
            reactions = await corpus_manager.get_physiological_reactions(
                intensity_level=level,
                count=5
            )
            
            log_info("测试", f"强度等级 '{level}' 反应数据", 数量=len(reactions))
            if reactions:
                log_debug("测试", f"反应示例", 示例=reactions[:3])
        
        log_success("测试", "✅ 生理反应数据获取测试完成")
        
    except Exception as e:
        log_error("测试", "❌ 生理反应数据获取测试失败", error=e)


async def test_sensory_triggers(corpus_manager):
    """测试感官触发器数据获取"""
    log_info("测试", "👁️ 开始测试感官触发器数据获取")
    
    try:
        # 测试不同感官类型
        sense_types = ["视觉", "听觉", "嗅觉", "触觉", "味觉"]
        
        for sense_type in sense_types:
            triggers = await corpus_manager.get_sensory_triggers(
                sense_type=sense_type,
                count=5
            )
            
            log_info("测试", f"感官类型 '{sense_type}' 触发器数据", 数量=len(triggers))
            if triggers:
                log_debug("测试", f"触发器示例", 示例=triggers[:3])
        
        # 测试情感上下文过滤
        emotion_contexts = ["快乐", "愤怒", "悲伤", "恐惧"]
        
        for emotion in emotion_contexts:
            triggers = await corpus_manager.get_sensory_triggers(
                emotion_context=emotion,
                count=5
            )
            
            log_info("测试", f"情感上下文 '{emotion}' 触发器数据", 数量=len(triggers))
            if triggers:
                log_debug("测试", f"触发器示例", 示例=triggers[:3])
        
        log_success("测试", "✅ 感官触发器数据获取测试完成")
        
    except Exception as e:
        log_error("测试", "❌ 感官触发器数据获取测试失败", error=e)


async def test_emotion_tags(corpus_manager):
    """测试情感标签列表获取"""
    log_info("测试", "📋 开始测试情感标签列表获取")
    
    try:
        emotion_tags = await corpus_manager.get_all_emotion_tags()
        
        log_info("测试", "所有可用情感标签", 数量=len(emotion_tags))
        log_debug("测试", "情感标签列表", 标签=emotion_tags)
        
        log_success("测试", "✅ 情感标签列表获取测试完成")
        
    except Exception as e:
        log_error("测试", "❌ 情感标签列表获取测试失败", error=e)


async def main():
    """主测试函数"""
    log_info("测试", "🧬 开始情感基因语料库系统测试")
    
    # 测试语料库初始化
    corpus_manager = await test_corpus_initialization()
    if not corpus_manager:
        log_error("测试", "❌ 语料库初始化失败，终止测试")
        return
    
    # 测试各项功能
    await test_emotion_gene_extraction(corpus_manager)
    await test_physiological_reactions(corpus_manager)
    await test_sensory_triggers(corpus_manager)
    await test_emotion_tags(corpus_manager)
    
    log_success("测试", "🎉 情感基因语料库系统测试完成")


if __name__ == "__main__":
    asyncio.run(main())
