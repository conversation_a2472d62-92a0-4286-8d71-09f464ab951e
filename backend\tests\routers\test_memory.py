"""
🧠 [记忆] 记忆嵌入API路由测试
测试记忆嵌入工作流的各个API端点功能
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient
from httpx import AsyncClient

from main import app
from app.schemas.memory import MemoryType
from app.services.vector_store import MemoryDocument, SearchResult
from app.models.story_bible import Chapter, GenerationStatus, AIProvider


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """设置测试环境"""
    # 模拟数据库初始化
    with patch('app.core.database.init_database'):
        with patch('app.services.vector_store.get_vector_store'):
            yield


class TestMemoryEmbedAPI:
    """记忆嵌入API测试类"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_chapter(self):
        """模拟章节数据"""
        chapter = MagicMock(spec=Chapter)
        chapter.id = "chapter_001"
        chapter.story_bible_id = "bible_001"
        chapter.chapter_number = 1
        chapter.chapter_title = "第一章：开始"
        chapter.chapter_outline = "故事的开始，主角登场"
        chapter.generated_content = "这是一个美丽的早晨，阳光透过窗户洒在桌案上。李明缓缓睁开眼睛，新的一天开始了。"
        chapter.status = GenerationStatus.COMPLETED
        chapter.ai_provider = AIProvider.ZHIPU
        return chapter
    
    @pytest.fixture
    def mock_memory_document(self):
        """模拟记忆文档"""
        return MemoryDocument(
            id="memory_001",
            content="这是一个美丽的早晨，阳光透过窗户洒在桌案上。",
            summary="美丽的早晨",
            story_id=1,
            chapter_id=1,
            memory_type="chapter",
            importance_score=0.8
        )
    
    @patch('app.core.database.get_chapter_repository')
    @patch('app.services.vector_store.get_vector_store')
    def test_embed_chapter_memory_success(self, mock_vector_store, mock_chapter_repo, client, mock_chapter):
        """测试成功嵌入章节记忆"""
        # 设置模拟
        mock_repo = AsyncMock()
        mock_repo.get_by_story_and_number.return_value = mock_chapter
        mock_chapter_repo.return_value = mock_repo

        mock_store = AsyncMock()
        mock_store.add_memory.return_value = "memory_001"
        mock_vector_store.return_value = mock_store

        # 发送请求
        response = client.post("/api/v1/memory/embed", json={
            "story_bible_id": "bible_001",
            "chapter_number": 1,
            "memory_type": "chapter",
            "importance_score": 0.8
        })

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["memory_id"] == "memory_001"
        assert "summary" in data
        assert data["embedding_dimension"] == 384
    
    @patch('app.core.database.get_chapter_repository')
    def test_embed_chapter_memory_not_found(self, mock_chapter_repo, client):
        """测试章节不存在的情况"""
        # 设置模拟
        mock_repo = AsyncMock()
        mock_repo.get_by_story_and_number.return_value = None
        mock_chapter_repo.return_value = mock_repo

        # 发送请求
        response = client.post("/api/v1/memory/embed", json={
            "story_bible_id": "bible_001",
            "chapter_number": 999
        })

        # 验证响应
        assert response.status_code == 404
        assert "章节不存在" in response.json()["detail"]

    @patch('app.core.database.get_chapter_repository')
    def test_embed_chapter_memory_not_completed(self, mock_chapter_repo, client, mock_chapter):
        """测试章节未完成生成的情况"""
        # 设置模拟
        mock_chapter.status = GenerationStatus.GENERATING
        mock_repo = AsyncMock()
        mock_repo.get_by_story_and_number.return_value = mock_chapter
        mock_chapter_repo.return_value = mock_repo

        # 发送请求
        response = client.post("/api/v1/memory/embed", json={
            "story_bible_id": "bible_001",
            "chapter_number": 1
        })

        # 验证响应
        assert response.status_code == 400
        assert "尚未完成生成" in response.json()["detail"]


class TestMemorySearchAPI:
    """记忆搜索API测试类"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_search_results(self, mock_memory_document):
        """模拟搜索结果"""
        return [
            SearchResult(
                document=mock_memory_document,
                similarity_score=0.95,
                distance=0.05
            )
        ]
    
    @patch('app.services.vector_store.get_vector_store')
    def test_search_memories_success(self, mock_vector_store, client, mock_search_results):
        """测试成功搜索记忆"""
        # 设置模拟
        mock_store = AsyncMock()
        mock_store.search_memories.return_value = mock_search_results
        mock_vector_store.return_value = mock_store

        # 发送请求
        response = client.post("/api/v1/memory/search", json={
            "query": "美丽的早晨",
            "n_results": 5,
            "min_similarity": 0.5
        })

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["total_results"] == 1
        assert len(data["results"]) == 1
        assert data["results"][0]["similarity_score"] == 0.95
        assert "search_time_ms" in data

    @patch('app.services.vector_store.get_vector_store')
    def test_search_memories_empty_results(self, mock_vector_store, client):
        """测试搜索无结果的情况"""
        # 设置模拟
        mock_store = AsyncMock()
        mock_store.search_memories.return_value = []
        mock_vector_store.return_value = mock_store

        # 发送请求
        response = client.post("/api/v1/memory/search", json={
            "query": "不存在的内容",
            "n_results": 5
        })

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["total_results"] == 0
        assert len(data["results"]) == 0
        assert data["average_similarity"] == 0.0


class TestMemoryStatsAPI:
    """记忆统计API测试类"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    @patch('app.services.vector_store.get_vector_store')
    def test_get_story_memory_stats_success(self, mock_vector_store, client, mock_search_results):
        """测试成功获取故事记忆统计"""
        # 设置模拟
        mock_store = AsyncMock()
        mock_store.search_memories.return_value = mock_search_results
        mock_vector_store.return_value = mock_store

        # 发送请求
        response = client.get("/api/v1/memory/stats/1")

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["story_id"] == 1
        assert data["total_memories"] == 1
        assert "memory_types" in data
        assert "average_importance" in data
        assert data["embedding_dimension"] == 384


class TestMemoryDeleteAPI:
    """记忆删除API测试类"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    @patch('app.services.vector_store.get_vector_store')
    def test_delete_story_memories_success(self, mock_vector_store, client):
        """测试成功删除故事记忆"""
        # 设置模拟
        mock_store = AsyncMock()
        mock_store.delete_memories_by_story.return_value = 5
        mock_vector_store.return_value = mock_store

        # 发送请求
        response = client.delete("/api/v1/memory/story/1")

        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["story_id"] == 1
        assert data["deleted_count"] == 5
        assert "成功删除" in data["message"]


class TestAutoSummaryAPI:
    """自动摘要API测试类"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    def test_generate_auto_summary_success(self, client):
        """测试成功生成自动摘要"""
        content = "这是一个很长的故事内容。主角李明在一个美丽的早晨醒来。阳光透过窗户洒在他的脸上。他决定开始新的冒险。"
        
        # 发送请求
        response = client.post("/api/v1/memory/summary", json={
            "content": content,
            "max_length": 50,
            "focus_keywords": ["李明", "早晨"]
        })
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["summary"]) <= 50
        assert data["original_length"] == len(content)
        assert "compression_ratio" in data
        assert "keywords" in data
    
    def test_generate_auto_summary_empty_content(self, client):
        """测试空内容的摘要生成"""
        # 发送请求
        response = client.post("/api/v1/memory/summary", json={
            "content": "",
            "max_length": 50
        })
        
        # 验证响应
        assert response.status_code == 422  # 验证错误
