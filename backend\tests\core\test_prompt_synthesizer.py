"""
🧠 [RAG] 动态提示词合成器测试
测试RAG叙事上下文引擎的核心组件功能
"""

import pytest
import asyncio
import tempfile
import shutil
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from app.core.prompt_synthesizer import (
    PromptSynthesizer, ContextBriefing, StructuredContext, UnstructuredContext
)
from app.repositories.bible_repo import StoryBibleRepository
from app.repositories.chapter_repo import ChapterRepository
from app.services.vector_store import VectorStoreManager, MemoryDocument, SearchResult
from app.models.story_bible import StoryBible, Chapter, GenerationStatus, AIProvider
from app.schemas.generation import StoryGenre


class TestPromptSynthesizer:
    """🧠 [RAG] 动态提示词合成器测试类"""
    
    @pytest.fixture
    def mock_story_bible(self):
        """模拟故事圣经数据"""
        bible = MagicMock(spec=StoryBible)
        bible.id = "test-bible-001"
        bible.title = "测试小说：穿越明朝"
        bible.genre = StoryGenre.HISTORICAL
        bible.theme = "现代人穿越到明朝，运用现代知识改变历史"
        bible.protagonist = "李明，现代程序员，28岁，聪明机智"
        bible.setting = "明朝洪武年间，从乞丐到皇帝的传奇时代"
        bible.plot_outline = "主角穿越后遇见朱元璋，运用现代知识帮助其建立明朝"
        bible.writing_style = "历史穿越风格，兼具现代理性和古代智慧"
        bible.target_audience = "历史爱好者和穿越小说读者"
        bible.generated_content = """
        这是一个关于现代程序员李明穿越到明朝的故事。
        李明凭借现代知识和历史了解，成为朱元璋的重要谋士。
        故事展现了现代智慧与古代环境的碰撞与融合。
        """
        return bible
    
    @pytest.fixture
    def mock_chapters(self):
        """模拟章节数据"""
        chapters = []
        
        # 第一章
        chapter1 = MagicMock(spec=Chapter)
        chapter1.id = "chapter-001"
        chapter1.chapter_number = 1
        chapter1.chapter_title = "穿越之始"
        chapter1.chapter_outline = "李明穿越到明朝，初遇困境"
        chapter1.generated_content = "李明睁开眼睛，发现自己身处一个陌生的古代环境..."
        chapters.append(chapter1)
        
        # 第二章
        chapter2 = MagicMock(spec=Chapter)
        chapter2.id = "chapter-002"
        chapter2.chapter_number = 2
        chapter2.chapter_title = "初遇朱元璋"
        chapter2.chapter_outline = "李明遇见乞丐朱元璋，展现现代知识"
        chapter2.generated_content = "在破庙中，李明遇到了一个衣衫褴褛的年轻人..."
        chapters.append(chapter2)
        
        return chapters
    
    @pytest.fixture
    def mock_memory_results(self):
        """模拟记忆搜索结果"""
        results = []
        
        # 角色记忆
        char_memory = MemoryDocument(
            id="memory-char-001",
            content="朱元璋是一个有野心的年轻人，虽然出身贫寒但志向远大",
            summary="朱元璋的性格特征",
            story_id=1,
            memory_type="character",
            importance_score=0.9
        )
        results.append(SearchResult(
            document=char_memory,
            similarity_score=0.85,
            distance=0.15
        ))
        
        # 情节记忆
        plot_memory = MemoryDocument(
            id="memory-plot-001",
            content="李明运用现代军事知识，帮助朱元璋制定了攻城策略",
            summary="现代知识在古代的应用",
            story_id=1,
            chapter_id=3,
            memory_type="chapter",
            importance_score=0.8
        )
        results.append(SearchResult(
            document=plot_memory,
            similarity_score=0.75,
            distance=0.25
        ))
        
        return results
    
    @pytest.fixture
    async def temp_vector_store(self):
        """创建临时向量存储"""
        temp_dir = tempfile.mkdtemp()
        
        try:
            manager = VectorStoreManager(persist_directory=temp_dir)
            await manager.initialize()
            yield manager
        finally:
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def prompt_synthesizer(self, temp_vector_store):
        """创建提示词合成器实例"""
        bible_repo = AsyncMock(spec=StoryBibleRepository)
        chapter_repo = AsyncMock(spec=ChapterRepository)
        
        return PromptSynthesizer(bible_repo, chapter_repo, temp_vector_store)
    
    @pytest.mark.asyncio
    async def test_build_context_briefing_success(
        self, 
        prompt_synthesizer, 
        mock_story_bible, 
        mock_chapters,
        mock_memory_results
    ):
        """测试成功构建上下文简报"""
        # 设置mock返回值
        prompt_synthesizer.bible_repo.get_bible_by_id.return_value = mock_story_bible
        prompt_synthesizer.chapter_repo.get_by_story_and_number.side_effect = [
            mock_chapters[0], mock_chapters[1]
        ]
        prompt_synthesizer.chapter_repo.get_chapters_by_bible_id.return_value = mock_chapters
        prompt_synthesizer.vector_store.search_memories = AsyncMock(return_value=mock_memory_results)
        
        # 执行测试
        briefing = await prompt_synthesizer.build_context_briefing(
            task_description="李明与朱元璋商讨军事策略",
            story_bible_id="test-bible-001",
            chapter_number=3
        )
        
        # 验证结果
        assert isinstance(briefing, ContextBriefing)
        assert briefing.task_description == "李明与朱元璋商讨军事策略"
        assert briefing.story_bible_id == "test-bible-001"
        assert briefing.chapter_number == 3
        
        # 验证结构化上下文
        assert briefing.structured_context.story_bible == mock_story_bible
        assert len(briefing.structured_context.previous_chapters) == 2
        assert briefing.structured_context.character_info is not None
        assert briefing.structured_context.world_building is not None
        
        # 验证非结构化上下文
        assert len(briefing.unstructured_context.relevant_memories) == 2
        assert briefing.unstructured_context.semantic_connections is not None
        
        # 验证生成的内容
        assert briefing.context_summary
        assert briefing.enhanced_prompt
        assert "李明与朱元璋商讨军事策略" in briefing.enhanced_prompt
        
        # 验证质量评分
        assert 0.0 <= briefing.context_quality_score <= 1.0
        assert briefing.retrieval_time > 0
        
        print(f"✅ 上下文简报构建成功")
        print(f"   质量评分: {briefing.context_quality_score:.3f}")
        print(f"   检索耗时: {briefing.retrieval_time:.3f}秒")
        print(f"   上下文摘要长度: {len(briefing.context_summary)}字符")
        print(f"   增强提示词长度: {len(briefing.enhanced_prompt)}字符")
    
    @pytest.mark.asyncio
    async def test_structured_context_retrieval(
        self, 
        prompt_synthesizer, 
        mock_story_bible, 
        mock_chapters
    ):
        """测试结构化上下文检索"""
        # 设置mock返回值
        prompt_synthesizer.bible_repo.get_bible_by_id.return_value = mock_story_bible
        prompt_synthesizer.chapter_repo.get_by_story_and_number.side_effect = [
            mock_chapters[0], mock_chapters[1]
        ]
        prompt_synthesizer.chapter_repo.get_chapters_by_bible_id.return_value = mock_chapters
        
        # 执行测试
        structured_context = await prompt_synthesizer._retrieve_structured_context(
            story_bible_id="test-bible-001",
            chapter_number=3,
            max_previous_chapters=5
        )
        
        # 验证结果
        assert isinstance(structured_context, StructuredContext)
        assert structured_context.story_bible == mock_story_bible
        assert len(structured_context.previous_chapters) == 2
        assert structured_context.current_chapter_info["chapter_number"] == 3
        assert structured_context.character_info is not None
        assert structured_context.world_building is not None
        
        # 验证世界观信息
        world_building = structured_context.world_building
        assert world_building["genre"] == "historical"
        assert world_building["setting"] == mock_story_bible.setting
        assert world_building["theme"] == mock_story_bible.theme
        
        print("✅ 结构化上下文检索测试通过")
    
    @pytest.mark.asyncio
    async def test_unstructured_context_retrieval(
        self, 
        prompt_synthesizer, 
        mock_memory_results
    ):
        """测试非结构化上下文检索"""
        # 设置mock返回值
        prompt_synthesizer.vector_store.search_memories = AsyncMock(return_value=mock_memory_results)
        
        # 执行测试
        unstructured_context = await prompt_synthesizer._retrieve_unstructured_context(
            task_description="军事策略讨论",
            story_bible_id="test-bible-001",
            max_results=10
        )
        
        # 验证结果
        assert isinstance(unstructured_context, UnstructuredContext)
        assert len(unstructured_context.relevant_memories) == 2
        assert unstructured_context.semantic_connections is not None
        assert unstructured_context.emotional_context is not None
        assert unstructured_context.plot_continuity is not None
        
        # 验证语义连接
        assert len(unstructured_context.semantic_connections) > 0
        assert "军事策略讨论" in unstructured_context.semantic_connections[0]
        
        print("✅ 非结构化上下文检索测试通过")
    
    @pytest.mark.asyncio
    async def test_context_quality_calculation(self, prompt_synthesizer, mock_story_bible, mock_memory_results):
        """测试上下文质量评分计算"""
        # 创建测试数据
        structured_context = StructuredContext(
            story_bible=mock_story_bible,
            previous_chapters=[MagicMock(), MagicMock()],
            character_info=[{"name": "李明"}, {"name": "朱元璋"}],
            world_building={"genre": "historical"}
        )
        
        unstructured_context = UnstructuredContext(
            relevant_memories=mock_memory_results,
            semantic_connections=["连接1", "连接2"],
            emotional_context=["情感1"],
            plot_continuity=["情节1"]
        )
        
        # 计算质量评分
        quality_score = prompt_synthesizer._calculate_context_quality(
            structured_context, unstructured_context
        )
        
        # 验证结果
        assert 0.0 <= quality_score <= 1.0
        assert quality_score > 0.5  # 应该有较高的质量评分
        
        print(f"✅ 上下文质量评分: {quality_score:.3f}")
    
    @pytest.mark.asyncio
    async def test_enhanced_prompt_generation(self, prompt_synthesizer, mock_story_bible):
        """测试增强提示词生成"""
        # 创建测试数据
        structured_context = StructuredContext(
            story_bible=mock_story_bible,
            character_info=[{"name": "李明", "description": "现代程序员"}]
        )
        
        context_summary = "【故事背景】测试小说：穿越明朝\n【主要角色】李明，现代程序员"
        
        # 生成增强提示词
        enhanced_prompt = prompt_synthesizer._generate_enhanced_prompt(
            task_description="李明制定作战计划",
            context_summary=context_summary,
            structured=structured_context
        )
        
        # 验证结果
        assert "李明制定作战计划" in enhanced_prompt
        assert "专业的小说作家" in enhanced_prompt
        assert "创作上下文" in enhanced_prompt
        assert "创作要求" in enhanced_prompt
        assert "historical" in enhanced_prompt or "历史" in enhanced_prompt
        
        print("✅ 增强提示词生成测试通过")
        print(f"   提示词长度: {len(enhanced_prompt)}字符")
    
    @pytest.mark.asyncio
    async def test_error_handling_missing_bible(self, prompt_synthesizer):
        """测试故事圣经不存在的错误处理"""
        # 设置mock返回None
        prompt_synthesizer.bible_repo.get_bible_by_id.return_value = None
        
        # 执行测试并验证异常
        with pytest.raises(ValueError, match="故事圣经不存在"):
            await prompt_synthesizer.build_context_briefing(
                task_description="测试任务",
                story_bible_id="nonexistent-bible",
                chapter_number=1
            )
        
        print("✅ 错误处理测试通过")


if __name__ == "__main__":
    """直接运行测试"""
    pytest.main([__file__, "-v"])
