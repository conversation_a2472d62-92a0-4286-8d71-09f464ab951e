"""
🧪 [测试] 世界知识图谱模型测试
测试Entity和EntityRelationship模型的基本功能
"""

import pytest
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.world_graph import Entity, EntityRelationship, EntityType, RelationshipStatus, RelationshipTypes
from app.models.story_bible import StoryBible
from app.schemas.generation import AIProvider, StoryGenre, GenerationStatus
from app.core.config import log_debug


class TestEntityModel:
    """🧪 [测试] Entity模型测试类"""
    
    def test_create_entity(self, db_session: Session, sample_story_bible: StoryBible):
        """测试创建实体"""
        log_debug('测试', '开始测试创建实体')
        
        # 创建角色实体
        entity = Entity(
            id="entity_001",
            story_id=sample_story_bible.id,
            name="张三",
            type=EntityType.CHARACTER,
            description="主角，年轻的剑客",
            properties={"age": 25, "weapon": "长剑", "personality": "勇敢"},
            importance_score=0.9,
            first_mentioned_chapter=1
        )
        
        db_session.add(entity)
        db_session.commit()
        
        # 验证创建成功
        saved_entity = db_session.query(Entity).filter_by(id="entity_001").first()
        assert saved_entity is not None
        assert saved_entity.name == "张三"
        assert saved_entity.type == EntityType.CHARACTER
        assert saved_entity.description == "主角，年轻的剑客"
        assert saved_entity.properties["age"] == 25
        assert saved_entity.importance_score == 0.9
        assert saved_entity.is_active is True
        
        log_debug('测试', '实体创建测试通过',
            entity_id=saved_entity.id,
            entity_name=saved_entity.name,
            entity_type=saved_entity.type.value
        )
    
    def test_create_different_entity_types(self, db_session: Session, sample_story_bible: StoryBible):
        """测试创建不同类型的实体"""
        log_debug('测试', '开始测试创建不同类型实体')
        
        entities = [
            Entity(
                id="char_001",
                story_id=sample_story_bible.id,
                name="李四",
                type=EntityType.CHARACTER,
                description="反派角色"
            ),
            Entity(
                id="item_001",
                story_id=sample_story_bible.id,
                name="龙泉剑",
                type=EntityType.ITEM,
                description="传说中的神剑"
            ),
            Entity(
                id="scene_001",
                story_id=sample_story_bible.id,
                name="华山",
                type=EntityType.SCENE,
                description="武林圣地"
            ),
            Entity(
                id="org_001",
                story_id=sample_story_bible.id,
                name="华山派",
                type=EntityType.ORGANIZATION,
                description="武林门派"
            )
        ]
        
        for entity in entities:
            db_session.add(entity)
        db_session.commit()
        
        # 验证所有实体都创建成功
        saved_entities = db_session.query(Entity).filter_by(story_id=sample_story_bible.id).all()
        assert len(saved_entities) == 4
        
        # 验证每种类型都存在
        entity_types = {entity.type for entity in saved_entities}
        expected_types = {EntityType.CHARACTER, EntityType.ITEM, EntityType.SCENE, EntityType.ORGANIZATION}
        assert entity_types == expected_types
        
        log_debug('测试', '不同类型实体创建测试通过',
            total_entities=len(saved_entities),
            entity_types=[t.value for t in entity_types]
        )
    
    def test_entity_story_relationship(self, db_session: Session, sample_story_bible: StoryBible):
        """测试实体与故事的关联关系"""
        log_debug('测试', '开始测试实体与故事关联关系')
        
        entity = Entity(
            id="test_entity",
            story_id=sample_story_bible.id,
            name="测试角色",
            type=EntityType.CHARACTER
        )
        
        db_session.add(entity)
        db_session.commit()
        
        # 验证关联关系
        saved_entity = db_session.query(Entity).filter_by(id="test_entity").first()
        assert saved_entity.story_bible.id == sample_story_bible.id
        assert saved_entity.story_bible.title == sample_story_bible.title
        
        log_debug('测试', '实体与故事关联关系测试通过')


class TestEntityRelationshipModel:
    """🧪 [测试] EntityRelationship模型测试类"""
    
    def test_create_relationship(self, db_session: Session, sample_story_bible: StoryBible):
        """测试创建实体关系"""
        log_debug('测试', '开始测试创建实体关系')
        
        # 创建两个实体
        entity1 = Entity(
            id="entity1",
            story_id=sample_story_bible.id,
            name="张三",
            type=EntityType.CHARACTER
        )
        entity2 = Entity(
            id="entity2",
            story_id=sample_story_bible.id,
            name="李四",
            type=EntityType.CHARACTER
        )
        
        db_session.add_all([entity1, entity2])
        db_session.commit()
        
        # 创建关系
        relationship = EntityRelationship(
            id="rel_001",
            source_entity_id="entity1",
            target_entity_id="entity2",
            relationship_type=RelationshipTypes.FRIEND,
            description="张三和李四是好朋友",
            strength=0.8,
            is_bidirectional=True,
            established_chapter=1
        )
        
        db_session.add(relationship)
        db_session.commit()
        
        # 验证关系创建成功
        saved_rel = db_session.query(EntityRelationship).filter_by(id="rel_001").first()
        assert saved_rel is not None
        assert saved_rel.relationship_type == RelationshipTypes.FRIEND
        assert saved_rel.strength == 0.8
        assert saved_rel.is_bidirectional is True
        assert saved_rel.status == RelationshipStatus.ACTIVE
        
        log_debug('测试', '实体关系创建测试通过',
            relationship_id=saved_rel.id,
            relationship_type=saved_rel.relationship_type,
            strength=saved_rel.strength
        )
    
    def test_relationship_entity_references(self, db_session: Session, sample_story_bible: StoryBible):
        """测试关系的实体引用"""
        log_debug('测试', '开始测试关系的实体引用')
        
        # 创建实体和关系
        entity1 = Entity(id="e1", story_id=sample_story_bible.id, name="角色A", type=EntityType.CHARACTER)
        entity2 = Entity(id="e2", story_id=sample_story_bible.id, name="神剑", type=EntityType.ITEM)
        
        relationship = EntityRelationship(
            id="rel_owns",
            source_entity_id="e1",
            target_entity_id="e2",
            relationship_type=RelationshipTypes.OWNS,
            description="角色A拥有神剑"
        )
        
        db_session.add_all([entity1, entity2, relationship])
        db_session.commit()
        
        # 验证关系的实体引用
        saved_rel = db_session.query(EntityRelationship).filter_by(id="rel_owns").first()
        assert saved_rel.source_entity.name == "角色A"
        assert saved_rel.target_entity.name == "神剑"
        assert saved_rel.source_entity.type == EntityType.CHARACTER
        assert saved_rel.target_entity.type == EntityType.ITEM
        
        log_debug('测试', '关系实体引用测试通过')
    
    def test_multiple_relationship_types(self, db_session: Session, sample_story_bible: StoryBible):
        """测试多种关系类型"""
        log_debug('测试', '开始测试多种关系类型')
        
        # 创建实体
        char1 = Entity(id="c1", story_id=sample_story_bible.id, name="主角", type=EntityType.CHARACTER)
        char2 = Entity(id="c2", story_id=sample_story_bible.id, name="反派", type=EntityType.CHARACTER)
        sword = Entity(id="s1", story_id=sample_story_bible.id, name="宝剑", type=EntityType.ITEM)
        place = Entity(id="p1", story_id=sample_story_bible.id, name="皇宫", type=EntityType.SCENE)
        
        db_session.add_all([char1, char2, sword, place])
        db_session.commit()
        
        # 创建多种关系
        relationships = [
            EntityRelationship(
                id="rel1", source_entity_id="c1", target_entity_id="c2",
                relationship_type=RelationshipTypes.ENEMY, description="主角与反派为敌"
            ),
            EntityRelationship(
                id="rel2", source_entity_id="c1", target_entity_id="s1",
                relationship_type=RelationshipTypes.OWNS, description="主角拥有宝剑"
            ),
            EntityRelationship(
                id="rel3", source_entity_id="c1", target_entity_id="p1",
                relationship_type=RelationshipTypes.LOCATED_IN, description="主角位于皇宫"
            )
        ]
        
        for rel in relationships:
            db_session.add(rel)
        db_session.commit()
        
        # 验证所有关系
        saved_rels = db_session.query(EntityRelationship).all()
        assert len(saved_rels) == 3
        
        rel_types = {rel.relationship_type for rel in saved_rels}
        expected_types = {RelationshipTypes.ENEMY, RelationshipTypes.OWNS, RelationshipTypes.LOCATED_IN}
        assert rel_types == expected_types
        
        log_debug('测试', '多种关系类型测试通过',
            total_relationships=len(saved_rels),
            relationship_types=list(rel_types)
        )
    
    def test_relationship_status_changes(self, db_session: Session, sample_story_bible: StoryBible):
        """测试关系状态变化"""
        log_debug('测试', '开始测试关系状态变化')
        
        # 创建实体和关系
        entity1 = Entity(id="e1", story_id=sample_story_bible.id, name="A", type=EntityType.CHARACTER)
        entity2 = Entity(id="e2", story_id=sample_story_bible.id, name="B", type=EntityType.CHARACTER)
        
        relationship = EntityRelationship(
            id="rel_status",
            source_entity_id="e1",
            target_entity_id="e2",
            relationship_type=RelationshipTypes.FRIEND,
            status=RelationshipStatus.ACTIVE
        )
        
        db_session.add_all([entity1, entity2, relationship])
        db_session.commit()
        
        # 验证初始状态
        saved_rel = db_session.query(EntityRelationship).filter_by(id="rel_status").first()
        assert saved_rel.status == RelationshipStatus.ACTIVE
        
        # 更改状态
        saved_rel.status = RelationshipStatus.INACTIVE
        db_session.commit()
        
        # 验证状态更改
        updated_rel = db_session.query(EntityRelationship).filter_by(id="rel_status").first()
        assert updated_rel.status == RelationshipStatus.INACTIVE
        
        log_debug('测试', '关系状态变化测试通过')


class TestRelationshipTypes:
    """🧪 [测试] RelationshipTypes常量测试类"""
    
    def test_relationship_type_constants(self):
        """测试关系类型常量"""
        log_debug('测试', '开始测试关系类型常量')
        
        # 验证人际关系常量
        assert RelationshipTypes.FRIEND == "朋友"
        assert RelationshipTypes.ENEMY == "敌人"
        assert RelationshipTypes.LOVER == "恋人"
        
        # 验证物理关系常量
        assert RelationshipTypes.OWNS == "拥有"
        assert RelationshipTypes.CARRIES == "携带"
        assert RelationshipTypes.USES == "使用"
        
        # 验证空间关系常量
        assert RelationshipTypes.LOCATED_IN == "位于"
        assert RelationshipTypes.LIVES_IN == "居住在"
        assert RelationshipTypes.WORKS_IN == "工作在"
        
        log_debug('测试', '关系类型常量测试通过')
