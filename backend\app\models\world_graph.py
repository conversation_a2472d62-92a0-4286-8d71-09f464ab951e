"""
🌐 [世界图谱] 世界知识图谱数据模型
定义叙事实体和关系的数据库表结构，构建动态的"世界大脑"
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import String, Text, DateTime, Float, Enum as SQLEnum, ForeignKey, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
import enum

from app.core.base import Base


class EntityType(enum.Enum):
    """实体类型枚举"""
    CHARACTER = "character"  # 角色
    ITEM = "item"           # 物品
    SCENE = "scene"         # 场景
    CONCEPT = "concept"     # 概念/抽象事物
    ORGANIZATION = "organization"  # 组织/团体


class RelationshipStatus(enum.Enum):
    """关系状态枚举"""
    ACTIVE = "active"       # 活跃状态
    INACTIVE = "inactive"   # 非活跃状态
    HISTORICAL = "historical"  # 历史状态（已结束但有记录价值）


class Entity(Base):
    """
    🌐 [世界图谱] 叙事实体数据模型
    
    存储故事中的所有实体：角色、物品、场景等
    """
    __tablename__ = "entities"
    
    # 主键
    id: Mapped[str] = mapped_column(String(50), primary_key=True, comment="实体唯一标识")
    
    # 外键关联
    story_id: Mapped[str] = mapped_column(
        String(50), 
        ForeignKey("story_bibles.id", ondelete="CASCADE"), 
        nullable=False, 
        comment="关联的故事ID"
    )
    
    # 基础信息
    name: Mapped[str] = mapped_column(String(200), nullable=False, comment="实体名称")
    type: Mapped[EntityType] = mapped_column(SQLEnum(EntityType), nullable=False, comment="实体类型")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="实体描述")
    
    # 扩展属性（JSON格式存储自定义属性）
    properties: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, comment="实体自定义属性")
    
    # 状态信息
    is_active: Mapped[bool] = mapped_column(default=True, comment="是否活跃")
    importance_score: Mapped[float] = mapped_column(Float, default=1.0, comment="重要性评分 0-1")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        nullable=False, 
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False, 
        comment="更新时间"
    )
    first_mentioned_chapter: Mapped[Optional[int]] = mapped_column(comment="首次提及章节")
    last_mentioned_chapter: Mapped[Optional[int]] = mapped_column(comment="最后提及章节")
    
    # 关系映射
    story_bible: Mapped["StoryBible"] = relationship("StoryBible")
    
    # 作为源实体的关系
    source_relationships: Mapped[List["EntityRelationship"]] = relationship(
        "EntityRelationship",
        foreign_keys="EntityRelationship.source_entity_id",
        back_populates="source_entity",
        cascade="all, delete-orphan"
    )
    
    # 作为目标实体的关系
    target_relationships: Mapped[List["EntityRelationship"]] = relationship(
        "EntityRelationship",
        foreign_keys="EntityRelationship.target_entity_id",
        back_populates="target_entity",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Entity(id='{self.id}', name='{self.name}', type='{self.type}')>"


class EntityRelationship(Base):
    """
    🌐 [世界图谱] 实体关系数据模型
    
    存储实体之间的各种关系：朋友、敌人、持有、位于等
    """
    __tablename__ = "entity_relationships"
    
    # 主键
    id: Mapped[str] = mapped_column(String(50), primary_key=True, comment="关系唯一标识")
    
    # 外键关联
    source_entity_id: Mapped[str] = mapped_column(
        String(50), 
        ForeignKey("entities.id", ondelete="CASCADE"), 
        nullable=False, 
        comment="源实体ID"
    )
    target_entity_id: Mapped[str] = mapped_column(
        String(50), 
        ForeignKey("entities.id", ondelete="CASCADE"), 
        nullable=False, 
        comment="目标实体ID"
    )
    
    # 关系信息
    relationship_type: Mapped[str] = mapped_column(String(100), nullable=False, comment="关系类型")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="关系描述")
    
    # 关系属性
    status: Mapped[RelationshipStatus] = mapped_column(
        SQLEnum(RelationshipStatus), 
        default=RelationshipStatus.ACTIVE, 
        comment="关系状态"
    )
    strength: Mapped[float] = mapped_column(Float, default=1.0, comment="关系强度 0-1")
    is_bidirectional: Mapped[bool] = mapped_column(default=False, comment="是否双向关系")
    
    # 扩展属性
    properties: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, comment="关系自定义属性")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        nullable=False, 
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False, 
        comment="更新时间"
    )
    established_chapter: Mapped[Optional[int]] = mapped_column(comment="关系建立章节")
    last_updated_chapter: Mapped[Optional[int]] = mapped_column(comment="关系最后更新章节")
    
    # 关系映射
    source_entity: Mapped["Entity"] = relationship(
        "Entity",
        foreign_keys=[source_entity_id],
        back_populates="source_relationships"
    )
    target_entity: Mapped["Entity"] = relationship(
        "Entity",
        foreign_keys=[target_entity_id],
        back_populates="target_relationships"
    )
    
    def __repr__(self) -> str:
        return f"<EntityRelationship(id='{self.id}', type='{self.relationship_type}', status='{self.status}')>"


# 常用关系类型常量
class RelationshipTypes:
    """常用关系类型定义"""
    
    # 人际关系
    FRIEND = "朋友"
    ENEMY = "敌人"
    LOVER = "恋人"
    FAMILY = "家人"
    COLLEAGUE = "同事"
    MENTOR = "师父"
    STUDENT = "弟子"
    ALLY = "盟友"
    RIVAL = "对手"
    
    # 物理关系
    OWNS = "拥有"
    CARRIES = "携带"
    WEARS = "穿戴"
    USES = "使用"
    GUARDS = "守护"
    
    # 空间关系
    LOCATED_IN = "位于"
    LIVES_IN = "居住在"
    WORKS_IN = "工作在"
    VISITS = "拜访"
    TRAVELS_TO = "前往"
    
    # 组织关系
    MEMBER_OF = "成员"
    LEADER_OF = "领导"
    SERVES = "服务于"
    OPPOSES = "对抗"
    SUPPORTS = "支持"
    
    # 抽象关系
    KNOWS = "了解"
    FEARS = "恐惧"
    TRUSTS = "信任"
    SUSPECTS = "怀疑"
    ADMIRES = "敬佩"
    DESPISES = "鄙视"
