"""
🧪 [功能测试] 动态事件处理器功能测试
测试事件处理器的完整功能流程
"""

import pytest
import json
from unittest.mock import patch, AsyncMock, MagicMock
from sqlalchemy.orm import Session

from app.core.event_processor import EventProcessor
from app.models.story_bible import StoryBible, AIProvider
from app.models.world_graph import Entity, EntityType
from app.services.zhipu_client import ChatCompletionResponse
from app.core.config import log_debug, log_info


class TestEventProcessorFunctional:
    """事件处理器功能测试类"""
    
    def test_create_story_and_entities(self, db_session: Session):
        """测试创建故事和实体的完整流程"""
        log_info("功能测试", "开始测试创建故事和实体的完整流程")
        
        # 创建测试故事
        story = StoryBible(
            id="functional_test_story",
            title="功能测试小说",
            genre="fantasy",
            theme="测试主题",
            protagonist="张三",
            setting="测试背景",
            plot_outline="测试大纲",
            target_audience="adult",
            ai_provider=AIProvider.ZHIPU
        )
        
        db_session.add(story)
        db_session.commit()
        
        # 创建测试实体
        entity = Entity(
            id="functional_test_entity",
            story_id=story.id,
            name="张三",
            type=EntityType.CHARACTER,
            description="主角",
            properties={"年龄": 25, "职业": "剑客"},
            first_mentioned_chapter=1,
            is_active=True
        )
        
        db_session.add(entity)
        db_session.commit()
        
        # 验证创建成功
        saved_story = db_session.query(StoryBible).filter_by(id="functional_test_story").first()
        saved_entity = db_session.query(Entity).filter_by(id="functional_test_entity").first()
        
        assert saved_story is not None
        assert saved_story.title == "功能测试小说"
        assert saved_entity is not None
        assert saved_entity.name == "张三"
        assert saved_entity.story_id == story.id
        
        log_info("功能测试", "故事和实体创建测试通过",
                story_id=saved_story.id,
                entity_id=saved_entity.id)
    
    @pytest.mark.asyncio
    async def test_complete_event_extraction_flow(self):
        """测试完整的事件提取流程"""
        log_info("功能测试", "开始测试完整的事件提取流程")
        
        # 准备测试数据
        chapter_text = """
        张三在森林中遇到了一位神秘的老者王五。老者告诉张三，他是一位隐居的武林高手。
        王五看中了张三的天赋，决定收他为徒。张三激动地接受了，从此王五成为了张三的师父。
        王五送给张三一本《九阳神功》秘籍，这是他多年来珍藏的武功秘籍。
        """
        
        # Mock AI响应
        mock_ai_response = json.dumps([
            {
                "event": "create_entity",
                "data": {
                    "name": "王五",
                    "type": "character",
                    "description": "神秘的武林高手，隐居在森林中",
                    "properties": {"年龄": 60, "职业": "武林高手", "居住地": "森林"},
                    "first_mentioned_chapter": 2
                }
            },
            {
                "event": "create_entity",
                "data": {
                    "name": "九阳神功",
                    "type": "item",
                    "description": "珍贵的武功秘籍",
                    "properties": {"类型": "秘籍", "品质": "高级"},
                    "first_mentioned_chapter": 2
                }
            },
            {
                "event": "create_relationship",
                "data": {
                    "source_entity": "王五",
                    "target_entity": "张三",
                    "relationship_type": "师父",
                    "description": "王五是张三的师父",
                    "properties": {"关系建立时间": "第2章"},
                    "established_chapter": 2
                }
            },
            {
                "event": "create_relationship",
                "data": {
                    "source_entity": "张三",
                    "target_entity": "九阳神功",
                    "relationship_type": "持有",
                    "description": "张三获得了九阳神功秘籍",
                    "properties": {"获得方式": "师父赠送"},
                    "established_chapter": 2
                }
            }
        ])
        
        mock_response = ChatCompletionResponse(
            id="functional_test_extraction",
            object="chat.completion",
            created=1234567890,
            model="glm-4.5-flash",
            choices=[{
                "message": {"content": mock_ai_response},
                "finish_reason": "stop"
            }],
            usage={"total_tokens": 300}
        )
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            # 创建事件处理器并提取事件
            processor = EventProcessor()
            events = await processor.extract_events_from_chapter(
                chapter_text=chapter_text,
                story_id="functional_test_story",
                chapter_number=2
            )
            
            # 验证提取结果
            assert len(events) == 4
            
            # 验证第一个事件（创建王五）
            entity_event = events[0]
            assert entity_event.event == "create_entity"
            assert entity_event.data["name"] == "王五"
            assert entity_event.data["type"] == "character"
            assert entity_event.data["properties"]["年龄"] == 60
            
            # 验证第二个事件（创建九阳神功）
            item_event = events[1]
            assert item_event.event == "create_entity"
            assert item_event.data["name"] == "九阳神功"
            assert item_event.data["type"] == "item"
            
            # 验证第三个事件（师父关系）
            master_relationship = events[2]
            assert master_relationship.event == "create_relationship"
            assert master_relationship.data["source_entity"] == "王五"
            assert master_relationship.data["target_entity"] == "张三"
            assert master_relationship.data["relationship_type"] == "师父"
            
            # 验证第四个事件（持有关系）
            possession_relationship = events[3]
            assert possession_relationship.event == "create_relationship"
            assert possession_relationship.data["source_entity"] == "张三"
            assert possession_relationship.data["target_entity"] == "九阳神功"
            assert possession_relationship.data["relationship_type"] == "持有"
            
            log_info("功能测试", "事件提取流程测试通过",
                    total_events=len(events),
                    entity_events=2,
                    relationship_events=2)
    
    @pytest.mark.asyncio
    async def test_error_handling_ai_failure(self):
        """测试AI调用失败的错误处理"""
        log_info("功能测试", "开始测试AI调用失败的错误处理")
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.side_effect = Exception("AI服务不可用")
            mock_get_client.return_value = mock_client
            
            processor = EventProcessor()
            events = await processor.extract_events_from_chapter(
                chapter_text="测试文本",
                story_id="test_story",
                chapter_number=1
            )
            
            # 应该返回空列表而不是抛出异常
            assert len(events) == 0
            log_info("功能测试", "AI调用失败错误处理测试通过")
    
    @pytest.mark.asyncio
    async def test_prompt_customization(self):
        """测试提示词定制功能"""
        log_info("功能测试", "开始测试提示词定制功能")
        
        processor = EventProcessor()
        
        # 测试不同的章节文本和参数
        test_cases = [
            {
                "text": "张三拿起了宝剑",
                "story_id": "story_001",
                "chapter": 1,
                "expected_keywords": ["story_001", "章节编号: 1", "张三拿起了宝剑"]
            },
            {
                "text": "李四和王五成为了朋友",
                "story_id": "story_002", 
                "chapter": 5,
                "expected_keywords": ["story_002", "章节编号: 5", "李四和王五成为了朋友"]
            }
        ]
        
        for case in test_cases:
            prompt = processor._build_extraction_prompt(
                case["text"], case["story_id"], case["chapter"]
            )
            
            # 验证提示词包含预期的关键词
            for keyword in case["expected_keywords"]:
                assert keyword in prompt, f"提示词中缺少关键词: {keyword}"
            
            # 验证提示词结构
            assert "JSON数组格式" in prompt
            assert "create_entity" in prompt
            assert "create_relationship" in prompt
            
        log_info("功能测试", "提示词定制功能测试通过", test_cases=len(test_cases))
    
    def test_event_data_validation_edge_cases(self):
        """测试事件数据验证的边界情况"""
        log_info("功能测试", "开始测试事件数据验证的边界情况")
        
        from app.core.event_processor import CreateEntityEventData, CreateRelationshipEventData
        
        # 测试最小有效数据
        minimal_entity = CreateEntityEventData(
            name="最小实体",
            type="character",
            first_mentioned_chapter=1
        )
        assert minimal_entity.name == "最小实体"
        assert minimal_entity.description is None
        # Pydantic会将None转换为空字典
        assert minimal_entity.properties == {} or minimal_entity.properties is None
        
        # 测试完整数据
        full_entity = CreateEntityEventData(
            name="完整实体",
            type="item",
            description="详细描述",
            properties={"属性1": "值1", "属性2": 123},
            first_mentioned_chapter=5
        )
        assert full_entity.properties["属性1"] == "值1"
        assert full_entity.properties["属性2"] == 123
        
        # 测试关系数据
        relationship = CreateRelationshipEventData(
            source_entity="实体A",
            target_entity="实体B",
            relationship_type="关系类型",
            established_chapter=3
        )
        assert relationship.source_entity == "实体A"
        assert relationship.target_entity == "实体B"
        assert relationship.description is None
        assert relationship.properties == {} or relationship.properties is None
        
        log_info("功能测试", "事件数据验证边界情况测试通过")
    
    @pytest.mark.asyncio
    async def test_concurrent_event_processing(self):
        """测试并发事件处理"""
        log_info("功能测试", "开始测试并发事件处理")
        
        import asyncio
        
        # Mock AI响应
        mock_response = ChatCompletionResponse(
            id="concurrent_test",
            object="chat.completion", 
            created=1234567890,
            model="glm-4.5-flash",
            choices=[{
                "message": {"content": "[]"},
                "finish_reason": "stop"
            }],
            usage={"total_tokens": 50}
        )
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            processor = EventProcessor()
            
            # 创建多个并发任务
            tasks = []
            for i in range(5):
                task = processor.extract_events_from_chapter(
                    chapter_text=f"测试文本 {i}",
                    story_id=f"story_{i}",
                    chapter_number=i + 1
                )
                tasks.append(task)
            
            # 并发执行所有任务
            results = await asyncio.gather(*tasks)
            
            # 验证所有任务都成功完成
            assert len(results) == 5
            for result in results:
                assert isinstance(result, list)
            
            log_info("功能测试", "并发事件处理测试通过", concurrent_tasks=len(tasks))
