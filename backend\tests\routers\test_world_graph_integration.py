"""
🧪 [测试] 世界知识图谱API集成测试
测试完整的CRUD操作流程，包含数据库交互
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.config import log_debug
from app.models.story_bible import StoryBible
from app.models.world_graph import Entity, EntityRelationship, EntityType, RelationshipStatus
from app.schemas.generation import AIProvider
from main import create_app
from app.core.database import get_database_session


@pytest.fixture(scope="function")
async def test_story_bible(async_db_session: AsyncSession):
    """创建测试故事圣经"""
    story_bible = StoryBible(
        id="test_story_001",
        title="测试小说",
        genre="fantasy",
        theme="测试主题",
        protagonist="测试主角",
        setting="测试背景",
        plot_outline="测试大纲",
        target_audience="adult",
        ai_provider=AIProvider.ZHIPU
    )
    
    async_db_session.add(story_bible)
    await async_db_session.commit()
    await async_db_session.refresh(story_bible)
    
    log_debug("测试", "创建测试故事圣经", story_id=story_bible.id)
    return story_bible


@pytest.fixture(scope="function")
def integration_test_client(async_db_session: AsyncSession):
    """创建集成测试客户端"""
    app = create_app()
    
    # 覆盖数据库依赖
    async def override_get_database_session():
        yield async_db_session
    
    app.dependency_overrides[get_database_session] = override_get_database_session
    
    with TestClient(app) as client:
        yield client
    
    # 清理依赖覆盖
    app.dependency_overrides.clear()


class TestWorldGraphIntegration:
    """世界图谱API集成测试"""
    
    @pytest.mark.asyncio
    async def test_complete_entity_crud_flow(self, integration_test_client: TestClient, test_story_bible: StoryBible):
        """测试完整的实体CRUD流程"""
        log_debug("测试", "开始测试完整的实体CRUD流程")
        
        # 1. 创建实体
        entity_data = {
            "story_id": test_story_bible.id,
            "name": "李小明",
            "type": "character",
            "description": "主角，年轻的剑客",
            "properties": {
                "年龄": 20,
                "职业": "剑客",
                "性格": "勇敢"
            },
            "first_mentioned_chapter": 1,
            "last_mentioned_chapter": 5
        }
        
        response = integration_test_client.post("/api/v1/world/entities", json=entity_data)
        assert response.status_code == 201
        
        created_entity = response.json()
        entity_id = created_entity["id"]
        assert created_entity["name"] == "李小明"
        assert created_entity["type"] == "character"
        
        log_debug("测试", "实体创建成功", entity_id=entity_id)
        
        # 2. 获取实体
        response = integration_test_client.get(f"/api/v1/world/entities/{entity_id}")
        assert response.status_code == 200
        
        retrieved_entity = response.json()
        assert retrieved_entity["id"] == entity_id
        assert retrieved_entity["name"] == "李小明"
        
        log_debug("测试", "实体获取成功")
        
        # 3. 更新实体
        update_data = {
            "description": "主角，经验丰富的剑客",
            "properties": {
                "年龄": 25,
                "职业": "剑客",
                "性格": "勇敢且智慧"
            }
        }
        
        response = integration_test_client.put(f"/api/v1/world/entities/{entity_id}", json=update_data)
        assert response.status_code == 200
        
        updated_entity = response.json()
        assert updated_entity["description"] == "主角，经验丰富的剑客"
        assert updated_entity["properties"]["年龄"] == 25
        
        log_debug("测试", "实体更新成功")
        
        # 4. 删除实体
        response = integration_test_client.delete(f"/api/v1/world/entities/{entity_id}")
        assert response.status_code == 200
        
        # 5. 验证删除
        response = integration_test_client.get(f"/api/v1/world/entities/{entity_id}")
        assert response.status_code == 404
        
        log_debug("测试", "完整的实体CRUD流程测试通过")
    
    @pytest.mark.asyncio
    async def test_relationship_creation_and_retrieval(self, integration_test_client: TestClient, test_story_bible: StoryBible):
        """测试关系创建和检索"""
        log_debug("测试", "开始测试关系创建和检索")
        
        # 1. 创建两个实体
        entity1_data = {
            "story_id": test_story_bible.id,
            "name": "张三",
            "type": "character",
            "description": "男主角"
        }
        
        entity2_data = {
            "story_id": test_story_bible.id,
            "name": "李四",
            "type": "character", 
            "description": "男主角的朋友"
        }
        
        response1 = integration_test_client.post("/api/v1/world/entities", json=entity1_data)
        response2 = integration_test_client.post("/api/v1/world/entities", json=entity2_data)
        
        assert response1.status_code == 201
        assert response2.status_code == 201
        
        entity1_id = response1.json()["id"]
        entity2_id = response2.json()["id"]
        
        log_debug("测试", "创建了两个测试实体", entity1_id=entity1_id, entity2_id=entity2_id)
        
        # 2. 创建关系
        relationship_data = {
            "source_entity_id": entity1_id,
            "target_entity_id": entity2_id,
            "relationship_type": "朋友",
            "description": "从小一起长大的好朋友",
            "properties": {
                "友谊程度": "深厚",
                "认识时间": "10年"
            },
            "established_chapter": 1
        }
        
        response = integration_test_client.post("/api/v1/world/relationships", json=relationship_data)
        assert response.status_code == 201
        
        created_relationship = response.json()
        relationship_id = created_relationship["id"]
        assert created_relationship["relationship_type"] == "朋友"
        
        log_debug("测试", "关系创建成功", relationship_id=relationship_id)
        
        # 3. 获取世界图谱
        response = integration_test_client.get(f"/api/v1/world/stories/{test_story_bible.id}/graph")
        assert response.status_code == 200
        
        world_graph = response.json()
        assert world_graph["story_id"] == test_story_bible.id
        assert world_graph["entity_count"] == 2
        assert world_graph["relationship_count"] == 1
        assert len(world_graph["entities"]) == 2
        assert len(world_graph["relationships"]) == 1
        
        log_debug("测试", "世界图谱获取成功", 
                 entity_count=world_graph["entity_count"],
                 relationship_count=world_graph["relationship_count"])
        
        log_debug("测试", "关系创建和检索测试通过")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
