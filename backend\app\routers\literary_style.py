"""
🎨 [API路由] 文学风格增强API路由
提供文学风格增强服务的REST API接口
"""

from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import JSONResponse

from app.core.config import log_info, log_debug, log_error, log_success
from app.core.literary_style_engine import (
    create_literary_style_engine, LiteraryStyleConfig, StyleConfigBuilder
)
from app.schemas.agent_orchestration import (
    StyleEnhancementRequest, StyleEnhancementResponse, StyleEnhancementResult,
    LiteraryStylePreferences
)

# 创建路由器
router = APIRouter(prefix="/api/v1/literary-style", tags=["文学风格增强"])

# 全局文学风格引擎实例
_style_engine = None


def get_style_engine():
    """获取文学风格引擎实例"""
    global _style_engine
    if _style_engine is None:
        _style_engine = create_literary_style_engine()
    return _style_engine


@router.post("/enhance", response_model=StyleEnhancementResponse)
async def enhance_text_style(
    request: StyleEnhancementRequest,
    background_tasks: BackgroundTasks
) -> StyleEnhancementResponse:
    """
    🎨 [风格增强] 对文本进行文学风格增强
    
    支持自定义风格配置或使用预设配置进行文本风格增强
    """
    log_info("文学风格API", "收到文学风格增强请求",
            项目ID=request.project_id,
            文本长度=len(request.text),
            预设配置=request.preset_name)
    
    try:
        style_engine = get_style_engine()
        
        # 如果指定了预设配置，使用预设增强
        if request.preset_name:
            result = await style_engine.enhance_with_preset(
                request.text,
                request.preset_name,
                request.custom_overrides
            )
        else:
            # 使用自定义配置
            config = _convert_preferences_to_config(request.style_preferences)
            result = await style_engine.enhance_literary_style(request.text, config)
        
        # 生成改进建议
        recommendations = _generate_recommendations(result)
        
        # 获取可用预设列表
        available_presets = style_engine.get_available_presets()
        
        log_success("文学风格API", "文学风格增强完成",
                   项目ID=request.project_id,
                   增强成功=result.success,
                   综合评分=f"{result.overall_enhancement_score:.2f}")
        
        # 转换结果格式
        api_result = StyleEnhancementResult(
            success=result.success,
            original_text=result.original_text,
            enhanced_text=result.enhanced_text,
            overall_enhancement_score=result.overall_enhancement_score,
            style_consistency_score=result.style_consistency_score,
            readability_score=result.readability_score,
            processing_pipeline=result.processing_pipeline,
            enhancement_details=result.enhancement_details,
            performance_metrics=result.performance_metrics
        )
        
        return StyleEnhancementResponse(
            result=api_result,
            available_presets=available_presets,
            recommendations=recommendations
        )
        
    except Exception as e:
        log_error("文学风格API", "文学风格增强失败", 
                 项目ID=request.project_id, error=e)
        raise HTTPException(status_code=500, detail=f"文学风格增强失败: {str(e)}")


@router.get("/presets", response_model=Dict[str, Any])
async def get_style_presets() -> Dict[str, Any]:
    """
    📋 [预设配置] 获取所有可用的文学风格预设配置
    """
    log_info("文学风格API", "获取文学风格预设配置列表")
    
    try:
        style_engine = get_style_engine()
        presets = style_engine.get_available_presets()
        
        preset_details = {}
        for preset_name in presets:
            preset_details[preset_name] = style_engine.get_preset_description(preset_name)
        
        log_success("文学风格API", "获取预设配置成功", 预设数量=len(presets))
        
        return {
            "presets": preset_details,
            "total_count": len(presets),
            "categories": _categorize_presets(presets)
        }
        
    except Exception as e:
        log_error("文学风格API", "获取预设配置失败", error=e)
        raise HTTPException(status_code=500, detail=f"获取预设配置失败: {str(e)}")


@router.get("/presets/{preset_name}", response_model=Dict[str, Any])
async def get_preset_details(preset_name: str) -> Dict[str, Any]:
    """
    🔍 [预设详情] 获取特定预设配置的详细信息
    """
    log_info("文学风格API", f"获取预设配置详情: {preset_name}")
    
    try:
        style_engine = get_style_engine()
        available_presets = style_engine.get_available_presets()
        
        if preset_name not in available_presets:
            raise HTTPException(status_code=404, detail=f"预设配置不存在: {preset_name}")
        
        preset_details = style_engine.get_preset_description(preset_name)
        
        log_success("文学风格API", f"获取预设配置详情成功: {preset_name}")
        
        return preset_details
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("文学风格API", f"获取预设配置详情失败: {preset_name}", error=e)
        raise HTTPException(status_code=500, detail=f"获取预设配置详情失败: {str(e)}")


@router.post("/config/build", response_model=Dict[str, Any])
async def build_custom_config(
    config_params: Dict[str, Any]
) -> Dict[str, Any]:
    """
    🔧 [配置构建] 使用配置构建器创建自定义风格配置
    """
    log_info("文学风格API", "构建自定义风格配置", 参数数量=len(config_params))
    
    try:
        builder = StyleConfigBuilder()
        
        # 根据参数构建配置
        if "style_types" in config_params:
            builder.with_style_types(*config_params["style_types"])
        
        if "style_intensity" in config_params:
            builder.with_style_intensity(config_params["style_intensity"])
        
        if "zodiac_sign" in config_params:
            builder.with_zodiac_sign(config_params["zodiac_sign"])
        
        if "personality_traits" in config_params:
            builder.with_personality_traits(*config_params["personality_traits"])
        
        if "pleasure_types" in config_params:
            builder.with_pleasure_types(*config_params["pleasure_types"])
        
        if "enhancement_mode" in config_params:
            builder.with_enhancement_mode(config_params["enhancement_mode"])
        
        if "target_audience" in config_params:
            builder.with_target_audience(config_params["target_audience"])
        
        config = builder.build()
        
        log_success("文学风格API", "自定义风格配置构建成功")
        
        return {
            "config": config.dict(),
            "validation": "配置构建成功",
            "estimated_enhancement_level": _estimate_enhancement_level(config)
        }
        
    except Exception as e:
        log_error("文学风格API", "构建自定义风格配置失败", error=e)
        raise HTTPException(status_code=500, detail=f"构建配置失败: {str(e)}")


# ==================== 辅助函数 ====================

def _convert_preferences_to_config(preferences: LiteraryStylePreferences) -> LiteraryStyleConfig:
    """将API偏好设置转换为内部配置对象"""
    return LiteraryStyleConfig(
        style_types=preferences.style_types,
        style_intensity=preferences.style_intensity,
        zodiac_sign=preferences.zodiac_sign,
        personality_traits=preferences.personality_traits,
        personality_intensity=preferences.personality_intensity,
        pleasure_types=preferences.pleasure_types,
        pleasure_intensity=preferences.pleasure_intensity,
        rhythm_template=preferences.rhythm_template,
        enhancement_mode=preferences.enhancement_mode,
        preserve_meaning=preferences.preserve_meaning,
        target_audience=preferences.target_audience
    )


def _generate_recommendations(result) -> List[str]:
    """根据增强结果生成改进建议"""
    recommendations = []
    
    if result.overall_enhancement_score < 5.0:
        recommendations.append("建议增加风格强度或选择更多风格类型")
    
    if result.style_consistency_score < 6.0:
        recommendations.append("建议调整增强模式以提高风格一致性")
    
    if result.readability_score < 7.0:
        recommendations.append("建议降低风格强度以提高可读性")
    
    if len(result.processing_pipeline) < 2:
        recommendations.append("建议启用更多增强组件以获得更好效果")
    
    if not recommendations:
        recommendations.append("当前配置效果良好，可以继续使用")
    
    return recommendations


def _categorize_presets(presets: List[str]) -> Dict[str, List[str]]:
    """对预设配置进行分类"""
    categories = {
        "网络小说": [],
        "传统文学": [],
        "类型小说": []
    }
    
    for preset in presets:
        if "网文" in preset or "爽文" in preset:
            categories["网络小说"].append(preset)
        elif "古风" in preset or "仙侠" in preset:
            categories["传统文学"].append(preset)
        else:
            categories["类型小说"].append(preset)
    
    return categories


def _estimate_enhancement_level(config: LiteraryStyleConfig) -> str:
    """估算增强程度"""
    total_intensity = (
        config.style_intensity + 
        config.personality_intensity + 
        (0.5 if config.pleasure_types else 0)
    ) / 3
    
    if total_intensity < 0.3:
        return "轻度增强"
    elif total_intensity < 0.6:
        return "中度增强"
    elif total_intensity < 0.8:
        return "重度增强"
    else:
        return "极致增强"
