"""
🧠 [RAG] RAG增强章节生成集成测试
测试完整的RAG叙事上下文引擎在章节生成中的应用
"""

import pytest
import asyncio
import tempfile
import shutil
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from app.services.generation_service import generate_chapter_content_with_rag
from app.core.prompt_synthesizer import PromptSynthesizer, ContextBriefing, StructuredContext, UnstructuredContext
from app.schemas.generation import ChapterGenerationRequest, AIProvider
from app.repositories.bible_repo import StoryBibleRepository
from app.repositories.chapter_repo import ChapterRepository
from app.services.vector_store import VectorStoreManager, MemoryDocument, SearchResult
from app.models.story_bible import StoryBible, Chapter, StoryGenre


class TestRAGChapterGeneration:
    """🧠 [RAG] RAG增强章节生成测试类"""
    
    @pytest.fixture
    def mock_context_briefing(self):
        """模拟上下文简报"""
        # 创建结构化上下文
        mock_bible = MagicMock(spec=StoryBible)
        mock_bible.title = "测试小说：穿越明朝"
        mock_bible.genre = StoryGenre.HISTORICAL
        mock_bible.theme = "现代人穿越到明朝的传奇故事"
        mock_bible.setting = "明朝洪武年间"
        mock_bible.writing_style = "历史穿越风格"
        
        structured_context = StructuredContext(
            story_bible=mock_bible,
            previous_chapters=[],
            character_info=[{"name": "李明", "description": "现代程序员"}],
            world_building={"genre": "historical", "setting": "明朝洪武年间"}
        )
        
        # 创建非结构化上下文
        memory_doc = MemoryDocument(
            id="memory-001",
            content="李明运用现代知识帮助朱元璋制定军事策略",
            summary="现代知识在古代的应用",
            story_id=1,
            memory_type="chapter",
            importance_score=0.8
        )
        
        unstructured_context = UnstructuredContext(
            relevant_memories=[SearchResult(
                document=memory_doc,
                similarity_score=0.85,
                distance=0.15
            )],
            semantic_connections=["与军事策略相关的历史事件"],
            emotional_context=["紧张的战略讨论氛围"],
            plot_continuity=["第2章：初遇朱元璋"]
        )
        
        # 创建上下文简报
        context_summary = """【故事背景】测试小说：穿越明朝
类型：historical
主题：现代人穿越到明朝的传奇故事
背景设定：明朝洪武年间

【主要角色】李明，现代程序员

【相关记忆】现代知识在古代的应用"""
        
        enhanced_prompt = """你是一位专业的小说作家，擅长创作引人入胜的故事情节。

【创作上下文】
""" + context_summary + """

【创作任务】创作第3章：军事策略讨论

【创作要求】
- 保持historical风格
- 采用历史穿越风格的写作风格
- 确保情节连贯性和角色一致性
- 融入丰富的细节描写和情感表达
- 保持故事的吸引力和可读性

【输出要求】请直接输出章节内容，无需额外说明。"""
        
        briefing = ContextBriefing(
            task_description="创作第3章：军事策略讨论",
            story_bible_id="test-bible-001",
            chapter_number=3,
            structured_context=structured_context,
            unstructured_context=unstructured_context,
            context_summary=context_summary,
            enhanced_prompt=enhanced_prompt,
            retrieval_time=0.5,
            context_quality_score=0.85,
            created_at=datetime.now()
        )
        
        return briefing
    
    @pytest.fixture
    def chapter_request(self):
        """模拟章节生成请求"""
        return ChapterGenerationRequest(
            story_bible_id="test-bible-001",
            chapter_number=3,
            chapter_title="军事策略讨论",
            chapter_outline="李明与朱元璋讨论如何运用现代军事知识",
            target_word_count=2000,
            ai_provider=AIProvider.ZHIPU,
            temperature=0.7,
            max_tokens=4000,
            enable_emotion_enhancement=False,
            enable_entropy_injection=False
        )
    
    @pytest.mark.asyncio
    async def test_rag_enhanced_chapter_generation_success(
        self, 
        mock_context_briefing, 
        chapter_request
    ):
        """测试RAG增强章节生成成功流程"""
        
        # Mock智谱AI响应
        mock_ai_response = {
            "choices": [{
                "message": {
                    "content": """第三章：军事策略讨论

李明看着眼前这位衣衫褴褛却目光坚毅的年轻人，心中暗自惊叹。历史书上记载的朱元璋，此时还只是一个普通的乞丐，但从他的眼神中，李明已经能看出那种不甘平凡的野心。

"朱兄弟，你说的攻城之法，确实有些道理。"李明缓缓开口，"不过，我觉得还可以再完善一些。"

朱元璋眼中闪过一丝好奇："愿闻其详。"

李明在地上画了一个简单的城池图案："你看，如果我们从这三个方向同时进攻..."

他开始运用现代军事理论，结合当时的实际情况，为朱元璋详细分析攻城策略。朱元璋听得入神，不时点头称赞。

"李兄弟，你这些见解，真是闻所未闻！"朱元璋激动地说道，"若能按此计行事，何愁大业不成！"

李明心中暗想，历史的车轮正在悄然改变方向..."""
                }
            }]
        }
        
        with patch('app.services.generation_service.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_ai_response
            mock_get_client.return_value = mock_client
            
            # 执行RAG增强生成
            result = await generate_chapter_content_with_rag(
                request=chapter_request,
                context_briefing=mock_context_briefing,
                gene_repository=None
            )
            
            # 验证结果
            assert result is not None
            assert len(result) > 100  # 确保生成了足够的内容
            assert "李明" in result  # 确保包含主角名字
            assert "朱元璋" in result  # 确保包含关键角色
            assert "军事" in result or "策略" in result  # 确保包含主题相关内容
            
            # 验证AI客户端被正确调用
            mock_client.chat_completion.assert_called_once()
            call_args = mock_client.chat_completion.call_args
            
            # 验证消息格式
            messages = call_args[1]['messages']
            assert len(messages) == 2
            assert messages[0].role == 'system'
            assert messages[1].role == 'user'

            # 验证系统消息包含长期记忆相关内容
            system_message = messages[0].content
            assert "长期记忆" in system_message
            assert "连贯性" in system_message

            # 验证用户消息包含RAG增强的上下文
            user_message = messages[1].content
            assert "创作上下文" in user_message
            assert "测试小说：穿越明朝" in user_message
            assert "李明，现代程序员" in user_message
            assert "现代知识在古代的应用" in user_message
            
            print("✅ RAG增强章节生成测试通过")
            print(f"   生成内容长度: {len(result)}字符")
            print(f"   上下文质量评分: {mock_context_briefing.context_quality_score:.3f}")
    
    @pytest.mark.asyncio
    async def test_rag_enhanced_generation_with_emotion(
        self, 
        mock_context_briefing, 
        chapter_request
    ):
        """测试带情感增强的RAG章节生成"""
        
        # 启用情感增强
        chapter_request.enable_emotion_enhancement = True
        
        # Mock情感基因仓库
        mock_gene_repo = AsyncMock()
        mock_emotion_gene = MagicMock()
        mock_emotion_gene.emotion_type = "紧张"
        mock_emotion_gene.description = "战略讨论中的紧张氛围"
        mock_gene_repo.get_genes_by_story_id.return_value = [mock_emotion_gene]
        
        # Mock智谱AI响应
        mock_ai_response = {
            "choices": [{
                "message": {
                    "content": "带有情感增强的章节内容..."
                }
            }]
        }
        
        with patch('app.services.generation_service.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_ai_response
            mock_get_client.return_value = mock_client
            
            # 执行带情感增强的RAG生成
            result = await generate_chapter_content_with_rag(
                request=chapter_request,
                context_briefing=mock_context_briefing,
                gene_repository=mock_gene_repo
            )
            
            # 验证结果
            assert result is not None
            assert "带有情感增强的章节内容" in result
            
            # 验证情感基因仓库被调用
            mock_gene_repo.get_genes_by_story_id.assert_called_once()
            
            # 验证提示词包含情感增强内容
            call_args = mock_client.chat_completion.call_args
            user_message = call_args[1]['messages'][1].content
            assert "情感增强上下文" in user_message
            assert "紧张" in user_message
            
            print("✅ 情感增强RAG生成测试通过")
    
    @pytest.mark.asyncio
    async def test_rag_enhanced_generation_with_entropy(
        self, 
        mock_context_briefing, 
        chapter_request
    ):
        """测试带熵增扰动的RAG章节生成"""
        
        # 启用熵增扰动
        chapter_request.enable_entropy_injection = True
        
        # Mock智谱AI响应（多段落内容）
        mock_ai_response = {
            "choices": [{
                "message": {
                    "content": """第一段内容。

第二段内容。

第三段内容。"""
                }
            }]
        }
        
        with patch('app.services.generation_service.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_ai_response
            mock_get_client.return_value = mock_client
            
            # 执行带熵增扰动的RAG生成
            result = await generate_chapter_content_with_rag(
                request=chapter_request,
                context_briefing=mock_context_briefing,
                gene_repository=None
            )
            
            # 验证结果
            assert result is not None
            assert "第一段内容" in result
            assert "第二段内容" in result
            assert "第三段内容" in result
            
            # 验证熵增扰动效果（应该包含插入的细节描写）
            assert "微风轻拂" in result or len(result.split('\n\n')) > 3
            
            print("✅ 熵增扰动RAG生成测试通过")
    
    @pytest.mark.asyncio
    async def test_rag_enhanced_generation_error_handling(
        self, 
        mock_context_briefing, 
        chapter_request
    ):
        """测试RAG增强生成的错误处理"""
        
        # Mock智谱AI返回空响应
        mock_ai_response = {"choices": []}
        
        with patch('app.services.generation_service.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_ai_response
            mock_get_client.return_value = mock_client
            
            # 执行测试并验证异常
            with pytest.raises(ValueError, match="智谱AI返回了空的响应内容"):
                await generate_chapter_content_with_rag(
                    request=chapter_request,
                    context_briefing=mock_context_briefing,
                    gene_repository=None
                )
            
            print("✅ RAG增强生成错误处理测试通过")


if __name__ == "__main__":
    """直接运行测试"""
    pytest.main([__file__, "-v"])
