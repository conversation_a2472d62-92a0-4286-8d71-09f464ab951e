"""
⏰ [核心模块] 时间感知扭曲器

模拟人类在不同情绪下对时间的主观感知，
将客观的时间描述转换为带有情绪色彩的主观时间体验。

主要功能:
1. 情绪化时间感知模式
2. 客观时间描述替换
3. 主观时间流逝调整
4. 与情感基因系统集成

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import re
import random
from enum import Enum
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

from app.core.config import log_info, log_debug, log_error


class EmotionalState(Enum):
    """情绪状态枚举"""
    ANGER = "愤怒"
    SADNESS = "悲伤"
    ANXIETY = "焦虑"
    JOY = "喜悦"
    FEAR = "恐惧"
    DISGUST = "厌恶"
    SURPRISE = "惊讶"
    NEUTRAL = "中性"


class TimePerceptionMode(Enum):
    """时间感知模式枚举"""
    ACCELERATED = "加速感"      # 愤怒、兴奋时的时间加速
    DECELERATED = "缓慢感"      # 悲伤、无聊时的时间缓慢
    URGENT = "紧迫感"           # 焦虑、恐惧时的时间紧迫
    STRETCHED = "延展感"        # 期待、等待时的时间延展
    FRAGMENTED = "碎片感"       # 混乱、惊讶时的时间碎片
    SUSPENDED = "悬停感"        # 震惊、专注时的时间悬停


@dataclass
class TimeDistortionPattern:
    """时间扭曲模式数据类"""
    emotion: EmotionalState
    perception_mode: TimePerceptionMode
    objective_patterns: List[str]  # 客观时间表达模式
    subjective_replacements: List[str]  # 主观时间替换表达
    intensity_modifiers: Dict[str, List[str]]  # 强度修饰词
    context_requirements: List[str]  # 上下文要求
    probability: float  # 应用概率


@dataclass
class TimeDistortionResult:
    """时间扭曲结果"""
    original_text: str
    modified_text: str
    distortions_applied: List[Dict[str, Any]]
    emotional_context: Dict[str, Any]
    processing_details: Dict[str, Any]


class TimePerceptionDistorter:
    """时间感知扭曲器
    
    模拟人类在不同情绪下对时间的主观感知
    """
    
    def __init__(self):
        """初始化时间感知扭曲器"""
        self.distortion_patterns = self._initialize_distortion_patterns()
        log_debug("时间扭曲", "时间扭曲模式初始化完成", 模式总数=len(self.distortion_patterns))
        
    def _initialize_distortion_patterns(self) -> Dict[EmotionalState, List[TimeDistortionPattern]]:
        """初始化时间扭曲模式"""
        patterns = {emotion: [] for emotion in EmotionalState}
        
        # 愤怒时间 - 加速感
        anger_patterns = [
            TimeDistortionPattern(
                emotion=EmotionalState.ANGER,
                perception_mode=TimePerceptionMode.ACCELERATED,
                objective_patterns=["几分钟", "一会儿", "片刻", "不久", "很快"],
                subjective_replacements=["一瞬间", "眨眼间", "转眼就", "立刻", "马上"],
                intensity_modifiers={
                    "high": ["猛然", "突然", "瞬间", "霎时"],
                    "medium": ["很快", "迅速", "急速"],
                    "low": ["快速", "较快"]
                },
                context_requirements=["愤怒", "生气", "恼火", "暴怒"],
                probability=0.8
            ),
            TimeDistortionPattern(
                emotion=EmotionalState.ANGER,
                perception_mode=TimePerceptionMode.ACCELERATED,
                objective_patterns=["半小时", "一小时", "两小时"],
                subjective_replacements=["没多久", "一下子", "很快", "转眼间"],
                intensity_modifiers={
                    "high": ["瞬间", "立马", "马上"],
                    "medium": ["很快", "迅速"],
                    "low": ["比较快", "还算快"]
                },
                context_requirements=["愤怒", "急躁", "不耐烦"],
                probability=0.7
            )
        ]
        
        # 悲伤时间 - 缓慢感
        sadness_patterns = [
            TimeDistortionPattern(
                emotion=EmotionalState.SADNESS,
                perception_mode=TimePerceptionMode.DECELERATED,
                objective_patterns=["几分钟", "一会儿", "片刻", "不久"],
                subjective_replacements=["很久很久", "漫长的时间", "仿佛过了一个世纪", "无尽的等待"],
                intensity_modifiers={
                    "high": ["仿佛过了一个世纪", "无穷无尽", "永恒般"],
                    "medium": ["很久很久", "漫长", "缓慢"],
                    "low": ["有点久", "比较慢"]
                },
                context_requirements=["悲伤", "难过", "沮丧", "失落"],
                probability=0.9
            ),
            TimeDistortionPattern(
                emotion=EmotionalState.SADNESS,
                perception_mode=TimePerceptionMode.DECELERATED,
                objective_patterns=["一小时", "两小时", "半天"],
                subjective_replacements=["漫长的煎熬", "度日如年", "时间仿佛停滞", "每一秒都像一年"],
                intensity_modifiers={
                    "high": ["度日如年", "每一秒都是煎熬", "时间仿佛凝固"],
                    "medium": ["漫长的等待", "缓慢的流逝"],
                    "low": ["有些漫长", "稍显缓慢"]
                },
                context_requirements=["悲伤", "痛苦", "煎熬"],
                probability=0.8
            )
        ]
        
        # 焦虑时间 - 紧迫感
        anxiety_patterns = [
            TimeDistortionPattern(
                emotion=EmotionalState.ANXIETY,
                perception_mode=TimePerceptionMode.URGENT,
                objective_patterns=["还有时间", "时间充足", "不着急"],
                subjective_replacements=["时间不够了", "来不及了", "时间在飞逝", "分秒必争"],
                intensity_modifiers={
                    "high": ["时间在疯狂流逝", "每一秒都珍贵", "争分夺秒"],
                    "medium": ["时间紧迫", "有些着急"],
                    "low": ["稍微紧张", "有点赶"]
                },
                context_requirements=["焦虑", "紧张", "担心", "着急"],
                probability=0.9
            ),
            TimeDistortionPattern(
                emotion=EmotionalState.ANXIETY,
                perception_mode=TimePerceptionMode.URGENT,
                objective_patterns=["几分钟后", "一会儿后", "稍后"],
                subjective_replacements=["马上就要", "眼看就要", "迫在眉睫", "刻不容缓"],
                intensity_modifiers={
                    "high": ["迫在眉睫", "刻不容缓", "火烧眉毛"],
                    "medium": ["很快就要", "马上就"],
                    "low": ["快要", "即将"]
                },
                context_requirements=["焦虑", "紧张", "恐慌"],
                probability=0.8
            )
        ]
        
        # 喜悦时间 - 加速感/悬停感
        joy_patterns = [
            TimeDistortionPattern(
                emotion=EmotionalState.JOY,
                perception_mode=TimePerceptionMode.ACCELERATED,
                objective_patterns=["几小时", "一天", "整个下午"],
                subjective_replacements=["一眨眼", "转瞬即逝", "飞快地过去", "时光飞逝"],
                intensity_modifiers={
                    "high": ["转瞬即逝", "时光飞逝", "眨眼间"],
                    "medium": ["很快过去", "飞快"],
                    "low": ["比较快", "还算快"]
                },
                context_requirements=["开心", "高兴", "快乐", "兴奋"],
                probability=0.7
            ),
            TimeDistortionPattern(
                emotion=EmotionalState.JOY,
                perception_mode=TimePerceptionMode.SUSPENDED,
                objective_patterns=["这一刻", "此时", "现在"],
                subjective_replacements=["这美好的瞬间", "这永恒的一刻", "时间仿佛静止", "这珍贵的时光"],
                intensity_modifiers={
                    "high": ["永恒的瞬间", "时间静止", "完美的时刻"],
                    "medium": ["美好的时光", "珍贵的瞬间"],
                    "low": ["不错的时刻", "愉快的时光"]
                },
                context_requirements=["开心", "幸福", "满足", "陶醉"],
                probability=0.6
            )
        ]
        
        # 恐惧时间 - 悬停感/碎片感
        fear_patterns = [
            TimeDistortionPattern(
                emotion=EmotionalState.FEAR,
                perception_mode=TimePerceptionMode.SUSPENDED,
                objective_patterns=["几秒钟", "一瞬间", "片刻"],
                subjective_replacements=["仿佛永恒", "无尽的瞬间", "时间凝固", "漫长的一刻"],
                intensity_modifiers={
                    "high": ["时间凝固", "永恒般的瞬间", "无尽的恐惧"],
                    "medium": ["漫长的瞬间", "缓慢的时刻"],
                    "low": ["有些漫长", "稍显缓慢"]
                },
                context_requirements=["害怕", "恐惧", "惊恐", "畏惧"],
                probability=0.8
            )
        ]
        
        # 惊讶时间 - 悬停感/碎片感
        surprise_patterns = [
            TimeDistortionPattern(
                emotion=EmotionalState.SURPRISE,
                perception_mode=TimePerceptionMode.SUSPENDED,
                objective_patterns=["一瞬间", "突然", "忽然"],
                subjective_replacements=["时间仿佛停止", "那一刻世界静止", "瞬间的永恒", "时空凝固"],
                intensity_modifiers={
                    "high": ["世界静止", "时空凝固", "瞬间的永恒"],
                    "medium": ["时间停止", "那一刻"],
                    "low": ["那个瞬间", "突然间"]
                },
                context_requirements=["惊讶", "震惊", "意外", "吃惊"],
                probability=0.7
            )
        ]
        
        patterns[EmotionalState.ANGER] = anger_patterns
        patterns[EmotionalState.SADNESS] = sadness_patterns
        patterns[EmotionalState.ANXIETY] = anxiety_patterns
        patterns[EmotionalState.JOY] = joy_patterns
        patterns[EmotionalState.FEAR] = fear_patterns
        patterns[EmotionalState.SURPRISE] = surprise_patterns
        
        return patterns

    def detect_emotional_context(self, text: str) -> Dict[str, Any]:
        """检测文本中的情绪上下文

        Args:
            text: 输入文本

        Returns:
            情绪上下文分析结果
        """
        log_debug("时间扭曲", "开始检测情绪上下文", 文本长度=len(text))

        # 情绪关键词映射
        emotion_keywords = {
            EmotionalState.ANGER: [
                "愤怒", "生气", "恼火", "暴怒", "气愤", "恼怒", "火大",
                "讨厌", "烦躁", "不爽", "气死了", "受不了", "急躁", "不耐烦"
            ],
            EmotionalState.SADNESS: [
                "悲伤", "难过", "伤心", "痛苦", "沮丧", "失落", "绝望",
                "哭", "眼泪", "心痛", "心碎", "忧郁", "抑郁", "煎熬"
            ],
            EmotionalState.ANXIETY: [
                "焦虑", "紧张", "担心", "不安", "恐慌", "忧虑", "害怕",
                "慌张", "心慌", "烦躁", "坐立不安", "着急", "紧迫"
            ],
            EmotionalState.JOY: [
                "开心", "高兴", "快乐", "兴奋", "喜悦", "愉快", "欣喜",
                "激动", "满足", "幸福", "欢乐", "乐观", "陶醉"
            ],
            EmotionalState.FEAR: [
                "害怕", "恐惧", "惊恐", "恐慌", "畏惧", "胆怯", "惊吓",
                "可怕", "吓人", "恐怖", "威胁", "危险"
            ],
            EmotionalState.SURPRISE: [
                "惊讶", "震惊", "意外", "吃惊", "惊奇", "诧异", "惊愕"
            ]
        }

        # 检测情绪
        detected_emotions = {}
        text_lower = text.lower()

        for emotion, keywords in emotion_keywords.items():
            matches = [kw for kw in keywords if kw in text_lower]
            if matches:
                # 计算情绪强度
                intensity = min(len(matches) * 0.3 + 0.4, 1.0)
                detected_emotions[emotion] = {
                    "intensity": intensity,
                    "keywords": matches,
                    "confidence": len(matches) / len(keywords)
                }

        # 检测时间相关词汇
        time_patterns = [
            "几分钟", "一会儿", "片刻", "不久", "很快", "半小时", "一小时", "两小时",
            "还有时间", "时间充足", "不着急", "几分钟后", "一会儿后", "稍后",
            "几小时", "一天", "整个下午", "这一刻", "此时", "现在", "几秒钟",
            "一瞬间", "突然", "忽然", "时间", "时候", "瞬间", "刹那"
        ]

        time_references = []
        for pattern in time_patterns:
            if pattern in text:
                time_references.append(pattern)

        # 确定主导情绪
        primary_emotion = None
        max_intensity = 0

        if detected_emotions:
            for emotion, data in detected_emotions.items():
                if data["intensity"] > max_intensity:
                    max_intensity = data["intensity"]
                    primary_emotion = emotion

        result = {
            "primary_emotion": primary_emotion,
            "emotion_intensity": max_intensity,
            "all_emotions": detected_emotions,
            "time_references": time_references,
            "has_emotional_content": len(detected_emotions) > 0,
            "has_time_content": len(time_references) > 0,
            "text_length": len(text)
        }

        log_info("时间扭曲", "情绪上下文检测完成",
                主导情绪=primary_emotion.value if primary_emotion else "无",
                情绪强度=max_intensity,
                时间引用数量=len(time_references))

        return result

    def distort_time_perception(
        self,
        text: str,
        intensity: float = 0.7,
        preserve_meaning: bool = True
    ) -> TimeDistortionResult:
        """扭曲时间感知

        Args:
            text: 原始文本
            intensity: 扭曲强度 (0.0-1.0)
            preserve_meaning: 是否保持原意

        Returns:
            时间扭曲结果
        """
        log_debug("时间扭曲", "开始时间感知扭曲",
                 文本长度=len(text), 强度=intensity)

        # 检测情绪上下文
        emotional_context = self.detect_emotional_context(text)

        if not emotional_context["has_emotional_content"] or not emotional_context["has_time_content"]:
            reason = "无情绪内容" if not emotional_context["has_emotional_content"] else "无时间内容"
            log_info("时间扭曲", f"跳过扭曲：{reason}")
            return TimeDistortionResult(
                original_text=text,
                modified_text=text,
                distortions_applied=[],
                emotional_context=emotional_context,
                processing_details={
                    "skipped": True,
                    "reason": reason,
                    "distortion_intensity": 0.0,
                    "original_length": len(text),
                    "modified_length": len(text),
                    "patterns_attempted": 0,
                    "distortions_applied": 0
                }
            )

        primary_emotion = emotional_context["primary_emotion"]
        emotion_intensity = emotional_context["emotion_intensity"]

        # 调整扭曲强度
        adjusted_intensity = min(intensity * emotion_intensity, 1.0)

        # 获取适用的扭曲模式
        applicable_patterns = self._get_applicable_patterns(
            primary_emotion,
            text,
            adjusted_intensity
        )

        if not applicable_patterns:
            log_info("时间扭曲", "未找到适用的扭曲模式", 情绪=primary_emotion.value)
            return TimeDistortionResult(
                original_text=text,
                modified_text=text,
                distortions_applied=[],
                emotional_context=emotional_context,
                processing_details={
                    "skipped": True,
                    "reason": "无适用模式",
                    "distortion_intensity": adjusted_intensity,
                    "original_length": len(text),
                    "modified_length": len(text),
                    "patterns_attempted": len(applicable_patterns),
                    "distortions_applied": 0,
                    "primary_emotion": primary_emotion.value if primary_emotion else None
                }
            )

        # 执行时间扭曲
        modified_text = text
        distortions_applied = []

        for pattern in applicable_patterns:
            distortion_result = self._apply_time_distortion(
                modified_text,
                pattern,
                adjusted_intensity,
                preserve_meaning
            )

            if distortion_result["modified"]:
                modified_text = distortion_result["text"]
                distortions_applied.append({
                    "pattern": pattern.perception_mode.value,
                    "emotion": pattern.emotion.value,
                    "original_expressions": distortion_result["original_expressions"],
                    "distorted_expressions": distortion_result["distorted_expressions"],
                    "positions": distortion_result["positions"]
                })

                log_debug("时间扭曲", f"成功应用{pattern.perception_mode.value}模式",
                         修改数量=len(distortion_result["original_expressions"]))

        processing_details = {
            "original_length": len(text),
            "modified_length": len(modified_text),
            "patterns_attempted": len(applicable_patterns),
            "distortions_applied": len(distortions_applied),
            "distortion_intensity": adjusted_intensity,
            "primary_emotion": primary_emotion.value if primary_emotion else None,
            "perception_modes": [d["pattern"] for d in distortions_applied]
        }

        log_info("时间扭曲", "时间感知扭曲完成",
                扭曲数量=len(distortions_applied),
                文本变化=len(modified_text) - len(text))

        return TimeDistortionResult(
            original_text=text,
            modified_text=modified_text,
            distortions_applied=distortions_applied,
            emotional_context=emotional_context,
            processing_details=processing_details
        )

    def _get_applicable_patterns(
        self,
        emotion: EmotionalState,
        text: str,
        intensity: float
    ) -> List[TimeDistortionPattern]:
        """获取适用的扭曲模式

        Args:
            emotion: 情绪状态
            text: 文本
            intensity: 强度

        Returns:
            适用的模式列表
        """
        if emotion not in self.distortion_patterns:
            return []

        available_patterns = self.distortion_patterns[emotion]
        applicable_patterns = []

        for pattern in available_patterns:
            # 检查上下文要求
            context_match = False
            if not pattern.context_requirements:
                context_match = True
            else:
                for requirement in pattern.context_requirements:
                    if requirement in text:
                        context_match = True
                        break

            # 检查是否有可替换的时间表达
            has_time_expressions = False
            for obj_pattern in pattern.objective_patterns:
                if obj_pattern in text:
                    has_time_expressions = True
                    break

            if context_match and has_time_expressions:
                # 根据强度调整概率
                adjusted_probability = pattern.probability * intensity
                if random.random() < adjusted_probability:
                    applicable_patterns.append(pattern)

        log_debug("时间扭曲", "模式筛选完成",
                 可用模式数=len(available_patterns),
                 适用模式数=len(applicable_patterns))

        return applicable_patterns

    def _apply_time_distortion(
        self,
        text: str,
        pattern: TimeDistortionPattern,
        intensity: float,
        preserve_meaning: bool = True
    ) -> Dict[str, Any]:
        """应用时间扭曲模式

        Args:
            text: 文本
            pattern: 扭曲模式
            intensity: 强度
            preserve_meaning: 是否保持原意

        Returns:
            扭曲结果
        """
        modified_text = text
        original_expressions = []
        distorted_expressions = []
        positions = []

        # 选择强度修饰词
        intensity_level = "high" if intensity > 0.8 else "medium" if intensity > 0.5 else "low"
        modifiers = pattern.intensity_modifiers.get(intensity_level, [])

        # 替换客观时间表达
        for obj_pattern in pattern.objective_patterns:
            if obj_pattern in modified_text:
                # 选择主观替换表达
                replacement_options = pattern.subjective_replacements.copy()

                # 根据强度添加修饰词
                if modifiers and random.random() < intensity:
                    modifier = random.choice(modifiers)
                    replacement_options = [f"{modifier}{repl}" for repl in replacement_options]

                # 选择替换表达
                replacement = random.choice(replacement_options)

                # 执行替换
                old_text = modified_text
                modified_text = modified_text.replace(obj_pattern, replacement, 1)

                if modified_text != old_text:
                    original_expressions.append(obj_pattern)
                    distorted_expressions.append(replacement)
                    # 找到替换位置
                    pos = old_text.find(obj_pattern)
                    if pos != -1:
                        positions.append(pos)

        return {
            "modified": len(original_expressions) > 0,
            "text": modified_text,
            "original_expressions": original_expressions,
            "distorted_expressions": distorted_expressions,
            "positions": positions
        }

    def apply_subjective_timing(
        self,
        text: str,
        emotion: EmotionalState,
        intensity: float = 0.7
    ) -> str:
        """应用主观时间流逝调整

        Args:
            text: 文本
            emotion: 情绪状态
            intensity: 强度

        Returns:
            调整后的文本
        """
        log_debug("时间扭曲", "开始主观时间调整", 情绪=emotion.value, 强度=intensity)

        # 时间流逝的主观描述
        subjective_timing = {
            EmotionalState.ANGER: {
                "fast": ["时间飞逝", "转眼间", "眨眼功夫", "瞬间"],
                "slow": ["每一秒都煎熬", "时间拖沓", "缓慢流逝"]
            },
            EmotionalState.SADNESS: {
                "slow": ["时间缓慢流淌", "度日如年", "每分每秒都漫长", "时光凝滞"],
                "fast": ["时间悄然流逝", "不知不觉"]
            },
            EmotionalState.ANXIETY: {
                "urgent": ["时间紧迫", "分秒必争", "时不我待", "争分夺秒"],
                "fast": ["时间飞逝", "来不及", "时间不够"]
            },
            EmotionalState.JOY: {
                "fast": ["时光飞逝", "快乐时光总是短暂", "转眼即逝"],
                "suspended": ["美好的时光", "珍贵的瞬间", "时间仿佛静止"]
            },
            EmotionalState.FEAR: {
                "suspended": ["时间凝固", "那一刻仿佛永恒", "时间停滞"],
                "slow": ["漫长的恐惧", "每一秒都是煎熬"]
            }
        }

        if emotion not in subjective_timing:
            return text

        timing_options = subjective_timing[emotion]

        # 根据情绪选择合适的时间流逝描述
        if emotion == EmotionalState.ANGER:
            chosen_timing = timing_options["fast"] if intensity > 0.6 else timing_options["slow"]
        elif emotion == EmotionalState.SADNESS:
            chosen_timing = timing_options["slow"]
        elif emotion == EmotionalState.ANXIETY:
            chosen_timing = timing_options["urgent"] if intensity > 0.7 else timing_options["fast"]
        elif emotion == EmotionalState.JOY:
            chosen_timing = timing_options["fast"] if intensity > 0.6 else timing_options["suspended"]
        elif emotion == EmotionalState.FEAR:
            chosen_timing = timing_options["suspended"] if intensity > 0.7 else timing_options["slow"]
        else:
            chosen_timing = list(timing_options.values())[0]

        # 在适当位置插入主观时间描述
        timing_phrase = random.choice(chosen_timing)

        # 寻找插入点（句子结尾）
        sentences = re.split(r'[。！？]', text)
        if len(sentences) > 1:
            # 在第一个句子后插入
            modified_text = f"{sentences[0]}。{timing_phrase}，{text[len(sentences[0])+1:]}"
            log_debug("时间扭曲", "主观时间调整完成", 插入短语=timing_phrase)
            return modified_text

        return text


# 工厂函数和便捷接口

def create_time_perception_distorter() -> TimePerceptionDistorter:
    """创建时间感知扭曲器实例"""
    log_debug("时间扭曲", "创建时间感知扭曲器实例")
    return TimePerceptionDistorter()


def distort_time_perception(
    text: str,
    intensity: float = 0.7,
    preserve_meaning: bool = True
) -> str:
    """快速时间感知扭曲

    Args:
        text: 输入文本
        intensity: 扭曲强度
        preserve_meaning: 是否保持原意

    Returns:
        处理后的文本
    """
    distorter = create_time_perception_distorter()
    result = distorter.distort_time_perception(text, intensity, preserve_meaning)
    return result.modified_text


def apply_subjective_timing(
    text: str,
    emotion: EmotionalState,
    intensity: float = 0.7
) -> str:
    """应用主观时间流逝

    Args:
        text: 输入文本
        emotion: 情绪状态
        intensity: 强度

    Returns:
        处理后的文本
    """
    distorter = create_time_perception_distorter()
    return distorter.apply_subjective_timing(text, emotion, intensity)


def analyze_time_distortion_potential(text: str) -> Dict[str, Any]:
    """分析文本的时间扭曲潜力

    Args:
        text: 输入文本

    Returns:
        分析结果
    """
    distorter = create_time_perception_distorter()
    emotional_context = distorter.detect_emotional_context(text)

    # 计算时间扭曲潜力
    potential_score = 0.0
    if emotional_context["has_emotional_content"] and emotional_context["has_time_content"]:
        potential_score = emotional_context["emotion_intensity"] * 0.7
        potential_score += len(emotional_context["time_references"]) * 0.1
        potential_score = min(potential_score, 1.0)

    return {
        "emotional_context": emotional_context,
        "distortion_potential": potential_score,
        "recommended_intensity": potential_score * 0.8,
        "suitable_for_distortion": potential_score > 0.3,
        "time_references_found": len(emotional_context["time_references"]),
        "primary_emotion": emotional_context["primary_emotion"].value if emotional_context["primary_emotion"] else None
    }
