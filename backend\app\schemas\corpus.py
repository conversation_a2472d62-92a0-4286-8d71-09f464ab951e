"""
🗂️ [语料库] 语料库相关的Pydantic数据模型
定义语料库管理API的请求和响应结构
"""

from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class CorpusCategory(str, Enum):
    """语料库分类枚举"""
    LANGUAGE_STYLES = "language_styles"
    EMOTIONAL_GENES = "emotional_genes"
    PLEASURE_PATTERNS = "pleasure_patterns"
    PERSONALITY_TRAITS = "personality_traits"
    WORLD_KNOWLEDGE = "world_knowledge"


class QualityLevel(str, Enum):
    """质量等级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    PREMIUM = "premium"


class UsageFrequency(str, Enum):
    """使用频率枚举"""
    RARE = "rare"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


# ==================== 基础数据模型 ====================

class CorpusMetadataBase(BaseModel):
    """语料库元数据基础模型"""
    name: str = Field(..., description="数据名称", max_length=200)
    version: str = Field(default="1.0.0", description="版本号")
    description: str = Field(..., description="数据描述", max_length=1000)
    category: CorpusCategory = Field(..., description="数据分类")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    source: str = Field(default="", description="数据来源", max_length=500)
    quality_score: float = Field(default=0.0, ge=0.0, le=1.0, description="质量评分")
    usage_frequency: UsageFrequency = Field(default=UsageFrequency.MEDIUM, description="使用频率")


class CorpusEntryBase(BaseModel):
    """语料库条目基础模型"""
    content: str = Field(..., description="内容", max_length=5000)
    weight: float = Field(default=1.0, ge=0.0, le=10.0, description="权重")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    tags: List[str] = Field(default_factory=list, description="标签")
    examples: List[str] = Field(default_factory=list, description="使用示例")


class CorpusCategoryBase(BaseModel):
    """语料库分类基础模型"""
    name: str = Field(..., description="分类名称", max_length=100)
    description: str = Field(default="", description="分类描述", max_length=500)
    weight: float = Field(default=1.0, ge=0.0, le=10.0, description="分类权重")


# ==================== 请求模型 ====================

class CorpusFileLoadRequest(BaseModel):
    """语料库文件加载请求"""
    file_path: str = Field(..., description="文件路径")
    force_reload: bool = Field(default=False, description="强制重新加载")
    validate_data: bool = Field(default=True, description="是否验证数据")


class CorpusSearchRequest(BaseModel):
    """语料库搜索请求"""
    query: str = Field(..., description="搜索关键词", max_length=200)
    category: Optional[CorpusCategory] = Field(None, description="搜索分类")
    tags: List[str] = Field(default_factory=list, description="标签过滤")
    quality_threshold: float = Field(default=0.0, ge=0.0, le=1.0, description="质量阈值")
    limit: int = Field(default=50, ge=1, le=1000, description="返回数量限制")
    offset: int = Field(default=0, ge=0, description="偏移量")


class CorpusEntryCreateRequest(BaseModel):
    """语料库条目创建请求"""
    file_path: str = Field(..., description="目标文件路径")
    category_name: str = Field(..., description="分类名称")
    entry: CorpusEntryBase = Field(..., description="条目数据")


class CorpusEntryUpdateRequest(BaseModel):
    """语料库条目更新请求"""
    file_path: str = Field(..., description="文件路径")
    category_name: str = Field(..., description="分类名称")
    entry_index: int = Field(..., ge=0, description="条目索引")
    entry: CorpusEntryBase = Field(..., description="更新后的条目数据")


class CorpusBatchImportRequest(BaseModel):
    """语料库批量导入请求"""
    file_path: str = Field(..., description="目标文件路径")
    data: Dict[str, Any] = Field(..., description="导入数据")
    merge_strategy: str = Field(default="append", description="合并策略: append, replace, merge")
    validate_before_import: bool = Field(default=True, description="导入前验证")


# ==================== 响应模型 ====================

class CorpusEntryResponse(CorpusEntryBase):
    """语料库条目响应"""
    id: Optional[str] = Field(None, description="条目ID")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


class CorpusCategoryResponse(CorpusCategoryBase):
    """语料库分类响应"""
    items: List[CorpusEntryResponse] = Field(default_factory=list, description="条目列表")
    item_count: int = Field(default=0, description="条目数量")


class CorpusFileResponse(BaseModel):
    """语料库文件响应"""
    metadata: CorpusMetadataBase = Field(..., description="文件元数据")
    categories: Dict[str, CorpusCategoryResponse] = Field(default_factory=dict, description="分类数据")
    patterns: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="模式数据")
    rules: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="规则数据")
    examples: List[Dict[str, Any]] = Field(default_factory=list, description="示例数据")
    statistics: Dict[str, Any] = Field(default_factory=dict, description="统计信息")


class CorpusSearchResponse(BaseModel):
    """语料库搜索响应"""
    query: str = Field(..., description="搜索关键词")
    total_count: int = Field(..., description="总结果数")
    results: List[CorpusEntryResponse] = Field(..., description="搜索结果")
    categories: Dict[str, int] = Field(default_factory=dict, description="分类统计")
    search_time_ms: float = Field(..., description="搜索耗时(毫秒)")


class CorpusListResponse(BaseModel):
    """语料库文件列表响应"""
    files: List[Dict[str, Any]] = Field(..., description="文件列表")
    total_files: int = Field(..., description="文件总数")
    categories: Dict[str, int] = Field(default_factory=dict, description="分类统计")
    total_entries: int = Field(default=0, description="条目总数")


class CorpusStatsResponse(BaseModel):
    """语料库统计响应"""
    total_files: int = Field(..., description="文件总数")
    total_entries: int = Field(..., description="条目总数")
    categories: Dict[str, int] = Field(..., description="分类统计")
    quality_distribution: Dict[str, int] = Field(..., description="质量分布")
    usage_frequency_distribution: Dict[str, int] = Field(..., description="使用频率分布")
    last_updated: datetime = Field(..., description="最后更新时间")
    cache_status: Dict[str, Any] = Field(..., description="缓存状态")


class CorpusOperationResponse(BaseModel):
    """语料库操作响应"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作消息")
    operation: str = Field(..., description="操作类型")
    affected_items: int = Field(default=0, description="影响的条目数")
    execution_time_ms: float = Field(..., description="执行时间(毫秒)")
    details: Dict[str, Any] = Field(default_factory=dict, description="详细信息")


# ==================== 验证和配置模型 ====================

class CorpusValidationResult(BaseModel):
    """语料库验证结果"""
    is_valid: bool = Field(..., description="是否有效")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
    file_path: str = Field(..., description="文件路径")
    validation_time_ms: float = Field(..., description="验证耗时(毫秒)")


class CorpusConfigResponse(BaseModel):
    """语料库配置响应"""
    corpus_info: Dict[str, Any] = Field(..., description="语料库信息")
    corpus_structure: Dict[str, Any] = Field(..., description="语料库结构")
    data_format: Dict[str, Any] = Field(..., description="数据格式配置")
    loading_config: Dict[str, Any] = Field(..., description="加载配置")
    quality_control: Dict[str, Any] = Field(..., description="质量控制配置")
