"""
🎨 [风格] 语言风格注入器
基于指导文档中的创意想法，实现方言、古文、讽刺等多种语言风格的智能注入
"""

import random
import re
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from app.core.config import log_info, log_debug, log_error, log_success
from app.core.corpus_manager import create_corpus_manager, CorpusManager


class StyleType(str, Enum):
    """风格类型枚举"""
    DIALECT = "dialect"              # 方言
    ANCIENT_CHINESE = "ancient"      # 古文
    SARCASM = "sarcasm"             # 讽刺
    CREATIVE_INSULT = "insult"      # 创意骂人
    POETRY_ADAPTATION = "poetry"     # 诗词改编
    REGIONAL_FLAVOR = "regional"     # 地域特色


@dataclass
class StyleInjectionResult:
    """风格注入结果"""
    success: bool
    original_text: str
    styled_text: str
    applied_styles: List[str]
    injection_points: List[Dict[str, Any]]
    style_intensity: float
    processing_details: Dict[str, Any]


class LanguageStyleInjector:
    """
    🎨 [风格注入器] 语言风格注入器

    基于指导文档中的创意想法，为文本注入各种语言风格，
    包括方言、古文表达、讽刺语气、创意骂人等。
    """

    def __init__(self, corpus_root: Optional[str] = None):
        """初始化风格注入器"""
        self.corpus_manager = create_corpus_manager(corpus_root)
        self.corpus_data = {}
        self._initialized = False

        # 语料库文件路径映射
        self.corpus_files = {
            "dialect": "language_styles/dialect_library.json",
            "ancient": "language_styles/ancient_expressions.json",
            "sarcasm": "language_styles/sarcasm_patterns.json",
            "creative_insults": "language_styles/creative_insults.json"
        }

        log_debug("风格", "语言风格注入器创建完成", 语料库文件数=len(self.corpus_files))

    async def _ensure_initialized(self):
        """确保语料库已初始化"""
        if not self._initialized:
            await self._initialize_corpus()
            self._initialized = True

    async def _initialize_corpus(self):
        """初始化语料库数据"""
        log_info("风格", "开始初始化语料库数据")

        try:
            # 初始化语料库管理器
            await self.corpus_manager.initialize()

            # 加载各类语料库文件
            for corpus_type, file_path in self.corpus_files.items():
                try:
                    corpus_data = await self.corpus_manager.load_corpus_file(file_path)
                    self.corpus_data[corpus_type] = corpus_data
                    log_debug("风格", f"成功加载{corpus_type}语料库",
                             文件=file_path,
                             分类数=len(corpus_data.categories))
                except Exception as e:
                    log_error("风格", f"加载{corpus_type}语料库失败", error=e, 文件=file_path)
                    # 使用空数据作为后备
                    self.corpus_data[corpus_type] = None

            log_success("风格", "语料库数据初始化完成",
                       成功加载=len([k for k, v in self.corpus_data.items() if v is not None]))

        except Exception as e:
            log_error("风格", "语料库初始化失败", error=e)
            # 使用硬编码数据作为后备
            await self._load_fallback_data()

    async def _load_fallback_data(self):
        """加载后备硬编码数据"""
        log_info("风格", "使用后备硬编码数据")

        # 保留原有的硬编码数据作为后备
        self.fallback_data = {
            "dialect_vocabulary": {
                "东北话": {
                    "替换词": {
                        "很": "老", "非常": "贼", "什么": "啥", "这个": "这嘎达",
                        "那个": "那嘎达", "厉害": "牛逼", "漂亮": "水灵", "聪明": "精明"
                    },
                    "语气词": ["呗", "咋地", "整啥呢", "嘎哈", "老铁"],
                    "句式": ["...整的挺好", "...贼拉...", "...老...了"]
                },
                "四川话": {
                    "替换词": {
                        "很": "巴适", "厉害": "凶", "漂亮": "乖", "聪明": "精灵",
                        "什么": "啥子", "这个": "这个嘛", "吃": "恰"
                    },
                    "语气词": ["嘛", "哦", "撒", "得板", "安逸"],
                    "句式": ["...巴适得很", "...凶得很", "...安逸得板"]
                },
                "广东话": {
                    "替换词": {
                        "很": "好", "厉害": "犀利", "漂亮": "靓", "聪明": "醒目",
                        "什么": "乜嘢", "吃": "食", "走": "行"
                    },
                    "语气词": ["啦", "咯", "嘅", "喔", "呀"],
                    "句式": ["...好犀利", "...好靓仔", "...好醒目"]
                }
            },
            "ancient_expressions": {
                "情感表达": {
                    "高兴": ["欣然", "大悦", "喜不自胜", "心花怒放"],
                    "愤怒": ["怒不可遏", "勃然大怒", "怒发冲冠", "义愤填膺"],
                    "悲伤": ["黯然神伤", "泪如雨下", "肝肠寸断", "痛不欲生"],
                    "惊讶": ["瞠目结舌", "目瞪口呆", "大惊失色", "骇然失色"]
                },
                "动作描述": {
                    "走": ["踱步", "疾行", "缓步", "踉跄"],
                    "看": ["凝视", "瞥见", "端详", "睥睨"],
                    "说": ["言道", "曰", "云", "谓"],
                    "想": ["思忖", "沉吟", "寻思", "暗忖"]
                },
                "修饰词": {
                    "很": ["甚", "颇", "极", "殊"],
                    "非常": ["极其", "甚为", "颇为", "殊为"],
                    "突然": ["忽然", "倏忽", "猛然", "蓦地"]
                }
            },
            "sarcasm_patterns": {
                "反讽": [
                    "真是{形容词}啊", "可真{形容词}", "这就是传说中的{名词}吧",
                    "哦，原来如此{形容词}", "果然是{形容词}的典范"
                ],
                "夸张": [
                    "简直{形容词}到了极点", "这{名词}，绝了", "真是{形容词}得不行",
                    "这{名词}，堪称一绝", "果然{形容词}得令人发指"
                ],
                "对比": [
                    "比{对象}还{形容词}", "连{对象}都不如", "简直是{对象}的反面教材",
                    "和{对象}一比，真是天壤之别"
                ]
            },
            "creative_insults": {
                "智商类": [
                    "脑回路清奇", "思维独特", "逻辑感人", "智慧如海（深不可测）",
                    "头脑简单四肢发达", "一根筋到底", "脑子是个好东西"
                ],
                "能力类": [
                    "实力不允许", "技能点全点错了", "天赋异禀（异于常人）",
                    "能力有限公司", "水平堪忧", "功力深厚（深不见底）"
                ],
                "性格类": [
                    "个性鲜明", "特立独行", "与众不同", "独树一帜",
                    "性格使然", "天生丽质（天生如此）"
                ]
            },
            "poetry_templates": {
                "七言绝句": [
                    "{主题}不见{对象}影，{情感}空余{场景}中。",
                    "{动作}{对象}如{比喻}，{结果}总是{情感}同。"
                ],
                "五言律诗": [
                    "{对象}{动作}去，{情感}满{场景}。",
                    "{时间}{事件}时，{结果}自{状态}。"
                ],
                "词牌改编": [
                    "念{对象}·{主题}：{情感}如{比喻}，{动作}向{方向}。{结果}知何处，{场景}空{状态}。"
                ]
            }
        }
    
    async def inject_style(
        self,
        text: str,
        style_types: List[StyleType],
        intensity: float = 0.3,
        preserve_meaning: bool = True
    ) -> StyleInjectionResult:
        """
        🎨 [风格注入] 为文本注入指定的语言风格

        Args:
            text: 原始文本
            style_types: 要注入的风格类型列表
            intensity: 注入强度 (0.0-1.0)
            preserve_meaning: 是否保持原意

        Returns:
            StyleInjectionResult: 风格注入结果
        """
        log_info("风格", "开始语言风格注入",
                文本长度=len(text),
                风格类型=len(style_types),
                注入强度=intensity)

        try:
            # 确保语料库已初始化
            await self._ensure_initialized()

            styled_text = text
            applied_styles = []
            injection_points = []
            processing_details = {}
            
            # 按风格类型依次处理
            for style_type in style_types:
                if style_type == StyleType.DIALECT:
                    result = await self._inject_dialect(styled_text, intensity)
                elif style_type == StyleType.ANCIENT_CHINESE:
                    result = await self._inject_ancient_chinese(styled_text, intensity)
                elif style_type == StyleType.SARCASM:
                    result = await self._inject_sarcasm(styled_text, intensity)
                elif style_type == StyleType.CREATIVE_INSULT:
                    result = await self._inject_creative_insult(styled_text, intensity)
                elif style_type == StyleType.POETRY_ADAPTATION:
                    result = await self._inject_poetry_adaptation(styled_text, intensity)
                elif style_type == StyleType.REGIONAL_FLAVOR:
                    result = await self._inject_regional_flavor(styled_text, intensity)
                else:
                    continue
                
                if result["success"]:
                    styled_text = result["text"]
                    applied_styles.append(style_type.value)
                    injection_points.extend(result["injection_points"])
                    processing_details[style_type.value] = result["details"]
                    
                    log_debug("风格", f"成功应用{style_type.value}风格",
                             注入点数=len(result["injection_points"]))
            
            # 计算最终风格强度
            final_intensity = len(applied_styles) / len(style_types) if style_types else 0.0
            
            log_success("风格", "语言风格注入完成",
                       应用风格数=len(applied_styles),
                       注入点总数=len(injection_points),
                       最终强度=f"{final_intensity:.2f}")
            
            return StyleInjectionResult(
                success=len(applied_styles) > 0,
                original_text=text,
                styled_text=styled_text,
                applied_styles=applied_styles,
                injection_points=injection_points,
                style_intensity=final_intensity,
                processing_details=processing_details
            )
            
        except Exception as e:
            log_error("风格", "语言风格注入失败", error=e)
            return StyleInjectionResult(
                success=False,
                original_text=text,
                styled_text=text,
                applied_styles=[],
                injection_points=[],
                style_intensity=0.0,
                processing_details={"error": str(e)}
            )
    
    async def _inject_dialect(self, text: str, intensity: float) -> Dict[str, Any]:
        """注入方言风格"""
        styled_text = text
        injection_points = []

        # 获取方言数据
        dialect_data = self._get_dialect_data()
        if not dialect_data:
            return {"success": False, "text": text, "injection_points": [], "details": {"error": "无方言数据"}}

        # 随机选择一种方言
        dialect_names = list(dialect_data.keys())
        dialect_name = random.choice(dialect_names)
        dialect_info = dialect_data[dialect_name]

        # 词汇替换
        if "替换词" in dialect_info:
            replacements = dialect_info["替换词"]
            for original, dialect_word in replacements.items():
                if random.random() < intensity and original in styled_text:
                    styled_text = styled_text.replace(original, dialect_word)
                    injection_points.append({
                        "type": "词汇替换",
                        "original": original,
                        "replacement": dialect_word,
                        "dialect": dialect_name
                    })

        # 语气词添加
        if "语气词" in dialect_info:
            sentences = re.split(r'[。！？]', styled_text)
            for i, sentence in enumerate(sentences):
                if sentence.strip() and random.random() < intensity * 0.5:
                    mood_word = random.choice(dialect_info["语气词"])
                    sentences[i] = sentence + mood_word
                    injection_points.append({
                        "type": "语气词",
                        "addition": mood_word,
                        "dialect": dialect_name
                    })
            styled_text = '。'.join(sentences)

        return {
            "success": len(injection_points) > 0,
            "text": styled_text,
            "injection_points": injection_points,
            "details": {"dialect": dialect_name, "modifications": len(injection_points)}
        }

    def _get_dialect_data(self) -> Dict[str, Any]:
        """获取方言数据"""
        # 优先使用语料库数据
        if self.corpus_data.get("dialect"):
            corpus_data = self.corpus_data["dialect"]
            dialect_data = {}

            # 转换语料库格式为原有格式
            for category_name, category in corpus_data.categories.items():
                dialect_info = {}

                # 查找替换词汇
                for item in category.items:
                    if item.metadata.get("type") == "word_replacement":
                        dialect_info["替换词"] = item.data
                    elif item.metadata.get("type") == "modal_particles":
                        dialect_info["语气词"] = item.data
                    elif item.metadata.get("type") == "sentence_patterns":
                        dialect_info["句式"] = item.data

                dialect_data[category_name] = dialect_info

            return dialect_data

        # 使用后备数据
        return self.fallback_data.get("dialect_vocabulary", {})
    
    async def _inject_ancient_chinese(self, text: str, intensity: float) -> Dict[str, Any]:
        """注入古文风格"""
        styled_text = text
        injection_points = []

        # 获取古文表达数据
        ancient_data = self._get_ancient_expressions_data()
        if not ancient_data:
            return {"success": False, "text": text, "injection_points": [], "details": {"error": "无古文数据"}}

        # 情感表达替换
        if "情感表达" in ancient_data:
            emotion_patterns = {
                "高兴": ["开心", "快乐", "兴奋", "愉快"],
                "愤怒": ["生气", "愤怒", "恼火", "气愤"],
                "悲伤": ["难过", "伤心", "痛苦", "悲伤"],
                "惊讶": ["惊讶", "震惊", "吃惊", "意外"]
            }

            for emotion, expressions in ancient_data["情感表达"].items():
                if emotion in emotion_patterns:
                    for modern_word in emotion_patterns[emotion]:
                        if modern_word in styled_text and random.random() < intensity:
                            ancient_expr = random.choice(expressions)
                            styled_text = styled_text.replace(modern_word, ancient_expr, 1)
                            injection_points.append({
                                "type": "情感表达",
                                "original": modern_word,
                                "replacement": ancient_expr
                            })

        # 动作描述替换
        if "动作描述" in ancient_data:
            for action, ancient_actions in ancient_data["动作描述"].items():
                if action in styled_text and random.random() < intensity:
                    ancient_action = random.choice(ancient_actions)
                    styled_text = styled_text.replace(action, ancient_action, 1)
                    injection_points.append({
                        "type": "动作描述",
                        "original": action,
                        "replacement": ancient_action
                    })

        return {
            "success": len(injection_points) > 0,
            "text": styled_text,
            "injection_points": injection_points,
            "details": {"ancient_expressions": len(injection_points)}
        }

    def _get_ancient_expressions_data(self) -> Dict[str, Any]:
        """获取古文表达数据"""
        # 优先使用语料库数据
        if self.corpus_data.get("ancient"):
            corpus_data = self.corpus_data["ancient"]
            ancient_data = {}

            # 转换语料库格式
            for category_name, category in corpus_data.categories.items():
                category_data = {}
                for item in category.items:
                    category_data[item.content] = item.data
                ancient_data[category_name] = category_data

            return ancient_data

        # 使用后备数据
        return self.fallback_data.get("ancient_expressions", {})

    async def _inject_sarcasm(self, text: str, intensity: float) -> Dict[str, Any]:
        """注入讽刺风格"""
        styled_text = text
        injection_points = []

        # 获取讽刺模式数据
        sarcasm_data = self._get_sarcasm_patterns_data()
        if not sarcasm_data:
            return {"success": False, "text": text, "injection_points": [], "details": {"error": "无讽刺数据"}}

        # 寻找适合讽刺的句子（通常是陈述句）
        sentences = re.split(r'[。！？]', text)

        for i, sentence in enumerate(sentences):
            if sentence.strip() and random.random() < intensity * 0.3:
                # 随机选择讽刺模式
                sarcasm_type = random.choice(list(sarcasm_data.keys()))
                patterns = sarcasm_data[sarcasm_type]
                pattern = random.choice(patterns)

                # 简单的讽刺转换
                if "真是" in pattern:
                    sarcastic_sentence = f"真是{sentence.strip()}啊"
                elif "可真" in pattern:
                    sarcastic_sentence = f"可真{sentence.strip()}"
                else:
                    sarcastic_sentence = sentence + "，真是厉害"

                sentences[i] = sarcastic_sentence
                injection_points.append({
                    "type": "讽刺转换",
                    "original": sentence,
                    "sarcastic": sarcastic_sentence,
                    "pattern": pattern
                })

        styled_text = '。'.join(sentences)

        return {
            "success": len(injection_points) > 0,
            "text": styled_text,
            "injection_points": injection_points,
            "details": {"sarcasm_injections": len(injection_points)}
        }

    def _get_sarcasm_patterns_data(self) -> Dict[str, Any]:
        """获取讽刺模式数据"""
        # 优先使用语料库数据
        if self.corpus_data.get("sarcasm"):
            corpus_data = self.corpus_data["sarcasm"]
            sarcasm_data = {}

            # 从patterns中获取数据
            if hasattr(corpus_data, 'patterns') and corpus_data.patterns:
                for pattern_name, pattern_data in corpus_data.patterns.items():
                    if "templates" in pattern_data:
                        templates = []
                        for template_info in pattern_data["templates"]:
                            templates.append(template_info.get("template", ""))
                        sarcasm_data[pattern_name] = templates

            # 如果patterns为空，尝试从categories获取
            elif hasattr(corpus_data, 'categories') and corpus_data.categories:
                for category_name, category in corpus_data.categories.items():
                    templates = []
                    for item in category.items:
                        if item.data:  # 如果有data字段，使用data
                            templates.extend(item.data.get("templates", []))
                        else:  # 否则使用content
                            templates.append(item.content)
                    sarcasm_data[category_name] = templates

            return sarcasm_data

        # 使用后备数据
        return self.fallback_data.get("sarcasm_patterns", {})

    async def _inject_creative_insult(self, text: str, intensity: float) -> Dict[str, Any]:
        """注入创意骂人风格"""
        styled_text = text
        injection_points = []

        # 获取创意表达数据
        creative_data = self._get_creative_insults_data()
        if not creative_data:
            return {"success": False, "text": text, "injection_points": [], "details": {"error": "无创意表达数据"}}

        # 寻找负面评价的机会
        negative_words = ["笨", "蠢", "傻", "差", "烂", "糟", "坏"]

        for word in negative_words:
            if word in styled_text and random.random() < intensity:
                # 随机选择创意表达类别
                insult_category = random.choice(list(creative_data.keys()))
                creative_insult = random.choice(creative_data[insult_category])

                styled_text = styled_text.replace(word, creative_insult, 1)
                injection_points.append({
                    "type": "创意表达",
                    "original": word,
                    "creative": creative_insult,
                    "category": insult_category
                })

        return {
            "success": len(injection_points) > 0,
            "text": styled_text,
            "injection_points": injection_points,
            "details": {"creative_insults": len(injection_points)}
        }

    def _get_creative_insults_data(self) -> Dict[str, Any]:
        """获取创意表达数据"""
        # 优先使用语料库数据
        if self.corpus_data.get("creative_insults"):
            corpus_data = self.corpus_data["creative_insults"]
            creative_data = {}

            # 转换语料库格式
            for category_name, category in corpus_data.categories.items():
                creative_list = []
                for item in category.items:
                    creative_list.append(item.content)
                creative_data[category_name] = creative_list

            return creative_data

        # 使用后备数据
        return self.fallback_data.get("creative_insults", {})

    async def _inject_poetry_adaptation(self, text: str, intensity: float) -> Dict[str, Any]:
        """注入诗词改编风格"""
        styled_text = text
        injection_points = []

        # 寻找适合诗词化的句子
        sentences = re.split(r'[。！？]', text)

        for i, sentence in enumerate(sentences):
            if len(sentence.strip()) > 10 and random.random() < intensity * 0.2:
                # 随机选择诗词模板
                template_type = random.choice(list(self.poetry_templates.keys()))
                template = random.choice(self.poetry_templates[template_type])

                # 简单的诗词化转换（这里是示例，实际需要更复杂的NLP处理）
                poetic_sentence = f"如诗云：{sentence.strip()}，意境深远"

                sentences[i] = poetic_sentence
                injection_points.append({
                    "type": "诗词改编",
                    "original": sentence,
                    "poetic": poetic_sentence,
                    "template": template_type
                })

        styled_text = '。'.join(sentences)

        return {
            "success": len(injection_points) > 0,
            "text": styled_text,
            "injection_points": injection_points,
            "details": {"poetry_adaptations": len(injection_points)}
        }

    async def _inject_regional_flavor(self, text: str, intensity: float) -> Dict[str, Any]:
        """注入地域特色风格"""
        styled_text = text
        injection_points = []

        # 地域特色词汇
        regional_flavors = {
            "江南": ["烟雨", "小桥流水", "粉墙黛瓦", "吴侬软语"],
            "北方": ["大漠", "胡杨", "驼铃", "塞外"],
            "川蜀": ["巴山蜀水", "天府之国", "蜀道", "锦官城"],
            "岭南": ["荔枝", "木棉", "珠江", "南国"]
        }

        # 随机选择地域
        region = random.choice(list(regional_flavors.keys()))
        flavor_words = regional_flavors[region]

        # 在适当位置插入地域特色词汇
        sentences = re.split(r'[。！？]', text)
        for i, sentence in enumerate(sentences):
            if sentence.strip() and random.random() < intensity * 0.4:
                flavor_word = random.choice(flavor_words)
                enhanced_sentence = f"{sentence.strip()}，如{flavor_word}般"
                sentences[i] = enhanced_sentence
                injection_points.append({
                    "type": "地域特色",
                    "original": sentence,
                    "enhanced": enhanced_sentence,
                    "region": region,
                    "flavor": flavor_word
                })

        styled_text = '。'.join(sentences)

        return {
            "success": len(injection_points) > 0,
            "text": styled_text,
            "injection_points": injection_points,
            "details": {"regional_flavor": region, "injections": len(injection_points)}
        }


# ==================== 工厂函数 ====================

def create_style_injector(corpus_root: Optional[str] = None) -> LanguageStyleInjector:
    """
    🏭 [工厂] 创建语言风格注入器实例

    Args:
        corpus_root: 语料库根目录路径

    Returns:
        LanguageStyleInjector: 风格注入器实例
    """
    return LanguageStyleInjector(corpus_root)


async def quick_style_injection(
    text: str,
    style_types: List[StyleType],
    intensity: float = 0.3
) -> StyleInjectionResult:
    """
    ⚡ [快速接口] 快速风格注入

    Args:
        text: 原始文本
        style_types: 风格类型列表
        intensity: 注入强度

    Returns:
        StyleInjectionResult: 注入结果
    """
    injector = create_style_injector()
    return await injector.inject_style(text, style_types, intensity)
