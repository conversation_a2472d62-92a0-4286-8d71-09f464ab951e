{"name": "网文节奏模板库", "version": "1.0.0", "description": "网文创作中的各种节奏控制模板，包含情感曲线、结构安排、节奏变化等", "category": "pleasure_patterns", "tags": ["节奏", "模板", "结构", "情感曲线", "张弛"], "metadata": {"source": "网文创作理论和经典作品分析", "quality_score": 0.9, "usage_frequency": "very_high", "last_updated": "2025-08-05", "contributor": "系统内置", "review_status": "approved", "language": "zh-CN", "encoding": "utf-8"}, "usage_contexts": {"章节节奏": {"description": "单章节内的节奏控制", "weight": 1.0, "conditions": ["章节创作", "情节安排", "节奏把控"]}, "情感调节": {"description": "情感起伏的节奏安排", "weight": 0.95, "conditions": ["情感变化", "氛围营造", "读者体验"]}, "冲突设计": {"description": "冲突和解决的节奏模式", "weight": 0.9, "conditions": ["冲突设置", "矛盾解决", "剧情推进"]}}, "data": {"categories": {"经典三段式": {"description": "最经典的三段式节奏结构：铺垫-冲突-爽点", "weight": 0.95, "items": [{"content": "标准三段式", "weight": 0.9, "metadata": {"intensity": 0.85, "reliability": 0.95, "quality": 0.9, "source": "网文创作经典理论"}, "tags": ["三段式", "经典", "稳定"], "examples": ["铺垫30%-冲突40%-爽点30%", "起承转合", "欲扬先抑"], "data": {"structure_breakdown": {"铺垫阶段": {"percentage": "30%", "description": "设置背景，营造氛围，埋下伏笔", "emotion_level": [3, 4, 3], "key_elements": ["背景介绍", "人物出场", "矛盾萌芽", "氛围营造", "读者期待"]}, "冲突阶段": {"percentage": "40%", "description": "矛盾激化，冲突升级，情绪压抑", "emotion_level": [2, 1, 2], "key_elements": ["矛盾爆发", "冲突升级", "情绪低谷", "压抑感", "转折铺垫"]}, "爽点阶段": {"percentage": "30%", "description": "问题解决，情绪爆发，读者满足", "emotion_level": [8, 9, 10], "key_elements": ["问题解决", "反转惊喜", "情绪高潮", "读者满足", "余韵回味"]}}, "emotion_curve": [3, 4, 3, 2, 1, 2, 8, 9, 10], "transition_techniques": ["欲扬先抑：先压抑后爆发", "对比反差：前后形成强烈对比", "情绪积累：逐步积累情绪能量", "节奏控制：张弛有度的节奏变化", "高潮设计：精心设计的情绪高潮"], "application_scenarios": ["单章节完整情节", "短篇爽文创作", "情节片段设计", "冲突解决模式", "读者情绪引导"]}}]}, "渐进式爽感": {"description": "逐步递进的爽感积累模式", "weight": 0.9, "items": [{"content": "层层递进", "weight": 0.88, "metadata": {"intensity": 0.82, "reliability": 0.88, "quality": 0.85, "source": "升级流网文分析"}, "tags": ["递进", "积累", "升级"], "examples": ["实力逐步提升", "地位层层攀升", "影响力扩大"], "data": {"structure_breakdown": {"起始阶段": {"percentage": "20%", "description": "建立基础，设定目标", "emotion_level": [4, 5], "key_elements": ["现状展示", "目标设定", "动机建立", "基础铺垫"]}, "积累阶段": {"percentage": "50%", "description": "逐步进步，小幅提升", "emotion_level": [5, 6, 6, 7, 7], "key_elements": ["渐进提升", "小成就感", "持续进步", "期待积累", "节奏控制"]}, "爆发阶段": {"percentage": "30%", "description": "质变突破，大幅跃升", "emotion_level": [8, 9, 10], "key_elements": ["质的飞跃", "突破瓶颈", "成就感爆发", "读者满足"]}}, "emotion_curve": [4, 5, 5, 6, 6, 7, 7, 8, 9, 10], "progression_patterns": ["小步快跑：频繁的小进步", "积少成多：量变引起质变", "节节攀升：稳步向上的趋势", "厚积薄发：积累后的爆发", "螺旋上升：波浪式的进步"], "satisfaction_points": ["每个小进步都有成就感", "持续的正反馈", "期待值的不断满足", "最终大爆发的极致快感", "成长轨迹的清晰可见"]}}]}, "波浪式节奏": {"description": "起伏波动的节奏模式，营造跌宕起伏的阅读体验", "weight": 0.88, "items": [{"content": "双峰结构", "weight": 0.85, "metadata": {"intensity": 0.8, "reliability": 0.85, "quality": 0.82, "source": "复杂情节网文"}, "tags": ["波浪", "起伏", "双峰"], "examples": ["小高潮-低谷-大高潮", "波峰波谷交替", "情绪过山车"], "data": {"structure_breakdown": {"第一波峰": {"percentage": "25%", "description": "初步高潮，小幅满足", "emotion_level": [6, 7], "key_elements": ["初步成功", "小幅满足", "期待更多", "为后续铺垫"]}, "低谷阶段": {"percentage": "25%", "description": "情绪回落，新的挑战", "emotion_level": [3, 2], "key_elements": ["新的困难", "情绪低落", "挫折感", "蓄势待发"]}, "第二波峰": {"percentage": "50%", "description": "最终高潮，极致爆发", "emotion_level": [8, 9, 10], "key_elements": ["终极解决", "情绪爆发", "极致满足", "完美结局"]}}, "emotion_curve": [6, 7, 3, 2, 8, 9, 10], "wave_characteristics": ["对比强烈：高低落差明显", "节奏多变：快慢交替", "情绪丰富：多层次体验", "记忆深刻：起伏印象深刻", "参与感强：跟随情绪波动"], "reader_experience": ["第一次小满足建立期待", "低谷期的焦虑和担心", "最终爆发的极致快感", "情绪过山车般的体验", "深刻的阅读记忆"]}}]}, "悬念递进": {"description": "通过悬念层层递进营造紧张感和期待感", "weight": 0.85, "items": [{"content": "谜题解开", "weight": 0.82, "metadata": {"intensity": 0.78, "reliability": 0.82, "quality": 0.8, "source": "悬疑推理网文"}, "tags": ["悬念", "谜题", "解密"], "examples": ["层层解谜", "真相揭露", "悬念解开"], "data": {"structure_breakdown": {"谜题设置": {"percentage": "30%", "description": "建立悬念，引发好奇", "emotion_level": [5, 6], "key_elements": ["谜题出现", "好奇心激发", "线索埋设", "期待建立"]}, "线索收集": {"percentage": "40%", "description": "逐步揭示，保持紧张", "emotion_level": [6, 7, 6, 7], "key_elements": ["线索发现", "推理过程", "紧张维持", "期待递增"]}, "真相大白": {"percentage": "30%", "description": "谜底揭晓，恍然大悟", "emotion_level": [8, 9, 8], "key_elements": ["真相揭露", "恍然大悟", "满足感", "智力快感"]}}, "emotion_curve": [5, 6, 6, 7, 6, 7, 8, 9, 8], "suspense_techniques": ["信息控制：适度透露信息", "节奏把控：张弛有度", "线索设计：巧妙的线索安排", "反转设置：意外的真相", "满足感营造：解谜的快感"]}}]}}}, "statistics": {"total_entries": 4, "categories_count": 4, "patterns_count": 0, "average_quality": 0.84, "last_calculated": "2025-08-05"}}