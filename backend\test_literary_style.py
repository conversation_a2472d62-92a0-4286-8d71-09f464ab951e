"""
🧪 [测试] 文学风格增强系统测试脚本
验证文学风格增强引擎的各个组件是否正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.literary_style_engine import (
    create_literary_style_engine, StyleConfigBuilder, quick_style_enhancement
)
from app.core.style_injector import StyleType
from app.core.personality_driver import ZodiacSign, PersonalityTrait
from app.core.pleasure_engine import PleasureType, EmotionalIntensity
from app.core.literary_style_engine import StyleEnhancementMode


async def test_basic_style_enhancement():
    """测试基础风格增强功能"""
    print("🎨 [测试] 开始测试基础风格增强功能...")
    
    # 测试文本
    test_text = """
    李明走进了房间，看到桌子上放着一本书。他拿起书翻了翻，发现这是一本武功秘籍。
    他心想这下发财了，赶紧把书收了起来。突然门外传来脚步声，他紧张地藏好了书。
    """
    
    try:
        # 使用预设配置进行增强
        result = await quick_style_enhancement(
            text=test_text,
            preset_name="网文爽文"
        )
        
        print(f"✅ 增强成功: {result.success}")
        print(f"📊 综合评分: {result.overall_enhancement_score:.2f}")
        print(f"🎯 一致性评分: {result.style_consistency_score:.2f}")
        print(f"📖 可读性评分: {result.readability_score:.2f}")
        print(f"🔧 处理流水线: {', '.join(result.processing_pipeline)}")
        
        print("\n📝 原始文本:")
        print(result.original_text.strip())
        
        print("\n✨ 增强后文本:")
        print(result.enhanced_text.strip())
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_custom_config():
    """测试自定义配置功能"""
    print("\n🔧 [测试] 开始测试自定义配置功能...")
    
    test_text = """
    张三是个普通的上班族，每天朝九晚五地工作。今天他收到了一个神秘的包裹，
    里面是一枚古老的戒指。戴上戒指后，他发现自己拥有了超能力。
    """
    
    try:
        # 使用配置构建器创建自定义配置
        config = (StyleConfigBuilder()
                 .with_style_types(StyleType.CREATIVE_INSULT, StyleType.SARCASM)
                 .with_style_intensity(0.7)
                 .with_zodiac_sign(ZodiacSign.ARIES)
                 .with_personality_traits(PersonalityTrait.AGGRESSIVE, PersonalityTrait.EXTROVERT)
                 .with_pleasure_types(PleasureType.POWER_FANTASY, PleasureType.UPGRADE)
                 .with_enhancement_mode(StyleEnhancementMode.HEAVY)
                 .with_target_audience("网文读者")
                 .build())
        
        # 创建引擎并进行增强
        engine = create_literary_style_engine()
        result = await engine.enhance_literary_style(test_text, config)
        
        print(f"✅ 自定义配置增强成功: {result.success}")
        print(f"📊 综合评分: {result.overall_enhancement_score:.2f}")
        print(f"🔧 处理流水线: {', '.join(result.processing_pipeline)}")
        
        print("\n📝 原始文本:")
        print(result.original_text.strip())
        
        print("\n✨ 增强后文本:")
        print(result.enhanced_text.strip())
        
        return True
        
    except Exception as e:
        print(f"❌ 自定义配置测试失败: {e}")
        return False


async def test_all_presets():
    """测试所有预设配置"""
    print("\n📋 [测试] 开始测试所有预设配置...")
    
    test_text = "王小明发现了一个秘密，这个秘密将改变他的一生。"
    
    try:
        engine = create_literary_style_engine()
        presets = engine.get_available_presets()
        
        print(f"📊 发现 {len(presets)} 个预设配置: {', '.join(presets)}")
        
        success_count = 0
        for preset_name in presets:
            try:
                result = await engine.enhance_with_preset(test_text, preset_name)
                # 修复判断逻辑：只要系统返回success=True就算成功，不管处理步骤数
                if result.success:
                    success_count += 1
                    print(f"✅ {preset_name}: 成功 (评分: {result.overall_enhancement_score:.2f}, 步骤: {len(result.processing_pipeline)})")
                else:
                    print(f"⚠️ {preset_name}: 系统返回失败")
            except Exception as e:
                print(f"❌ {preset_name}: 异常 - {e}")
        
        print(f"\n📊 预设配置测试结果: {success_count}/{len(presets)} 成功")
        return success_count == len(presets)
        
    except Exception as e:
        print(f"❌ 预设配置测试失败: {e}")
        return False


async def test_preset_details():
    """测试预设配置详情获取"""
    print("\n🔍 [测试] 开始测试预设配置详情获取...")
    
    try:
        engine = create_literary_style_engine()
        presets = engine.get_available_presets()
        
        for preset_name in presets:
            details = engine.get_preset_description(preset_name)
            print(f"\n📋 {preset_name}:")
            print(f"   目标读者: {details.get('target_audience', '未知')}")
            print(f"   增强模式: {details.get('enhancement_mode', '未知')}")
            print(f"   描述: {details.get('description', '无描述')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 预设配置详情测试失败: {e}")
        return False


async def test_performance():
    """测试性能指标"""
    print("\n⚡ [测试] 开始测试性能指标...")
    
    # 较长的测试文本
    test_text = """
    在这个充满魔法的世界里，年轻的法师艾伦正在寻找传说中的魔法石。
    他穿过了黑暗的森林，越过了危险的山脉，终于来到了古老的遗迹前。
    遗迹的大门紧闭，上面刻着古老的符文。艾伦仔细研究着这些符文，
    试图找到开启大门的方法。突然，他想起了师父曾经教过他的咒语。
    他开始念诵咒语，符文开始发光，大门缓缓打开。里面是一个巨大的宝库，
    各种珍贵的魔法物品闪闪发光。在宝库的中央，放着一颗璀璨的魔法石。
    """ * 3  # 重复3次增加文本长度
    
    try:
        import time
        start_time = time.time()
        
        result = await quick_style_enhancement(
            text=test_text,
            preset_name="古风仙侠"
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✅ 性能测试完成")
        print(f"📊 处理时间: {processing_time:.2f} 秒")
        print(f"📏 原文长度: {len(test_text)} 字符")
        print(f"📏 增强后长度: {len(result.enhanced_text)} 字符")
        print(f"📈 长度增长比: {len(result.enhanced_text) / len(test_text):.2f}")
        print(f"⚡ 处理速度: {len(test_text) / processing_time:.0f} 字符/秒")
        
        # 检查性能指标
        if 'processing_time' in result.performance_metrics:
            engine_time = result.performance_metrics['processing_time']
            print(f"🔧 引擎内部处理时间: {engine_time:.2f} 秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 [开始] 文学风格增强系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(await test_basic_style_enhancement())
    test_results.append(await test_custom_config())
    test_results.append(await test_all_presets())
    test_results.append(await test_preset_details())
    test_results.append(await test_performance())
    
    # 统计结果
    success_count = sum(test_results)
    total_count = len(test_results)
    
    print("\n" + "=" * 60)
    print(f"🏁 [完成] 测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！文学风格增强系统工作正常。")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查系统配置。")
        return 1


if __name__ == "__main__":
    """运行测试"""
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
