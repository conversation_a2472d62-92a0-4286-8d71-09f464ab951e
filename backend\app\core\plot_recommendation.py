"""
🎭 [剧情推荐] 智能剧情推荐引擎
基于知识图谱分析当前故事状态，为作者提供智能的剧情发展建议和情节路径选项
"""

from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import asyncio
import json
from enum import Enum

from app.core.config import log_info, log_debug, log_error
from app.schemas.world_graph import EntityResponse, RelationshipResponse, WorldGraphResponse
from app.models.world_graph import EntityType, RelationshipStatus, RelationshipTypes


class PlotPatternType(Enum):
    """剧情模式类型枚举"""
    CONFLICT_ESCALATION = "conflict_escalation"      # 冲突升级
    RELATIONSHIP_CHANGE = "relationship_change"      # 关系转折
    CHARACTER_GROWTH = "character_growth"            # 角色成长
    MYSTERY_REVEAL = "mystery_reveal"                # 悬念揭示
    ALLIANCE_FORMATION = "alliance_formation"        # 联盟形成
    BETRAYAL = "betrayal"                           # 背叛
    REDEMPTION = "redemption"                       # 救赎
    POWER_STRUGGLE = "power_struggle"               # 权力斗争
    ROMANTIC_DEVELOPMENT = "romantic_development"    # 感情发展
    SACRIFICE = "sacrifice"                         # 牺牲


class RecommendationPriority(Enum):
    """推荐优先级枚举"""
    HIGH = "high"       # 高优先级 - 强烈推荐
    MEDIUM = "medium"   # 中等优先级 - 建议考虑
    LOW = "low"         # 低优先级 - 可选方案


@dataclass
class PlotPattern:
    """剧情模式数据类"""
    pattern_type: PlotPatternType
    name: str
    description: str
    prerequisites: List[str]  # 前置条件
    outcomes: List[str]       # 可能结果
    dramatic_tension: float   # 戏剧张力评分 (0-1)
    complexity: float         # 复杂度评分 (0-1)
    reader_appeal: float      # 读者吸引力评分 (0-1)


@dataclass
class PlotRecommendation:
    """剧情推荐数据类"""
    id: str
    title: str
    description: str
    pattern_type: PlotPatternType
    priority: RecommendationPriority
    
    # 评分系统
    feasibility_score: float      # 可行性评分 (0-1)
    dramatic_impact: float        # 戏剧效果评分 (0-1)
    character_development: float  # 角色发展评分 (0-1)
    plot_advancement: float       # 情节推进评分 (0-1)
    overall_score: float          # 综合评分 (0-1)
    
    # 详细信息
    involved_entities: List[str]  # 涉及的实体ID
    required_relationships: List[str]  # 需要的关系
    potential_conflicts: List[str]     # 潜在冲突
    execution_steps: List[str]         # 执行步骤
    expected_outcomes: List[str]       # 预期结果
    
    # 元数据
    created_at: datetime
    reasoning: str                # 推荐理由


@dataclass
class StoryAnalysis:
    """故事状态分析数据类"""
    story_id: str
    current_chapter: int
    
    # 实体分析
    main_characters: List[EntityResponse]
    supporting_characters: List[EntityResponse]
    key_items: List[EntityResponse]
    important_scenes: List[EntityResponse]
    
    # 关系分析
    active_relationships: List[RelationshipResponse]
    conflict_relationships: List[RelationshipResponse]
    alliance_relationships: List[RelationshipResponse]
    romantic_relationships: List[RelationshipResponse]
    
    # 状态评估
    tension_level: float          # 当前张力水平 (0-1)
    character_development_stage: Dict[str, float]  # 角色发展阶段
    plot_complexity: float        # 情节复杂度 (0-1)
    pacing_score: float          # 节奏评分 (0-1)
    
    # 潜在机会
    underutilized_characters: List[str]  # 未充分利用的角色
    dormant_relationships: List[str]     # 休眠关系
    unresolved_conflicts: List[str]      # 未解决冲突
    
    created_at: datetime


class PlotRecommendationEngine:
    """
    🎭 [剧情推荐] 智能剧情推荐引擎核心类
    
    基于知识图谱分析当前故事状态，生成智能的剧情发展建议
    """
    
    def __init__(self):
        """初始化剧情推荐引擎"""
        self.plot_patterns = self._initialize_plot_patterns()
        log_info("剧情推荐", "剧情推荐引擎初始化完成", 
                模式数量=len(self.plot_patterns))
    
    def _initialize_plot_patterns(self) -> List[PlotPattern]:
        """
        🎨 [剧情推荐] 初始化剧情模式库
        
        构建经典剧情模式的数据库，包含各种常见的故事发展模式
        """
        patterns = [
            PlotPattern(
                pattern_type=PlotPatternType.CONFLICT_ESCALATION,
                name="冲突升级",
                description="现有冲突进一步激化，矛盾达到新的高度",
                prerequisites=["存在活跃的敌对关系", "角色间有未解决的分歧"],
                outcomes=["关系彻底破裂", "公开对抗", "第三方介入"],
                dramatic_tension=0.9,
                complexity=0.6,
                reader_appeal=0.8
            ),
            PlotPattern(
                pattern_type=PlotPatternType.RELATIONSHIP_CHANGE,
                name="关系转折",
                description="角色间关系发生重大转变，如敌转友或友转敌",
                prerequisites=["角色间有历史关系", "存在转变的契机"],
                outcomes=["关系性质改变", "新的联盟形成", "旧有平衡被打破"],
                dramatic_tension=0.7,
                complexity=0.8,
                reader_appeal=0.9
            ),
            PlotPattern(
                pattern_type=PlotPatternType.CHARACTER_GROWTH,
                name="角色成长",
                description="主要角色面临挑战，实现个人成长和突破",
                prerequisites=["角色有成长空间", "存在成长的催化事件"],
                outcomes=["角色能力提升", "性格更加成熟", "获得新的认知"],
                dramatic_tension=0.6,
                complexity=0.7,
                reader_appeal=0.8
            ),
            PlotPattern(
                pattern_type=PlotPatternType.MYSTERY_REVEAL,
                name="悬念揭示",
                description="揭示之前埋下的伏笔或谜团，为读者提供答案",
                prerequisites=["存在未解之谜", "有足够的铺垫"],
                outcomes=["真相大白", "新的疑问产生", "角色认知改变"],
                dramatic_tension=0.8,
                complexity=0.5,
                reader_appeal=0.9
            ),
            PlotPattern(
                pattern_type=PlotPatternType.ALLIANCE_FORMATION,
                name="联盟形成",
                description="原本独立的角色或势力结成联盟，共同面对挑战",
                prerequisites=["存在共同威胁", "角色有合作基础"],
                outcomes=["力量平衡改变", "新的对抗格局", "团队协作"],
                dramatic_tension=0.6,
                complexity=0.7,
                reader_appeal=0.7
            ),
            PlotPattern(
                pattern_type=PlotPatternType.BETRAYAL,
                name="背叛",
                description="信任关系被打破，盟友变成敌人",
                prerequisites=["存在信任关系", "有背叛的动机"],
                outcomes=["信任崩塌", "新的敌对关系", "情感创伤"],
                dramatic_tension=0.9,
                complexity=0.8,
                reader_appeal=0.9
            ),
            PlotPattern(
                pattern_type=PlotPatternType.REDEMPTION,
                name="救赎",
                description="角色为过去的错误寻求救赎和原谅",
                prerequisites=["角色有过错", "存在救赎机会"],
                outcomes=["获得原谅", "自我救赎", "关系修复"],
                dramatic_tension=0.7,
                complexity=0.6,
                reader_appeal=0.8
            ),
            PlotPattern(
                pattern_type=PlotPatternType.POWER_STRUGGLE,
                name="权力斗争",
                description="角色间为了权力、地位或资源展开竞争",
                prerequisites=["存在权力真空", "多方有竞争意愿"],
                outcomes=["权力重新分配", "新的统治者", "势力重组"],
                dramatic_tension=0.8,
                complexity=0.9,
                reader_appeal=0.8
            ),
            PlotPattern(
                pattern_type=PlotPatternType.ROMANTIC_DEVELOPMENT,
                name="感情发展",
                description="角色间的浪漫关系进一步发展或面临考验",
                prerequisites=["存在感情基础", "有发展机会"],
                outcomes=["关系确立", "感情深化", "面临考验"],
                dramatic_tension=0.5,
                complexity=0.6,
                reader_appeal=0.9
            ),
            PlotPattern(
                pattern_type=PlotPatternType.SACRIFICE,
                name="牺牲",
                description="角色为了更大的目标或他人利益做出牺牲",
                prerequisites=["存在需要保护的对象", "角色有牺牲精神"],
                outcomes=["目标达成", "他人获救", "英雄形象确立"],
                dramatic_tension=0.9,
                complexity=0.7,
                reader_appeal=0.9
            )
        ]
        
        log_debug("剧情推荐", "剧情模式库初始化完成", 
                 模式数量=len(patterns),
                 模式类型=[p.pattern_type.value for p in patterns])
        
        return patterns
    
    async def analyze_story_state(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int
    ) -> StoryAnalysis:
        """
        📊 [剧情推荐] 分析当前故事状态
        
        深度分析故事的当前状态，包括角色关系、情节发展、张力水平等
        
        Args:
            world_graph: 世界知识图谱数据
            current_chapter: 当前章节号
            
        Returns:
            StoryAnalysis: 详细的故事状态分析
        """
        log_info("剧情推荐", "开始分析故事状态",
                故事ID=world_graph.story_id,
                当前章节=current_chapter,
                实体数量=len(world_graph.entities),
                关系数量=len(world_graph.relationships))
        
        try:
            # 分类实体
            main_characters = []
            supporting_characters = []
            key_items = []
            important_scenes = []
            
            for entity in world_graph.entities:
                if entity.type == EntityType.CHARACTER:
                    if entity.importance_score >= 0.7:
                        main_characters.append(entity)
                    else:
                        supporting_characters.append(entity)
                elif entity.type == EntityType.ITEM and entity.importance_score >= 0.5:
                    key_items.append(entity)
                elif entity.type == EntityType.SCENE and entity.importance_score >= 0.5:
                    important_scenes.append(entity)
            
            # 分类关系
            active_relationships = []
            conflict_relationships = []
            alliance_relationships = []
            romantic_relationships = []
            
            for rel in world_graph.relationships:
                if rel.status == RelationshipStatus.ACTIVE:
                    active_relationships.append(rel)
                    
                    # 根据关系类型分类
                    if rel.relationship_type in [RelationshipTypes.ENEMY, RelationshipTypes.RIVAL, RelationshipTypes.OPPOSES]:
                        conflict_relationships.append(rel)
                    elif rel.relationship_type in [RelationshipTypes.FRIEND, RelationshipTypes.ALLY, RelationshipTypes.SUPPORTS]:
                        alliance_relationships.append(rel)
                    elif rel.relationship_type in [RelationshipTypes.LOVER]:
                        romantic_relationships.append(rel)
            
            # 计算张力水平
            tension_level = self._calculate_tension_level(conflict_relationships, alliance_relationships)
            
            # 分析角色发展阶段
            character_development_stage = self._analyze_character_development(main_characters, current_chapter)
            
            # 计算情节复杂度
            plot_complexity = self._calculate_plot_complexity(world_graph)
            
            # 计算节奏评分
            pacing_score = self._calculate_pacing_score(current_chapter, len(world_graph.relationships))
            
            # 识别机会
            underutilized_characters = self._identify_underutilized_characters(supporting_characters)
            dormant_relationships = self._identify_dormant_relationships(world_graph.relationships)
            unresolved_conflicts = self._identify_unresolved_conflicts(conflict_relationships)
            
            analysis = StoryAnalysis(
                story_id=world_graph.story_id,
                current_chapter=current_chapter,
                main_characters=main_characters,
                supporting_characters=supporting_characters,
                key_items=key_items,
                important_scenes=important_scenes,
                active_relationships=active_relationships,
                conflict_relationships=conflict_relationships,
                alliance_relationships=alliance_relationships,
                romantic_relationships=romantic_relationships,
                tension_level=tension_level,
                character_development_stage=character_development_stage,
                plot_complexity=plot_complexity,
                pacing_score=pacing_score,
                underutilized_characters=underutilized_characters,
                dormant_relationships=dormant_relationships,
                unresolved_conflicts=unresolved_conflicts,
                created_at=datetime.now()
            )
            
            log_info("剧情推荐", "故事状态分析完成",
                    主要角色数=len(main_characters),
                    冲突关系数=len(conflict_relationships),
                    张力水平=f"{tension_level:.3f}",
                    情节复杂度=f"{plot_complexity:.3f}")
            
            return analysis
            
        except Exception as e:
            log_error("剧情推荐", "故事状态分析失败", error=e)
            raise

    def _calculate_tension_level(
        self,
        conflict_relationships: List[RelationshipResponse],
        alliance_relationships: List[RelationshipResponse]
    ) -> float:
        """
        ⚡ [剧情推荐] 计算当前故事的张力水平

        基于冲突关系和联盟关系的数量和强度计算整体张力
        """
        if not conflict_relationships and not alliance_relationships:
            return 0.0

        # 冲突张力：冲突关系越多、强度越高，张力越大
        conflict_tension = 0.0
        if conflict_relationships:
            total_conflict_strength = sum(rel.strength for rel in conflict_relationships)
            conflict_tension = min(1.0, total_conflict_strength / len(conflict_relationships))

        # 联盟稳定性：联盟关系越强，张力越低
        alliance_stability = 0.0
        if alliance_relationships:
            total_alliance_strength = sum(rel.strength for rel in alliance_relationships)
            alliance_stability = min(1.0, total_alliance_strength / len(alliance_relationships))

        # 综合张力 = 冲突张力 - 联盟稳定性的缓解作用
        tension = conflict_tension - (alliance_stability * 0.3)
        return max(0.0, min(1.0, tension))

    def _analyze_character_development(
        self,
        main_characters: List[EntityResponse],
        current_chapter: int
    ) -> Dict[str, float]:
        """
        👥 [剧情推荐] 分析角色发展阶段

        评估每个主要角色的发展程度和成长空间
        """
        development_stages = {}

        for character in main_characters:
            # 基于首次提及章节和当前章节计算发展阶段
            if character.first_mentioned_chapter:
                chapters_since_introduction = current_chapter - character.first_mentioned_chapter
                # 假设角色在10章内完成主要发展弧线
                development_progress = min(1.0, chapters_since_introduction / 10.0)
            else:
                development_progress = 0.5  # 默认中等发展阶段

            # 考虑角色重要性对发展的影响
            adjusted_progress = development_progress * character.importance_score
            development_stages[character.id] = adjusted_progress

        return development_stages

    def _calculate_plot_complexity(self, world_graph: WorldGraphResponse) -> float:
        """
        🧩 [剧情推荐] 计算情节复杂度

        基于实体数量、关系复杂性等因素评估情节复杂度
        """
        entity_count = len(world_graph.entities)
        relationship_count = len(world_graph.relationships)

        # 基础复杂度：实体和关系的数量
        base_complexity = min(1.0, (entity_count + relationship_count) / 50.0)

        # 关系复杂度：不同类型关系的多样性
        relationship_types = set(rel.relationship_type for rel in world_graph.relationships)
        type_diversity = min(1.0, len(relationship_types) / 10.0)

        # 综合复杂度
        complexity = (base_complexity * 0.7) + (type_diversity * 0.3)
        return complexity

    def _calculate_pacing_score(self, current_chapter: int, relationship_count: int) -> float:
        """
        🏃 [剧情推荐] 计算节奏评分

        评估故事发展的节奏是否合适
        """
        if current_chapter == 0:
            return 0.5

        # 关系密度：每章平均关系数
        relationship_density = relationship_count / current_chapter

        # 理想密度范围：每章2-5个关系
        if 2.0 <= relationship_density <= 5.0:
            pacing_score = 1.0
        elif relationship_density < 2.0:
            pacing_score = relationship_density / 2.0  # 节奏偏慢
        else:
            pacing_score = max(0.3, 5.0 / relationship_density)  # 节奏偏快

        return pacing_score

    def _identify_underutilized_characters(
        self,
        supporting_characters: List[EntityResponse]
    ) -> List[str]:
        """
        🎭 [剧情推荐] 识别未充分利用的角色

        找出有潜力但使用不足的配角
        """
        underutilized = []

        for character in supporting_characters:
            # 重要性较高但最近未更新的角色
            if (character.importance_score >= 0.4 and
                character.last_mentioned_chapter and
                character.last_mentioned_chapter < character.first_mentioned_chapter + 3):
                underutilized.append(character.id)

        return underutilized

    def _identify_dormant_relationships(
        self,
        relationships: List[RelationshipResponse]
    ) -> List[str]:
        """
        😴 [剧情推荐] 识别休眠关系

        找出长期未更新但仍有潜力的关系
        """
        dormant = []

        for rel in relationships:
            # 状态为非活跃但强度较高的关系
            if (rel.status != RelationshipStatus.ACTIVE and
                rel.strength >= 0.5):
                dormant.append(rel.id)

        return dormant

    def _identify_unresolved_conflicts(
        self,
        conflict_relationships: List[RelationshipResponse]
    ) -> List[str]:
        """
        ⚔️ [剧情推荐] 识别未解决冲突

        找出需要进一步发展或解决的冲突关系
        """
        unresolved = []

        for conflict in conflict_relationships:
            # 强度较高但长期未更新的冲突
            if (conflict.strength >= 0.6 and
                conflict.last_updated_chapter and
                conflict.established_chapter and
                conflict.last_updated_chapter - conflict.established_chapter <= 2):
                unresolved.append(conflict.id)

        return unresolved

    async def generate_recommendations(
        self,
        story_analysis: StoryAnalysis,
        max_recommendations: int = 5
    ) -> List[PlotRecommendation]:
        """
        🎯 [剧情推荐] 生成剧情推荐

        基于故事状态分析，生成多个高质量的剧情发展建议

        Args:
            story_analysis: 故事状态分析结果
            max_recommendations: 最大推荐数量

        Returns:
            List[PlotRecommendation]: 排序后的推荐列表
        """
        log_info("剧情推荐", "开始生成剧情推荐",
                故事ID=story_analysis.story_id,
                当前章节=story_analysis.current_chapter,
                最大推荐数=max_recommendations)

        try:
            recommendations = []

            # 为每种剧情模式生成推荐
            for pattern in self.plot_patterns:
                recommendation = await self._generate_pattern_recommendation(
                    pattern, story_analysis
                )
                if recommendation:
                    recommendations.append(recommendation)

            # 按综合评分排序
            recommendations.sort(key=lambda x: x.overall_score, reverse=True)

            # 返回前N个推荐
            top_recommendations = recommendations[:max_recommendations]

            log_info("剧情推荐", "剧情推荐生成完成",
                    生成数量=len(recommendations),
                    返回数量=len(top_recommendations),
                    最高评分=f"{top_recommendations[0].overall_score:.3f}" if top_recommendations else "无")

            return top_recommendations

        except Exception as e:
            log_error("剧情推荐", "生成剧情推荐失败", error=e)
            raise

    async def _generate_pattern_recommendation(
        self,
        pattern: PlotPattern,
        analysis: StoryAnalysis
    ) -> Optional[PlotRecommendation]:
        """
        🎨 [剧情推荐] 为特定模式生成推荐

        基于剧情模式和当前故事状态生成具体的推荐方案
        """
        try:
            # 检查模式是否适用于当前故事状态
            if not self._is_pattern_applicable(pattern, analysis):
                return None

            # 生成推荐ID
            recommendation_id = f"{analysis.story_id}-{pattern.pattern_type.value}-{analysis.current_chapter}"

            # 根据模式类型生成具体推荐
            if pattern.pattern_type == PlotPatternType.CONFLICT_ESCALATION:
                return await self._generate_conflict_escalation_recommendation(
                    recommendation_id, pattern, analysis
                )
            elif pattern.pattern_type == PlotPatternType.RELATIONSHIP_CHANGE:
                return await self._generate_relationship_change_recommendation(
                    recommendation_id, pattern, analysis
                )
            elif pattern.pattern_type == PlotPatternType.CHARACTER_GROWTH:
                return await self._generate_character_growth_recommendation(
                    recommendation_id, pattern, analysis
                )
            elif pattern.pattern_type == PlotPatternType.ALLIANCE_FORMATION:
                return await self._generate_alliance_formation_recommendation(
                    recommendation_id, pattern, analysis
                )
            elif pattern.pattern_type == PlotPatternType.BETRAYAL:
                return await self._generate_betrayal_recommendation(
                    recommendation_id, pattern, analysis
                )
            else:
                # 通用推荐生成
                return await self._generate_generic_recommendation(
                    recommendation_id, pattern, analysis
                )

        except Exception as e:
            log_error("剧情推荐", f"生成{pattern.pattern_type.value}推荐失败", error=e)
            return None

    def _is_pattern_applicable(self, pattern: PlotPattern, analysis: StoryAnalysis) -> bool:
        """
        ✅ [剧情推荐] 检查剧情模式是否适用

        验证当前故事状态是否满足模式的前置条件
        """
        if pattern.pattern_type == PlotPatternType.CONFLICT_ESCALATION:
            # 需要存在冲突关系
            return len(analysis.conflict_relationships) > 0

        elif pattern.pattern_type == PlotPatternType.RELATIONSHIP_CHANGE:
            # 需要存在可以转变的关系
            return len(analysis.active_relationships) > 0

        elif pattern.pattern_type == PlotPatternType.CHARACTER_GROWTH:
            # 需要有发展空间的角色
            return any(stage < 0.8 for stage in analysis.character_development_stage.values())

        elif pattern.pattern_type == PlotPatternType.ALLIANCE_FORMATION:
            # 需要有多个独立角色或存在共同威胁
            return len(analysis.main_characters) >= 2

        elif pattern.pattern_type == PlotPatternType.BETRAYAL:
            # 需要存在信任关系
            return len(analysis.alliance_relationships) > 0

        # 其他模式默认适用
        return True

    async def _generate_conflict_escalation_recommendation(
        self,
        recommendation_id: str,
        pattern: PlotPattern,
        analysis: StoryAnalysis
    ) -> PlotRecommendation:
        """
        ⚔️ [剧情推荐] 生成冲突升级推荐
        """
        # 选择最强的冲突关系
        strongest_conflict = max(analysis.conflict_relationships, key=lambda x: x.strength)

        # 获取冲突双方的实体信息
        source_entity = next((e for e in analysis.main_characters + analysis.supporting_characters
                             if e.id == strongest_conflict.source_entity_id), None)
        target_entity = next((e for e in analysis.main_characters + analysis.supporting_characters
                             if e.id == strongest_conflict.target_entity_id), None)

        title = f"{source_entity.name if source_entity else '角色A'}与{target_entity.name if target_entity else '角色B'}的冲突升级"
        description = f"将{strongest_conflict.relationship_type}关系进一步激化，通过具体事件或第三方介入使矛盾达到新的高度"

        # 计算评分
        feasibility_score = min(1.0, strongest_conflict.strength + 0.2)
        dramatic_impact = pattern.dramatic_tension
        character_development = 0.7  # 冲突有助于角色发展
        plot_advancement = 0.8  # 冲突推进情节

        overall_score = (feasibility_score * 0.3 + dramatic_impact * 0.3 +
                        character_development * 0.2 + plot_advancement * 0.2)

        # 确定优先级
        if overall_score >= 0.8:
            priority = RecommendationPriority.HIGH
        elif overall_score >= 0.6:
            priority = RecommendationPriority.MEDIUM
        else:
            priority = RecommendationPriority.LOW

        return PlotRecommendation(
            id=recommendation_id,
            title=title,
            description=description,
            pattern_type=pattern.pattern_type,
            priority=priority,
            feasibility_score=feasibility_score,
            dramatic_impact=dramatic_impact,
            character_development=character_development,
            plot_advancement=plot_advancement,
            overall_score=overall_score,
            involved_entities=[strongest_conflict.source_entity_id, strongest_conflict.target_entity_id],
            required_relationships=[strongest_conflict.id],
            potential_conflicts=["关系彻底破裂", "第三方被迫选边站队"],
            execution_steps=[
                "设计具体的冲突触发事件",
                "展现双方的激烈对抗",
                "考虑第三方角色的反应",
                "为后续发展埋下伏笔"
            ],
            expected_outcomes=["矛盾公开化", "故事张力显著提升", "为后续情节发展创造机会"],
            created_at=datetime.now(),
            reasoning=f"基于{strongest_conflict.relationship_type}关系的高强度({strongest_conflict.strength:.2f})，适合进行冲突升级"
        )

    async def _generate_relationship_change_recommendation(
        self,
        recommendation_id: str,
        pattern: PlotPattern,
        analysis: StoryAnalysis
    ) -> PlotRecommendation:
        """
        🔄 [剧情推荐] 生成关系转折推荐
        """
        # 选择最适合转变的关系（中等强度的关系更容易转变）
        suitable_relationships = [rel for rel in analysis.active_relationships
                                if 0.3 <= rel.strength <= 0.7]

        if not suitable_relationships:
            suitable_relationships = analysis.active_relationships

        target_relationship = max(suitable_relationships, key=lambda x: abs(x.strength - 0.5))

        # 获取关系双方的实体信息
        source_entity = next((e for e in analysis.main_characters + analysis.supporting_characters
                             if e.id == target_relationship.source_entity_id), None)
        target_entity = next((e for e in analysis.main_characters + analysis.supporting_characters
                             if e.id == target_relationship.target_entity_id), None)

        # 确定转变方向
        if target_relationship.relationship_type in [RelationshipTypes.ENEMY, RelationshipTypes.RIVAL]:
            new_relationship = "朋友或盟友"
            change_direction = "敌转友"
        else:
            new_relationship = "敌人或对手"
            change_direction = "友转敌"

        title = f"{source_entity.name if source_entity else '角色A'}与{target_entity.name if target_entity else '角色B'}的关系转折"
        description = f"通过关键事件使{target_relationship.relationship_type}关系转变为{new_relationship}关系，实现{change_direction}的戏剧效果"

        # 计算评分
        feasibility_score = 1.0 - abs(target_relationship.strength - 0.5)  # 中等强度关系更容易转变
        dramatic_impact = pattern.dramatic_tension
        character_development = 0.8  # 关系转变促进角色发展
        plot_advancement = 0.9  # 关系转变大幅推进情节

        overall_score = (feasibility_score * 0.25 + dramatic_impact * 0.25 +
                        character_development * 0.25 + plot_advancement * 0.25)

        # 确定优先级
        if overall_score >= 0.8:
            priority = RecommendationPriority.HIGH
        elif overall_score >= 0.6:
            priority = RecommendationPriority.MEDIUM
        else:
            priority = RecommendationPriority.LOW

        return PlotRecommendation(
            id=recommendation_id,
            title=title,
            description=description,
            pattern_type=pattern.pattern_type,
            priority=priority,
            feasibility_score=feasibility_score,
            dramatic_impact=dramatic_impact,
            character_development=character_development,
            plot_advancement=plot_advancement,
            overall_score=overall_score,
            involved_entities=[target_relationship.source_entity_id, target_relationship.target_entity_id],
            required_relationships=[target_relationship.id],
            potential_conflicts=["原有关系网络的重新调整", "其他角色的立场选择"],
            execution_steps=[
                "设计关系转变的触发事件",
                "展现角色内心的挣扎和变化",
                "处理其他角色的反应",
                "建立新的关系动态"
            ],
            expected_outcomes=["关系网络重构", "故事格局改变", "角色弧线发展"],
            created_at=datetime.now(),
            reasoning=f"关系强度{target_relationship.strength:.2f}适中，具备转变的可能性和戏剧效果"
        )

    async def _generate_character_growth_recommendation(
        self,
        recommendation_id: str,
        pattern: PlotPattern,
        analysis: StoryAnalysis
    ) -> PlotRecommendation:
        """
        🌱 [剧情推荐] 生成角色成长推荐
        """
        # 选择发展空间最大的角色
        character_id = min(analysis.character_development_stage.items(), key=lambda x: x[1])[0]
        character = next((c for c in analysis.main_characters if c.id == character_id), None)

        if not character:
            character = analysis.main_characters[0] if analysis.main_characters else None

        title = f"{character.name if character else '主角'}的成长突破"
        description = f"为{character.name if character else '角色'}设计成长挑战，通过克服困难实现个人突破和能力提升"

        # 计算评分
        current_development = analysis.character_development_stage.get(character_id, 0.5) if character else 0.5
        feasibility_score = 1.0 - current_development  # 发展空间越大，可行性越高
        dramatic_impact = pattern.dramatic_tension
        character_development = 0.9  # 角色成长模式本身就是为了角色发展
        plot_advancement = 0.6  # 角色成长对情节推进作用中等

        overall_score = (feasibility_score * 0.3 + dramatic_impact * 0.2 +
                        character_development * 0.3 + plot_advancement * 0.2)

        priority = RecommendationPriority.HIGH if overall_score >= 0.7 else RecommendationPriority.MEDIUM

        return PlotRecommendation(
            id=recommendation_id,
            title=title,
            description=description,
            pattern_type=pattern.pattern_type,
            priority=priority,
            feasibility_score=feasibility_score,
            dramatic_impact=dramatic_impact,
            character_development=character_development,
            plot_advancement=plot_advancement,
            overall_score=overall_score,
            involved_entities=[character.id] if character else [],
            required_relationships=[],
            potential_conflicts=["成长过程中的内心挣扎", "外界的质疑和阻力"],
            execution_steps=[
                "设计符合角色特点的成长挑战",
                "展现角色面对困难的过程",
                "描述角色的内心变化",
                "体现成长后的新能力或认知"
            ],
            expected_outcomes=["角色能力提升", "性格更加成熟", "为后续情节奠定基础"],
            created_at=datetime.now(),
            reasoning=f"角色当前发展阶段{current_development:.2f}，具有较大成长空间"
        )

    async def _generate_alliance_formation_recommendation(
        self,
        recommendation_id: str,
        pattern: PlotPattern,
        analysis: StoryAnalysis
    ) -> PlotRecommendation:
        """
        🤝 [剧情推荐] 生成联盟形成推荐
        """
        # 选择可以结盟的角色（没有敌对关系的主要角色）
        potential_allies = []
        for char1 in analysis.main_characters:
            for char2 in analysis.main_characters:
                if char1.id != char2.id:
                    # 检查是否存在敌对关系
                    has_conflict = any(
                        (rel.source_entity_id == char1.id and rel.target_entity_id == char2.id) or
                        (rel.source_entity_id == char2.id and rel.target_entity_id == char1.id)
                        for rel in analysis.conflict_relationships
                    )
                    if not has_conflict:
                        potential_allies.append((char1, char2))

        if not potential_allies:
            # 如果没有合适的盟友，选择前两个主要角色
            if len(analysis.main_characters) >= 2:
                potential_allies = [(analysis.main_characters[0], analysis.main_characters[1])]

        if potential_allies:
            ally1, ally2 = potential_allies[0]
            title = f"{ally1.name}与{ally2.name}结成联盟"
            description = f"面对共同威胁或目标，{ally1.name}和{ally2.name}决定携手合作，形成新的力量平衡"
        else:
            title = "角色联盟形成"
            description = "主要角色们面对共同挑战，决定放下分歧，结成临时或长期联盟"

        # 计算评分
        feasibility_score = 0.8 if potential_allies else 0.5
        dramatic_impact = pattern.dramatic_tension
        character_development = 0.7  # 联盟有助于角色发展
        plot_advancement = 0.8  # 联盟形成推进情节

        overall_score = (feasibility_score * 0.3 + dramatic_impact * 0.2 +
                        character_development * 0.25 + plot_advancement * 0.25)

        priority = RecommendationPriority.MEDIUM

        involved_entities = [ally1.id, ally2.id] if potential_allies else [c.id for c in analysis.main_characters[:2]]

        return PlotRecommendation(
            id=recommendation_id,
            title=title,
            description=description,
            pattern_type=pattern.pattern_type,
            priority=priority,
            feasibility_score=feasibility_score,
            dramatic_impact=dramatic_impact,
            character_development=character_development,
            plot_advancement=plot_advancement,
            overall_score=overall_score,
            involved_entities=involved_entities,
            required_relationships=[],
            potential_conflicts=["联盟内部的分歧", "外部势力的挑战"],
            execution_steps=[
                "确立共同的威胁或目标",
                "展现角色间的协商过程",
                "建立联盟的规则和目标",
                "展示联盟的初步行动"
            ],
            expected_outcomes=["力量平衡改变", "新的对抗格局", "角色关系深化"],
            created_at=datetime.now(),
            reasoning=f"当前有{len(potential_allies)}对潜在盟友，适合形成联盟"
        )

    async def _generate_betrayal_recommendation(
        self,
        recommendation_id: str,
        pattern: PlotPattern,
        analysis: StoryAnalysis
    ) -> PlotRecommendation:
        """
        🗡️ [剧情推荐] 生成背叛推荐
        """
        # 选择最强的联盟关系作为背叛目标
        if not analysis.alliance_relationships:
            return None

        strongest_alliance = max(analysis.alliance_relationships, key=lambda x: x.strength)

        # 获取关系双方的实体信息
        source_entity = next((e for e in analysis.main_characters + analysis.supporting_characters
                             if e.id == strongest_alliance.source_entity_id), None)
        target_entity = next((e for e in analysis.main_characters + analysis.supporting_characters
                             if e.id == strongest_alliance.target_entity_id), None)

        title = f"{source_entity.name if source_entity else '角色A'}背叛{target_entity.name if target_entity else '角色B'}"
        description = f"由于利益冲突或外界诱惑，{strongest_alliance.relationship_type}关系被打破，信任变成背叛"

        # 计算评分
        feasibility_score = strongest_alliance.strength * 0.8  # 关系越强，背叛的冲击越大
        dramatic_impact = pattern.dramatic_tension
        character_development = 0.8  # 背叛对角色发展影响很大
        plot_advancement = 0.9  # 背叛大幅推进情节

        overall_score = (feasibility_score * 0.25 + dramatic_impact * 0.35 +
                        character_development * 0.2 + plot_advancement * 0.2)

        priority = RecommendationPriority.HIGH if overall_score >= 0.8 else RecommendationPriority.MEDIUM

        return PlotRecommendation(
            id=recommendation_id,
            title=title,
            description=description,
            pattern_type=pattern.pattern_type,
            priority=priority,
            feasibility_score=feasibility_score,
            dramatic_impact=dramatic_impact,
            character_development=character_development,
            plot_advancement=plot_advancement,
            overall_score=overall_score,
            involved_entities=[strongest_alliance.source_entity_id, strongest_alliance.target_entity_id],
            required_relationships=[strongest_alliance.id],
            potential_conflicts=["信任关系彻底破裂", "其他角色被迫选边站队", "复仇情节的引发"],
            execution_steps=[
                "设计背叛的动机和诱因",
                "展现背叛者的内心挣扎",
                "描述背叛行为的实施",
                "展示被背叛者的反应和后果"
            ],
            expected_outcomes=["关系网络重构", "故事张力急剧上升", "为复仇或救赎情节铺路"],
            created_at=datetime.now(),
            reasoning=f"基于强度{strongest_alliance.strength:.2f}的{strongest_alliance.relationship_type}关系，背叛将产生强烈戏剧效果"
        )

    async def _generate_generic_recommendation(
        self,
        recommendation_id: str,
        pattern: PlotPattern,
        analysis: StoryAnalysis
    ) -> PlotRecommendation:
        """
        🎭 [剧情推荐] 生成通用推荐

        为其他剧情模式生成通用的推荐方案
        """
        title = f"{pattern.name}情节发展"
        description = f"基于当前故事状态，适合发展{pattern.description}类型的情节"

        # 通用评分计算
        feasibility_score = 0.7  # 默认可行性
        dramatic_impact = pattern.dramatic_tension
        character_development = pattern.complexity * 0.8
        plot_advancement = pattern.reader_appeal * 0.8

        overall_score = (feasibility_score * 0.25 + dramatic_impact * 0.25 +
                        character_development * 0.25 + plot_advancement * 0.25)

        priority = RecommendationPriority.MEDIUM

        # 选择相关实体
        involved_entities = [c.id for c in analysis.main_characters[:2]]

        return PlotRecommendation(
            id=recommendation_id,
            title=title,
            description=description,
            pattern_type=pattern.pattern_type,
            priority=priority,
            feasibility_score=feasibility_score,
            dramatic_impact=dramatic_impact,
            character_development=character_development,
            plot_advancement=plot_advancement,
            overall_score=overall_score,
            involved_entities=involved_entities,
            required_relationships=[],
            potential_conflicts=["情节发展中的不确定性"],
            execution_steps=[
                "分析当前故事状态",
                "设计符合模式的具体情节",
                "考虑角色反应和发展",
                "为后续发展预留空间"
            ],
            expected_outcomes=pattern.outcomes,
            created_at=datetime.now(),
            reasoning=f"基于{pattern.name}模式的通用推荐，适合当前故事发展阶段"
        )


def create_plot_recommendation_engine() -> PlotRecommendationEngine:
    """
    🏭 [剧情推荐] 创建剧情推荐引擎实例

    工厂函数，用于创建和初始化剧情推荐引擎
    """
    return PlotRecommendationEngine()
