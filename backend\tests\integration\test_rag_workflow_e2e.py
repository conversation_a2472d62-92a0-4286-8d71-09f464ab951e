"""
🧠 [RAG] RAG增强章节生成端到端集成测试
测试从API路由到后台任务的完整RAG工作流
"""

import pytest
import asyncio
import tempfile
import shutil
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from app.services.background_tasks import background_generate_chapter
from app.schemas.generation import ChapterGenerationRequest, AIProvider, GenerationStatus
from app.repositories.bible_repo import StoryBibleRepository
from app.repositories.chapter_repo import ChapterRepository
from app.services.vector_store import VectorStoreManager, MemoryDocument
from app.models.story_bible import StoryBible, Chapter, StoryGenre


class TestRAGWorkflowE2E:
    """🧠 [RAG] RAG工作流端到端测试类"""
    
    @pytest.fixture
    async def temp_vector_store(self):
        """创建临时向量存储"""
        temp_dir = tempfile.mkdtemp()
        
        try:
            manager = VectorStoreManager(persist_directory=temp_dir)
            await manager.initialize()
            
            # 添加一些测试记忆数据
            test_memory = MemoryDocument(
                id="test-memory-001",
                content="李明运用现代军事知识，帮助朱元璋制定了详细的攻城策略",
                summary="现代军事知识在古代的应用",
                story_id=1,
                chapter_id=2,
                memory_type="chapter",
                importance_score=0.9
            )
            
            await manager.add_memory(test_memory)
            
            yield manager
        finally:
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def mock_story_bible(self):
        """模拟故事圣经"""
        bible = MagicMock(spec=StoryBible)
        bible.id = "test-bible-001"
        bible.title = "穿越明朝：程序员的逆袭"
        bible.genre = StoryGenre.HISTORICAL
        bible.theme = "现代程序员穿越到明朝，运用现代知识改变历史"
        bible.protagonist = "李明，28岁程序员，聪明机智"
        bible.setting = "明朝洪武年间，从乞丐到皇帝的传奇时代"
        bible.writing_style = "历史穿越风格"
        bible.status = GenerationStatus.COMPLETED
        bible.generated_content = """
        这是一个关于现代程序员李明穿越到明朝的传奇故事。
        李明凭借现代知识和历史了解，成为朱元璋的重要谋士。
        故事展现了现代智慧与古代环境的碰撞与融合。
        主要角色包括：
        - 李明：现代程序员，主角
        - 朱元璋：明朝开国皇帝，历史人物
        - 马皇后：朱元璋的妻子，智慧女性
        """
        return bible
    
    @pytest.fixture
    def mock_previous_chapters(self):
        """模拟前置章节"""
        chapters = []
        
        # 第一章
        chapter1 = MagicMock(spec=Chapter)
        chapter1.id = "chapter-001"
        chapter1.chapter_number = 1
        chapter1.chapter_title = "穿越之始"
        chapter1.generated_content = "李明睁开眼睛，发现自己身处一个陌生的古代环境..."
        chapters.append(chapter1)
        
        # 第二章
        chapter2 = MagicMock(spec=Chapter)
        chapter2.id = "chapter-002"
        chapter2.chapter_number = 2
        chapter2.chapter_title = "初遇朱元璋"
        chapter2.generated_content = "在破庙中，李明遇到了一个衣衫褴褛但目光坚毅的年轻人..."
        chapters.append(chapter2)
        
        return chapters
    
    @pytest.fixture
    def chapter_request(self):
        """章节生成请求"""
        return ChapterGenerationRequest(
            story_bible_id="test-bible-001",
            chapter_number=3,
            chapter_title="智谋初显",
            chapter_outline="李明向朱元璋展示现代军事策略，获得信任",
            target_word_count=2000,
            ai_provider=AIProvider.ZHIPU,
            temperature=0.7,
            max_tokens=4000,
            enable_emotion_enhancement=False,
            enable_entropy_injection=False
        )
    
    @pytest.mark.asyncio
    async def test_complete_rag_workflow(
        self,
        temp_vector_store,
        mock_story_bible,
        mock_previous_chapters,
        chapter_request
    ):
        """测试完整的RAG增强章节生成工作流"""
        
        # Mock数据库会话和仓库
        mock_session = AsyncMock()
        mock_bible_repo = AsyncMock(spec=StoryBibleRepository)
        mock_chapter_repo = AsyncMock(spec=ChapterRepository)
        
        # 设置mock返回值
        mock_bible_repo.get_bible_by_id.return_value = mock_story_bible
        mock_chapter_repo.get_by_story_and_number.side_effect = mock_previous_chapters
        mock_chapter_repo.get_chapters_by_bible_id.return_value = mock_previous_chapters
        mock_chapter_repo.update_chapter_status = AsyncMock()
        
        # Mock智谱AI响应
        mock_ai_response = {
            "choices": [{
                "message": {
                    "content": """第三章：智谋初显

李明看着眼前这位目光如炬的年轻人，心中暗自惊叹。眼前这个人，正是历史上赫赫有名的明太祖朱元璋，只是此时的他还只是一个普通的乞丐。

"朱兄弟，你刚才说的攻城之法，确实有些道理。"李明缓缓开口，"不过，我觉得还可以再完善一些。"

朱元璋眼中闪过一丝好奇："愿闻其详。"

李明在地上画了一个简单的城池图案："你看，如果我们从这三个方向同时进攻，采用声东击西的策略..."

他开始运用现代军事理论中的多点突破战术，结合当时的实际情况，为朱元璋详细分析攻城策略。朱元璋听得入神，不时点头称赞。

"李兄弟，你这些见解，真是闻所未闻！"朱元璋激动地说道，"若能按此计行事，何愁大业不成！"

李明心中暗想，历史的车轮正在悄然改变方向。他知道，自己的现代知识将在这个时代发挥巨大的作用。

"朱兄弟，这只是开始。"李明神秘地笑了笑，"我还有更多的想法要与你分享。"

朱元璋的眼中燃起了希望的火焰，他仿佛看到了一个全新的未来正在向他招手..."""
                }
            }]
        }
        
        # Mock数据库管理器
        with patch('app.core.database.db_manager') as mock_db_manager:
            mock_db_manager.get_session.return_value.__aenter__.return_value = mock_session
            
            # Mock仓库创建
            with patch('app.repositories.bible_repo.StoryBibleRepository', return_value=mock_bible_repo):
                with patch('app.repositories.chapter_repo.ChapterRepository', return_value=mock_chapter_repo):
                    with patch('app.services.vector_store.get_vector_store', return_value=temp_vector_store):
                        with patch('app.services.generation_service.get_zhipu_client') as mock_get_client:
                            mock_client = AsyncMock()
                            mock_client.chat_completion.return_value = mock_ai_response
                            mock_get_client.return_value = mock_client
                            
                            # 执行完整的后台生成任务
                            await background_generate_chapter(
                                task_id="test-task-001",
                                request=chapter_request,
                                chapter_repo=mock_chapter_repo
                            )
                            
                            # 验证工作流执行
                            
                            # 1. 验证故事圣经查询（RAG工作流中会被调用多次）
                            assert mock_bible_repo.get_bible_by_id.call_count >= 1
                            mock_bible_repo.get_bible_by_id.assert_any_call("test-bible-001")
                            
                            # 2. 验证前置章节查询
                            assert mock_chapter_repo.get_by_story_and_number.call_count >= 1
                            
                            # 3. 验证向量存储搜索被调用
                            # 这里通过检查AI调用的提示词来验证RAG上下文是否被正确构建
                            mock_client.chat_completion.assert_called_once()
                            call_args = mock_client.chat_completion.call_args
                            
                            # 验证提示词包含RAG增强的上下文
                            user_message = call_args[1]['messages'][1].content
                            assert "创作上下文" in user_message
                            assert "穿越明朝" in user_message
                            assert "李明" in user_message
                            assert "现代军事知识在古代的应用" in user_message  # 来自向量存储的记忆
                            
                            # 4. 验证章节状态更新
                            assert mock_chapter_repo.update_chapter_status.call_count >= 2
                            
                            # 验证最终状态更新为完成
                            final_call = mock_chapter_repo.update_chapter_status.call_args_list[-1]
                            assert final_call[0][1] == GenerationStatus.COMPLETED  # 状态
                            assert "第三章：智谋初显" in final_call[1]['generated_content']  # 生成内容
                            
                            print("✅ 完整RAG工作流测试通过")
                            print(f"   任务ID: test-task-001")
                            print(f"   章节号: {chapter_request.chapter_number}")
                            print(f"   生成内容包含RAG上下文: ✓")
                            print(f"   状态更新正确: ✓")
    
    @pytest.mark.asyncio
    async def test_rag_workflow_with_memory_integration(
        self,
        temp_vector_store,
        mock_story_bible,
        mock_previous_chapters,
        chapter_request
    ):
        """测试RAG工作流与记忆系统的集成"""
        
        # 添加更多测试记忆
        additional_memory = MemoryDocument(
            id="test-memory-002",
            content="朱元璋展现出卓越的领导才能，深得部下信任",
            summary="朱元璋的领导能力",
            story_id=1,
            chapter_id=1,
            memory_type="character",
            importance_score=0.8
        )
        await temp_vector_store.add_memory(additional_memory)
        
        # Mock数据库组件
        mock_session = AsyncMock()
        mock_bible_repo = AsyncMock(spec=StoryBibleRepository)
        mock_chapter_repo = AsyncMock(spec=ChapterRepository)
        
        mock_bible_repo.get_bible_by_id.return_value = mock_story_bible
        mock_chapter_repo.get_by_story_and_number.side_effect = mock_previous_chapters
        mock_chapter_repo.get_chapters_by_bible_id.return_value = mock_previous_chapters
        mock_chapter_repo.update_chapter_status = AsyncMock()
        
        # Mock AI响应
        mock_ai_response = {
            "choices": [{
                "message": {
                    "content": "基于丰富记忆上下文生成的章节内容..."
                }
            }]
        }
        
        with patch('app.core.database.db_manager') as mock_db_manager:
            mock_db_manager.get_session.return_value.__aenter__.return_value = mock_session
            
            with patch('app.repositories.bible_repo.StoryBibleRepository', return_value=mock_bible_repo):
                with patch('app.repositories.chapter_repo.ChapterRepository', return_value=mock_chapter_repo):
                    with patch('app.services.vector_store.get_vector_store', return_value=temp_vector_store):
                        with patch('app.services.generation_service.get_zhipu_client') as mock_get_client:
                            mock_client = AsyncMock()
                            mock_client.chat_completion.return_value = mock_ai_response
                            mock_get_client.return_value = mock_client
                            
                            # 执行生成任务
                            await background_generate_chapter(
                                task_id="test-task-002",
                                request=chapter_request,
                                chapter_repo=mock_chapter_repo
                            )
                            
                            # 验证记忆检索效果
                            call_args = mock_client.chat_completion.call_args
                            user_message = call_args[1]['messages'][1].content
                            
                            # 验证多个记忆被正确检索和使用
                            assert "现代军事知识在古代的应用" in user_message
                            assert "朱元璋的领导能力" in user_message or "领导才能" in user_message
                            
                            print("✅ RAG记忆集成测试通过")
                            print(f"   检索到的记忆数量: 2+")
                            print(f"   记忆内容正确集成: ✓")


if __name__ == "__main__":
    """直接运行测试"""
    pytest.main([__file__, "-v"])
