"""
🧠 [AI驱动] 动态事件处理器
从章节文本中自动提取关键事件，并更新世界知识图谱
"""

import json
import asyncio
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field, ValidationError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import log_info, log_error, log_debug, log_warning
from app.services.zhipu_client import get_zhipu_client, ChatMessage, ZhipuAIError
from app.core.database import get_database_session, db_manager
from app.models.world_graph import Entity, EntityRelationship, EntityType, RelationshipStatus
from app.schemas.world_graph import EntityCreate, RelationshipCreate
from sqlalchemy import select


class EventData(BaseModel):
    """事件数据基础模型"""
    pass


class CreateEntityEventData(EventData):
    """创建实体事件数据"""
    name: str = Field(..., description="实体名称")
    type: str = Field(..., description="实体类型")
    description: Optional[str] = Field(None, description="实体描述")
    properties: Optional[Dict[str, Any]] = Field(default_factory=dict, description="实体属性")
    first_mentioned_chapter: Optional[int] = Field(None, description="首次提及章节")


class UpdateEntityEventData(EventData):
    """更新实体事件数据"""
    entity_name: str = Field(..., description="实体名称")
    description: Optional[str] = Field(None, description="新描述")
    properties: Optional[Dict[str, Any]] = Field(None, description="更新的属性")
    status: Optional[str] = Field(None, description="实体状态")


class CreateRelationshipEventData(EventData):
    """创建关系事件数据"""
    source_entity: str = Field(..., description="源实体名称")
    target_entity: str = Field(..., description="目标实体名称")
    relationship_type: str = Field(..., description="关系类型")
    description: Optional[str] = Field(None, description="关系描述")
    properties: Optional[Dict[str, Any]] = Field(default_factory=dict, description="关系属性")
    established_chapter: Optional[int] = Field(None, description="建立关系的章节")


class UpdateRelationshipEventData(EventData):
    """更新关系事件数据"""
    source_entity: str = Field(..., description="源实体名称")
    target_entity: str = Field(..., description="目标实体名称")
    new_type: Optional[str] = Field(None, description="新关系类型")
    new_status: Optional[str] = Field(None, description="新关系状态")
    description: Optional[str] = Field(None, description="关系描述")
    properties: Optional[Dict[str, Any]] = Field(None, description="更新的属性")


class WorldEvent(BaseModel):
    """世界事件模型"""
    event: str = Field(..., description="事件类型")
    data: Dict[str, Any] = Field(..., description="事件数据")


class EventProcessor:
    """
    🧠 [AI驱动] 动态事件处理器
    
    核心功能：
    1. 使用智谱AI从章节文本中提取关键事件
    2. 解析结构化的事件数据
    3. 调用世界图谱API更新数据库
    """
    
    def __init__(self):
        self.supported_events = {
            "create_entity": CreateEntityEventData,
            "update_entity": UpdateEntityEventData,
            "create_relationship": CreateRelationshipEventData,
            "update_relationship": UpdateRelationshipEventData
        }
        
        log_info("事件处理", "动态事件处理器初始化完成", 
                支持的事件类型=list(self.supported_events.keys()))
    
    def _build_extraction_prompt(self, chapter_text: str, story_id: str, chapter_number: int) -> str:
        """
        🧠 [AI驱动] 构建事件提取提示词
        """
        prompt = f"""你是一个专业的小说分析AI，需要从章节文本中提取出所有导致世界状态变化的关键事件。

**任务说明**：
分析以下章节文本，识别出所有会影响故事世界状态的重要事件，包括：
1. 新角色、物品、场景的出现
2. 角色关系的建立、变化或结束
3. 重要物品的获得、失去或状态改变
4. 场景的变化或新场景的引入

**章节信息**：
- 故事ID: {story_id}
- 章节编号: {chapter_number}

**章节文本**：
{chapter_text}

**输出要求**：
请以JSON数组格式返回所有识别到的事件，每个事件包含event和data字段。

**支持的事件类型**：
1. create_entity - 创建新实体（角色、物品、场景、概念、组织）
2. update_entity - 更新现有实体信息
3. create_relationship - 创建新的实体关系
4. update_relationship - 更新现有关系状态

**JSON格式示例**：
```json
[
  {{
    "event": "create_entity",
    "data": {{
      "name": "李小明",
      "type": "character",
      "description": "年轻的剑客，主角的朋友",
      "properties": {{"年龄": 20, "职业": "剑客"}},
      "first_mentioned_chapter": {chapter_number}
    }}
  }},
  {{
    "event": "create_relationship",
    "data": {{
      "source_entity": "张三",
      "target_entity": "李小明",
      "relationship_type": "朋友",
      "description": "从小一起长大的好友",
      "properties": {{"友谊程度": "深厚"}},
      "established_chapter": {chapter_number}
    }}
  }},
  {{
    "event": "update_relationship",
    "data": {{
      "source_entity": "张三",
      "target_entity": "王五",
      "new_type": "敌人",
      "new_status": "active",
      "description": "因为误会而反目成仇"
    }}
  }}
]
```

**注意事项**：
1. 只返回JSON数组，不要包含其他文字说明
2. 实体类型必须是：character, item, scene, concept, organization 之一
3. 关系类型要具体明确，如：朋友、敌人、师父、持有、位于等
4. 如果没有发现任何事件，返回空数组 []
5. 确保JSON格式正确，可以被解析

请开始分析："""
        
        return prompt
    
    async def extract_events_from_chapter(
        self, 
        chapter_text: str, 
        story_id: str, 
        chapter_number: int
    ) -> List[WorldEvent]:
        """
        🧠 [AI驱动] 从章节文本中提取事件
        
        Args:
            chapter_text: 章节文本内容
            story_id: 故事ID
            chapter_number: 章节编号
            
        Returns:
            List[WorldEvent]: 提取到的事件列表
        """
        log_info("事件处理", "开始从章节文本中提取事件", 
                story_id=story_id, 
                chapter_number=chapter_number,
                text_length=len(chapter_text))
        
        try:
            # 构建提示词
            prompt = self._build_extraction_prompt(chapter_text, story_id, chapter_number)
            
            # 调用智谱AI
            zhipu_client = await get_zhipu_client()
            messages = [ChatMessage(role="user", content=prompt)]
            
            log_debug("事件处理", "调用智谱AI进行事件提取", 
                     prompt_length=len(prompt))
            
            response = await zhipu_client.chat_completion(
                messages=messages,
                temperature=0.3,  # 较低温度确保输出稳定
                max_tokens=2000
            )
            
            # 提取AI响应内容
            ai_response = response.choices[0]["message"]["content"].strip()
            log_debug("事件处理", "智谱AI响应获取成功", 
                     response_length=len(ai_response))
            
            # 解析JSON响应
            try:
                events_data = json.loads(ai_response)
                if not isinstance(events_data, list):
                    log_warning("事件处理", "AI响应不是数组格式", response=ai_response[:200])
                    return []
                
                # 验证和转换事件数据
                events = []
                for event_dict in events_data:
                    try:
                        event = WorldEvent(**event_dict)
                        events.append(event)
                    except ValidationError as e:
                        log_warning("事件处理", "事件数据验证失败", 
                                   event_data=event_dict, 
                                   error=str(e))
                        continue
                
                log_info("事件处理", "事件提取完成", 
                        提取到的事件数量=len(events),
                        有效事件数量=len([e for e in events if e.event in self.supported_events]))
                
                return events
                
            except json.JSONDecodeError as e:
                log_error("事件处理", "AI响应JSON解析失败", 
                         response=ai_response[:500], 
                         error=str(e))
                return []
                
        except ZhipuAIError as e:
            log_error("事件处理", "智谱AI调用失败", error=str(e))
            return []
        except Exception as e:
            log_error("事件处理", "事件提取过程发生未知错误", error=str(e))
            return []

    async def _find_entity_by_name(self, db: AsyncSession, story_id: str, entity_name: str) -> Optional[str]:
        """
        🔍 [查询] 根据名称查找实体ID
        """
        try:
            # 确保在异步上下文中执行查询
            stmt = select(Entity.id).filter(
                Entity.story_id == story_id,
                Entity.name == entity_name,
                Entity.is_active == True
            )
            result = await db.execute(stmt)
            entity_id = result.scalar_one_or_none()

            log_debug("事件处理", "实体查找完成",
                     entity_name=entity_name,
                     found=entity_id is not None,
                     entity_id=entity_id)
            return entity_id
        except Exception as e:
            log_error("事件处理", "查找实体失败",
                     entity_name=entity_name,
                     story_id=story_id,
                     error=str(e))
            return None

    async def _process_create_entity_event(
        self,
        event_data: CreateEntityEventData,
        story_id: str,
        db: AsyncSession
    ) -> bool:
        """
        🌐 [事件处理] 处理创建实体事件
        """
        log_info("事件处理", "处理创建实体事件",
                entity_name=event_data.name,
                entity_type=event_data.type)

        try:
            # 检查实体是否已存在
            existing_entity_id = await self._find_entity_by_name(db, story_id, event_data.name)
            if existing_entity_id:
                log_warning("事件处理", "实体已存在，跳过创建",
                           entity_name=event_data.name,
                           existing_id=existing_entity_id)
                return True

            # 验证实体类型
            try:
                entity_type = EntityType(event_data.type.lower())
            except ValueError:
                log_error("事件处理", "无效的实体类型",
                         entity_type=event_data.type,
                         valid_types=[t.value for t in EntityType])
                return False

            # 直接创建实体对象
            import uuid
            entity_id = str(uuid.uuid4())

            entity = Entity(
                id=entity_id,
                story_id=story_id,
                name=event_data.name,
                type=entity_type,
                description=event_data.description or f"通过AI事件处理器自动创建的{entity_type.value}",
                properties=event_data.properties or {},
                first_mentioned_chapter=event_data.first_mentioned_chapter,
                is_active=True
            )

            # 保存到数据库，使用事务
            try:
                db.add(entity)
                await db.flush()  # 先flush确保数据写入
                await db.commit()
                await db.refresh(entity)

                log_info("事件处理", "实体创建成功",
                        entity_id=entity.id,
                        entity_name=entity.name)
                return True
            except Exception as e:
                await db.rollback()
                log_error("事件处理", "实体创建失败",
                         entity_name=event_data.name,
                         error=str(e))
                return False

        except Exception as e:
            log_error("事件处理", "创建实体事件处理失败",
                     entity_name=event_data.name,
                     error=str(e))
            return False

    async def _process_create_relationship_event(
        self,
        event_data: CreateRelationshipEventData,
        story_id: str,
        db: AsyncSession
    ) -> bool:
        """
        🌐 [事件处理] 处理创建关系事件
        """
        log_info("事件处理", "处理创建关系事件",
                source_entity=event_data.source_entity,
                target_entity=event_data.target_entity,
                relationship_type=event_data.relationship_type)

        try:
            # 查找源实体和目标实体
            source_entity_id = await self._find_entity_by_name(db, story_id, event_data.source_entity)
            target_entity_id = await self._find_entity_by_name(db, story_id, event_data.target_entity)

            print(f"DEBUG: 查找实体结果 - 源实体ID: {source_entity_id}, 目标实体ID: {target_entity_id}")

            if not source_entity_id:
                log_error("事件处理", "源实体不存在", entity_name=event_data.source_entity)
                print(f"DEBUG: 源实体不存在: {event_data.source_entity}")
                return False

            if not target_entity_id:
                log_error("事件处理", "目标实体不存在", entity_name=event_data.target_entity)
                print(f"DEBUG: 目标实体不存在: {event_data.target_entity}")
                return False

            # 直接创建关系对象
            import uuid
            relationship_id = str(uuid.uuid4())

            relationship = EntityRelationship(
                id=relationship_id,
                source_entity_id=source_entity_id,
                target_entity_id=target_entity_id,
                relationship_type=event_data.relationship_type,
                description=event_data.description or f"{event_data.source_entity}与{event_data.target_entity}的{event_data.relationship_type}关系",
                properties=event_data.properties or {},
                status=RelationshipStatus.ACTIVE,
                established_chapter=event_data.established_chapter
            )

            # 保存到数据库，使用事务
            try:
                db.add(relationship)
                await db.flush()  # 先flush确保数据写入
                await db.commit()
                await db.refresh(relationship)

                log_info("事件处理", "关系创建成功",
                        relationship_id=relationship.id,
                        source_entity=event_data.source_entity,
                        target_entity=event_data.target_entity)
                return True
            except Exception as e:
                await db.rollback()
                log_error("事件处理", "关系创建失败",
                         source_entity=event_data.source_entity,
                         target_entity=event_data.target_entity,
                         error=str(e))
                return False

        except Exception as e:
            log_error("事件处理", "创建关系事件处理失败",
                     source_entity=event_data.source_entity,
                     target_entity=event_data.target_entity,
                     error=str(e))
            return False

    async def process_chapter_events(
        self,
        chapter_text: str,
        story_id: str,
        chapter_number: int,
        db: Optional[AsyncSession] = None
    ) -> Dict[str, Any]:
        """
        🧠 [AI驱动] 处理章节事件的主要入口方法

        Args:
            chapter_text: 章节文本内容
            story_id: 故事ID
            chapter_number: 章节编号
            db: 数据库会话（可选，如果不提供会自动创建）

        Returns:
            Dict[str, Any]: 处理结果统计
        """
        log_info("事件处理", "开始处理章节事件",
                story_id=story_id,
                chapter_number=chapter_number,
                text_length=len(chapter_text))

        # 初始化结果统计
        result = {
            "story_id": story_id,
            "chapter_number": chapter_number,
            "total_events": 0,
            "processed_events": 0,
            "successful_events": 0,
            "failed_events": 0,
            "event_details": [],
            "errors": []
        }

        try:
            # 如果没有提供数据库会话，创建一个新的
            if db is None:
                # 确保数据库管理器已初始化
                if not db_manager._initialized:
                    log_warning("事件处理", "数据库管理器未初始化，尝试使用默认配置初始化")
                    db_manager.initialize()

                async with db_manager.async_session_maker() as db_session:
                    return await self._process_events_with_db(
                        chapter_text, story_id, chapter_number, db_session, result
                    )
            else:
                return await self._process_events_with_db(
                    chapter_text, story_id, chapter_number, db, result
                )

        except Exception as e:
            error_msg = f"章节事件处理过程发生严重错误: {str(e)}"
            log_error("事件处理", error_msg,
                     story_id=story_id,
                     chapter_number=chapter_number)
            result["errors"].append(error_msg)
            return result

    async def _process_events_with_db(
        self,
        chapter_text: str,
        story_id: str,
        chapter_number: int,
        db: AsyncSession,
        result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        🧠 [AI驱动] 使用数据库会话处理事件
        """
        try:
            # 1. 从章节文本中提取事件
            events = await self.extract_events_from_chapter(
                chapter_text, story_id, chapter_number
            )

            result["total_events"] = len(events)

            if not events:
                log_info("事件处理", "未从章节中提取到任何事件",
                        story_id=story_id,
                        chapter_number=chapter_number)
                return result

            # 2. 逐个处理事件
            for event in events:
                event_detail = {
                    "event_type": event.event,
                    "event_data": event.data,
                    "success": False,
                    "error": None
                }

                try:
                    success = await self._process_single_event(event, story_id, db)
                    event_detail["success"] = success

                    if success:
                        result["successful_events"] += 1
                        log_debug("事件处理", "单个事件处理成功",
                                 event_type=event.event,
                                 event_data=event.data)
                    else:
                        result["failed_events"] += 1

                    result["processed_events"] += 1

                except Exception as e:
                    error_msg = f"处理事件时发生错误: {str(e)}"
                    event_detail["error"] = error_msg
                    result["failed_events"] += 1
                    result["processed_events"] += 1

                    log_error("事件处理", "单个事件处理失败",
                             event_type=event.event,
                             event_data=event.data,
                             error=str(e))

                result["event_details"].append(event_detail)

            # 3. 记录处理结果
            log_info("事件处理", "章节事件处理完成",
                    story_id=story_id,
                    chapter_number=chapter_number,
                    total_events=result["total_events"],
                    successful_events=result["successful_events"],
                    failed_events=result["failed_events"])

            return result

        except Exception as e:
            error_msg = f"事件处理过程发生错误: {str(e)}"
            log_error("事件处理", error_msg,
                     story_id=story_id,
                     chapter_number=chapter_number)
            result["errors"].append(error_msg)
            return result

    async def _process_single_event(
        self,
        event: WorldEvent,
        story_id: str,
        db: AsyncSession
    ) -> bool:
        """
        🧠 [AI驱动] 处理单个事件
        """
        if event.event not in self.supported_events:
            log_warning("事件处理", "不支持的事件类型", event_type=event.event)
            return False

        try:
            # 验证事件数据
            event_data_class = self.supported_events[event.event]
            event_data = event_data_class(**event.data)

            # 根据事件类型调用相应的处理方法
            if event.event == "create_entity":
                return await self._process_create_entity_event(event_data, story_id, db)
            elif event.event == "create_relationship":
                return await self._process_create_relationship_event(event_data, story_id, db)
            elif event.event == "update_entity":
                # TODO: 实现更新实体事件处理
                log_warning("事件处理", "更新实体事件处理尚未实现")
                return False
            elif event.event == "update_relationship":
                # TODO: 实现更新关系事件处理
                log_warning("事件处理", "更新关系事件处理尚未实现")
                return False
            else:
                log_warning("事件处理", "未知的事件类型", event_type=event.event)
                return False

        except ValidationError as e:
            log_error("事件处理", "事件数据验证失败",
                     event_type=event.event,
                     event_data=event.data,
                     validation_error=str(e))
            return False
        except Exception as e:
            log_error("事件处理", "单个事件处理发生未知错误",
                     event_type=event.event,
                     error=str(e))
            return False


# 全局事件处理器实例
_event_processor: Optional[EventProcessor] = None


async def get_event_processor() -> EventProcessor:
    """
    🧠 [AI驱动] 获取事件处理器实例（单例模式）
    """
    global _event_processor
    if _event_processor is None:
        _event_processor = EventProcessor()
        log_info("事件处理", "创建事件处理器单例实例")
    return _event_processor
