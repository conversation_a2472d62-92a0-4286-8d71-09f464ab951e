"""
🧠 [向量存储] 向量数据库服务模块

基于ChromaDB和sentence-transformers实现的语义记忆存储系统：
1. 文本向量化和嵌入
2. 语义相似度搜索
3. 记忆数据管理
4. 多条件过滤查询

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

import uuid
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
import numpy as np

from app.core.config import settings, log_info, log_debug, log_error, log_success


@dataclass
class MemoryDocument:
    """
    📝 [记忆文档] 记忆文档数据结构
    
    表示存储在向量数据库中的单个记忆片段
    """
    id: str                          # 文档唯一标识
    content: str                     # 文档内容
    summary: str                     # 内容摘要
    story_id: int                    # 所属故事ID
    chapter_id: Optional[int] = None # 所属章节ID
    memory_type: str = "chapter"     # 记忆类型：chapter, character, plot, setting
    importance_score: float = 0.5    # 重要性分数 (0-1)
    created_at: str = ""            # 创建时间
    metadata: Dict[str, Any] = None  # 额外元数据
    
    def __post_init__(self):
        """初始化后处理"""
        if self.metadata is None:
            self.metadata = {}
        if not self.created_at:
            from datetime import datetime
            self.created_at = datetime.now().isoformat()


@dataclass
class SearchResult:
    """
    🔍 [搜索结果] 向量搜索结果数据结构
    """
    document: MemoryDocument         # 匹配的文档
    similarity_score: float          # 相似度分数 (0-1)
    distance: float                  # 向量距离


class VectorStoreManager:
    """
    🧠 [向量存储管理器] ChromaDB向量数据库管理器
    
    负责向量数据库的初始化、数据嵌入、相似度搜索等核心操作
    """
    
    def __init__(self, persist_directory: Optional[str] = None):
        """
        初始化向量存储管理器
        
        Args:
            persist_directory: 持久化目录路径，默认为data/chroma_db
        """
        self.persist_directory = persist_directory or str(Path("data/chroma_db"))
        self.client: Optional[chromadb.Client] = None
        self.collection = None
        self.embedding_model: Optional[SentenceTransformer] = None
        self.collection_name = "novel_memories"
        
        log_debug("向量存储", "向量存储管理器初始化", 
                 持久化目录=self.persist_directory,
                 集合名称=self.collection_name)
    
    async def initialize(self) -> None:
        """
        🏗️ [系统] 初始化向量数据库
        
        创建ChromaDB客户端、加载嵌入模型、创建集合
        """
        try:
            log_info("向量存储", "开始初始化向量数据库")
            
            # 确保持久化目录存在
            persist_path = Path(self.persist_directory)
            persist_path.mkdir(parents=True, exist_ok=True)
            log_debug("向量存储", "确保持久化目录存在", 目录=str(persist_path))
            
            # 创建ChromaDB客户端
            self.client = chromadb.PersistentClient(
                path=self.persist_directory,
                settings=Settings(
                    anonymized_telemetry=False,  # 禁用遥测
                    allow_reset=True,            # 允许重置
                )
            )
            log_debug("向量存储", "ChromaDB客户端创建成功")
            
            # 在异步环境中加载嵌入模型
            await self._load_embedding_model()
            
            # 获取或创建集合
            try:
                self.collection = self.client.get_collection(name=self.collection_name)
                log_debug("向量存储", "获取现有集合成功", 集合名称=self.collection_name)
            except Exception:
                # 集合不存在，创建新集合
                self.collection = self.client.create_collection(
                    name=self.collection_name,
                    metadata={"description": "小说记忆向量存储集合"}
                )
                log_debug("向量存储", "创建新集合成功", 集合名称=self.collection_name)
            
            log_success("向量存储", "向量数据库初始化完成",
                       客户端类型=type(self.client).__name__,
                       集合名称=self.collection_name,
                       嵌入模型=self.embedding_model.get_sentence_embedding_dimension())
            
        except Exception as e:
            log_error("向量存储", "向量数据库初始化失败", error=e)
            raise
    
    async def _load_embedding_model(self) -> None:
        """
        🤖 [AI] 异步加载嵌入模型

        在线程池中加载sentence-transformers模型，避免阻塞事件循环
        支持离线模式和网络模式
        """
        def _load_model():
            """在线程池中执行的模型加载函数"""
            try:
                # 首先尝试使用中文友好的嵌入模型
                model_name = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
                log_debug("向量存储", "尝试加载在线模型", 模型名称=model_name)
                return SentenceTransformer(model_name)
            except Exception as e:
                log_debug("向量存储", "在线模型加载失败，尝试使用本地模型", 错误=str(e))
                try:
                    # 如果在线模型失败，尝试使用更小的本地模型
                    model_name = "all-MiniLM-L6-v2"
                    log_debug("向量存储", "尝试加载备用模型", 模型名称=model_name)
                    return SentenceTransformer(model_name)
                except Exception as e2:
                    log_debug("向量存储", "备用模型加载失败，使用Mock模型", 错误=str(e2))
                    # 如果都失败，使用Mock模型进行测试
                    return MockEmbeddingModel()

        log_debug("向量存储", "开始加载嵌入模型")

        # 在线程池中异步加载模型
        loop = asyncio.get_event_loop()
        self.embedding_model = await loop.run_in_executor(None, _load_model)

        log_debug("向量存储", "嵌入模型加载完成",
                 模型类型=type(self.embedding_model).__name__,
                 模型维度=self.embedding_model.get_sentence_embedding_dimension())
    
    async def embed_text(self, text: str) -> List[float]:
        """
        🔢 [向量化] 将文本转换为向量
        
        Args:
            text: 待向量化的文本
            
        Returns:
            List[float]: 文本向量
        """
        if not self.embedding_model:
            raise RuntimeError("嵌入模型尚未初始化")
        
        log_debug("向量存储", "开始文本向量化", 文本长度=len(text))
        
        # 在线程池中执行向量化，避免阻塞
        def _embed():
            return self.embedding_model.encode(text).tolist()
        
        loop = asyncio.get_event_loop()
        embedding = await loop.run_in_executor(None, _embed)
        
        log_debug("向量存储", "文本向量化完成", 向量维度=len(embedding))
        return embedding
    
    async def add_memory(self, memory: MemoryDocument) -> str:
        """
        💾 [数据库] 添加记忆到向量数据库
        
        Args:
            memory: 记忆文档对象
            
        Returns:
            str: 文档ID
        """
        if not self.collection:
            raise RuntimeError("向量数据库尚未初始化")
        
        log_debug("向量存储", "开始添加记忆", 
                 记忆ID=memory.id, 故事ID=memory.story_id, 记忆类型=memory.memory_type)
        
        try:
            # 向量化内容
            embedding = await self.embed_text(memory.content)
            
            # 准备元数据（ChromaDB不允许None值）
            metadata = {
                "story_id": memory.story_id,
                "memory_type": memory.memory_type,
                "importance_score": memory.importance_score,
                "created_at": memory.created_at,
                "summary": memory.summary,
            }

            # 只添加非None的可选字段
            if memory.chapter_id is not None:
                metadata["chapter_id"] = memory.chapter_id

            # 添加额外的元数据，过滤None值
            if memory.metadata:
                for key, value in memory.metadata.items():
                    if value is not None:
                        metadata[key] = value
            
            # 添加到集合
            self.collection.add(
                ids=[memory.id],
                embeddings=[embedding],
                documents=[memory.content],
                metadatas=[metadata]
            )
            
            log_success("向量存储", "记忆添加成功", 记忆ID=memory.id)
            return memory.id
            
        except Exception as e:
            log_error("向量存储", "添加记忆失败", error=e, 记忆ID=memory.id)
            raise
    
    async def search_memories(
        self,
        query: str,
        n_results: int = 5,
        story_id: Optional[int] = None,
        memory_type: Optional[str] = None,
        min_importance: Optional[float] = None
    ) -> List[SearchResult]:
        """
        🔍 [搜索] 基于语义相似度搜索记忆
        
        Args:
            query: 搜索查询文本
            n_results: 返回结果数量
            story_id: 过滤特定故事ID
            memory_type: 过滤特定记忆类型
            min_importance: 最小重要性分数
            
        Returns:
            List[SearchResult]: 搜索结果列表
        """
        if not self.collection:
            raise RuntimeError("向量数据库尚未初始化")
        
        log_debug("向量存储", "开始搜索记忆",
                 查询文本=query[:50] + "..." if len(query) > 50 else query,
                 结果数量=n_results, 故事ID=story_id, 记忆类型=memory_type)
        
        try:
            # 向量化查询
            query_embedding = await self.embed_text(query)
            
            # 构建过滤条件
            where_conditions = []
            if story_id is not None:
                where_conditions.append({"story_id": {"$eq": story_id}})
            if memory_type is not None:
                where_conditions.append({"memory_type": {"$eq": memory_type}})
            if min_importance is not None:
                where_conditions.append({"importance_score": {"$gte": min_importance}})

            # 执行搜索
            search_kwargs = {
                "query_embeddings": [query_embedding],
                "n_results": n_results,
                "include": ["documents", "metadatas", "distances"]
            }

            if where_conditions:
                if len(where_conditions) == 1:
                    search_kwargs["where"] = where_conditions[0]
                else:
                    search_kwargs["where"] = {"$and": where_conditions}
            
            results = self.collection.query(**search_kwargs)
            
            # 转换结果格式
            search_results = []
            if results["ids"] and results["ids"][0]:
                for i, doc_id in enumerate(results["ids"][0]):
                    metadata_item = results["metadatas"][0][i]
                    document = MemoryDocument(
                        id=doc_id,
                        content=results["documents"][0][i],
                        summary=metadata_item.get("summary", ""),
                        story_id=metadata_item["story_id"],
                        chapter_id=metadata_item.get("chapter_id"),  # 可能为None
                        memory_type=metadata_item["memory_type"],
                        importance_score=metadata_item["importance_score"],
                        created_at=metadata_item["created_at"],
                        metadata={k: v for k, v in metadata_item.items()
                                if k not in ["story_id", "chapter_id", "memory_type",
                                           "importance_score", "created_at", "summary"]}
                    )
                    
                    # 计算相似度分数 (距离越小，相似度越高)
                    distance = results["distances"][0][i]
                    # ChromaDB使用L2距离，需要转换为相似度分数
                    # 对于L2距离，我们使用 1 / (1 + distance) 来计算相似度
                    similarity_score = 1.0 / (1.0 + distance)
                    
                    search_results.append(SearchResult(
                        document=document,
                        similarity_score=similarity_score,
                        distance=distance
                    ))
            
            log_success("向量存储", "记忆搜索完成", 
                       查询结果数量=len(search_results),
                       平均相似度=sum(r.similarity_score for r in search_results) / len(search_results) if search_results else 0)
            
            return search_results

        except Exception as e:
            log_error("向量存储", "搜索记忆失败", error=e)
            raise

    async def delete_memories_by_story(self, story_id: int) -> int:
        """
        🗑️ [删除] 删除指定故事的所有记忆

        Args:
            story_id: 故事ID

        Returns:
            int: 删除的记忆数量
        """
        if not self.collection:
            raise RuntimeError("向量数据库尚未初始化")

        log_debug("向量存储", "开始删除故事记忆", 故事ID=story_id)

        try:
            # 先查询要删除的记忆
            existing_memories = await self.search_memories(
                query="",  # 空查询获取所有记忆
                n_results=1000,
                story_id=story_id
            )

            if not existing_memories:
                log_debug("向量存储", "没有找到要删除的记忆", 故事ID=story_id)
                return 0

            # 收集要删除的ID
            ids_to_delete = [result.document.id for result in existing_memories]

            # 执行删除
            self.collection.delete(ids=ids_to_delete)

            deleted_count = len(ids_to_delete)
            log_success("向量存储", "故事记忆删除成功",
                       故事ID=story_id,
                       删除数量=deleted_count)

            return deleted_count

        except Exception as e:
            log_error("向量存储", "删除故事记忆失败", error=e, 故事ID=story_id)
            raise

    async def delete_memory_by_id(self, memory_id: str) -> bool:
        """
        🗑️ [删除] 根据ID删除单个记忆

        Args:
            memory_id: 记忆文档ID

        Returns:
            bool: 是否删除成功
        """
        if not self.collection:
            raise RuntimeError("向量数据库尚未初始化")

        log_debug("向量存储", "开始删除记忆", 记忆ID=memory_id)

        try:
            # 检查记忆是否存在
            try:
                result = self.collection.get(ids=[memory_id])
                if not result['ids']:
                    log_debug("向量存储", "记忆不存在", 记忆ID=memory_id)
                    return False
            except Exception:
                log_debug("向量存储", "记忆不存在", 记忆ID=memory_id)
                return False

            # 执行删除
            self.collection.delete(ids=[memory_id])

            log_success("向量存储", "记忆删除成功", 记忆ID=memory_id)
            return True

        except Exception as e:
            log_error("向量存储", "删除记忆失败", error=e, 记忆ID=memory_id)
            raise


# 全局向量存储管理器实例
_vector_store_manager: Optional[VectorStoreManager] = None


async def get_vector_store() -> VectorStoreManager:
    """
    🔧 [依赖注入] 获取向量存储管理器实例
    
    Returns:
        VectorStoreManager: 向量存储管理器实例
    """
    global _vector_store_manager
    
    if _vector_store_manager is None:
        _vector_store_manager = VectorStoreManager()
        await _vector_store_manager.initialize()
    
    return _vector_store_manager


async def close_vector_store() -> None:
    """
    🔒 [系统] 关闭向量存储管理器
    """
    global _vector_store_manager
    
    if _vector_store_manager is not None:
        log_info("向量存储", "关闭向量存储管理器")
        # ChromaDB客户端会自动处理资源清理
        _vector_store_manager = None
