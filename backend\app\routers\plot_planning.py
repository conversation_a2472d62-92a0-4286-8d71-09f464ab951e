"""
🎯 [情节规划] 情节发展路径规划API路由

提供情节路径生成、评估和建议的REST API接口。

主要端点：
- POST /api/v1/plot/generate - 生成情节发展路径
- GET /api/v1/plot/paths/{story_id} - 获取故事的路径历史
- POST /api/v1/plot/evaluate - 评估自定义路径
- GET /api/v1/plot/suggestions/{story_id} - 获取下一章建议
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
import asyncio

from app.core.plot_path_planner import create_plot_path_planner, PlotPathPlanner, PathType
from app.core.database import get_database_session
from app.schemas.plot_planning import (
    PlotPathGenerationRequest,
    PlotPathGenerationResponse,
    PlotPathEvaluationRequest,
    PlotPathEvaluationResponse,
    PlotSuggestionsResponse,
    PathHistoryResponse
)
from app.core.config import log_info, log_debug, log_error
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter(prefix="/api/v1/plot", tags=["情节规划"])


async def get_world_graph_data(story_id: str, db: AsyncSession):
    """获取世界知识图谱数据的辅助函数"""
    # TODO: 实现从数据库获取世界知识图谱数据
    # 这里暂时返回模拟数据，实际应该从数据库查询
    from app.schemas.world_graph import WorldGraphResponse, EntityResponse, RelationshipResponse
    from app.models.world_graph import EntityType, RelationshipStatus
    from datetime import datetime
    
    log_debug("情节规划", "获取世界知识图谱数据", 故事ID=story_id)
    
    # 模拟数据 - 实际应该从数据库查询
    entities = [
        EntityResponse(
            id="char-001",
            story_id=story_id,
            name="主角",
            type=EntityType.CHARACTER,
            description="故事的主要角色",
            properties={"personality": "勇敢善良"},
            importance_score=0.9,
            first_mentioned_chapter=1,
            last_mentioned_chapter=5,
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    ]
    
    relationships = []
    
    return WorldGraphResponse(
        story_id=story_id,
        story_title="示例故事",
        entities=entities,
        relationships=relationships,
        entity_count=len(entities),
        relationship_count=len(relationships),
        entity_stats={"CHARACTER": len(entities)},
        relationship_stats={}
    )


@router.post("/generate", response_model=PlotPathGenerationResponse)
async def generate_plot_paths(
    request: PlotPathGenerationRequest,
    planner: PlotPathPlanner = Depends(create_plot_path_planner),
    db: AsyncSession = Depends(get_database_session)
):
    """
    🎯 [情节规划] 生成情节发展路径
    
    基于当前故事状态和知识图谱，生成多种可能的情节发展路径，
    并提供智能的路径选择建议。
    """
    log_info("情节规划", "收到情节路径生成请求", 
             故事ID=request.story_id, 
             当前章节=request.current_chapter,
             目标路径数=request.target_paths)
    
    try:
        # 获取世界知识图谱数据
        world_graph = await get_world_graph_data(request.story_id, db)
        
        log_debug("情节规划", "世界知识图谱数据获取完成",
                 实体数量=len(world_graph.entities),
                 关系数量=len(world_graph.relationships))
        
        # 生成情节路径
        result = await planner.generate_plot_paths(
            world_graph=world_graph,
            current_chapter=request.current_chapter,
            target_paths=request.target_paths,
            focus_types=request.focus_types
        )
        
        # 构建响应
        response = PlotPathGenerationResponse(
            story_id=result.story_id,
            current_chapter=result.current_chapter,
            analysis_timestamp=result.analysis_timestamp,
            current_state_summary=result.current_state_summary,
            active_conflicts=result.active_conflicts,
            character_motivations=result.character_motivations,
            unresolved_threads=result.unresolved_threads,
            recommended_paths=result.recommended_paths,
            alternative_paths=result.alternative_paths,
            next_chapter_suggestions=result.next_chapter_suggestions,
            long_term_strategy=result.long_term_strategy,
            pacing_recommendations=result.pacing_recommendations,
            total_paths_generated=result.total_paths_generated,
            analysis_confidence=result.analysis_confidence
        )
        
        log_info("情节规划", "情节路径生成成功",
                故事ID=request.story_id,
                推荐路径数=len(response.recommended_paths),
                分析置信度=response.analysis_confidence)
        
        return response
        
    except Exception as e:
        log_error("情节规划", "情节路径生成失败", 
                 故事ID=request.story_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"情节路径生成失败: {str(e)}"
        )


@router.get("/paths/{story_id}", response_model=PathHistoryResponse)
async def get_plot_path_history(
    story_id: str,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(10, ge=1, le=50, description="返回的记录数"),
    db: AsyncSession = Depends(get_database_session)
):
    """
    📚 [情节规划] 获取故事的路径生成历史
    
    返回指定故事的历史路径生成记录，用于追踪和分析。
    """
    log_info("情节规划", "获取路径历史", 故事ID=story_id)
    
    try:
        # TODO: 从数据库查询路径历史
        # 这里返回模拟数据
        
        response = PathHistoryResponse(
            story_id=story_id,
            total_generations=0,
            generations=[],
            skip=skip,
            limit=limit
        )
        
        log_info("情节规划", "路径历史获取成功", 
                故事ID=story_id, 
                历史记录数=response.total_generations)
        
        return response
        
    except Exception as e:
        log_error("情节规划", "获取路径历史失败", 
                 故事ID=story_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"获取路径历史失败: {str(e)}"
        )


@router.post("/evaluate", response_model=PlotPathEvaluationResponse)
async def evaluate_custom_path(
    request: PlotPathEvaluationRequest,
    planner: PlotPathPlanner = Depends(create_plot_path_planner),
    db: AsyncSession = Depends(get_database_session)
):
    """
    🔍 [情节规划] 评估自定义情节路径
    
    对用户提供的自定义情节路径进行评估，
    提供可行性、吸引力等多维度分析。
    """
    log_info("情节规划", "收到路径评估请求", 
             故事ID=request.story_id,
             路径标题=request.path.title)
    
    try:
        # 获取世界知识图谱数据
        world_graph = await get_world_graph_data(request.story_id, db)
        
        # 评估路径
        evaluated_paths = await planner._evaluate_paths([request.path], world_graph)
        evaluated_path = evaluated_paths[0] if evaluated_paths else request.path
        
        # 构建响应
        response = PlotPathEvaluationResponse(
            story_id=request.story_id,
            evaluated_path=evaluated_path,
            evaluation_summary=f"路径综合评分: {evaluated_path.overall_score:.1f}/10.0",
            strengths=[
                f"可行性评分: {evaluated_path.feasibility_score:.1f}",
                f"吸引力评分: {evaluated_path.appeal_score:.1f}",
                f"连贯性评分: {evaluated_path.coherence_score:.1f}",
                f"原创性评分: {evaluated_path.originality_score:.1f}"
            ],
            weaknesses=evaluated_path.risk_factors,
            improvement_suggestions=[
                "考虑增加更多角色互动",
                "平衡情节节奏",
                "增强情感冲击力"
            ],
            compatibility_score=0.8,
            evaluation_timestamp=evaluated_path.last_updated
        )
        
        log_info("情节规划", "路径评估完成",
                故事ID=request.story_id,
                综合评分=evaluated_path.overall_score)
        
        return response
        
    except Exception as e:
        log_error("情节规划", "路径评估失败", 
                 故事ID=request.story_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"路径评估失败: {str(e)}"
        )


@router.get("/suggestions/{story_id}", response_model=PlotSuggestionsResponse)
async def get_next_chapter_suggestions(
    story_id: str,
    current_chapter: int = Query(..., ge=1, description="当前章节号"),
    suggestion_count: int = Query(5, ge=1, le=10, description="建议数量"),
    planner: PlotPathPlanner = Depends(create_plot_path_planner),
    db: AsyncSession = Depends(get_database_session)
):
    """
    💡 [情节规划] 获取下一章节的写作建议
    
    基于当前故事状态，提供下一章节的具体写作建议和情节方向。
    """
    log_info("情节规划", "获取下一章建议", 
             故事ID=story_id, 
             当前章节=current_chapter)
    
    try:
        # 获取世界知识图谱数据
        world_graph = await get_world_graph_data(story_id, db)
        
        # 生成快速建议（只生成1个路径用于建议）
        result = await planner.generate_plot_paths(
            world_graph=world_graph,
            current_chapter=current_chapter,
            target_paths=1
        )
        
        # 构建响应
        response = PlotSuggestionsResponse(
            story_id=story_id,
            current_chapter=current_chapter,
            suggestions=result.next_chapter_suggestions[:suggestion_count],
            pacing_advice=result.pacing_recommendations,
            character_focus_suggestions=[
                f"重点发展{entity.name}的角色弧线" 
                for entity in world_graph.entities[:2]
                if entity.type.value == "CHARACTER"
            ],
            conflict_development_tips=[
                "考虑引入新的冲突元素",
                "深化现有角色关系的复杂性"
            ],
            world_building_opportunities=[
                "探索新的场景设定",
                "丰富世界观的细节描述"
            ],
            confidence_level=result.analysis_confidence,
            generation_timestamp=result.analysis_timestamp
        )
        
        log_info("情节规划", "下一章建议生成成功",
                故事ID=story_id,
                建议数量=len(response.suggestions))
        
        return response
        
    except Exception as e:
        log_error("情节规划", "获取下一章建议失败", 
                 故事ID=story_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"获取下一章建议失败: {str(e)}"
        )
