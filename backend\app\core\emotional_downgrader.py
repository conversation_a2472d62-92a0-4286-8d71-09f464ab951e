"""
🎭 [情感降级] 情感表达降级器模块

让过于丰富和准确的情感描述变得更加真实和不充分，
模拟人类在表达复杂情感时的词汇贫乏和表达局限性，
打破AI过度精确的情感描述问题。

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import re
import random
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass
from enum import Enum

from app.core.config import log_info, log_debug, log_error


class InarticulationMode(Enum):
    """表达不充分模式枚举"""
    VOCABULARY_POOR = "词汇贫乏"
    EMOTIONAL_AVOIDANCE = "情感回避"
    TOPIC_SHIFTING = "转移话题"
    BODY_SUBSTITUTION = "身体代替"
    VAGUE_EXPRESSION = "模糊表达"


@dataclass
class DowngradePattern:
    """降级模式数据类"""
    mode: InarticulationMode
    trigger_patterns: List[str]  # 触发模式的关键词
    replacement_phrases: List[str]  # 替换短语
    probability: float  # 应用概率
    context_requirements: List[str]  # 上下文要求


@dataclass
class DowngradeResult:
    """降级结果数据类"""
    downgraded_text: str
    original_expressions: List[str]
    simplified_expressions: List[str]
    applied_modes: List[str]
    processing_details: Dict[str, Any]


class OverDescriptionDetector:
    """🔍 过度描述检测器 - 识别AI典型的过度精确情感表达"""
    
    def __init__(self):
        """初始化过度描述检测器"""
        self.ai_patterns = self._initialize_ai_patterns()
        log_debug("情感降级", "过度描述检测器初始化完成", 
                 AI模式数=len(self.ai_patterns))
    
    def _initialize_ai_patterns(self) -> Dict[str, List[str]]:
        """初始化AI典型的过度描述模式"""
        
        patterns = {
            "过度复杂情感": [
                "复杂的情感", "交织在一起", "五味杂陈", "百感交集",
                "情感的漩涡", "情绪的波澜", "内心的挣扎", "矛盾的心情",
                "难以名状的", "说不出的", "无法言喻的", "千头万绪"
            ],
            "过度精确描述": [
                "精确地感受到", "清晰地意识到", "准确地捕捉到", "完美地理解",
                "深刻地体会到", "真切地感受", "强烈地意识到", "敏锐地察觉"
            ],
            "过度文学化": [
                "如潮水般涌来", "像刀子一样", "仿佛被雷击", "犹如晴天霹雳",
                "宛如", "恰似", "好比", "正如", "仿佛置身于", "如同身处"
            ],
            "过度心理分析": [
                "内心深处", "潜意识里", "心理层面", "精神世界", "意识深处",
                "心灵深处", "灵魂深处", "思维深处", "认知层面", "情感层面"
            ],
            "过度完整表达": [
                "完整地表达", "全面地描述", "详细地阐述", "充分地说明",
                "彻底地理解", "完全地掌握", "深入地分析", "系统地梳理"
            ]
        }
        
        log_debug("情感降级", "AI过度描述模式初始化完成", 模式分类数=len(patterns))
        return patterns
    
    def detect_over_description(self, text: str) -> Dict[str, Any]:
        """
        🔍 检测文本中的过度描述
        
        Args:
            text: 输入文本
            
        Returns:
            Dict[str, Any]: 检测结果
        """
        log_debug("情感降级", "开始检测过度描述", 文本长度=len(text))
        
        detected_patterns = {}
        total_score = 0
        
        for pattern_type, keywords in self.ai_patterns.items():
            matches = []
            score = 0
            
            for keyword in keywords:
                if keyword in text:
                    matches.append(keyword)
                    score += 1
            
            if matches:
                detected_patterns[pattern_type] = {
                    "matches": matches,
                    "score": score,
                    "severity": self._calculate_severity(score, len(keywords))
                }
                total_score += score
        
        # 计算整体过度描述程度
        over_description_level = self._calculate_over_description_level(total_score, len(text))
        
        result = {
            "over_description_level": over_description_level,
            "total_score": total_score,
            "detected_patterns": detected_patterns,
            "needs_downgrade": over_description_level > 0.3,
            "text_length": len(text)
        }
        
        log_info("情感降级", "过度描述检测完成", 
                过度描述程度=f"{over_description_level:.2f}",
                检测到模式数=len(detected_patterns),
                需要降级=result["needs_downgrade"])
        
        return result
    
    def _calculate_severity(self, score: int, total_keywords: int) -> str:
        """计算严重程度"""
        ratio = score / total_keywords if total_keywords > 0 else 0
        
        if ratio >= 0.5:
            return "严重"
        elif ratio >= 0.3:
            return "中等"
        elif ratio >= 0.1:
            return "轻微"
        else:
            return "无"
    
    def _calculate_over_description_level(self, total_score: int, text_length: int) -> float:
        """计算整体过度描述程度"""
        if text_length == 0:
            return 0.0
        
        # 基于分数密度计算
        density = total_score / (text_length / 100)  # 每100字符的分数
        
        # 归一化到0-1范围
        return min(1.0, density / 5.0)


class EmotionalInarticulationSystem:
    """🗣️ 情感表达不充分系统 - 模拟人类表达的局限性和不完整性"""
    
    def __init__(self):
        """初始化情感表达不充分系统"""
        self.downgrade_patterns = self._initialize_downgrade_patterns()
        self.detector = OverDescriptionDetector()
        log_debug("情感降级", "情感表达不充分系统初始化完成", 
                 降级模式数=len(self.downgrade_patterns))
    
    def _initialize_downgrade_patterns(self) -> Dict[InarticulationMode, DowngradePattern]:
        """初始化各种表达不充分模式"""
        
        patterns = {
            InarticulationMode.VOCABULARY_POOR: DowngradePattern(
                mode=InarticulationMode.VOCABULARY_POOR,
                trigger_patterns=[
                    "复杂的情感", "难以形容", "说不出", "无法言喻", "千头万绪",
                    "五味杂陈", "百感交集", "情感交织", "心情复杂"
                ],
                replacement_phrases=[
                    "就是...", "反正...", "算了", "怎么说呢", "说不上来",
                    "不知道怎么说", "就那样吧", "挺复杂的", "一言难尽",
                    "说不清楚", "反正就是", "总之", "就是感觉"
                ],
                probability=0.8,
                context_requirements=["情感描述", "心理活动"]
            ),
            
            InarticulationMode.EMOTIONAL_AVOIDANCE: DowngradePattern(
                mode=InarticulationMode.EMOTIONAL_AVOIDANCE,
                trigger_patterns=[
                    "愤怒", "悲伤", "失望", "痛苦", "绝望", "恐惧", "焦虑",
                    "强烈的", "深深的", "巨大的", "无比的", "极度的"
                ],
                replacement_phrases=[
                    "还行吧", "没事", "无所谓", "算了", "不说了",
                    "没什么", "还好", "一般般", "不重要", "随便",
                    "不想说", "懒得说", "没意思", "不提了"
                ],
                probability=0.6,
                context_requirements=["负面情绪", "情感表达"]
            ),
            
            InarticulationMode.TOPIC_SHIFTING: DowngradePattern(
                mode=InarticulationMode.TOPIC_SHIFTING,
                trigger_patterns=[
                    "内心", "心里", "感受", "情感", "心情", "想法",
                    "觉得", "认为", "以为", "心中", "脑海"
                ],
                replacement_phrases=[
                    "对了", "说起来", "不说这个了", "换个话题",
                    "算了不提了", "说别的吧", "不聊这个",
                    "话说回来", "顺便说一下", "突然想到"
                ],
                probability=0.4,
                context_requirements=["对话", "内心独白"]
            ),
            
            InarticulationMode.BODY_SUBSTITUTION: DowngradePattern(
                mode=InarticulationMode.BODY_SUBSTITUTION,
                trigger_patterns=[
                    "感到", "觉得", "意识到", "体会到", "感受到",
                    "心中", "内心", "精神", "情绪", "心情"
                ],
                replacement_phrases=[
                    "叹了口气", "摇摇头", "耸耸肩", "皱了皱眉",
                    "揉了揉太阳穴", "深吸一口气", "闭上眼睛",
                    "挠了挠头", "咬了咬嘴唇", "握紧拳头",
                    "低下头", "转过身", "看向别处"
                ],
                probability=0.7,
                context_requirements=["情感表达", "心理描述"]
            ),
            
            InarticulationMode.VAGUE_EXPRESSION: DowngradePattern(
                mode=InarticulationMode.VAGUE_EXPRESSION,
                trigger_patterns=[
                    "精确地", "清晰地", "准确地", "完美地", "深刻地",
                    "强烈地", "敏锐地", "真切地", "完整地", "详细地"
                ],
                replacement_phrases=[
                    "好像", "似乎", "大概", "可能", "也许",
                    "差不多", "应该是", "感觉像", "有点",
                    "不太确定", "说不准", "模模糊糊", "隐约"
                ],
                probability=0.9,
                context_requirements=["描述性语言", "感知描述"]
            )
        }
        
        log_debug("情感降级", "表达不充分模式初始化完成", 模式数量=len(patterns))
        return patterns

    def make_expression_inadequate(
        self,
        text: str,
        intensity: float = 0.7,
        target_modes: Optional[List[InarticulationMode]] = None
    ) -> DowngradeResult:
        """
        🎭 让情感表达变得不充分和真实

        Args:
            text: 输入文本
            intensity: 降级强度 (0.0-1.0)
            target_modes: 指定的降级模式，如果为None则自动选择

        Returns:
            DowngradeResult: 降级结果
        """
        log_debug("情感降级", "开始情感表达降级",
                 文本长度=len(text), 强度=intensity)

        # 首先检测是否需要降级
        detection_result = self.detector.detect_over_description(text)

        # 如果指定了特定模式，即使检测器认为不需要降级也要处理
        if not detection_result["needs_downgrade"] and intensity < 0.5 and target_modes is None:
            log_info("情感降级", "文本无需降级", 过度描述程度=detection_result["over_description_level"])
            return DowngradeResult(
                downgraded_text=text,
                original_expressions=[],
                simplified_expressions=[],
                applied_modes=[],
                processing_details={"skipped": True, "reason": "无需降级"}
            )

        # 应用降级处理
        processed_text = text
        original_expressions = []
        simplified_expressions = []
        applied_modes = []

        # 确定要应用的模式
        if target_modes is None:
            target_modes = self._select_appropriate_modes(text, detection_result, intensity)

        # 按优先级应用各种降级模式
        for mode in target_modes:
            if mode in self.downgrade_patterns:
                pattern = self.downgrade_patterns[mode]

                # 检查上下文要求
                if self._check_context_requirements(processed_text, pattern.context_requirements):
                    # 应用降级
                    result = self._apply_downgrade_pattern(processed_text, pattern, intensity)

                    if result["modified"]:
                        processed_text = result["text"]
                        original_expressions.extend(result["original"])
                        simplified_expressions.extend(result["simplified"])
                        applied_modes.append(mode.value)

                        log_debug("情感降级", f"成功应用{mode.value}模式",
                                 修改数量=len(result["original"]))

        # 构建结果
        result = DowngradeResult(
            downgraded_text=processed_text,
            original_expressions=original_expressions,
            simplified_expressions=simplified_expressions,
            applied_modes=applied_modes,
            processing_details={
                "original_length": len(text),
                "processed_length": len(processed_text),
                "compression_ratio": 1 - len(processed_text) / len(text) if len(text) > 0 else 0,
                "detection_result": detection_result,
                "intensity": intensity,
                "modes_applied": len(applied_modes)
            }
        )

        log_info("情感降级", "情感表达降级完成",
                应用模式数=len(applied_modes),
                原始长度=len(text),
                处理后长度=len(processed_text),
                压缩比例=f"{result.processing_details['compression_ratio']:.2%}")

        return result

    def _select_appropriate_modes(
        self,
        text: str,
        detection_result: Dict[str, Any],
        intensity: float
    ) -> List[InarticulationMode]:
        """根据文本特征和检测结果选择合适的降级模式"""

        selected_modes = []

        # 根据检测到的过度描述类型选择对应模式
        detected_patterns = detection_result.get("detected_patterns", {})

        if "过度复杂情感" in detected_patterns:
            selected_modes.append(InarticulationMode.VOCABULARY_POOR)

        if "过度精确描述" in detected_patterns:
            selected_modes.append(InarticulationMode.VAGUE_EXPRESSION)

        if "过度文学化" in detected_patterns or "过度心理分析" in detected_patterns:
            selected_modes.append(InarticulationMode.BODY_SUBSTITUTION)

        # 根据强度添加额外模式
        if intensity > 0.6:
            if InarticulationMode.EMOTIONAL_AVOIDANCE not in selected_modes:
                selected_modes.append(InarticulationMode.EMOTIONAL_AVOIDANCE)

        if intensity > 0.8:
            if InarticulationMode.TOPIC_SHIFTING not in selected_modes:
                selected_modes.append(InarticulationMode.TOPIC_SHIFTING)

        # 如果没有选择任何模式，使用默认模式
        if not selected_modes:
            selected_modes = [InarticulationMode.VOCABULARY_POOR, InarticulationMode.VAGUE_EXPRESSION]

        log_debug("情感降级", "选择降级模式",
                 模式数量=len(selected_modes),
                 模式列表=[mode.value for mode in selected_modes])

        return selected_modes

    def _check_context_requirements(self, text: str, requirements: List[str]) -> bool:
        """检查文本是否满足上下文要求"""
        if not requirements:
            return True

        text_lower = text.lower()

        # 检查是否包含任何要求的上下文
        for requirement in requirements:
            if requirement == "情感描述":
                if any(word in text_lower for word in ["感到", "觉得", "情感", "心情", "感受"]):
                    return True
            elif requirement == "心理活动":
                if any(word in text_lower for word in ["想", "认为", "以为", "心里", "内心"]):
                    return True
            elif requirement == "心理描述":
                if any(word in text_lower for word in ["内心", "心中", "精神", "情绪", "心情", "意识", "感受", "察觉"]):
                    return True
            elif requirement == "负面情绪":
                if any(word in text_lower for word in ["愤怒", "悲伤", "失望", "痛苦", "焦虑"]):
                    return True
            elif requirement == "对话":
                if any(word in text_lower for word in ["说", "道", "问", "答", "回"]):
                    return True
            elif requirement == "内心独白":
                if any(word in text_lower for word in ["心想", "暗想", "想到", "觉得"]):
                    return True
            elif requirement == "描述性语言":
                if any(word in text_lower for word in ["地", "的", "着", "了"]):
                    return True
            elif requirement == "感知描述":
                if any(word in text_lower for word in ["看到", "听到", "感受", "察觉", "意识"]):
                    return True

        return False

    def _apply_downgrade_pattern(
        self,
        text: str,
        pattern: DowngradePattern,
        intensity: float
    ) -> Dict[str, Any]:
        """应用特定的降级模式"""

        modified = False
        original_expressions = []
        simplified_expressions = []
        processed_text = text

        # 根据强度调整应用概率
        adjusted_probability = pattern.probability * intensity

        # 查找并替换触发模式
        for trigger in pattern.trigger_patterns:
            if trigger in processed_text:
                # 决定是否应用替换
                if random.random() < adjusted_probability:
                    # 随机选择替换短语
                    replacement = random.choice(pattern.replacement_phrases)

                    # 执行替换
                    if pattern.mode == InarticulationMode.BODY_SUBSTITUTION:
                        # 身体代替模式：替换整个情感表达
                        processed_text = self._replace_with_body_language(
                            processed_text, trigger, replacement
                        )
                    elif pattern.mode == InarticulationMode.TOPIC_SHIFTING:
                        # 转移话题模式：在句子后添加转移语句
                        processed_text = self._add_topic_shift(
                            processed_text, trigger, replacement
                        )
                    else:
                        # 其他模式：直接替换
                        processed_text = processed_text.replace(trigger, replacement, 1)

                    original_expressions.append(trigger)
                    simplified_expressions.append(replacement)
                    modified = True

                    log_debug("情感降级", f"应用{pattern.mode.value}",
                             原表达=trigger, 新表达=replacement)

        return {
            "modified": modified,
            "text": processed_text,
            "original": original_expressions,
            "simplified": simplified_expressions
        }

    def _replace_with_body_language(self, text: str, trigger: str, replacement: str) -> str:
        """用身体语言替换情感表达"""
        # 查找包含触发词的句子
        sentences = re.split(r'[。！？]', text)
        processed_sentences = []

        for sentence in sentences:
            if trigger in sentence:
                # 替换为身体语言
                new_sentence = replacement
                processed_sentences.append(new_sentence)
            else:
                processed_sentences.append(sentence)

        return "。".join([s for s in processed_sentences if s.strip()])

    def _add_topic_shift(self, text: str, trigger: str, replacement: str) -> str:
        """添加转移话题的表达"""
        # 在包含触发词的句子后添加转移话题
        sentences = re.split(r'[。！？]', text)
        processed_sentences = []

        for sentence in sentences:
            processed_sentences.append(sentence)
            if trigger in sentence:
                # 添加转移话题
                processed_sentences.append(replacement)

        return "。".join([s for s in processed_sentences if s.strip()])

    def analyze_emotional_expression(self, text: str) -> Dict[str, Any]:
        """
        📊 分析文本的情感表达特征

        Args:
            text: 输入文本

        Returns:
            Dict[str, Any]: 分析结果
        """
        log_debug("情感降级", "开始分析情感表达", 文本长度=len(text))

        # 检测过度描述
        detection_result = self.detector.detect_over_description(text)

        # 统计各种表达模式的出现频率
        expression_stats = {}

        for mode, pattern in self.downgrade_patterns.items():
            count = 0
            found_triggers = []

            for trigger in pattern.trigger_patterns:
                if trigger in text:
                    count += 1
                    found_triggers.append(trigger)

            if count > 0:
                expression_stats[mode.value] = {
                    "count": count,
                    "triggers": found_triggers,
                    "density": count / len(text) * 100  # 每100字符的密度
                }

        result = {
            "over_description_analysis": detection_result,
            "expression_patterns": expression_stats,
            "total_triggers": sum(stats["count"] for stats in expression_stats.values()),
            "complexity_score": self._calculate_complexity_score(detection_result, expression_stats),
            "recommendation": self._generate_recommendation(detection_result, expression_stats)
        }

        log_info("情感降级", "情感表达分析完成",
                触发器总数=result["total_triggers"],
                复杂度评分=f"{result['complexity_score']:.2f}",
                建议=result["recommendation"])

        return result

    def _calculate_complexity_score(
        self,
        detection_result: Dict[str, Any],
        expression_stats: Dict[str, Any]
    ) -> float:
        """计算情感表达复杂度评分"""

        # 基础分数来自过度描述检测
        base_score = detection_result.get("over_description_level", 0) * 50

        # 表达模式密度分数
        pattern_score = 0
        for stats in expression_stats.values():
            pattern_score += stats["density"] * 2

        # 综合评分（0-100）
        total_score = min(100, base_score + pattern_score)

        return total_score

    def _generate_recommendation(
        self,
        detection_result: Dict[str, Any],
        expression_stats: Dict[str, Any]
    ) -> str:
        """生成降级建议"""

        over_description_level = detection_result.get("over_description_level", 0)
        total_triggers = sum(stats["count"] for stats in expression_stats.values())

        if over_description_level > 0.7:
            return "强烈建议进行情感表达降级，过度描述严重"
        elif over_description_level > 0.5:
            return "建议进行中等强度的情感表达降级"
        elif over_description_level > 0.3:
            return "建议进行轻度的情感表达降级"
        elif total_triggers > 5:
            return "表达模式较多，建议适度简化"
        else:
            return "情感表达相对自然，可选择性优化"


# 🏭 工厂函数和便捷接口

def create_emotional_downgrader() -> EmotionalInarticulationSystem:
    """
    🏭 创建情感表达降级器实例

    Returns:
        EmotionalInarticulationSystem: 情感表达降级器实例
    """
    log_debug("情感降级", "创建情感表达降级器实例")
    return EmotionalInarticulationSystem()


def quick_emotional_downgrade(
    text: str,
    intensity: float = 0.7,
    mode: Optional[str] = None
) -> str:
    """
    🚀 快速情感表达降级

    Args:
        text: 输入文本
        intensity: 降级强度 (0.0-1.0)
        mode: 指定模式 ("词汇贫乏", "情感回避", "转移话题", "身体代替", "模糊表达")

    Returns:
        str: 降级后的文本
    """
    log_debug("情感降级", "快速情感表达降级",
             文本长度=len(text), 强度=intensity, 模式=mode)

    downgrader = create_emotional_downgrader()

    # 转换模式字符串为枚举
    target_modes = None
    if mode:
        mode_mapping = {
            "词汇贫乏": InarticulationMode.VOCABULARY_POOR,
            "情感回避": InarticulationMode.EMOTIONAL_AVOIDANCE,
            "转移话题": InarticulationMode.TOPIC_SHIFTING,
            "身体代替": InarticulationMode.BODY_SUBSTITUTION,
            "模糊表达": InarticulationMode.VAGUE_EXPRESSION
        }
        if mode in mode_mapping:
            target_modes = [mode_mapping[mode]]

    result = downgrader.make_expression_inadequate(text, intensity, target_modes)

    log_info("情感降级", "快速降级完成",
            原始长度=len(text),
            处理后长度=len(result.downgraded_text),
            应用模式=result.applied_modes)

    return result.downgraded_text


def analyze_text_emotional_complexity(text: str) -> Dict[str, Any]:
    """
    📊 分析文本情感表达复杂度

    Args:
        text: 输入文本

    Returns:
        Dict[str, Any]: 分析结果
    """
    log_debug("情感降级", "分析文本情感表达复杂度", 文本长度=len(text))

    downgrader = create_emotional_downgrader()
    analysis = downgrader.analyze_emotional_expression(text)

    log_info("情感降级", "复杂度分析完成",
            复杂度评分=analysis["complexity_score"],
            建议=analysis["recommendation"])

    return analysis


def detect_ai_emotional_patterns(text: str) -> Dict[str, Any]:
    """
    🔍 检测AI情感表达模式

    Args:
        text: 输入文本

    Returns:
        Dict[str, Any]: 检测结果
    """
    log_debug("情感降级", "检测AI情感表达模式", 文本长度=len(text))

    detector = OverDescriptionDetector()
    result = detector.detect_over_description(text)

    log_info("情感降级", "AI模式检测完成",
            过度描述程度=f"{result['over_description_level']:.2f}",
            需要降级=result["needs_downgrade"])

    return result


def batch_emotional_downgrade(
    texts: List[str],
    intensity: float = 0.7,
    preserve_order: bool = True
) -> List[DowngradeResult]:
    """
    📦 批量情感表达降级

    Args:
        texts: 文本列表
        intensity: 降级强度
        preserve_order: 是否保持顺序

    Returns:
        List[DowngradeResult]: 降级结果列表
    """
    log_debug("情感降级", "开始批量情感表达降级",
             文本数量=len(texts), 强度=intensity)

    downgrader = create_emotional_downgrader()
    results = []

    for i, text in enumerate(texts):
        try:
            result = downgrader.make_expression_inadequate(text, intensity)
            results.append(result)

            log_debug("情感降级", f"处理文本{i+1}",
                     原始长度=len(text),
                     处理后长度=len(result.downgraded_text))

        except Exception as e:
            log_error("情感降级", f"处理文本{i+1}失败", error=e)
            # 创建失败结果
            error_result = DowngradeResult(
                downgraded_text=text,
                original_expressions=[],
                simplified_expressions=[],
                applied_modes=[],
                processing_details={"error": str(e), "failed": True}
            )
            results.append(error_result)

    log_info("情感降级", "批量降级完成",
            处理数量=len(texts),
            成功数量=len([r for r in results if not r.processing_details.get("failed", False)]))

    return results


# 🎯 预设降级配置

PRESET_CONFIGURATIONS = {
    "轻度自然化": {
        "intensity": 0.3,
        "modes": [InarticulationMode.VAGUE_EXPRESSION],
        "description": "轻微模糊化过于精确的表达"
    },
    "中度人性化": {
        "intensity": 0.6,
        "modes": [InarticulationMode.VOCABULARY_POOR, InarticulationMode.VAGUE_EXPRESSION],
        "description": "适度简化复杂情感表达，增加模糊性"
    },
    "重度去AI化": {
        "intensity": 0.9,
        "modes": [
            InarticulationMode.VOCABULARY_POOR,
            InarticulationMode.EMOTIONAL_AVOIDANCE,
            InarticulationMode.BODY_SUBSTITUTION,
            InarticulationMode.VAGUE_EXPRESSION
        ],
        "description": "全面降级AI特征，最大化人性化表达"
    },
    "对话优化": {
        "intensity": 0.7,
        "modes": [
            InarticulationMode.VOCABULARY_POOR,
            InarticulationMode.TOPIC_SHIFTING,
            InarticulationMode.EMOTIONAL_AVOIDANCE
        ],
        "description": "专门优化对话中的情感表达"
    },
    "内心独白优化": {
        "intensity": 0.8,
        "modes": [
            InarticulationMode.VOCABULARY_POOR,
            InarticulationMode.BODY_SUBSTITUTION,
            InarticulationMode.VAGUE_EXPRESSION
        ],
        "description": "优化内心独白的真实感"
    }
}


def apply_preset_downgrade(text: str, preset_name: str) -> DowngradeResult:
    """
    🎯 应用预设降级配置

    Args:
        text: 输入文本
        preset_name: 预设配置名称

    Returns:
        DowngradeResult: 降级结果
    """
    if preset_name not in PRESET_CONFIGURATIONS:
        available_presets = list(PRESET_CONFIGURATIONS.keys())
        log_error("情感降级", f"未知预设配置: {preset_name}", 可用配置=available_presets)
        raise ValueError(f"未知预设配置: {preset_name}，可用配置: {available_presets}")

    config = PRESET_CONFIGURATIONS[preset_name]

    log_debug("情感降级", f"应用预设配置: {preset_name}",
             配置描述=config["description"],
             强度=config["intensity"])

    downgrader = create_emotional_downgrader()
    result = downgrader.make_expression_inadequate(
        text,
        intensity=config["intensity"],
        target_modes=config["modes"]
    )

    log_info("情感降级", f"预设配置{preset_name}应用完成",
            应用模式数=len(result.applied_modes))

    return result
