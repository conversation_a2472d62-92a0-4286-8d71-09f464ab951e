"""
🌐 [世界图谱] 世界知识图谱客户端
提供对世界图谱API的访问接口，用于RAG系统集成
"""

from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.config import log_info, log_error, log_debug
from app.models.story_bible import StoryBible
from app.models.world_graph import Entity, EntityRelationship, RelationshipStatus
from app.schemas.world_graph import (
    WorldGraphResponse, 
    EntityResponse, 
    RelationshipResponse
)


class WorldGraphClient:
    """
    🌐 [世界图谱] 世界知识图谱客户端
    
    为RAG系统提供世界图谱数据访问接口，
    直接从数据库查询实体和关系信息
    """
    
    def __init__(self, db_session: AsyncSession):
        """
        初始化世界图谱客户端
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def get_world_graph(
        self,
        story_id: str,
        include_inactive: bool = False
    ) -> WorldGraphResponse:
        """
        🌐 [世界图谱] 获取完整的世界知识图谱
        
        Args:
            story_id: 故事ID
            include_inactive: 是否包含非活跃的实体和关系
            
        Returns:
            WorldGraphResponse: 完整的世界图谱数据
            
        Raises:
            ValueError: 当故事不存在时
        """
        log_debug("世界图谱客户端", "开始获取世界图谱",
                 story_id=story_id,
                 include_inactive=include_inactive)
        
        try:
            # 验证故事是否存在
            story_result = await self.db_session.execute(
                select(StoryBible).filter(StoryBible.id == story_id)
            )
            story = story_result.scalar_one_or_none()
            if not story:
                raise ValueError(f"故事 {story_id} 不存在")
            
            # 获取所有实体
            entities_query = select(Entity).filter(Entity.story_id == story_id)
            if not include_inactive:
                entities_query = entities_query.filter(Entity.is_active == True)
            
            entities_result = await self.db_session.execute(entities_query)
            entities = entities_result.scalars().all()
            
            # 获取所有关系
            relationships_query = select(EntityRelationship).join(
                Entity, EntityRelationship.source_entity_id == Entity.id
            ).filter(Entity.story_id == story_id)
            
            if not include_inactive:
                relationships_query = relationships_query.filter(
                    EntityRelationship.status == RelationshipStatus.ACTIVE
                )
            
            relationships_result = await self.db_session.execute(relationships_query)
            relationships = relationships_result.scalars().all()
            
            # 统计信息
            entity_stats = {}
            for entity in entities:
                entity_type = entity.type.value
                entity_stats[entity_type] = entity_stats.get(entity_type, 0) + 1
            
            relationship_stats = {}
            for relationship in relationships:
                rel_type = relationship.relationship_type
                relationship_stats[rel_type] = relationship_stats.get(rel_type, 0) + 1
            
            # 构建响应
            response = WorldGraphResponse(
                story_id=story_id,
                story_title=story.title,
                entities=[EntityResponse.from_attributes(entity) for entity in entities],
                relationships=[RelationshipResponse.from_attributes(rel) for rel in relationships],
                entity_count=len(entities),
                relationship_count=len(relationships),
                entity_stats=entity_stats,
                relationship_stats=relationship_stats
            )
            
            log_info("世界图谱客户端", "世界图谱获取成功",
                    story_id=story_id,
                    total_entities=len(entities),
                    total_relationships=len(relationships),
                    entity_stats=entity_stats,
                    relationship_stats=relationship_stats)
            
            return response
            
        except ValueError:
            raise
        except Exception as e:
            log_error("世界图谱客户端", "获取世界图谱失败", error=e, story_id=story_id)
            raise
    
    async def get_entities_by_names(
        self,
        story_id: str,
        entity_names: list[str]
    ) -> list[EntityResponse]:
        """
        🎯 [查询] 根据实体名称批量查询实体
        
        Args:
            story_id: 故事ID
            entity_names: 实体名称列表
            
        Returns:
            list[EntityResponse]: 匹配的实体列表
        """
        if not entity_names:
            return []
        
        try:
            query = select(Entity).filter(
                Entity.story_id == story_id,
                Entity.name.in_(entity_names),
                Entity.is_active == True
            )
            
            result = await self.db_session.execute(query)
            entities = result.scalars().all()
            
            return [EntityResponse.from_attributes(entity) for entity in entities]
            
        except Exception as e:
            log_error("世界图谱客户端", "批量查询实体失败", 
                     error=e, story_id=story_id, entity_names=entity_names)
            return []
    
    async def get_relationships_for_entities(
        self,
        entity_ids: list[str]
    ) -> list[RelationshipResponse]:
        """
        🔗 [查询] 获取指定实体的所有关系
        
        Args:
            entity_ids: 实体ID列表
            
        Returns:
            list[RelationshipResponse]: 相关关系列表
        """
        if not entity_ids:
            return []
        
        try:
            query = select(EntityRelationship).filter(
                (EntityRelationship.source_entity_id.in_(entity_ids)) |
                (EntityRelationship.target_entity_id.in_(entity_ids)),
                EntityRelationship.status == RelationshipStatus.ACTIVE
            )
            
            result = await self.db_session.execute(query)
            relationships = result.scalars().all()
            
            return [RelationshipResponse.from_attributes(rel) for rel in relationships]
            
        except Exception as e:
            log_error("世界图谱客户端", "查询实体关系失败", 
                     error=e, entity_ids=entity_ids)
            return []


def create_world_graph_client(db_session: AsyncSession) -> WorldGraphClient:
    """
    🏭 [工厂] 创建世界图谱客户端实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        WorldGraphClient: 世界图谱客户端实例
    """
    return WorldGraphClient(db_session)
