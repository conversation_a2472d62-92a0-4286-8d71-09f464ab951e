"""
🧠 [向量存储测试] 向量数据库服务测试模块

测试ChromaDB和sentence-transformers集成的向量存储功能：
1. 向量数据库初始化测试
2. 文本嵌入和向量化测试
3. 记忆添加和检索测试
4. 语义相似度搜索测试
5. 多条件过滤查询测试

作者: 文心小说后端服务系统
创建时间: 2025-08-02
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from typing import List

from app.services.vector_store import (
    VectorStoreManager, MemoryDocument, SearchResult,
    get_vector_store, close_vector_store
)


class TestVectorStoreManager:
    """向量存储管理器测试类"""
    
    @pytest.fixture
    async def temp_vector_store(self):
        """创建临时向量存储管理器"""
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 创建向量存储管理器
            manager = VectorStoreManager(persist_directory=temp_dir)
            await manager.initialize()
            yield manager
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_vector_store_initialization(self, temp_vector_store):
        """测试向量存储管理器初始化"""
        manager = temp_vector_store
        
        # 验证初始化状态
        assert manager.client is not None, "ChromaDB客户端应该已初始化"
        assert manager.collection is not None, "集合应该已创建"
        assert manager.embedding_model is not None, "嵌入模型应该已加载"
        
        # 验证嵌入模型维度
        embedding_dim = manager.embedding_model.get_sentence_embedding_dimension()
        assert embedding_dim > 0, "嵌入模型维度应该大于0"
        print(f"✅ 嵌入模型维度: {embedding_dim}")
    
    @pytest.mark.asyncio
    async def test_text_embedding(self, temp_vector_store):
        """测试文本向量化功能"""
        manager = temp_vector_store
        
        # 测试中文文本向量化
        test_text = "张三在月黑风高的夜晚，悄悄地走向了那座古老的城堡。"
        embedding = await manager.embed_text(test_text)
        
        # 验证向量结果
        assert isinstance(embedding, list), "嵌入结果应该是列表"
        assert len(embedding) > 0, "嵌入向量不应为空"
        assert all(isinstance(x, float) for x in embedding), "向量元素应该是浮点数"
        
        print(f"✅ 文本向量化成功，维度: {len(embedding)}")
        print(f"   原文: {test_text}")
        print(f"   向量前5维: {embedding[:5]}")
    
    @pytest.mark.asyncio
    async def test_add_memory(self, temp_vector_store):
        """测试添加记忆功能"""
        manager = temp_vector_store
        
        # 创建测试记忆文档
        memory = MemoryDocument(
            id="test_memory_001",
            content="李四发现了一个神秘的宝箱，里面装满了闪闪发光的金币。",
            summary="李四发现神秘宝箱",
            story_id=1,
            chapter_id=1,
            memory_type="plot",
            importance_score=0.8,
            metadata={"location": "古堡地下室", "character": "李四"}
        )
        
        # 添加记忆
        doc_id = await manager.add_memory(memory)
        
        # 验证结果
        assert doc_id == memory.id, "返回的文档ID应该与输入一致"
        
        # 验证集合中的文档数量
        collection_count = manager.collection.count()
        assert collection_count == 1, "集合中应该有1个文档"
        
        print(f"✅ 记忆添加成功，文档ID: {doc_id}")
    
    @pytest.mark.asyncio
    async def test_search_memories(self, temp_vector_store):
        """测试记忆搜索功能"""
        manager = temp_vector_store
        
        # 添加多个测试记忆
        memories = [
            MemoryDocument(
                id="memory_001",
                content="张三在森林中遇到了一只会说话的狐狸，狐狸告诉他一个古老的秘密。",
                summary="张三遇到会说话的狐狸",
                story_id=1,
                chapter_id=1,
                memory_type="character",
                importance_score=0.9
            ),
            MemoryDocument(
                id="memory_002", 
                content="李四在图书馆里找到了一本神秘的魔法书，书页上写满了古老的咒语。",
                summary="李四发现魔法书",
                story_id=1,
                chapter_id=2,
                memory_type="plot",
                importance_score=0.7
            ),
            MemoryDocument(
                id="memory_003",
                content="王五在山洞中发现了一座古老的祭坛，祭坛上刻着奇怪的符号。",
                summary="王五发现古老祭坛",
                story_id=2,
                chapter_id=1,
                memory_type="setting",
                importance_score=0.6
            )
        ]
        
        # 批量添加记忆
        for memory in memories:
            await manager.add_memory(memory)
        
        # 测试基本搜索
        query = "神秘的魔法"
        results = await manager.search_memories(query, n_results=3)
        
        # 验证搜索结果
        assert len(results) > 0, "应该有搜索结果"
        assert all(isinstance(r, SearchResult) for r in results), "结果应该是SearchResult类型"
        
        # 验证相似度分数
        for result in results:
            assert 0 <= result.similarity_score <= 1, "相似度分数应该在0-1之间"
            assert result.distance >= 0, "距离应该非负"
        
        print(f"✅ 基本搜索成功，找到 {len(results)} 个结果")
        for i, result in enumerate(results):
            print(f"   结果{i+1}: {result.document.summary} (相似度: {result.similarity_score:.3f})")
    
    @pytest.mark.asyncio
    async def test_filtered_search(self, temp_vector_store):
        """测试过滤搜索功能"""
        manager = temp_vector_store
        
        # 添加测试数据（复用上面的记忆数据）
        memories = [
            MemoryDocument(
                id="filter_001",
                content="主角在城堡中发现了一个秘密通道。",
                summary="发现秘密通道",
                story_id=1,
                chapter_id=1,
                memory_type="plot",
                importance_score=0.8
            ),
            MemoryDocument(
                id="filter_002",
                content="反派角色露出了邪恶的笑容。",
                summary="反派邪恶笑容",
                story_id=1,
                chapter_id=2,
                memory_type="character",
                importance_score=0.6
            ),
            MemoryDocument(
                id="filter_003",
                content="另一个故事中的英雄拯救了村庄。",
                summary="英雄拯救村庄",
                story_id=2,
                chapter_id=1,
                memory_type="plot",
                importance_score=0.9
            )
        ]
        
        for memory in memories:
            await manager.add_memory(memory)
        
        # 测试按故事ID过滤
        results = await manager.search_memories(
            query="发现",
            n_results=5,
            story_id=1
        )
        
        # 验证过滤结果
        assert len(results) > 0, "应该有过滤结果"
        for result in results:
            assert result.document.story_id == 1, "所有结果应该属于故事1"
        
        print(f"✅ 故事ID过滤搜索成功，找到 {len(results)} 个结果")
        
        # 测试按记忆类型过滤
        results = await manager.search_memories(
            query="角色",
            n_results=5,
            memory_type="character"
        )
        
        # 验证类型过滤
        for result in results:
            assert result.document.memory_type == "character", "所有结果应该是character类型"
        
        print(f"✅ 记忆类型过滤搜索成功，找到 {len(results)} 个结果")
        
        # 测试按重要性分数过滤
        results = await manager.search_memories(
            query="故事",
            n_results=5,
            min_importance=0.7
        )
        
        # 验证重要性过滤
        for result in results:
            assert result.document.importance_score >= 0.7, "所有结果重要性应该>=0.7"
        
        print(f"✅ 重要性分数过滤搜索成功，找到 {len(results)} 个结果")
    
    @pytest.mark.asyncio
    async def test_semantic_similarity(self, temp_vector_store):
        """测试语义相似度功能"""
        manager = temp_vector_store
        
        # 添加语义相关的测试数据
        memories = [
            MemoryDocument(
                id="semantic_001",
                content="小猫在阳光下懒洋洋地睡觉。",
                summary="小猫睡觉",
                story_id=1,
                memory_type="scene"
            ),
            MemoryDocument(
                id="semantic_002",
                content="猫咪在温暖的午后打盹。",
                summary="猫咪打盹",
                story_id=1,
                memory_type="scene"
            ),
            MemoryDocument(
                id="semantic_003",
                content="狗狗在公园里快乐地奔跑。",
                summary="狗狗奔跑",
                story_id=1,
                memory_type="scene"
            )
        ]
        
        for memory in memories:
            await manager.add_memory(memory)
        
        # 搜索语义相似的内容
        query = "猫在休息"
        results = await manager.search_memories(query, n_results=3)
        
        # 验证语义相似度
        assert len(results) >= 2, "应该找到至少2个相关结果"
        
        # 前两个结果应该是关于猫的，相似度较高
        cat_results = [r for r in results if "猫" in r.document.content]
        assert len(cat_results) >= 2, "应该找到至少2个关于猫的结果"
        
        # 验证相似度排序
        similarities = [r.similarity_score for r in results]
        assert similarities == sorted(similarities, reverse=True), "结果应该按相似度降序排列"
        
        print(f"✅ 语义相似度测试成功")
        for i, result in enumerate(results):
            print(f"   结果{i+1}: {result.document.summary} (相似度: {result.similarity_score:.3f})")


class TestVectorStoreIntegration:
    """向量存储集成测试类"""
    
    @pytest.mark.asyncio
    async def test_global_vector_store(self):
        """测试全局向量存储管理器"""
        try:
            # 获取全局实例
            manager = await get_vector_store()
            
            # 验证实例有效性
            assert manager is not None, "全局向量存储管理器不应为空"
            assert manager.client is not None, "客户端应该已初始化"
            assert manager.collection is not None, "集合应该已创建"
            
            # 测试基本功能
            test_memory = MemoryDocument(
                id="global_test_001",
                content="这是一个全局测试记忆。",
                summary="全局测试",
                story_id=999,
                memory_type="test"
            )
            
            doc_id = await manager.add_memory(test_memory)
            assert doc_id == test_memory.id, "文档ID应该匹配"
            
            # 搜索测试
            results = await manager.search_memories("全局测试", n_results=1)
            assert len(results) > 0, "应该找到测试记忆"
            
            print("✅ 全局向量存储管理器测试成功")
            
        finally:
            # 清理资源
            await close_vector_store()


if __name__ == "__main__":
    """直接运行测试"""
    pytest.main([__file__, "-v"])
