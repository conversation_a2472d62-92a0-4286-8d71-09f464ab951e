{"corpus_info": {"name": "AI小说生成语料库", "version": "1.0.0", "description": "用于AI小说生成系统的综合语料库，包含语言风格、情感基因、爽感模式等多种数据", "created_at": "2025-08-05", "updated_at": "2025-08-05", "maintainer": "AI小说工作流系统", "encoding": "utf-8"}, "corpus_structure": {"language_styles": {"description": "语言风格相关语料，包括方言、古文、讽刺等", "files": ["dialect_library.json", "ancient_expressions.json", "sarcasm_patterns.json", "creative_insults.json", "poetry_templates.json", "regional_expressions.json"]}, "emotional_genes": {"description": "情感基因库，包含各种情感的生理反应和感官触发器", "files": ["basic_emotions.json", "complex_emotions.json", "physiological_reactions.json", "sensory_triggers.json", "emotional_transitions.json"]}, "pleasure_patterns": {"description": "网文爽感模式库，包含各种爽点类型和节奏模板", "files": ["face_slapping_patterns.json", "power_fantasy_patterns.json", "upgrade_patterns.json", "romance_patterns.json", "rhythm_templates.json", "emotional_peaks.json"]}, "personality_traits": {"description": "性格特征库，基于星座和心理学的性格驱动数据", "files": ["zodiac_profiles.json", "personality_traits.json", "behavioral_patterns.json", "speech_patterns.json", "interaction_styles.json"]}, "world_knowledge": {"description": "世界知识库，包含各种背景设定和常识数据", "files": ["historical_periods.json", "fantasy_elements.json", "modern_settings.json", "cultural_references.json", "technical_terms.json"]}}, "data_format": {"version": "1.0", "schema_url": "https://github.com/ai-novel-workflow/corpus-schema", "required_fields": ["name", "version", "description", "data"], "optional_fields": ["tags", "usage_contexts", "metadata", "examples"], "encoding": "utf-8", "validation_rules": {"max_file_size_mb": 50, "max_entries_per_file": 10000, "required_metadata": ["source", "quality_score", "usage_frequency"]}}, "loading_config": {"lazy_loading": true, "cache_enabled": true, "cache_ttl_seconds": 3600, "preload_files": ["language_styles/dialect_library.json", "emotional_genes/basic_emotions.json", "pleasure_patterns/rhythm_templates.json"], "hot_reload": true, "backup_enabled": true}, "quality_control": {"validation_enabled": true, "duplicate_detection": true, "quality_threshold": 0.7, "auto_cleanup": false, "review_required": true, "approval_workflow": true}}