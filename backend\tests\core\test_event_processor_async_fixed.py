"""
🧪 [异步测试] 动态事件处理器异步测试（修复版）
使用正确的异步测试模式测试事件处理器
"""

import pytest
import json
import asyncio
from unittest.mock import patch, AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import select

from app.core.event_processor import EventProcessor, get_event_processor
from app.models.story_bible import StoryBible, AIProvider
from app.models.world_graph import Entity, EntityType
from app.core.base import Base
from app.services.zhipu_client import ChatCompletionResponse
from app.core.config import log_debug, log_info


@pytest.mark.asyncio
class TestEventProcessorAsyncFixed:
    """事件处理器异步测试类（修复版）"""
    
    async def create_test_db_session(self):
        """创建测试数据库会话"""
        # 使用内存数据库
        db_url = "sqlite+aiosqlite:///:memory:"

        # 创建异步引擎，添加更多配置以避免greenlet问题
        engine = create_async_engine(
            db_url,
            echo=False,
            connect_args={
                "check_same_thread": False,
                "timeout": 30
            },
            pool_pre_ping=True,
            pool_recycle=300
        )

        # 创建会话工厂
        async_session_factory = async_sessionmaker(
            bind=engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=True,
            autocommit=False
        )

        # 创建所有表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        # 返回会话
        session = async_session_factory()
        return session, engine
    
    async def test_find_entity_by_name_async(self):
        """测试异步查找实体"""
        log_info("异步测试", "开始测试异步查找实体")
        
        session, engine = await self.create_test_db_session()
        
        try:
            # 创建测试故事
            story = StoryBible(
                id="async_test_story",
                title="异步测试小说",
                genre="fantasy",
                theme="测试主题",
                protagonist="张三",
                setting="测试背景",
                plot_outline="测试大纲",
                target_audience="adult",
                ai_provider=AIProvider.ZHIPU
            )
            
            session.add(story)
            await session.commit()
            
            # 创建测试实体
            entity = Entity(
                id="async_test_entity",
                story_id=story.id,
                name="张三",
                type=EntityType.CHARACTER,
                description="测试角色",
                properties={"年龄": 25},
                first_mentioned_chapter=1,
                is_active=True
            )
            
            session.add(entity)
            await session.commit()
            
            # 测试查找实体
            processor = EventProcessor()
            found_entity_id = await processor._find_entity_by_name(
                session, story.id, "张三"
            )
            
            assert found_entity_id == entity.id
            log_info("异步测试", "异步查找实体测试通过", entity_id=found_entity_id)
            
        finally:
            await session.close()
            await engine.dispose()
    
    async def test_create_entity_async(self):
        """测试异步创建实体"""
        log_info("异步测试", "开始测试异步创建实体")
        
        session, engine = await self.create_test_db_session()
        
        try:
            # 创建测试故事
            story = StoryBible(
                id="async_create_story",
                title="异步创建测试小说",
                genre="fantasy",
                theme="测试主题",
                protagonist="主角",
                setting="测试背景",
                plot_outline="测试大纲",
                target_audience="adult",
                ai_provider=AIProvider.ZHIPU
            )
            
            session.add(story)
            await session.commit()
            
            # 测试创建实体的事件数据
            from app.core.event_processor import CreateEntityEventData
            
            event_data = CreateEntityEventData(
                name="李四",
                type="character",
                description="测试角色",
                properties={"年龄": 30},
                first_mentioned_chapter=1
            )
            
            # 测试创建实体
            processor = EventProcessor()
            success = await processor._process_create_entity_event(
                event_data, story.id, session
            )
            
            assert success is True
            
            # 验证实体是否创建成功
            found_entity_id = await processor._find_entity_by_name(
                session, story.id, "李四"
            )
            
            assert found_entity_id is not None
            log_info("异步测试", "异步创建实体测试通过", entity_id=found_entity_id)
            
        finally:
            await session.close()
            await engine.dispose()
    
    async def test_create_relationship_async(self):
        """测试异步创建关系"""
        log_info("异步测试", "开始测试异步创建关系")
        
        session, engine = await self.create_test_db_session()
        
        try:
            # 创建测试故事
            story = StoryBible(
                id="async_relationship_story",
                title="异步关系测试小说",
                genre="fantasy",
                theme="测试主题",
                protagonist="主角",
                setting="测试背景",
                plot_outline="测试大纲",
                target_audience="adult",
                ai_provider=AIProvider.ZHIPU
            )
            
            session.add(story)
            await session.commit()
            
            # 创建两个测试实体
            entity1 = Entity(
                id="async_entity_zhang",
                story_id=story.id,
                name="张三",
                type=EntityType.CHARACTER,
                description="测试角色1",
                properties={"年龄": 25},
                first_mentioned_chapter=1,
                is_active=True
            )
            
            entity2 = Entity(
                id="async_entity_li",
                story_id=story.id,
                name="李四",
                type=EntityType.CHARACTER,
                description="测试角色2",
                properties={"年龄": 30},
                first_mentioned_chapter=1,
                is_active=True
            )
            
            session.add(entity1)
            session.add(entity2)
            await session.commit()
            
            # 测试创建关系的事件数据
            from app.core.event_processor import CreateRelationshipEventData
            
            event_data = CreateRelationshipEventData(
                source_entity="张三",
                target_entity="李四",
                relationship_type="朋友",
                description="测试关系",
                properties={"友谊程度": "深厚"},
                established_chapter=1
            )
            
            # 测试创建关系
            processor = EventProcessor()

            # 先验证实体是否存在
            zhang_id = await processor._find_entity_by_name(session, story.id, "张三")
            li_id = await processor._find_entity_by_name(session, story.id, "李四")

            log_info("异步测试", "实体查找结果",
                    zhang_id=zhang_id, li_id=li_id)

            success = await processor._process_create_relationship_event(
                event_data, story.id, session
            )

            log_info("异步测试", "关系创建结果", success=success)
            assert success is True
            log_info("异步测试", "异步创建关系测试通过")
            
        finally:
            await session.close()
            await engine.dispose()
    
    async def test_process_chapter_events_async(self):
        """测试异步处理章节事件"""
        log_info("异步测试", "开始测试异步处理章节事件")
        
        session, engine = await self.create_test_db_session()
        
        try:
            # 创建测试故事
            story = StoryBible(
                id="async_chapter_story",
                title="异步章节测试小说",
                genre="fantasy",
                theme="测试主题",
                protagonist="张三",
                setting="测试背景",
                plot_outline="测试大纲",
                target_audience="adult",
                ai_provider=AIProvider.ZHIPU
            )
            
            session.add(story)
            await session.commit()
            
            # 创建已存在的实体
            existing_entity = Entity(
                id="async_existing_zhang",
                story_id=story.id,
                name="张三",
                type=EntityType.CHARACTER,
                description="主角",
                properties={"年龄": 25},
                first_mentioned_chapter=1,
                is_active=True
            )
            
            session.add(existing_entity)
            await session.commit()
            
            # Mock AI响应
            mock_ai_response = json.dumps([
                {
                    "event": "create_entity",
                    "data": {
                        "name": "王五",
                        "type": "character",
                        "description": "神秘老者",
                        "properties": {"年龄": 60},
                        "first_mentioned_chapter": 2
                    }
                },
                {
                    "event": "create_relationship",
                    "data": {
                        "source_entity": "王五",
                        "target_entity": "张三",
                        "relationship_type": "师父",
                        "description": "师父关系",
                        "properties": {"关系建立": "第2章"},
                        "established_chapter": 2
                    }
                }
            ])
            
            mock_response = ChatCompletionResponse(
                id="async_chapter_test",
                object="chat.completion",
                created=1234567890,
                model="glm-4.5-flash",
                choices=[{
                    "message": {"content": mock_ai_response},
                    "finish_reason": "stop"
                }],
                usage={"total_tokens": 200}
            )
            
            with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
                mock_client = AsyncMock()
                mock_client.chat_completion.return_value = mock_response
                mock_get_client.return_value = mock_client
                
                # 处理章节事件
                processor = EventProcessor()
                result = await processor.process_chapter_events(
                    chapter_text="张三遇到了神秘老者王五，王五成为了张三的师父。",
                    story_id=story.id,
                    chapter_number=2,
                    db=session
                )
                
                # 验证处理结果
                assert result["story_id"] == story.id
                assert result["chapter_number"] == 2
                assert result["total_events"] == 2
                assert result["processed_events"] == 2
                
                log_info("异步测试", "异步处理章节事件测试通过", result=result)
                
        finally:
            await session.close()
            await engine.dispose()
    
    async def test_singleton_pattern_async(self):
        """测试异步单例模式"""
        processor1 = await get_event_processor()
        processor2 = await get_event_processor()
        
        assert processor1 is processor2
        assert isinstance(processor1, EventProcessor)
        
        log_info("异步测试", "异步单例模式测试通过")
