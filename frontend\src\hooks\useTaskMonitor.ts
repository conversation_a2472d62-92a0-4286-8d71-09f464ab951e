/**
 * 🔧 [Hook] 任务监控Hook
 * 用于监控后端异步任务的状态和进度
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { ApiAdapter } from '../services/apiAdapter';
import { debugLog } from '../config/env';
import type { TaskStatusResponse, GenerationStatus } from '../types/backend';

export interface UseTaskMonitorOptions {
  /** 轮询间隔（毫秒），默认2000ms */
  pollInterval?: number;
  /** 最大轮询次数，默认150次（5分钟） */
  maxPolls?: number;
  /** 是否自动开始监控 */
  autoStart?: boolean;
  /** 任务完成回调 */
  onComplete?: (task: TaskStatusResponse) => void;
  /** 任务失败回调 */
  onError?: (task: TaskStatusResponse, error?: string) => void;
  /** 进度更新回调 */
  onProgress?: (task: TaskStatusResponse) => void;
}

export interface UseTaskMonitorReturn {
  /** 当前任务状态 */
  task: TaskStatusResponse | null;
  /** 是否正在监控 */
  isMonitoring: boolean;
  /** 是否已完成 */
  isCompleted: boolean;
  /** 是否失败 */
  isFailed: boolean;
  /** 错误信息 */
  error: string | null;
  /** 轮询次数 */
  pollCount: number;
  /** 开始监控任务 */
  startMonitoring: (taskId: string) => void;
  /** 停止监控 */
  stopMonitoring: () => void;
  /** 手动查询一次状态 */
  queryStatus: () => Promise<void>;
}

/**
 * 任务监控Hook
 */
export function useTaskMonitor(options: UseTaskMonitorOptions = {}): UseTaskMonitorReturn {
  const {
    pollInterval = 2000,
    maxPolls = 150,
    autoStart = true,
    onComplete,
    onError,
    onProgress
  } = options;

  // 状态
  const [task, setTask] = useState<TaskStatusResponse | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pollCount, setPollCount] = useState(0);

  // Refs
  const taskIdRef = useRef<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const pollCountRef = useRef(0);

  // 计算状态
  const isCompleted = task?.status === 'completed';
  const isFailed = task?.status === 'failed';

  /**
   * 查询任务状态
   */
  const queryStatus = useCallback(async () => {
    if (!taskIdRef.current) {
      debugLog('任务', '无任务ID，跳过状态查询');
      return;
    }

    try {
      debugLog('任务', '查询任务状态', { 
        taskId: taskIdRef.current, 
        pollCount: pollCountRef.current 
      });

      const taskStatus = await ApiAdapter.getTaskStatus(taskIdRef.current);
      
      if (!taskStatus) {
        throw new Error('任务状态查询失败');
      }

      setTask(taskStatus);
      setPollCount(pollCountRef.current);
      setError(null);

      debugLog('任务', '任务状态更新', {
        taskId: taskStatus.id,
        status: taskStatus.status,
        progress: taskStatus.progress,
        currentStep: taskStatus.current_step
      });

      // 调用进度回调
      if (onProgress) {
        onProgress(taskStatus);
      }

      // 检查是否完成
      if (taskStatus.status === 'completed') {
        debugLog('任务', '任务已完成', { taskId: taskStatus.id });
        stopMonitoring();
        if (onComplete) {
          onComplete(taskStatus);
        }
        return;
      }

      // 检查是否失败
      if (taskStatus.status === 'failed') {
        debugLog('任务', '任务失败', { 
          taskId: taskStatus.id, 
          error: taskStatus.error_message 
        });
        stopMonitoring();
        setError(taskStatus.error_message || '任务执行失败');
        if (onError) {
          onError(taskStatus, taskStatus.error_message);
        }
        return;
      }

      // 增加轮询计数
      pollCountRef.current += 1;

      // 检查是否超过最大轮询次数
      if (pollCountRef.current >= maxPolls) {
        debugLog('任务', '达到最大轮询次数，停止监控', { 
          taskId: taskStatus.id, 
          maxPolls 
        });
        stopMonitoring();
        setError('任务监控超时');
        if (onError) {
          onError(taskStatus, '监控超时');
        }
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '状态查询失败';
      debugLog('错误', '任务状态查询失败', { 
        taskId: taskIdRef.current, 
        error: errorMessage 
      });
      setError(errorMessage);
      
      // 如果是网络错误，继续重试；如果是其他错误，停止监控
      if (errorMessage.includes('网络') || errorMessage.includes('连接')) {
        // 网络错误，继续重试
        pollCountRef.current += 1;
      } else {
        // 其他错误，停止监控
        stopMonitoring();
        if (onError && task) {
          onError(task, errorMessage);
        }
      }
    }
  }, [maxPolls, onComplete, onError, onProgress, task]);

  /**
   * 开始监控任务
   */
  const startMonitoring = useCallback((taskId: string) => {
    debugLog('任务', '开始监控任务', { taskId, pollInterval, maxPolls });
    
    // 停止之前的监控
    stopMonitoring();
    
    // 设置新的任务ID
    taskIdRef.current = taskId;
    pollCountRef.current = 0;
    setPollCount(0);
    setError(null);
    setIsMonitoring(true);
    
    // 立即查询一次
    queryStatus();
    
    // 设置定时器
    intervalRef.current = setInterval(queryStatus, pollInterval);
  }, [pollInterval, maxPolls, queryStatus]);

  /**
   * 停止监控
   */
  const stopMonitoring = useCallback(() => {
    debugLog('任务', '停止监控任务', { taskId: taskIdRef.current });
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    
    setIsMonitoring(false);
  }, []);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      stopMonitoring();
    };
  }, [stopMonitoring]);

  return {
    task,
    isMonitoring,
    isCompleted,
    isFailed,
    error,
    pollCount,
    startMonitoring,
    stopMonitoring,
    queryStatus
  };
}

/**
 * 简化版任务监控Hook - 只监控单个任务直到完成
 */
export function useSimpleTaskMonitor(
  taskId: string | null,
  onComplete?: (task: TaskStatusResponse) => void,
  onError?: (error: string) => void
) {
  const monitor = useTaskMonitor({
    onComplete,
    onError: (task, error) => {
      if (onError) {
        onError(error || '任务失败');
      }
    }
  });

  useEffect(() => {
    if (taskId) {
      monitor.startMonitoring(taskId);
    } else {
      monitor.stopMonitoring();
    }
  }, [taskId, monitor]);

  return {
    task: monitor.task,
    isMonitoring: monitor.isMonitoring,
    isCompleted: monitor.isCompleted,
    isFailed: monitor.isFailed,
    error: monitor.error,
    progress: monitor.task?.progress || 0,
    currentStep: monitor.task?.current_step || '',
    status: monitor.task?.status || 'pending'
  };
}
