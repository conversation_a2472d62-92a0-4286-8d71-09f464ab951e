{"name": "数据文件名称", "version": "1.0.0", "description": "数据文件描述", "category": "数据分类", "tags": ["标签1", "标签2", "标签3"], "metadata": {"source": "数据来源", "quality_score": 0.85, "usage_frequency": "high", "last_updated": "2025-08-05", "contributor": "贡献者", "review_status": "approved", "language": "zh-CN", "encoding": "utf-8"}, "usage_contexts": {"适用场景1": {"description": "场景描述", "weight": 1.0, "conditions": ["条件1", "条件2"]}, "适用场景2": {"description": "场景描述", "weight": 0.8, "conditions": ["条件3", "条件4"]}}, "data": {"categories": {"分类1": {"description": "分类描述", "items": [{"content": "数据内容", "weight": 1.0, "metadata": {"source": "来源", "quality": 0.9, "frequency": "high"}, "tags": ["标签"], "examples": ["使用示例1", "使用示例2"]}]}}, "patterns": {"模式1": {"template": "模板内容，使用{变量}占位符", "variables": {"变量": {"type": "string", "description": "变量描述", "examples": ["示例1", "示例2"]}}, "weight": 1.0, "usage_contexts": ["场景1", "场景2"]}}, "rules": {"规则1": {"condition": "触发条件", "action": "执行动作", "priority": 1, "enabled": true}}}, "examples": [{"input": "输入示例", "output": "输出示例", "context": "使用场景", "explanation": "说明"}], "validation": {"schema_version": "1.0", "required_fields": ["content", "weight"], "validation_rules": [{"field": "weight", "type": "float", "range": [0.0, 1.0]}]}, "statistics": {"total_entries": 0, "categories_count": 0, "patterns_count": 0, "average_quality": 0.0, "last_calculated": "2025-08-05"}}