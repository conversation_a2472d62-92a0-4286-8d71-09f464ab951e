"""
🎭 [演示] 注意力失焦系统演示脚本

展示注意力失焦系统如何根据不同情绪状态过滤和处理文本，
模拟人类主观注意力的选择性和不完整性。

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.attention_defocus import (
    AttentionDefocusSystem, EmotionalState,
    quick_emotional_filter, analyze_attention_patterns
)
from app.core.config import log_info, log_debug


def print_separator(title: str):
    """打印分隔符"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)


def demo_emotion_detection():
    """演示情绪检测功能"""
    print_separator("情绪检测演示")
    
    system = AttentionDefocusSystem()
    
    test_texts = [
        ("愤怒文本", "这个该死的障碍让我非常愤怒！红色的警告灯刺眼地闪烁，尖锐的警报声响个不停。"),
        ("悲伤文本", "失去了她之后，一切都变得空虚。灰色的天空下，只有孤独的回忆在心中徘徊。"),
        ("焦虑文本", "时间不够了！如果出现危险怎么办？心跳加速，呼吸急促，来不及了。"),
        ("喜悦文本", "成功了！明亮的阳光照耀着大地，这是完美的胜利时刻，一切都那么美好。"),
        ("中性文本", "今天天气不错，我去了商店买了一些日用品，然后回家做饭。")
    ]
    
    for text_type, text in test_texts:
        detected_emotion = system.detect_emotional_state(text)
        print(f"\n📝 {text_type}:")
        print(f"   文本: {text}")
        print(f"   🧠 检测结果: {detected_emotion.value}")


def demo_emotional_filtering():
    """演示情感过滤功能"""
    print_separator("情感过滤演示")
    
    # 测试文本：包含多种元素的复杂场景
    original_text = """
    阳光透过窗户洒进房间，微风轻抚着脸颊，花香弥漫在空气中。
    突然传来刺耳的警报声，红色的警告灯开始闪烁。
    房间里的温度很舒适，墙上挂着美丽的画作。
    但是那个尖锐的声音让人感到烦躁和愤怒。
    窗外的鸟儿在树枝上欢快地歌唱。
    这个突如其来的障碍阻挡了我的去路。
    """
    
    emotions_to_test = [
        EmotionalState.ANGER,
        EmotionalState.SADNESS,
        EmotionalState.ANXIETY,
        EmotionalState.JOY
    ]
    
    system = AttentionDefocusSystem()
    
    print(f"\n📖 原始文本:")
    print(original_text.strip())
    
    for emotion in emotions_to_test:
        print(f"\n🎭 {emotion.value}状态下的过滤结果:")
        result = system.apply_emotional_filter(original_text, emotion, intensity=0.8)
        
        print(f"   📝 过滤后文本:")
        print(f"   {result.filtered_text}")
        
        if result.removed_elements:
            print(f"   🗑️ 移除的元素 ({len(result.removed_elements)}个):")
            for element in result.removed_elements[:3]:  # 只显示前3个
                print(f"      - {element}")
        
        if result.emphasized_elements:
            print(f"   ⭐ 强调的元素 ({len(result.emphasized_elements)}个):")
            for element in result.emphasized_elements[:3]:  # 只显示前3个
                print(f"      - {element}")


def demo_subjective_details():
    """演示主观细节提取功能"""
    print_separator("主观细节提取演示")
    
    system = AttentionDefocusSystem()
    
    test_scenarios = [
        (EmotionalState.ANGER, "红色的警告灯刺眼地闪烁，尖锐的警报声响起，这个该死的障碍阻挡了去路。突然的冲突让人愤怒。"),
        (EmotionalState.SADNESS, "空荡荡的房间里只有黑暗，冷清的街道上没有人影，旧照片散落在地上，回忆着过去的美好时光。"),
        (EmotionalState.ANXIETY, "时间快不够了，急促的脚步声在走廊里回响，危险的信号不断出现，不确定的未来让人担心。")
    ]
    
    for emotion, text in test_scenarios:
        print(f"\n🎭 {emotion.value}状态下的主观细节:")
        print(f"   📝 场景文本: {text}")
        
        details = system.extract_subjective_details(text, emotion, max_details=3)
        
        if details:
            print(f"   🔍 提取的主观细节:")
            for i, detail in enumerate(details, 1):
                print(f"      {i}. {detail}")
        else:
            print(f"   ❌ 未提取到相关细节")


def demo_quick_filter():
    """演示快速过滤接口"""
    print_separator("快速过滤接口演示")
    
    test_text = "这个该死的障碍让我愤怒，刺耳的声音响个不停。但是窗外的花园很美丽，阳光温暖地照射着。"
    
    print(f"📝 原始文本: {test_text}")
    
    # 测试不同情绪的快速过滤
    emotions = ["愤怒", "悲伤", "焦虑", "喜悦"]
    
    for emotion in emotions:
        filtered_text = quick_emotional_filter(test_text, emotion, intensity=0.7)
        print(f"\n🎭 {emotion}过滤结果:")
        print(f"   {filtered_text}")
    
    # 测试自动检测
    auto_filtered = quick_emotional_filter(test_text, emotion_state=None, intensity=0.7)
    print(f"\n🤖 自动检测过滤结果:")
    print(f"   {auto_filtered}")


def demo_attention_analysis():
    """演示注意力模式分析"""
    print_separator("注意力模式分析演示")
    
    test_text = "这个该死的障碍让我非常愤怒！红色的警告灯刺眼地闪烁着，尖锐的警报声让人烦躁。"
    
    analysis = analyze_attention_patterns(test_text)
    
    print(f"📝 分析文本: {test_text}")
    print(f"\n📊 分析结果:")
    print(f"   🧠 检测情绪: {analysis['detected_emotion']}")
    print(f"   📏 文本长度: {analysis['text_length']} 字符")
    
    if analysis['attention_pattern']:
        pattern = analysis['attention_pattern']
        print(f"   🎯 注意力模式:")
        print(f"      - 关注关键词数量: {pattern['focus_keywords_count']}")
        print(f"      - 忽略关键词数量: {pattern['ignore_keywords_count']}")
        print(f"      - 强度修饰符: {pattern['intensity_modifier']}")
        print(f"      - 扭曲规则: {', '.join(pattern['distortion_rules'])}")
    
    if analysis['subjective_details']:
        print(f"   🔍 主观细节:")
        for i, detail in enumerate(analysis['subjective_details'], 1):
            print(f"      {i}. {detail}")


def demo_comparison():
    """演示AI生成内容的对比效果"""
    print_separator("AI内容去AI化对比演示")
    
    # 模拟AI生成的过度详细描述
    ai_generated_text = """
    阳光明媚地透过精致的百叶窗洒进宽敞明亮的房间，温暖的金色光线轻柔地抚摸着每一寸光滑的木质地板。
    微风徐徐地从半开的窗户中飘进来，带着淡淡的茉莉花香和清新的草木气息。
    房间里的每一个细节都显得那么完美和谐：雪白的墙壁上挂着精美的油画，
    柔软的沙发上摆放着蓬松的抱枕，茶几上的水晶花瓶里插着新鲜的玫瑰花。
    突然，刺耳的警报声打破了这份宁静，红色的警告灯开始急促地闪烁。
    """
    
    print("🤖 原始AI生成文本（过度详细）:")
    print(ai_generated_text.strip())
    
    # 应用愤怒情绪过滤，模拟人类在愤怒状态下的注意力
    filtered_text = quick_emotional_filter(ai_generated_text, "愤怒", intensity=0.8)
    
    print("\n🧠 愤怒状态下的人类化处理结果:")
    print(filtered_text)
    
    print("\n📈 效果分析:")
    print(f"   - 原文长度: {len(ai_generated_text)} 字符")
    print(f"   - 处理后长度: {len(filtered_text)} 字符")
    print(f"   - 压缩比例: {(1 - len(filtered_text)/len(ai_generated_text))*100:.1f}%")
    print("   - 移除了过度的环境美化描述")
    print("   - 保留并强调了刺激性元素")
    print("   - 更符合人类在愤怒状态下的注意力特征")


def main():
    """主演示函数"""
    print("🎭 注意力失焦系统演示")
    print("=" * 60)
    print("本演示展示如何通过模拟人类情绪状态下的选择性注意力，")
    print("让AI生成的内容更具真实性和主观性，减少'AI味'。")
    
    try:
        # 运行各个演示
        demo_emotion_detection()
        demo_emotional_filtering()
        demo_subjective_details()
        demo_quick_filter()
        demo_attention_analysis()
        demo_comparison()
        
        print_separator("演示完成")
        print("✅ 注意力失焦系统演示成功完成！")
        print("🎯 系统能够有效模拟人类在不同情绪状态下的注意力特征，")
        print("   帮助AI生成更真实、更具主观性的内容。")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
