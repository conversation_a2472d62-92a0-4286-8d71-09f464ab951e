"""
🎯 [情节规划] 情节发展路径规划器核心模块

基于知识图谱和角色动机分析，自动生成多种可能的情节发展路径，
并评估每种路径的合理性、吸引力和冲突潜力。

主要功能：
1. 分析当前故事状态和角色动机
2. 生成多种情节发展路径
3. 评估路径的合理性和吸引力
4. 提供路径选择建议和风险评估
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import List, Dict, Any, Optional, Tuple
import uuid
import random

from app.schemas.world_graph import WorldGraphResponse, EntityResponse, RelationshipResponse
from app.models.world_graph import EntityType, RelationshipStatus
from app.core.config import log_info, log_debug, log_error


class PathType(Enum):
    """情节路径类型枚举"""
    CONFLICT_ESCALATION = "conflict_escalation"      # 冲突升级路径
    RELATIONSHIP_DEVELOPMENT = "relationship_development"  # 关系发展路径
    CHARACTER_GROWTH = "character_growth"            # 角色成长路径
    MYSTERY_REVELATION = "mystery_revelation"        # 谜团揭示路径
    WORLD_EXPLORATION = "world_exploration"          # 世界探索路径
    EMOTIONAL_JOURNEY = "emotional_journey"          # 情感历程路径


class PathPriority(Enum):
    """路径优先级枚举"""
    LOW = "low"           # 低优先级 - 可选的支线情节
    MEDIUM = "medium"     # 中优先级 - 重要的发展方向
    HIGH = "high"         # 高优先级 - 关键的主线情节
    CRITICAL = "critical" # 关键优先级 - 必须解决的核心冲突


@dataclass
class PlotEvent:
    """情节事件数据类"""
    id: str
    title: str                    # 事件标题
    description: str              # 事件描述
    event_type: str              # 事件类型
    involved_entities: List[str]  # 涉及的实体ID
    involved_relationships: List[str]  # 涉及的关系ID
    prerequisites: List[str]      # 前置条件
    consequences: List[str]       # 后果影响
    emotional_impact: float       # 情感冲击力 (0.0-10.0)
    tension_level: float          # 紧张程度 (0.0-10.0)
    complexity: float             # 复杂度 (0.0-10.0)
    estimated_chapters: int       # 预估章节数
    created_at: datetime


@dataclass
class PlotPath:
    """情节发展路径数据类"""
    id: str
    title: str                    # 路径标题
    description: str              # 路径描述
    path_type: PathType           # 路径类型
    priority: PathPriority        # 优先级
    events: List[PlotEvent]       # 路径中的事件序列
    
    # 评估指标
    feasibility_score: float      # 可行性评分 (0.0-10.0)
    appeal_score: float           # 吸引力评分 (0.0-10.0)
    coherence_score: float        # 连贯性评分 (0.0-10.0)
    originality_score: float      # 原创性评分 (0.0-10.0)
    overall_score: float          # 综合评分 (0.0-10.0)
    
    # 风险评估
    risk_factors: List[str]       # 风险因素
    potential_conflicts: List[str] # 潜在冲突
    
    # 元数据
    estimated_total_chapters: int # 预估总章节数
    target_audience: List[str]    # 目标受众
    genre_tags: List[str]         # 类型标签
    
    created_at: datetime
    last_updated: datetime


@dataclass
class PathPlanningResult:
    """路径规划结果数据类"""
    story_id: str
    current_chapter: int
    analysis_timestamp: datetime
    
    # 当前状态分析
    current_state_summary: str    # 当前状态摘要
    active_conflicts: List[str]   # 活跃冲突
    character_motivations: Dict[str, List[str]]  # 角色动机
    unresolved_threads: List[str] # 未解决的线索
    
    # 生成的路径
    recommended_paths: List[PlotPath]  # 推荐路径
    alternative_paths: List[PlotPath]  # 备选路径
    
    # 整体建议
    next_chapter_suggestions: List[str]  # 下一章建议
    long_term_strategy: str             # 长期策略
    pacing_recommendations: str         # 节奏建议
    
    # 统计信息
    total_paths_generated: int
    analysis_confidence: float    # 分析置信度 (0.0-1.0)


class PlotPathPlanner:
    """
    🎯 [情节规划] 情节发展路径规划器核心类
    
    分析当前故事状态，生成多种可能的情节发展路径，
    并提供智能的路径选择建议和风险评估。
    """
    
    def __init__(self):
        """初始化情节路径规划器"""
        log_info("情节规划", "初始化情节路径规划器")
        
        # 路径生成规则配置
        self.path_generation_rules = self._initialize_path_rules()
        
        # 评估权重配置
        self.evaluation_weights = {
            "feasibility": 0.25,    # 可行性权重
            "appeal": 0.30,         # 吸引力权重
            "coherence": 0.25,      # 连贯性权重
            "originality": 0.20     # 原创性权重
        }
        
        log_info("情节规划", "情节路径规划器初始化完成",
                规则数量=len(self.path_generation_rules),
                评估维度=len(self.evaluation_weights))
    
    def _initialize_path_rules(self) -> Dict[PathType, Dict[str, Any]]:
        """初始化路径生成规则"""
        log_debug("情节规划", "初始化路径生成规则")
        
        rules = {
            PathType.CONFLICT_ESCALATION: {
                "description": "基于现有冲突生成升级路径",
                "triggers": ["unresolved_conflicts", "tension_buildup"],
                "event_types": ["confrontation", "betrayal", "revelation", "climax"],
                "min_events": 3,
                "max_events": 8,
                "tension_curve": "ascending"
            },
            PathType.RELATIONSHIP_DEVELOPMENT: {
                "description": "基于角色关系生成发展路径",
                "triggers": ["relationship_potential", "character_chemistry"],
                "event_types": ["meeting", "bonding", "conflict", "resolution"],
                "min_events": 2,
                "max_events": 6,
                "tension_curve": "wave"
            },
            PathType.CHARACTER_GROWTH: {
                "description": "基于角色成长弧线生成路径",
                "triggers": ["character_flaws", "growth_opportunities"],
                "event_types": ["challenge", "failure", "learning", "transformation"],
                "min_events": 4,
                "max_events": 10,
                "tension_curve": "growth"
            },
            PathType.MYSTERY_REVELATION: {
                "description": "基于谜团和秘密生成揭示路径",
                "triggers": ["hidden_information", "clues", "mysteries"],
                "event_types": ["clue_discovery", "red_herring", "revelation", "truth"],
                "min_events": 3,
                "max_events": 7,
                "tension_curve": "mystery"
            },
            PathType.WORLD_EXPLORATION: {
                "description": "基于世界设定生成探索路径",
                "triggers": ["unexplored_locations", "world_mysteries"],
                "event_types": ["journey", "discovery", "encounter", "understanding"],
                "min_events": 2,
                "max_events": 5,
                "tension_curve": "exploration"
            },
            PathType.EMOTIONAL_JOURNEY: {
                "description": "基于情感发展生成路径",
                "triggers": ["emotional_conflicts", "inner_struggles"],
                "event_types": ["realization", "emotional_peak", "catharsis", "healing"],
                "min_events": 3,
                "max_events": 6,
                "tension_curve": "emotional"
            }
        }
        
        log_debug("情节规划", "路径生成规则初始化完成",
                 路径类型=[path_type.value for path_type in rules.keys()])
        
        return rules
    
    async def generate_plot_paths(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int,
        target_paths: int = 5,
        focus_types: Optional[List[PathType]] = None
    ) -> PathPlanningResult:
        """
        🎯 [情节规划] 生成情节发展路径
        
        Args:
            world_graph: 世界知识图谱
            current_chapter: 当前章节
            target_paths: 目标路径数量
            focus_types: 重点关注的路径类型
            
        Returns:
            PathPlanningResult: 路径规划结果
        """
        log_info("情节规划", "开始生成情节发展路径",
                故事ID=world_graph.story_id,
                当前章节=current_chapter,
                目标路径数=target_paths)
        
        try:
            # 1. 分析当前故事状态
            current_state = await self._analyze_current_state(world_graph, current_chapter)
            
            # 2. 识别角色动机和冲突
            character_motivations = await self._analyze_character_motivations(world_graph)
            
            # 3. 生成候选路径
            candidate_paths = await self._generate_candidate_paths(
                world_graph, current_chapter, current_state, character_motivations, focus_types
            )
            
            # 4. 评估和排序路径
            evaluated_paths = await self._evaluate_paths(candidate_paths, world_graph)
            
            # 5. 选择推荐路径
            recommended_paths = evaluated_paths[:target_paths]
            alternative_paths = evaluated_paths[target_paths:target_paths*2]
            
            # 6. 生成建议和策略
            suggestions = await self._generate_suggestions(
                recommended_paths, world_graph, current_chapter
            )
            
            # 7. 构建结果
            result = PathPlanningResult(
                story_id=world_graph.story_id,
                current_chapter=current_chapter,
                analysis_timestamp=datetime.now(),
                current_state_summary=current_state["summary"],
                active_conflicts=current_state["conflicts"],
                character_motivations=character_motivations,
                unresolved_threads=current_state["unresolved_threads"],
                recommended_paths=recommended_paths,
                alternative_paths=alternative_paths,
                next_chapter_suggestions=suggestions["next_chapter"],
                long_term_strategy=suggestions["long_term"],
                pacing_recommendations=suggestions["pacing"],
                total_paths_generated=len(candidate_paths),
                analysis_confidence=self._calculate_confidence(world_graph, candidate_paths)
            )
            
            log_info("情节规划", "情节路径生成完成",
                    故事ID=world_graph.story_id,
                    推荐路径数=len(recommended_paths),
                    备选路径数=len(alternative_paths),
                    分析置信度=result.analysis_confidence)
            
            return result
            
        except Exception as e:
            log_error("情节规划", "情节路径生成失败", error=str(e))
            raise

    async def _analyze_current_state(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int
    ) -> Dict[str, Any]:
        """分析当前故事状态"""
        log_debug("情节规划", "分析当前故事状态", 当前章节=current_chapter)

        # 识别活跃的冲突
        active_conflicts = []
        # 创建实体ID到名称的映射
        entity_name_map = {entity.id: entity.name for entity in world_graph.entities}

        for rel in world_graph.relationships:
            if (rel.status == RelationshipStatus.ACTIVE and
                rel.relationship_type in ["敌人", "对手", "竞争者"]):
                source_name = entity_name_map.get(rel.source_entity_id, rel.source_entity_id)
                target_name = entity_name_map.get(rel.target_entity_id, rel.target_entity_id)
                active_conflicts.append(f"{source_name}与{target_name}的{rel.relationship_type}关系")

        # 识别未解决的线索
        unresolved_threads = []
        for entity in world_graph.entities:
            if entity.last_mentioned_chapter and entity.last_mentioned_chapter < current_chapter - 2:
                unresolved_threads.append(f"{entity.name}的后续发展")

        # 生成状态摘要
        summary = f"故事进行到第{current_chapter}章，当前有{len(active_conflicts)}个活跃冲突，{len(unresolved_threads)}个未解决线索"

        return {
            "summary": summary,
            "conflicts": active_conflicts,
            "unresolved_threads": unresolved_threads,
            "chapter": current_chapter
        }

    async def _analyze_character_motivations(
        self,
        world_graph: WorldGraphResponse
    ) -> Dict[str, List[str]]:
        """分析角色动机"""
        log_debug("情节规划", "分析角色动机")

        motivations = {}

        for entity in world_graph.entities:
            if entity.type != EntityType.CHARACTER:
                continue

            character_motivations = []

            # 基于属性分析动机
            if entity.properties:
                personality = entity.properties.get("personality", "")
                if "善良" in personality:
                    character_motivations.append("保护他人")
                if "邪恶" in personality:
                    character_motivations.append("获取权力")
                if "勇敢" in personality:
                    character_motivations.append("面对挑战")
                if "智慧" in personality:
                    character_motivations.append("寻求真相")

            # 基于关系分析动机
            for rel in world_graph.relationships:
                if rel.source_entity_id == entity.id:
                    if rel.relationship_type == "敌人":
                        character_motivations.append("击败敌人")
                    elif rel.relationship_type == "朋友":
                        character_motivations.append("维护友谊")
                    elif rel.relationship_type == "拥有":
                        character_motivations.append("保护财产")

            if not character_motivations:
                character_motivations.append("寻找人生目标")

            motivations[entity.id] = character_motivations

        return motivations

    async def _generate_candidate_paths(
        self,
        world_graph: WorldGraphResponse,
        current_chapter: int,
        current_state: Dict[str, Any],
        character_motivations: Dict[str, List[str]],
        focus_types: Optional[List[PathType]] = None
    ) -> List[PlotPath]:
        """生成候选路径"""
        log_debug("情节规划", "生成候选路径")

        candidate_paths = []
        path_types = focus_types if focus_types else list(PathType)

        for path_type in path_types:
            rule = self.path_generation_rules[path_type]

            # 为每种类型生成2-3个路径变体
            for variant in range(2):
                path = await self._generate_single_path(
                    path_type, rule, world_graph, current_chapter,
                    current_state, character_motivations, variant
                )
                if path:
                    candidate_paths.append(path)

        log_debug("情节规划", "候选路径生成完成", 路径数量=len(candidate_paths))
        return candidate_paths

    async def _generate_single_path(
        self,
        path_type: PathType,
        rule: Dict[str, Any],
        world_graph: WorldGraphResponse,
        current_chapter: int,
        current_state: Dict[str, Any],
        character_motivations: Dict[str, List[str]],
        variant: int
    ) -> Optional[PlotPath]:
        """生成单个路径"""
        try:
            # 生成路径事件
            events = await self._generate_path_events(
                path_type, rule, world_graph, current_chapter, variant
            )

            if not events:
                return None

            # 计算预估章节数
            total_chapters = sum(event.estimated_chapters for event in events)

            # 创建路径
            path = PlotPath(
                id=str(uuid.uuid4()),
                title=f"{rule['description']} - 变体{variant + 1}",
                description=f"基于{path_type.value}的情节发展路径",
                path_type=path_type,
                priority=self._determine_priority(path_type, current_state),
                events=events,
                feasibility_score=0.0,  # 稍后评估
                appeal_score=0.0,
                coherence_score=0.0,
                originality_score=0.0,
                overall_score=0.0,
                risk_factors=[],
                potential_conflicts=[],
                estimated_total_chapters=total_chapters,
                target_audience=["通用读者"],
                genre_tags=[path_type.value],
                created_at=datetime.now(),
                last_updated=datetime.now()
            )

            return path

        except Exception as e:
            log_error("情节规划", f"生成{path_type.value}路径失败", error=str(e))
            return None


    async def _generate_path_events(
        self,
        path_type: PathType,
        rule: Dict[str, Any],
        world_graph: WorldGraphResponse,
        current_chapter: int,
        variant: int
    ) -> List[PlotEvent]:
        """生成路径事件"""
        events = []
        event_types = rule["event_types"]
        min_events = rule["min_events"]
        max_events = rule["max_events"]

        # 随机选择事件数量
        num_events = random.randint(min_events, max_events)

        for i in range(num_events):
            event_type = random.choice(event_types)

            # 选择涉及的实体
            involved_entities = self._select_entities_for_event(world_graph, event_type)

            event = PlotEvent(
                id=str(uuid.uuid4()),
                title=f"{event_type}事件 {i+1}",
                description=f"基于{path_type.value}的{event_type}事件",
                event_type=event_type,
                involved_entities=involved_entities,
                involved_relationships=[],
                prerequisites=[],
                consequences=[],
                emotional_impact=random.uniform(3.0, 8.0),
                tension_level=random.uniform(2.0, 9.0),
                complexity=random.uniform(1.0, 7.0),
                estimated_chapters=random.randint(1, 3),
                created_at=datetime.now()
            )

            events.append(event)

        return events

    def _select_entities_for_event(
        self,
        world_graph: WorldGraphResponse,
        event_type: str
    ) -> List[str]:
        """为事件选择相关实体"""
        # 优先选择重要性高的角色
        characters = [e for e in world_graph.entities if e.type == EntityType.CHARACTER]
        characters.sort(key=lambda x: x.importance_score, reverse=True)

        # 根据事件类型选择实体数量
        if event_type in ["confrontation", "meeting", "conflict"]:
            return [c.id for c in characters[:2]]  # 选择前2个角色
        else:
            return [characters[0].id] if characters else []  # 选择主角

    def _determine_priority(
        self,
        path_type: PathType,
        current_state: Dict[str, Any]
    ) -> PathPriority:
        """确定路径优先级"""
        # 根据路径类型和当前状态确定优先级
        if path_type == PathType.CONFLICT_ESCALATION and current_state["conflicts"]:
            return PathPriority.HIGH
        elif path_type == PathType.CHARACTER_GROWTH:
            return PathPriority.MEDIUM
        elif path_type == PathType.MYSTERY_REVELATION:
            return PathPriority.HIGH
        else:
            return PathPriority.MEDIUM

    async def _evaluate_paths(
        self,
        candidate_paths: List[PlotPath],
        world_graph: WorldGraphResponse
    ) -> List[PlotPath]:
        """评估和排序路径"""
        log_debug("情节规划", "评估候选路径", 路径数量=len(candidate_paths))

        for path in candidate_paths:
            # 计算各项评分
            path.feasibility_score = self._calculate_feasibility(path, world_graph)
            path.appeal_score = self._calculate_appeal(path)
            path.coherence_score = self._calculate_coherence(path)
            path.originality_score = self._calculate_originality(path)

            # 计算综合评分
            path.overall_score = (
                path.feasibility_score * self.evaluation_weights["feasibility"] +
                path.appeal_score * self.evaluation_weights["appeal"] +
                path.coherence_score * self.evaluation_weights["coherence"] +
                path.originality_score * self.evaluation_weights["originality"]
            )

            # 评估风险因素
            path.risk_factors = self._identify_risk_factors(path, world_graph)
            path.potential_conflicts = self._identify_potential_conflicts(path, world_graph)

        # 按综合评分排序
        candidate_paths.sort(key=lambda x: x.overall_score, reverse=True)

        log_debug("情节规划", "路径评估完成",
                 最高评分=candidate_paths[0].overall_score if candidate_paths else 0)

        return candidate_paths

    def _calculate_feasibility(self, path: PlotPath, world_graph: WorldGraphResponse) -> float:
        """计算可行性评分"""
        score = 8.0  # 基础分

        # 检查涉及的实体是否存在
        all_entity_ids = {e.id for e in world_graph.entities}
        for event in path.events:
            for entity_id in event.involved_entities:
                if entity_id not in all_entity_ids:
                    score -= 2.0

        # 检查事件复杂度
        avg_complexity = sum(e.complexity for e in path.events) / len(path.events)
        if avg_complexity > 6.0:
            score -= 1.0

        return max(0.0, min(10.0, score))

    def _calculate_appeal(self, path: PlotPath) -> float:
        """计算吸引力评分"""
        # 基于情感冲击力和紧张程度
        avg_emotional_impact = sum(e.emotional_impact for e in path.events) / len(path.events)
        avg_tension = sum(e.tension_level for e in path.events) / len(path.events)

        appeal = (avg_emotional_impact + avg_tension) / 2

        # 路径类型加成
        if path.path_type in [PathType.CONFLICT_ESCALATION, PathType.MYSTERY_REVELATION]:
            appeal += 1.0

        return max(0.0, min(10.0, appeal))

    def _calculate_coherence(self, path: PlotPath) -> float:
        """计算连贯性评分"""
        score = 7.0  # 基础分

        # 检查事件数量是否合理
        if len(path.events) < 2:
            score -= 2.0
        elif len(path.events) > 10:
            score -= 1.0

        # 检查章节数是否合理
        if path.estimated_total_chapters > 20:
            score -= 1.0

        return max(0.0, min(10.0, score))

    def _calculate_originality(self, path: PlotPath) -> float:
        """计算原创性评分"""
        # 基于路径类型的稀有度
        type_rarity = {
            PathType.CONFLICT_ESCALATION: 6.0,
            PathType.RELATIONSHIP_DEVELOPMENT: 7.0,
            PathType.CHARACTER_GROWTH: 8.0,
            PathType.MYSTERY_REVELATION: 8.5,
            PathType.WORLD_EXPLORATION: 9.0,
            PathType.EMOTIONAL_JOURNEY: 7.5
        }

        return type_rarity.get(path.path_type, 7.0)


    def _identify_risk_factors(
        self,
        path: PlotPath,
        world_graph: WorldGraphResponse
    ) -> List[str]:
        """识别风险因素"""
        risks = []

        # 检查路径复杂度
        if path.estimated_total_chapters > 15:
            risks.append("路径过长，可能影响节奏")

        # 检查事件密度
        if len(path.events) > 8:
            risks.append("事件过多，可能造成信息过载")

        # 检查角色负担
        all_involved = set()
        for event in path.events:
            all_involved.update(event.involved_entities)

        if len(all_involved) > len(world_graph.entities) * 0.8:
            risks.append("涉及角色过多，可能分散注意力")

        return risks

    def _identify_potential_conflicts(
        self,
        path: PlotPath,
        world_graph: WorldGraphResponse
    ) -> List[str]:
        """识别潜在冲突"""
        conflicts = []

        # 检查时间线冲突
        if path.estimated_total_chapters > 10:
            conflicts.append("可能与主线时间线产生冲突")

        # 检查角色一致性
        for event in path.events:
            if len(event.involved_entities) > 3:
                conflicts.append("多角色事件可能导致角色行为不一致")

        return conflicts

    async def _generate_suggestions(
        self,
        recommended_paths: List[PlotPath],
        world_graph: WorldGraphResponse,
        current_chapter: int
    ) -> Dict[str, Any]:
        """生成建议和策略"""
        log_debug("情节规划", "生成建议和策略")

        next_chapter_suggestions = []

        if recommended_paths:
            best_path = recommended_paths[0]
            if best_path.events:
                first_event = best_path.events[0]
                next_chapter_suggestions.append(f"考虑在下一章引入{first_event.title}")
                next_chapter_suggestions.append(f"重点关注{first_event.event_type}类型的情节发展")

        # 长期策略
        long_term_strategy = "建议采用多线并行的叙事策略，平衡角色发展和情节推进"

        # 节奏建议
        pacing_recommendations = "保持适中的节奏，避免过快的情节发展导致读者跟不上"

        return {
            "next_chapter": next_chapter_suggestions,
            "long_term": long_term_strategy,
            "pacing": pacing_recommendations
        }

    def _calculate_confidence(
        self,
        world_graph: WorldGraphResponse,
        candidate_paths: List[PlotPath]
    ) -> float:
        """计算分析置信度"""
        confidence = 0.7  # 基础置信度

        # 基于数据完整性调整
        if len(world_graph.entities) >= 3:
            confidence += 0.1
        if len(world_graph.relationships) >= 2:
            confidence += 0.1

        # 基于路径质量调整
        if candidate_paths and candidate_paths[0].overall_score > 7.0:
            confidence += 0.1

        return min(1.0, confidence)


def create_plot_path_planner() -> PlotPathPlanner:
    """
    🎯 [情节规划] 创建情节路径规划器实例
    
    Returns:
        PlotPathPlanner: 情节路径规划器实例
    """
    log_info("情节规划", "创建情节路径规划器实例")
    
    planner = PlotPathPlanner()
    
    log_info("情节规划", "情节路径规划器创建完成",
            支持的路径类型=[pt.value for pt in PathType],
            优先级级别=[pp.value for pp in PathPriority])
    
    return planner
