"""
📚 [语料库管理] 语料库管理API测试模块

测试语料库管理API的各种端点：
1. 基础信息查询接口
2. 爽感语料库专用接口
3. 语料库管理操作接口
4. 搜索和查询接口
5. 健康检查接口

作者: 文心小说后端服务系统
创建时间: 2025-08-05
"""

import pytest
import asyncio
from httpx import AsyncClient
from fastapi import FastAPI
from typing import Dict, Any

from app.routers.corpus_management import router
from app.core.config import log_debug, log_info, log_success


# 创建测试应用
def create_test_app() -> FastAPI:
    """创建测试用的FastAPI应用"""
    app = FastAPI()
    app.include_router(router)
    return app


@pytest.fixture
async def test_app():
    """测试应用fixture"""
    app = create_test_app()
    return app


@pytest.fixture
async def test_client(test_app):
    """测试客户端fixture"""
    from httpx import ASGITransport
    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        yield client


class TestCorpusManagementAPI:
    """语料库管理API测试类"""

    async def test_corpus_health_check(self, test_client: AsyncClient):
        """测试语料库系统健康检查"""
        log_info("测试", "开始测试语料库系统健康检查")
        
        response = await test_client.get("/api/v1/corpus/health")
        
        # 验证响应状态
        assert response.status_code in [200, 503], f"健康检查响应状态码异常: {response.status_code}"
        
        # 验证响应内容
        data = response.json()
        assert "success" in data, "响应缺少success字段"
        assert "message" in data, "响应缺少message字段"
        assert "data" in data, "响应缺少data字段"
        
        health_data = data["data"]
        assert "status" in health_data, "健康数据缺少status字段"
        assert "components" in health_data, "健康数据缺少components字段"
        
        log_success("测试", "语料库系统健康检查测试通过", 状态=health_data["status"])

    async def test_get_pleasure_types(self, test_client: AsyncClient):
        """测试获取支持的爽感类型"""
        log_info("测试", "开始测试获取支持的爽感类型")
        
        response = await test_client.get("/api/v1/corpus/pleasure/types")
        
        # 验证响应状态
        assert response.status_code == 200, f"获取爽感类型响应状态码异常: {response.status_code}"
        
        # 验证响应内容
        data = response.json()
        assert data["success"] is True, "响应success字段应为True"
        assert "data" in data, "响应缺少data字段"
        
        pleasure_data = data["data"]
        assert "total_types" in pleasure_data, "爽感数据缺少total_types字段"
        assert "pleasure_types" in pleasure_data, "爽感数据缺少pleasure_types字段"
        
        # 验证爽感类型数据结构
        pleasure_types = pleasure_data["pleasure_types"]
        assert isinstance(pleasure_types, list), "pleasure_types应为列表类型"
        assert len(pleasure_types) > 0, "应至少有一种爽感类型"
        
        # 验证第一个爽感类型的结构
        first_type = pleasure_types[0]
        required_fields = ["type", "name", "description", "keywords"]
        for field in required_fields:
            assert field in first_type, f"爽感类型缺少{field}字段"
        
        log_success("测试", "获取支持的爽感类型测试通过", 类型数量=len(pleasure_types))

    async def test_search_corpus_basic(self, test_client: AsyncClient):
        """测试基础语料库搜索功能"""
        log_info("测试", "开始测试基础语料库搜索功能")
        
        # 测试基础搜索
        response = await test_client.get(
            "/api/v1/corpus/search",
            params={"query": "测试", "limit": 5}
        )
        
        # 验证响应状态（可能没有数据，但API应该正常响应）
        assert response.status_code == 200, f"搜索响应状态码异常: {response.status_code}"
        
        # 验证响应内容
        data = response.json()
        assert data["success"] is True, "响应success字段应为True"
        assert "data" in data, "响应缺少data字段"
        
        search_data = data["data"]
        required_fields = ["query", "total_results", "results"]
        for field in required_fields:
            assert field in search_data, f"搜索数据缺少{field}字段"
        
        assert search_data["query"] == "测试", "查询词不匹配"
        assert isinstance(search_data["results"], list), "搜索结果应为列表类型"
        
        log_success("测试", "基础语料库搜索功能测试通过", 
                   查询词=search_data["query"], 结果数=search_data["total_results"])

    async def test_get_all_categories(self, test_client: AsyncClient):
        """测试获取所有分类"""
        log_info("测试", "开始测试获取所有分类")
        
        response = await test_client.get("/api/v1/corpus/categories")
        
        # 验证响应状态
        assert response.status_code == 200, f"获取分类响应状态码异常: {response.status_code}"
        
        # 验证响应内容
        data = response.json()
        assert data["success"] is True, "响应success字段应为True"
        assert "data" in data, "响应缺少data字段"
        
        categories_data = data["data"]
        assert "total_categories" in categories_data, "分类数据缺少total_categories字段"
        assert "categories" in categories_data, "分类数据缺少categories字段"
        assert isinstance(categories_data["categories"], list), "分类列表应为列表类型"
        
        log_success("测试", "获取所有分类测试通过", 
                   分类数量=categories_data["total_categories"])

    async def test_corpus_info_and_stats(self, test_client: AsyncClient):
        """测试语料库信息和统计接口"""
        log_info("测试", "开始测试语料库信息和统计接口")
        
        # 测试获取基本信息
        info_response = await test_client.get("/api/v1/corpus/info")
        
        # 由于可能没有实际的语料库文件，这里主要测试API结构
        if info_response.status_code == 200:
            info_data = info_response.json()
            required_fields = ["name", "version", "description", "category", "file_count", "total_entries"]
            for field in required_fields:
                assert field in info_data, f"信息响应缺少{field}字段"
            
            log_success("测试", "语料库基本信息测试通过", 
                       文件数=info_data["file_count"], 条目数=info_data["total_entries"])
        
        # 测试获取统计信息
        stats_response = await test_client.get("/api/v1/corpus/stats")
        
        if stats_response.status_code == 200:
            stats_data = stats_response.json()
            required_fields = ["total_files", "total_categories", "total_entries", 
                             "file_types", "category_distribution", "quality_distribution"]
            for field in required_fields:
                assert field in stats_data, f"统计响应缺少{field}字段"
            
            log_success("测试", "语料库统计信息测试通过", 
                       总文件数=stats_data["total_files"], 总分类数=stats_data["total_categories"])

    async def test_corpus_files_list(self, test_client: AsyncClient):
        """测试获取语料库文件列表"""
        log_info("测试", "开始测试获取语料库文件列表")
        
        response = await test_client.get("/api/v1/corpus/files")
        
        # 验证响应状态
        assert response.status_code == 200, f"获取文件列表响应状态码异常: {response.status_code}"
        
        # 验证响应内容
        data = response.json()
        assert data["success"] is True, "响应success字段应为True"
        assert "data" in data, "响应缺少data字段"
        
        files_data = data["data"]
        assert "total_files" in files_data, "文件数据缺少total_files字段"
        assert "files" in files_data, "文件数据缺少files字段"
        assert isinstance(files_data["files"], list), "文件列表应为列表类型"
        
        log_success("测试", "获取语料库文件列表测试通过", 
                   文件数量=files_data["total_files"])

    async def test_pleasure_patterns_api_structure(self, test_client: AsyncClient):
        """测试爽感模式API结构（不依赖实际数据）"""
        log_info("测试", "开始测试爽感模式API结构")
        
        # 测试获取爽感模式（可能返回错误，但API结构应该正确）
        response = await test_client.get(
            "/api/v1/corpus/pleasure/patterns",
            params={"pleasure_type": "face_slapping", "count": 3}
        )
        
        # API应该能正常响应（即使没有数据）
        assert response.status_code in [200, 500], f"爽感模式API响应状态码异常: {response.status_code}"
        
        # 如果成功，验证响应结构
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, list), "爽感模式响应应为列表类型"
            
            if len(data) > 0:
                pattern = data[0]
                required_fields = ["content", "weight", "intensity", "reliability", "quality"]
                for field in required_fields:
                    assert field in pattern, f"爽感模式缺少{field}字段"
        
        log_success("测试", "爽感模式API结构测试通过")

    async def test_enhancement_expressions_api_structure(self, test_client: AsyncClient):
        """测试强化表达API结构"""
        log_info("测试", "开始测试强化表达API结构")
        
        response = await test_client.get(
            "/api/v1/corpus/pleasure/enhancement-expressions",
            params={"expression_type": "震惊反应", "count": 3}
        )
        
        # API应该能正常响应
        assert response.status_code in [200, 500], f"强化表达API响应状态码异常: {response.status_code}"
        
        # 如果成功，验证响应结构
        if response.status_code == 200:
            data = response.json()
            assert "success" in data, "响应缺少success字段"
            assert "data" in data, "响应缺少data字段"
            
            expr_data = data["data"]
            required_fields = ["expression_type", "count", "expressions"]
            for field in required_fields:
                assert field in expr_data, f"强化表达数据缺少{field}字段"
        
        log_success("测试", "强化表达API结构测试通过")


# 运行测试的主函数
async def run_corpus_management_api_tests():
    """运行语料库管理API测试"""
    log_info("测试", "开始运行语料库管理API完整测试套件")
    
    # 创建测试应用和客户端
    app = create_test_app()

    from httpx import ASGITransport
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        test_instance = TestCorpusManagementAPI()
        
        # 运行所有测试
        test_methods = [
            test_instance.test_corpus_health_check,
            test_instance.test_get_pleasure_types,
            test_instance.test_search_corpus_basic,
            test_instance.test_get_all_categories,
            test_instance.test_corpus_info_and_stats,
            test_instance.test_corpus_files_list,
            test_instance.test_pleasure_patterns_api_structure,
            test_instance.test_enhancement_expressions_api_structure
        ]
        
        passed_tests = 0
        total_tests = len(test_methods)
        
        for test_method in test_methods:
            try:
                await test_method(client)
                passed_tests += 1
                log_success("测试", f"测试方法 {test_method.__name__} 通过")
            except Exception as e:
                log_error("测试", f"测试方法 {test_method.__name__} 失败", error=e)
        
        log_success("测试", "语料库管理API测试套件完成", 
                   通过数=passed_tests, 总数=total_tests, 成功率=f"{passed_tests/total_tests*100:.1f}%")
        
        return passed_tests == total_tests


if __name__ == "__main__":
    """直接运行测试"""
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))

    from app.core.config import log_error
    
    try:
        result = asyncio.run(run_corpus_management_api_tests())
        if result:
            log_success("测试", "所有语料库管理API测试通过！")
        else:
            log_error("测试", "部分语料库管理API测试失败！")
    except Exception as e:
        log_error("测试", "运行语料库管理API测试时发生错误", error=e)
