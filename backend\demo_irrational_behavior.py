"""
🎬 [演示] 非理性行为注入器演示脚本

展示非理性行为注入器的各种功能，包括情绪检测、
行为选择、微行为注入等效果。

运行方式: python demo_irrational_behavior.py

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.irrational_behavior import (
    create_irrational_behavior_injector,
    inject_irrational_behavior,
    analyze_emotional_behavior_potential,
    EmotionalState
)


def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🎭 {title}")
    print(f"{'='*60}")


def print_result(original: str, modified: str, details: dict = None):
    """打印处理结果"""
    print(f"\n📝 原文:")
    print(f"   {original}")
    print(f"\n✨ 处理后:")
    print(f"   {modified}")
    
    if details:
        print(f"\n📊 处理详情:")
        for key, value in details.items():
            print(f"   {key}: {value}")
    
    # 计算变化
    if original != modified:
        change_ratio = (len(modified) - len(original)) / len(original) * 100
        print(f"\n📈 文本变化: {change_ratio:+.1f}%")
    else:
        print(f"\n📈 文本变化: 无变化")


def demo_emotion_detection():
    """演示情绪检测功能"""
    print_section("情绪检测功能演示")
    
    injector = create_irrational_behavior_injector()
    
    test_texts = [
        "他非常愤怒，气得想要砸东西，真是受不了了！",
        "她坐在房间里，看着照片，眼泪不停地流下来，心里很难过。",
        "他在等待消息，不停地刷手机，心里很紧张和担心。",
        "她收到好消息后，开心得跳了起来，忍不住想要分享给朋友。",
        "今天天气很好，他去了超市买菜。"
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n🔍 测试文本 {i}:")
        print(f"   {text}")
        
        result = injector.detect_emotional_context(text)
        
        print(f"   主导情绪: {result['primary_emotion'].value if result['primary_emotion'] else '无'}")
        print(f"   情绪强度: {result['emotion_intensity']:.2f}")
        print(f"   上下文触发器: {', '.join(result['context_triggers'][:3]) if result['context_triggers'] else '无'}")
        print(f"   是否有情绪内容: {'是' if result['has_emotional_content'] else '否'}")


def demo_anger_behavior_injection():
    """演示愤怒行为注入"""
    print_section("愤怒行为注入演示")
    
    injector = create_irrational_behavior_injector()
    
    test_texts = [
        "他非常愤怒，走出了房间。",
        "她气得不行，拿起手机想要发消息。",
        "他愤怒地站在门口，不知道该怎么办。",
        "她生气地走在街上，心情糟糕透了。"
    ]
    
    for text in test_texts:
        result = injector.inject_micro_behavior(text, intensity=0.8, max_injections=2)
        
        details = {
            "注入行为数": len(result.injected_behaviors),
            "主导情绪": result.emotional_context["primary_emotion"].value if result.emotional_context["primary_emotion"] else "无",
            "情绪强度": f"{result.emotional_context['emotion_intensity']:.2f}",
            "注入的行为": [b["behavior"] for b in result.injected_behaviors]
        }
        
        print_result(text, result.modified_text, details)


def demo_sadness_behavior_injection():
    """演示悲伤行为注入"""
    print_section("悲伤行为注入演示")
    
    injector = create_irrational_behavior_injector()
    
    test_texts = [
        "她很难过，一个人坐在房间里。",
        "他伤心地看着照片，想起了过去的美好时光。",
        "她躺在床上，把头埋在枕头里，不想见任何人。",
        "他悲伤地整理着房间里的东西。"
    ]
    
    for text in test_texts:
        result = injector.inject_micro_behavior(text, intensity=0.7, max_injections=1)
        
        details = {
            "注入行为数": len(result.injected_behaviors),
            "行为类型": [b["type"] for b in result.injected_behaviors],
            "上下文触发器": result.emotional_context["context_triggers"][:3]
        }
        
        print_result(text, result.modified_text, details)


def demo_anxiety_behavior_injection():
    """演示焦虑行为注入"""
    print_section("焦虑行为注入演示")
    
    injector = create_irrational_behavior_injector()
    
    test_texts = [
        "他在等待重要消息，心里很紧张。",
        "她担心考试结果，不停地刷手机。",
        "他焦虑地在房间里走来走去，不知道该怎么办。",
        "她紧张地坐在那里，手指不自觉地动着。"
    ]
    
    for text in test_texts:
        result = injector.inject_micro_behavior(text, intensity=0.9, max_injections=2)
        
        details = {
            "注入行为数": len(result.injected_behaviors),
            "注入强度": f"{result.processing_details['injection_intensity']:.2f}",
            "注入的行为": [b["behavior"] for b in result.injected_behaviors]
        }
        
        print_result(text, result.modified_text, details)


def demo_intensity_effects():
    """演示不同强度的效果"""
    print_section("不同强度效果演示")
    
    base_text = "他非常愤怒，在房间里走来走去，握紧了拳头。"
    intensities = [0.3, 0.5, 0.7, 0.9]
    
    print(f"📝 基础文本: {base_text}")
    
    for intensity in intensities:
        print(f"\n🎚️ 强度 {intensity}:")
        result = inject_irrational_behavior(base_text, intensity=intensity, max_injections=3)
        print(f"   {result}")


def demo_behavior_potential_analysis():
    """演示行为潜力分析"""
    print_section("行为潜力分析演示")
    
    test_texts = [
        "他非常愤怒，在房间里走来走去，握紧了拳头，想要砸东西。",  # 高潜力
        "她有点难过，坐在那里想事情。",  # 中等潜力
        "今天天气很好，阳光明媚。",  # 低潜力
        "他既愤怒又悲伤，不知道该怎么办，心里很焦虑。"  # 混合情绪
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n📊 分析文本 {i}:")
        print(f"   {text}")
        
        analysis = analyze_emotional_behavior_potential(text)
        
        print(f"   注入潜力: {analysis['injection_potential']:.2f}")
        print(f"   推荐强度: {analysis['recommended_intensity']:.2f}")
        print(f"   适合注入: {'是' if analysis['suitable_for_injection'] else '否'}")
        
        if analysis['emotional_context']['primary_emotion']:
            print(f"   主导情绪: {analysis['emotional_context']['primary_emotion'].value}")


def demo_quick_injection():
    """演示快速注入功能"""
    print_section("快速注入功能演示")
    
    test_cases = [
        ("他愤怒地离开了会议室。", 0.8),
        ("她悲伤地看着窗外的雨。", 0.6),
        ("他焦虑地等待着电话响起。", 0.9),
        ("她开心地跑向朋友。", 0.7)
    ]
    
    for text, intensity in test_cases:
        result = inject_irrational_behavior(text, intensity=intensity, max_injections=1)
        print_result(text, result)


def demo_complex_scenarios():
    """演示复杂场景"""
    print_section("复杂场景演示")
    
    complex_text = """他坐在办公室里，看着电脑屏幕上的邮件，心情越来越糟糕。这个项目又出问题了，客户很不满意，老板也在催促。他感到非常愤怒和焦虑，不知道该怎么办。他站起身来，在房间里走来走去，试图冷静下来。但是越想越气，最后他决定离开办公室，出去透透气。"""
    
    print(f"📝 复杂文本:")
    print(f"   {complex_text}")
    
    injector = create_irrational_behavior_injector()
    result = injector.inject_micro_behavior(complex_text, intensity=0.8, max_injections=3)
    
    print(f"\n✨ 注入后:")
    print(f"   {result.modified_text}")
    
    print(f"\n📊 详细分析:")
    print(f"   原文长度: {len(complex_text)} 字符")
    print(f"   处理后长度: {len(result.modified_text)} 字符")
    print(f"   注入行为数: {len(result.injected_behaviors)}")
    print(f"   主导情绪: {result.emotional_context['primary_emotion'].value if result.emotional_context['primary_emotion'] else '无'}")
    print(f"   情绪强度: {result.emotional_context['emotion_intensity']:.2f}")
    
    if result.injected_behaviors:
        print(f"\n🎭 注入的行为:")
        for i, behavior in enumerate(result.injected_behaviors, 1):
            print(f"   {i}. {behavior['behavior']} ({behavior['type']})")


def main():
    """主函数"""
    print("🎭 非理性行为注入器演示系统")
    print("=" * 60)
    print("本演示将展示AI内容去AI化引擎中的非理性行为注入功能")
    print("通过为角色添加情绪化的非逻辑行为，增加内容的真实感和人性化特征")
    
    try:
        demo_emotion_detection()
        demo_anger_behavior_injection()
        demo_sadness_behavior_injection()
        demo_anxiety_behavior_injection()
        demo_intensity_effects()
        demo_behavior_potential_analysis()
        demo_quick_injection()
        demo_complex_scenarios()
        
        print_section("演示完成")
        print("🎉 非理性行为注入器演示完成！")
        print("💡 该系统成功为AI生成内容添加了人性化的非理性行为")
        print("🎯 通过情绪化的微动作和冲动决定，让角色更加真实可信")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
