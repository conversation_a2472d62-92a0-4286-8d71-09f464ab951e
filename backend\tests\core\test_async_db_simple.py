"""
🧪 [异步测试] 简单的异步数据库操作测试
用于验证异步数据库会话的基本功能
"""

import pytest
import tempfile
from pathlib import Path
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import select

from app.core.base import Base
from app.models.story_bible import StoryBible, AIProvider
from app.models.world_graph import Entity, EntityType
from app.core.config import log_info, log_debug


@pytest.mark.asyncio
class TestAsyncDatabaseSimple:
    """简单的异步数据库测试"""
    
    async def test_basic_async_db_operations(self):
        """测试基本的异步数据库操作"""
        log_info("异步测试", "开始测试基本异步数据库操作")
        
        # 创建临时数据库文件
        temp_dir = tempfile.mkdtemp()
        temp_db_path = Path(temp_dir) / "test_simple_async.db"
        db_url = f"sqlite+aiosqlite:///{temp_db_path}"
        
        # 创建异步引擎
        engine = create_async_engine(
            db_url,
            echo=False,
            connect_args={"check_same_thread": False}
        )
        
        # 创建会话工厂
        async_session_factory = async_sessionmaker(
            bind=engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        try:
            # 创建所有表
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            
            # 测试数据库操作
            async with async_session_factory() as session:
                # 创建测试故事
                story = StoryBible(
                    id="simple_test_story",
                    title="简单测试小说",
                    genre="fantasy",
                    theme="测试主题",
                    protagonist="主角",
                    setting="测试背景",
                    plot_outline="测试大纲",
                    target_audience="adult",
                    ai_provider=AIProvider.ZHIPU
                )
                
                session.add(story)
                await session.commit()
                
                # 验证故事创建成功
                result = await session.execute(
                    select(StoryBible).filter(StoryBible.id == "simple_test_story")
                )
                found_story = result.scalar_one_or_none()
                
                assert found_story is not None
                assert found_story.title == "简单测试小说"
                
                log_info("异步测试", "故事创建和查询成功", story_id=found_story.id)
                
                # 创建测试实体
                entity = Entity(
                    id="simple_test_entity",
                    story_id=story.id,
                    name="测试角色",
                    type=EntityType.CHARACTER,
                    description="简单测试角色",
                    properties={"年龄": 25},
                    first_mentioned_chapter=1,
                    is_active=True
                )
                
                session.add(entity)
                await session.commit()
                
                # 验证实体创建成功
                entity_result = await session.execute(
                    select(Entity).filter(Entity.id == "simple_test_entity")
                )
                found_entity = entity_result.scalar_one_or_none()
                
                assert found_entity is not None
                assert found_entity.name == "测试角色"
                assert found_entity.type == EntityType.CHARACTER
                
                log_info("异步测试", "实体创建和查询成功", entity_id=found_entity.id)
                
        finally:
            await engine.dispose()
            
            # 清理临时文件
            try:
                if temp_db_path.exists():
                    temp_db_path.unlink()
                    log_debug("异步测试", "临时数据库文件已清理")
            except Exception as e:
                log_debug("异步测试", "临时数据库文件清理失败", error=str(e))
    
    async def test_async_query_operations(self):
        """测试异步查询操作"""
        log_info("异步测试", "开始测试异步查询操作")
        
        # 使用内存数据库
        db_url = "sqlite+aiosqlite:///:memory:"
        
        engine = create_async_engine(
            db_url,
            echo=False,
            connect_args={"check_same_thread": False}
        )
        
        async_session_factory = async_sessionmaker(
            bind=engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        try:
            # 创建表
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            
            async with async_session_factory() as session:
                # 创建多个测试实体
                entities = [
                    Entity(
                        id=f"query_test_entity_{i}",
                        story_id="query_test_story",
                        name=f"角色{i}",
                        type=EntityType.CHARACTER,
                        description=f"测试角色{i}",
                        properties={"编号": i},
                        first_mentioned_chapter=1,
                        is_active=True
                    )
                    for i in range(3)
                ]
                
                for entity in entities:
                    session.add(entity)
                
                await session.commit()
                
                # 测试查询所有实体
                all_entities_result = await session.execute(
                    select(Entity).filter(Entity.story_id == "query_test_story")
                )
                all_entities = all_entities_result.scalars().all()
                
                assert len(all_entities) == 3
                log_info("异步测试", "查询所有实体成功", count=len(all_entities))
                
                # 测试按名称查询
                name_query_result = await session.execute(
                    select(Entity).filter(
                        Entity.story_id == "query_test_story",
                        Entity.name == "角色1"
                    )
                )
                found_entity = name_query_result.scalar_one_or_none()
                
                assert found_entity is not None
                assert found_entity.name == "角色1"
                log_info("异步测试", "按名称查询实体成功", entity_name=found_entity.name)
                
        finally:
            await engine.dispose()
    
    async def test_async_transaction_rollback(self):
        """测试异步事务回滚"""
        log_info("异步测试", "开始测试异步事务回滚")
        
        db_url = "sqlite+aiosqlite:///:memory:"
        
        engine = create_async_engine(
            db_url,
            echo=False,
            connect_args={"check_same_thread": False}
        )
        
        async_session_factory = async_sessionmaker(
            bind=engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        try:
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            
            async with async_session_factory() as session:
                # 创建一个实体
                entity = Entity(
                    id="rollback_test_entity",
                    story_id="rollback_test_story",
                    name="回滚测试角色",
                    type=EntityType.CHARACTER,
                    description="用于测试回滚的角色",
                    properties={},
                    first_mentioned_chapter=1,
                    is_active=True
                )
                
                session.add(entity)
                await session.flush()  # 刷新但不提交
                
                # 验证实体在会话中存在
                result = await session.execute(
                    select(Entity).filter(Entity.id == "rollback_test_entity")
                )
                found_entity = result.scalar_one_or_none()
                assert found_entity is not None
                
                # 回滚事务
                await session.rollback()
                
                # 验证实体已被回滚
                result_after_rollback = await session.execute(
                    select(Entity).filter(Entity.id == "rollback_test_entity")
                )
                found_entity_after = result_after_rollback.scalar_one_or_none()
                assert found_entity_after is None
                
                log_info("异步测试", "异步事务回滚测试成功")
                
        finally:
            await engine.dispose()
