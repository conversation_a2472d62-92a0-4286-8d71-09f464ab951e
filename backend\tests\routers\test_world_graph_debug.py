"""
🧪 [测试] 世界知识图谱API调试测试
用于调试API问题的简单测试
"""

import pytest
from fastapi.testclient import TestClient
from app.core.config import log_debug
from main import create_app


@pytest.fixture(scope="function")
def debug_client():
    """创建调试测试客户端"""
    app = create_app()
    with TestClient(app) as client:
        yield client


class TestWorldGraphDebug:
    """世界图谱API调试测试"""
    
    def test_create_entity_debug(self, debug_client: TestClient):
        """调试实体创建API"""
        log_debug("测试", "开始调试实体创建API")
        
        entity_data = {
            "story_id": "nonexistent_story",
            "name": "测试实体",
            "type": "character",
            "description": "测试描述"
        }
        
        response = debug_client.post("/api/v1/world/entities", json=entity_data)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {response.headers}")
        print(f"Response content: {response.text}")
        
        # 不断言，只是看看返回什么
        log_debug("测试", "实体创建API调试完成", 
                 status=response.status_code,
                 content_length=len(response.text))
    
    def test_api_endpoints_exist(self, debug_client: TestClient):
        """测试API端点是否存在"""
        log_debug("测试", "开始测试API端点是否存在")
        
        # 测试OpenAPI文档
        response = debug_client.get("/docs")
        print(f"Docs endpoint status: {response.status_code}")
        
        # 测试根路径
        response = debug_client.get("/")
        print(f"Root endpoint status: {response.status_code}")
        print(f"Root response: {response.text}")
        
        log_debug("测试", "API端点存在性测试完成")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
