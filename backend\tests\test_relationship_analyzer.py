"""
🕸️ [关系分析] 角色关系网络分析器测试

测试关系网络分析器的各项功能，包括：
1. 分析器初始化
2. 网络图构建
3. 中心性指标计算
4. 社区检测
5. 可视化数据生成
6. 分析结果验证
"""

import pytest
import asyncio
from datetime import datetime
from typing import List, Dict, Any

from app.core.relationship_analyzer import (
    RelationshipNetworkAnalyzer,
    create_relationship_analyzer,
    NodeMetrics,
    EdgeMetrics,
    Community,
    CentralityType,
    CommunityAlgorithm
)
from app.schemas.world_graph import WorldGraphResponse, EntityResponse, RelationshipResponse
from app.models.world_graph import EntityType, RelationshipStatus


class TestRelationshipNetworkAnalyzer:
    """关系网络分析器测试类"""
    
    @pytest.fixture
    def analyzer(self) -> RelationshipNetworkAnalyzer:
        """创建分析器实例"""
        return create_relationship_analyzer()
    
    @pytest.fixture
    def sample_world_graph(self) -> WorldGraphResponse:
        """创建示例世界知识图谱"""
        entities = [
            EntityResponse(
                id="char-001",
                name="张三",
                type=EntityType.CHARACTER,
                description="主角，年轻的剑客",
                properties={"年龄": 20, "职业": "剑客"},
                importance_score=0.9,
                first_mentioned_chapter=1,
                last_mentioned_chapter=5,
                story_id="test-story",
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            EntityResponse(
                id="char-002",
                name="李四",
                type=EntityType.CHARACTER,
                description="张三的好友",
                properties={"年龄": 22, "职业": "商人"},
                importance_score=0.7,
                first_mentioned_chapter=1,
                last_mentioned_chapter=4,
                story_id="test-story",
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            EntityResponse(
                id="char-003",
                name="王五",
                type=EntityType.CHARACTER,
                description="反派角色",
                properties={"年龄": 35, "职业": "盗贼"},
                importance_score=0.8,
                first_mentioned_chapter=2,
                last_mentioned_chapter=5,
                story_id="test-story",
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            EntityResponse(
                id="char-004",
                name="赵六",
                type=EntityType.CHARACTER,
                description="师父角色",
                properties={"年龄": 60, "职业": "武师"},
                importance_score=0.6,
                first_mentioned_chapter=1,
                last_mentioned_chapter=3,
                story_id="test-story",
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            EntityResponse(
                id="item-001",
                name="神剑",
                type=EntityType.ITEM,
                description="传说中的神剑",
                properties={"类型": "武器"},
                importance_score=0.5,
                first_mentioned_chapter=3,
                last_mentioned_chapter=5,
                story_id="test-story",
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
        ]
        
        relationships = [
            RelationshipResponse(
                id="rel-001",
                source_entity_id="char-001",
                target_entity_id="char-002",
                relationship_type="朋友",
                description="张三和李四是好朋友",
                strength=0.8,
                status=RelationshipStatus.ACTIVE,
                established_chapter=1,
                last_updated_chapter=4,
                story_id="test-story",
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            RelationshipResponse(
                id="rel-002",
                source_entity_id="char-001",
                target_entity_id="char-003",
                relationship_type="敌人",
                description="张三和王五是敌人",
                strength=0.9,
                status=RelationshipStatus.ACTIVE,
                established_chapter=2,
                last_updated_chapter=5,
                story_id="test-story",
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            RelationshipResponse(
                id="rel-003",
                source_entity_id="char-001",
                target_entity_id="char-004",
                relationship_type="师父",
                description="赵六是张三的师父",
                strength=0.7,
                status=RelationshipStatus.ACTIVE,
                established_chapter=1,
                last_updated_chapter=3,
                story_id="test-story",
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            RelationshipResponse(
                id="rel-004",
                source_entity_id="char-002",
                target_entity_id="char-003",
                relationship_type="竞争者",
                description="李四和王五是商业竞争者",
                strength=0.6,
                status=RelationshipStatus.ACTIVE,
                established_chapter=2,
                last_updated_chapter=4,
                story_id="test-story",
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            RelationshipResponse(
                id="rel-005",
                source_entity_id="char-001",
                target_entity_id="item-001",
                relationship_type="拥有",
                description="张三拥有神剑",
                strength=0.5,
                status=RelationshipStatus.ACTIVE,
                established_chapter=3,
                last_updated_chapter=5,
                story_id="test-story",
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
        ]
        
        return WorldGraphResponse(
            story_id="test-story",
            story_title="测试故事",
            entities=entities,
            relationships=relationships,
            total_entities=len(entities),
            total_relationships=len(relationships),
            last_updated=datetime.now()
        )
    
    def test_analyzer_initialization(self, analyzer: RelationshipNetworkAnalyzer):
        """测试分析器初始化"""
        assert analyzer is not None
        assert len(analyzer.centrality_algorithms) > 0
        assert len(analyzer.community_algorithms) > 0
        assert len(analyzer.relationship_weights) > 0
        
        # 验证权重配置
        assert "朋友" in analyzer.relationship_weights
        assert "敌人" in analyzer.relationship_weights
        assert analyzer.relationship_weights["朋友"] > 0
        assert analyzer.relationship_weights["敌人"] > 0
    
    @pytest.mark.asyncio
    async def test_build_network_graph(self, analyzer: RelationshipNetworkAnalyzer, sample_world_graph: WorldGraphResponse):
        """测试网络图构建"""
        network_graph = await analyzer._build_network_graph(sample_world_graph)
        
        # 验证网络图结构
        assert "nodes" in network_graph
        assert "edges" in network_graph
        assert "adjacency_list" in network_graph
        
        # 验证节点数量（只包含角色）
        assert len(network_graph["nodes"]) == 4  # 4个角色
        
        # 验证边数量（只包含角色间关系）
        character_relationships = 0
        for rel in sample_world_graph.relationships:
            source_is_char = any(e.id == rel.source_entity_id and e.type == EntityType.CHARACTER for e in sample_world_graph.entities)
            target_is_char = any(e.id == rel.target_entity_id and e.type == EntityType.CHARACTER for e in sample_world_graph.entities)
            if source_is_char and target_is_char:
                character_relationships += 1
        
        assert len(network_graph["edges"]) == character_relationships
        
        # 验证邻接表
        assert "char-001" in network_graph["adjacency_list"]
        assert len(network_graph["adjacency_list"]["char-001"]) > 0
    
    @pytest.mark.asyncio
    async def test_calculate_network_metrics(self, analyzer: RelationshipNetworkAnalyzer, sample_world_graph: WorldGraphResponse):
        """测试网络基础指标计算"""
        network_graph = await analyzer._build_network_graph(sample_world_graph)
        metrics = await analyzer._calculate_network_metrics(network_graph)
        
        # 验证指标存在
        assert "density" in metrics
        assert "average_clustering" in metrics
        assert "diameter" in metrics
        
        # 验证指标范围
        assert 0.0 <= metrics["density"] <= 1.0
        assert 0.0 <= metrics["average_clustering"] <= 1.0
        assert metrics["diameter"] >= 0
    
    def test_degree_centrality_calculation(self, analyzer: RelationshipNetworkAnalyzer):
        """测试度中心性计算"""
        # 创建简单的邻接表
        adjacency_list = {
            "A": ["B", "C"],
            "B": ["A", "C"],
            "C": ["A", "B", "D"],
            "D": ["C"]
        }
        
        centrality = analyzer._calculate_degree_centrality(adjacency_list)
        
        # 验证结果
        assert len(centrality) == 4
        assert all(0.0 <= score <= 1.0 for score in centrality.values())
        
        # C应该有最高的度中心性（3个连接）
        assert centrality["C"] == 1.0  # 最大度数，归一化后为1.0
    
    def test_betweenness_centrality_calculation(self, analyzer: RelationshipNetworkAnalyzer):
        """测试介数中心性计算"""
        adjacency_list = {
            "A": ["B"],
            "B": ["A", "C"],
            "C": ["B", "D"],
            "D": ["C"]
        }
        
        centrality = analyzer._calculate_betweenness_centrality(adjacency_list)
        
        # 验证结果
        assert len(centrality) == 4
        assert all(0.0 <= score <= 1.0 for score in centrality.values())
        
        # B和C应该有较高的介数中心性（位于路径中间）
        assert centrality["B"] > 0 or centrality["C"] > 0
    
    def test_closeness_centrality_calculation(self, analyzer: RelationshipNetworkAnalyzer):
        """测试接近中心性计算"""
        adjacency_list = {
            "A": ["B"],
            "B": ["A", "C", "D"],
            "C": ["B"],
            "D": ["B"]
        }
        
        centrality = analyzer._calculate_closeness_centrality(adjacency_list)
        
        # 验证结果
        assert len(centrality) == 4
        assert all(0.0 <= score <= 1.0 for score in centrality.values())
        
        # B应该有最高的接近中心性（到其他节点距离最短）
        assert centrality["B"] == 1.0
    
    @pytest.mark.asyncio
    async def test_calculate_node_metrics(self, analyzer: RelationshipNetworkAnalyzer, sample_world_graph: WorldGraphResponse):
        """测试节点指标计算"""
        network_graph = await analyzer._build_network_graph(sample_world_graph)
        node_metrics = await analyzer._calculate_node_metrics(network_graph, sample_world_graph)
        
        # 验证节点指标数量
        assert len(node_metrics) == 4  # 4个角色
        
        # 验证每个节点指标的完整性
        for metrics in node_metrics:
            assert isinstance(metrics, NodeMetrics)
            assert metrics.node_id is not None
            assert metrics.name is not None
            assert 0.0 <= metrics.degree_centrality <= 1.0
            assert 0.0 <= metrics.betweenness_centrality <= 1.0
            assert 0.0 <= metrics.closeness_centrality <= 1.0
            assert 0.0 <= metrics.influence_score <= 1.0
            assert metrics.importance_rank > 0
            assert metrics.total_connections >= 0
        
        # 验证排名的唯一性
        ranks = [metrics.importance_rank for metrics in node_metrics]
        assert len(set(ranks)) == len(ranks)  # 所有排名都不同
    
    @pytest.mark.asyncio
    async def test_calculate_edge_metrics(self, analyzer: RelationshipNetworkAnalyzer, sample_world_graph: WorldGraphResponse):
        """测试边指标计算"""
        network_graph = await analyzer._build_network_graph(sample_world_graph)
        edge_metrics = await analyzer._calculate_edge_metrics(network_graph, sample_world_graph)
        
        # 验证边指标
        assert len(edge_metrics) > 0
        
        for metrics in edge_metrics:
            assert isinstance(metrics, EdgeMetrics)
            assert metrics.edge_id is not None
            assert metrics.source_id is not None
            assert metrics.target_id is not None
            assert metrics.relationship_type is not None
            assert 0.0 <= metrics.strength <= 1.0
            assert 0.0 <= metrics.weight <= 1.0
            assert 0.0 <= metrics.stability <= 1.0
            assert metrics.duration > 0
    
    @pytest.mark.asyncio
    async def test_detect_communities(self, analyzer: RelationshipNetworkAnalyzer, sample_world_graph: WorldGraphResponse):
        """测试社区检测"""
        network_graph = await analyzer._build_network_graph(sample_world_graph)
        communities = await analyzer._detect_communities(network_graph, sample_world_graph)
        
        # 验证社区检测结果
        assert isinstance(communities, list)
        
        for community in communities:
            assert isinstance(community, Community)
            assert community.id is not None
            assert community.name is not None
            assert len(community.members) >= 2  # 最小社区大小
            assert community.size == len(community.members)
            assert 0.0 <= community.density <= 1.0
    
    @pytest.mark.asyncio
    async def test_full_analysis(self, analyzer: RelationshipNetworkAnalyzer, sample_world_graph: WorldGraphResponse):
        """测试完整的关系网络分析"""
        result = await analyzer.analyze_relationship_network(sample_world_graph)
        
        # 验证分析结果的完整性
        assert result.story_id == "test-story"
        assert result.total_nodes > 0
        assert result.total_edges >= 0
        assert 0.0 <= result.network_density <= 1.0
        assert result.community_count >= 0
        assert len(result.node_metrics) > 0
        assert len(result.edge_metrics) >= 0
        assert result.visualization_data is not None
        assert len(result.key_insights) > 0
        assert 0.0 <= result.analysis_confidence <= 1.0
        assert result.computation_time > 0
        
        # 验证可视化数据
        viz_data = result.visualization_data
        assert len(viz_data.nodes) > 0
        assert len(viz_data.edges) >= 0
        assert viz_data.layout_algorithm is not None
        
        # 验证节点数据
        for node in viz_data.nodes:
            assert "id" in node
            assert "name" in node
            assert "size" in node
            assert "color" in node
            assert "x" in node
            assert "y" in node
    
    def test_create_relationship_analyzer(self):
        """测试分析器创建函数"""
        analyzer = create_relationship_analyzer()
        
        assert isinstance(analyzer, RelationshipNetworkAnalyzer)
        assert analyzer is not None
        
        # 验证配置
        assert len(analyzer.centrality_algorithms) > 0
        assert len(analyzer.community_algorithms) > 0
        assert len(analyzer.relationship_weights) > 0
