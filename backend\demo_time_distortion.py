"""
🎬 [演示] 时间感知扭曲器演示脚本

展示时间感知扭曲器的各种功能，包括情绪检测、
时间扭曲、主观时间调整等效果。

运行方式: python demo_time_distortion.py

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.time_distortion import (
    create_time_perception_distorter,
    distort_time_perception,
    apply_subjective_timing,
    analyze_time_distortion_potential,
    EmotionalState
)


def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"⏰ {title}")
    print(f"{'='*60}")


def print_result(original: str, modified: str, details: dict = None):
    """打印处理结果"""
    print(f"\n📝 原文:")
    print(f"   {original}")
    print(f"\n✨ 处理后:")
    print(f"   {modified}")
    
    if details:
        print(f"\n📊 处理详情:")
        for key, value in details.items():
            print(f"   {key}: {value}")
    
    # 计算变化
    if original != modified:
        change_ratio = (len(modified) - len(original)) / len(original) * 100
        print(f"\n📈 文本变化: {change_ratio:+.1f}%")
    else:
        print(f"\n📈 文本变化: 无变化")


def demo_emotion_time_detection():
    """演示情绪和时间检测功能"""
    print_section("情绪和时间检测功能演示")
    
    distorter = create_time_perception_distorter()
    
    test_texts = [
        "他非常愤怒，等了几分钟就爆发了。",
        "她很难过，感觉一会儿都像过了很久。",
        "他焦虑地等待着，觉得时间不够了。",
        "她开心地玩了几小时，时间过得真快。",
        "他害怕地等了片刻，感觉像过了一个世纪。",
        "今天天气很好，他去了超市。"  # 无情绪无时间
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n🔍 测试文本 {i}:")
        print(f"   {text}")
        
        result = distorter.detect_emotional_context(text)
        
        print(f"   主导情绪: {result['primary_emotion'].value if result['primary_emotion'] else '无'}")
        print(f"   情绪强度: {result['emotion_intensity']:.2f}")
        print(f"   时间引用: {', '.join(result['time_references'][:3]) if result['time_references'] else '无'}")
        print(f"   有情绪内容: {'是' if result['has_emotional_content'] else '否'}")
        print(f"   有时间内容: {'是' if result['has_time_content'] else '否'}")


def demo_anger_time_distortion():
    """演示愤怒时间扭曲"""
    print_section("愤怒时间扭曲演示")
    
    distorter = create_time_perception_distorter()
    
    test_texts = [
        "他愤怒地等了几分钟，然后离开了。",
        "她生气地坐了一会儿，想要冷静下来。",
        "他恼火地等了半小时，还是没有消息。",
        "她暴怒地等了不久，就开始砸东西。"
    ]
    
    for text in test_texts:
        result = distorter.distort_time_perception(text, intensity=0.8)
        
        details = {
            "扭曲数量": len(result.distortions_applied),
            "主导情绪": result.emotional_context["primary_emotion"].value if result.emotional_context["primary_emotion"] else "无",
            "情绪强度": f"{result.emotional_context['emotion_intensity']:.2f}",
            "感知模式": [d["pattern"] for d in result.distortions_applied]
        }
        
        print_result(text, result.modified_text, details)


def demo_sadness_time_distortion():
    """演示悲伤时间扭曲"""
    print_section("悲伤时间扭曲演示")
    
    distorter = create_time_perception_distorter()
    
    test_texts = [
        "她难过地坐了几分钟，想着过去的事情。",
        "他伤心地等了一会儿，希望有人来安慰。",
        "她沮丧地坐了片刻，感觉很孤独。",
        "他痛苦地等了不久，眼泪就流下来了。"
    ]
    
    for text in test_texts:
        result = distorter.distort_time_perception(text, intensity=0.9)
        
        details = {
            "扭曲数量": len(result.distortions_applied),
            "感知模式": [d["pattern"] for d in result.distortions_applied],
            "原始表达": [d["original_expressions"] for d in result.distortions_applied],
            "扭曲表达": [d["distorted_expressions"] for d in result.distortions_applied]
        }
        
        print_result(text, result.modified_text, details)


def demo_anxiety_time_distortion():
    """演示焦虑时间扭曲"""
    print_section("焦虑时间扭曲演示")
    
    distorter = create_time_perception_distorter()
    
    test_texts = [
        "他焦虑地等待着，还有时间完成任务。",
        "她紧张地看着时钟，几分钟后就要开始了。",
        "他担心地等了一会儿，不知道结果如何。",
        "她恐慌地发现时间充足，但还是很着急。"
    ]
    
    for text in test_texts:
        result = distorter.distort_time_perception(text, intensity=0.9)
        
        details = {
            "扭曲数量": len(result.distortions_applied),
            "扭曲强度": f"{result.processing_details['distortion_intensity']:.2f}",
            "感知模式": [d["pattern"] for d in result.distortions_applied]
        }
        
        print_result(text, result.modified_text, details)


def demo_subjective_timing():
    """演示主观时间流逝调整"""
    print_section("主观时间流逝调整演示")
    
    distorter = create_time_perception_distorter()
    
    test_cases = [
        ("他很愤怒。然后离开了房间。", EmotionalState.ANGER, 0.8),
        ("她很难过。坐在那里发呆。", EmotionalState.SADNESS, 0.7),
        ("他很焦虑。不知道该怎么办。", EmotionalState.ANXIETY, 0.9),
        ("她很开心。跑向朋友们。", EmotionalState.JOY, 0.6),
        ("他很害怕。躲在角落里。", EmotionalState.FEAR, 0.8)
    ]
    
    for text, emotion, intensity in test_cases:
        result = distorter.apply_subjective_timing(text, emotion, intensity)
        
        print(f"\n📝 原文: {text}")
        print(f"🎭 情绪: {emotion.value} (强度: {intensity})")
        print(f"✨ 调整后: {result}")


def demo_intensity_effects():
    """演示不同强度的效果"""
    print_section("不同强度效果演示")
    
    base_text = "他愤怒地等了几分钟，想要离开。"
    intensities = [0.3, 0.5, 0.7, 0.9]
    
    print(f"📝 基础文本: {base_text}")
    
    for intensity in intensities:
        print(f"\n⚡ 强度 {intensity}:")
        result = distort_time_perception(base_text, intensity=intensity)
        print(f"   {result}")


def demo_distortion_potential_analysis():
    """演示扭曲潜力分析"""
    print_section("扭曲潜力分析演示")
    
    test_texts = [
        "他非常愤怒，等了几分钟就爆发了。",  # 高潜力
        "她有点难过，坐了一会儿。",  # 中等潜力
        "他很愤怒，想要砸东西。",  # 有情绪无时间
        "几分钟后他就到了。",  # 有时间无情绪
        "今天天气很好。"  # 低潜力
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n📊 分析文本 {i}:")
        print(f"   {text}")
        
        analysis = analyze_time_distortion_potential(text)
        
        print(f"   扭曲潜力: {analysis['distortion_potential']:.2f}")
        print(f"   推荐强度: {analysis['recommended_intensity']:.2f}")
        print(f"   适合扭曲: {'是' if analysis['suitable_for_distortion'] else '否'}")
        print(f"   时间引用数: {analysis['time_references_found']}")
        
        if analysis['primary_emotion']:
            print(f"   主导情绪: {analysis['primary_emotion']}")


def demo_complex_scenarios():
    """演示复杂场景"""
    print_section("复杂场景演示")
    
    complex_text = """他坐在办公室里，焦虑地等待着重要电话。几分钟过去了，还是没有消息。他看了看时钟，感觉时间过得很慢。半小时后，电话终于响了，但他已经非常愤怒了。接通电话的一瞬间，他的心情突然变得复杂起来。"""
    
    print(f"📝 复杂文本:")
    print(f"   {complex_text}")
    
    distorter = create_time_perception_distorter()
    result = distorter.distort_time_perception(complex_text, intensity=0.8)
    
    print(f"\n✨ 扭曲后:")
    print(f"   {result.modified_text}")
    
    print(f"\n📊 详细分析:")
    print(f"   原文长度: {len(complex_text)} 字符")
    print(f"   处理后长度: {len(result.modified_text)} 字符")
    print(f"   扭曲数量: {len(result.distortions_applied)}")
    print(f"   主导情绪: {result.emotional_context['primary_emotion'].value if result.emotional_context['primary_emotion'] else '无'}")
    print(f"   情绪强度: {result.emotional_context['emotion_intensity']:.2f}")
    print(f"   时间引用: {', '.join(result.emotional_context['time_references'])}")
    
    if result.distortions_applied:
        print(f"\n⏰ 应用的扭曲:")
        for i, distortion in enumerate(result.distortions_applied, 1):
            print(f"   {i}. {distortion['pattern']} ({distortion['emotion']})")
            for j, (orig, dist) in enumerate(zip(distortion['original_expressions'], distortion['distorted_expressions'])):
                print(f"      '{orig}' → '{dist}'")


def demo_emotion_comparison():
    """演示不同情绪的时间感知对比"""
    print_section("不同情绪时间感知对比")
    
    base_scenarios = [
        ("等了几分钟", "愤怒"),
        ("等了几分钟", "悲伤"),
        ("等了几分钟", "焦虑"),
        ("等了几分钟", "开心"),
        ("等了几分钟", "害怕")
    ]
    
    emotion_map = {
        "愤怒": EmotionalState.ANGER,
        "悲伤": EmotionalState.SADNESS,
        "焦虑": EmotionalState.ANXIETY,
        "开心": EmotionalState.JOY,
        "害怕": EmotionalState.FEAR
    }
    
    for time_expr, emotion_name in base_scenarios:
        text = f"他很{emotion_name}，{time_expr}。"
        result = distort_time_perception(text, intensity=0.8)
        
        print(f"\n🎭 {emotion_name}情绪:")
        print(f"   原文: {text}")
        print(f"   扭曲: {result}")


def main():
    """主函数"""
    print("⏰ 时间感知扭曲器演示系统")
    print("=" * 60)
    print("本演示将展示AI内容去AI化引擎中的时间感知扭曲功能")
    print("通过模拟人类在不同情绪下的主观时间感知，让时间描述更加真实")
    
    try:
        demo_emotion_time_detection()
        demo_anger_time_distortion()
        demo_sadness_time_distortion()
        demo_anxiety_time_distortion()
        demo_subjective_timing()
        demo_intensity_effects()
        demo_distortion_potential_analysis()
        demo_complex_scenarios()
        demo_emotion_comparison()
        
        print_section("演示完成")
        print("🎉 时间感知扭曲器演示完成！")
        print("💡 该系统成功将客观时间描述转换为主观情绪化的时间感知")
        print("🎯 通过情绪驱动的时间扭曲，让AI生成内容更具人性化特征")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
