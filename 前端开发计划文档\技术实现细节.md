# 前端技术实现细节文档

## 🏗️ 架构设计

### 整体架构
```
┌─────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                    │
├─────────────────────────────────────────────────────────┤
│  React 19 组件 + Tailwind CSS + 响应式设计                │
├─────────────────────────────────────────────────────────┤
│                   业务逻辑层 (Logic Layer)                  │
├─────────────────────────────────────────────────────────┤
│  自定义Hooks + Zustand状态管理 + 业务逻辑封装               │
├─────────────────────────────────────────────────────────┤
│                   数据服务层 (Service Layer)                │
├─────────────────────────────────────────────────────────┤
│  API客户端 + 数据适配器 + 缓存管理                         │
├─────────────────────────────────────────────────────────┤
│                   基础设施层 (Infrastructure)               │
├─────────────────────────────────────────────────────────┤
│  Vite构建 + TypeScript + 路由 + 配置管理                   │
└─────────────────────────────────────────────────────────┘
```

## 📦 核心模块详解

### 1. 组件系统 (Components)

#### UI组件库 (`components/ui/`)
```typescript
// 基础组件设计原则
interface ComponentProps {
  className?: string;      // 支持样式扩展
  children?: ReactNode;    // 支持内容组合
  variant?: string;        // 支持变体切换
  size?: 'sm' | 'md' | 'lg'; // 支持尺寸调节
}

// 示例：Button组件
export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  className,
  children,
  ...props
}) => {
  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-md font-medium transition-colors',
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};
```

#### 高级组件 (`components/features/`)
- **AuthorWorkspace**: 专业作者工作台布局
- **NotificationProvider**: 全局通知系统
- **ThemeProvider**: 主题管理系统
- **AIModelSwitcher**: AI模型切换器

### 2. 状态管理 (Stores)

#### Zustand状态设计
```typescript
// 状态分片设计
interface AuthStore {
  isLoggedIn: boolean;
  token: string | null;
  user: User | null;
  login: (token: string) => void;
  logout: () => void;
}

interface StoryStore {
  currentStory: StoryBible | null;
  chapters: Chapter[];
  isGenerating: boolean;
  setCurrentStory: (story: StoryBible) => void;
  addChapter: (chapter: Chapter) => void;
}

// 状态持久化
const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      isLoggedIn: false,
      token: null,
      user: null,
      login: (token) => set({ isLoggedIn: true, token }),
      logout: () => set({ isLoggedIn: false, token: null, user: null }),
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
```

### 3. API服务层 (Services)

#### API客户端设计
```typescript
// 统一API客户端
export class SimpleApiService {
  // 健康检查
  static async healthCheck(): Promise<HealthResponse> {
    const startTime = performance.now();
    const response = await axios.get(API_ENDPOINTS.HEALTH);
    const latency = Math.round(performance.now() - startTime);
    
    debugLog('API健康检查', '服务器响应正常', { latency });
    return { connected: true, latency };
  }

  // 流式API支持
  static async generateStoryBibleStream(
    concept: string,
    onChunk: (chunk: string) => void,
    onComplete: (data: StoryBible) => void,
    onError: (error: string) => void
  ): Promise<void> {
    const response = await fetch(API_ENDPOINTS.STORY.GENERATE_BIBLE, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ story_concept: concept, stream: true })
    });

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value);
      // 处理SSE格式数据
      this.parseSSEData(chunk, onChunk, onComplete, onError);
    }
  }
}
```

### 4. 自定义Hooks系统

#### Hook设计模式
```typescript
// 业务逻辑封装
export const useBible = () => {
  const { currentStory, setCurrentStory } = useStoryStore();
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 生成故事圣经
  const handleGenerateChapter = useCallback(async (concept: string) => {
    debugLog('生成', '开始生成故事圣经', { concept });
    
    try {
      const result = await SimpleApiService.generateStoryBible(concept);
      setCurrentStory(result);
      debugLog('生成', '故事圣经生成成功', { id: result.id });
    } catch (error) {
      debugLog('错误', '故事圣经生成失败', { error });
      throw error;
    }
  }, [setCurrentStory]);

  // 内联编辑功能
  const startEditing = useCallback((section: string) => {
    setEditingSection(section);
    debugLog('UI', '开始编辑章节', { section });
  }, []);

  return {
    storyData: currentStory,
    editingSection,
    hasUnsavedChanges,
    handleGenerateChapter,
    startEditing,
    // ... 其他方法
  };
};
```

## 🎨 主题系统实现

### 主题提供器设计
```typescript
interface ThemeContextType {
  isDark: boolean;
  fontSize: 'small' | 'medium' | 'large';
  toggleTheme: () => void;
  setFontSize: (size: 'small' | 'medium' | 'large') => void;
}

export const ThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isDark, setIsDark] = useState(() => {
    const saved = localStorage.getItem('theme-dark');
    return saved ? JSON.parse(saved) : false;
  });

  const [fontSize, setFontSize] = useState<'small' | 'medium' | 'large'>(() => {
    return (localStorage.getItem('theme-font-size') as any) || 'medium';
  });

  // 主题切换逻辑
  const toggleTheme = useCallback(() => {
    setIsDark(prev => {
      const newValue = !prev;
      localStorage.setItem('theme-dark', JSON.stringify(newValue));
      debugLog('UI', '切换主题模式', { mode: newValue ? 'dark' : 'light' });
      return newValue;
    });
  }, []);

  // 应用主题类名
  useEffect(() => {
    document.documentElement.className = cn(
      isDark ? 'dark' : '',
      `font-${fontSize}`
    );
  }, [isDark, fontSize]);

  return (
    <ThemeContext.Provider value={{ isDark, fontSize, toggleTheme, setFontSize }}>
      {children}
    </ThemeContext.Provider>
  );
};
```

## 🔄 数据流管理

### 数据流向图
```
用户操作 → Hook处理 → Store更新 → 组件重渲染
    ↓
API调用 → 服务层处理 → 数据转换 → 状态更新
    ↓
错误处理 → 通知系统 → 用户反馈
```

### 状态更新策略
```typescript
// 乐观更新模式
const updateChapter = async (chapterId: number, content: string) => {
  // 1. 立即更新UI (乐观更新)
  updateChapterInStore(chapterId, content);
  
  try {
    // 2. 发送API请求
    await SimpleApiService.updateChapter(chapterId, content);
    debugLog('成功', '章节更新成功', { chapterId });
  } catch (error) {
    // 3. 失败时回滚
    revertChapterInStore(chapterId);
    debugLog('错误', '章节更新失败', { chapterId, error });
    throw error;
  }
};
```

## 🚀 性能优化策略

### 1. 组件优化
```typescript
// React.memo 优化
export const ExpensiveComponent = React.memo<Props>(({ data, onUpdate }) => {
  // 使用 useMemo 缓存计算结果
  const processedData = useMemo(() => {
    return expensiveCalculation(data);
  }, [data]);

  // 使用 useCallback 缓存函数
  const handleClick = useCallback((id: string) => {
    onUpdate(id);
  }, [onUpdate]);

  return <div>{/* 组件内容 */}</div>;
});
```

### 2. 代码分割
```typescript
// 路由级代码分割
const BibleStep = lazy(() => import('../pages/bible/page'));
const CreationCockpit = lazy(() => import('../pages/cockpit/page'));

// 组件级懒加载
const HeavyComponent = lazy(() => import('./HeavyComponent'));
```

### 3. 状态优化
```typescript
// 选择性订阅
const useOptimizedStore = () => {
  // 只订阅需要的状态片段
  const isLoggedIn = useAuthStore(state => state.isLoggedIn);
  const currentStory = useStoryStore(state => state.currentStory);
  
  return { isLoggedIn, currentStory };
};
```

## 🔍 调试与监控

### 日志系统
```typescript
// 结构化日志函数
export const debugLog = (category: string, message: string, ...args: any[]) => {
  if (!isDevelopment()) return;
  
  const timestamp = new Date().toISOString();
  const emoji = getCategoryEmoji(category);
  
  console.log(
    `${emoji} [${category}] ${timestamp} - ${message}`,
    ...args
  );
};

// 日志分类
const getCategoryEmoji = (category: string): string => {
  const emojiMap = {
    '认证': '🔐',
    '生成': '📝',
    'UI': '🎨',
    '系统': '🏗️',
    '文件': '📁',
    'API': '🌐',
    '错误': '❌',
    '成功': '✅'
  };
  return emojiMap[category] || '📋';
};
```

### 性能监控
```typescript
// 性能监控Hook
export const usePerformanceMonitor = (componentName: string) => {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      if (renderTime > 100) { // 超过100ms记录
        debugLog('性能', `${componentName} 渲染耗时`, { renderTime });
      }
    };
  });
};
```

## 📱 响应式设计

### 断点系统
```typescript
// Tailwind CSS 断点配置
const breakpoints = {
  sm: '640px',   // 手机横屏
  md: '768px',   // 平板
  lg: '1024px',  // 小型桌面
  xl: '1280px',  // 大型桌面
  '2xl': '1536px' // 超大屏幕
};

// 响应式Hook
export const useResponsive = () => {
  const [screenSize, setScreenSize] = useState<'sm' | 'md' | 'lg' | 'xl'>('lg');
  
  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width < 768) setScreenSize('sm');
      else if (width < 1024) setScreenSize('md');
      else if (width < 1280) setScreenSize('lg');
      else setScreenSize('xl');
    };
    
    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);
  
  return { screenSize, isMobile: screenSize === 'sm' };
};
```

这个技术实现细节文档详细说明了前端系统的核心技术实现，为开发团队提供了完整的技术参考。
