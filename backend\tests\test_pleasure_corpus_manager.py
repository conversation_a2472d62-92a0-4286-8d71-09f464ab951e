"""
🧪 [测试] 网文爽感语料库管理器测试

测试爽感语料库管理器的各项功能，包括：
- 语料库初始化
- 爽感模式获取
- 节奏模板获取
- 强化表达获取

作者: 系统
创建时间: 2025-08-05
"""

import pytest
import asyncio
from pathlib import Path

from app.core.pleasure_corpus_manager import (
    create_pleasure_corpus_manager,
    PleasureCorpusManager,
    PleasurePatternData,
    RhythmTemplateData
)
from app.core.config import log_info, log_debug, log_error, log_success


class TestPleasureCorpusManager:
    """🎯 [测试类] 爽感语料库管理器测试"""
    
    @pytest.fixture
    async def corpus_manager(self):
        """创建测试用的语料库管理器"""
        log_debug("🧪测试", "创建测试用爽感语料库管理器")
        
        # 使用项目根目录下的语料库
        corpus_root = str(Path(__file__).parent.parent / "data" / "corpus")
        manager = create_pleasure_corpus_manager(corpus_root)
        
        # 初始化语料库
        success = await manager.initialize()
        assert success, "语料库初始化失败"
        
        log_success("🧪测试", "测试用爽感语料库管理器创建成功")
        return manager
    
    @pytest.mark.asyncio
    async def test_corpus_initialization(self, corpus_manager):
        """测试语料库初始化"""
        log_info("🧪测试", "开始测试语料库初始化")
        
        # 检查语料库数据是否加载
        assert len(corpus_manager.corpus_data) > 0, "语料库数据为空"
        
        # 检查必要的语料库文件是否加载
        expected_files = ["face_slapping", "power_fantasy", "upgrade", "rhythm_templates", "enhancement_expressions"]
        for file_key in expected_files:
            assert file_key in corpus_manager.corpus_data, f"缺少语料库文件: {file_key}"
        
        log_success("🧪测试", "语料库初始化测试通过", 加载文件数=len(corpus_manager.corpus_data))
    
    @pytest.mark.asyncio
    async def test_get_pleasure_patterns_face_slapping(self, corpus_manager):
        """测试获取打脸爽感模式"""
        log_info("🧪测试", "开始测试获取打脸爽感模式")
        
        patterns = await corpus_manager.get_pleasure_patterns_by_type(
            "face_slapping", count=3, min_quality=0.5
        )
        
        assert len(patterns) > 0, "未获取到打脸爽感模式"
        
        # 检查模式数据结构
        for pattern in patterns:
            assert isinstance(pattern, PleasurePatternData), "模式数据类型错误"
            assert pattern.content, "模式内容为空"
            assert pattern.quality >= 0.5, "模式质量不符合要求"
            assert isinstance(pattern.tags, list), "标签应为列表"
            assert isinstance(pattern.data, dict), "数据应为字典"
        
        log_success("🧪测试", "打脸爽感模式获取测试通过", 获取数量=len(patterns))
    
    @pytest.mark.asyncio
    async def test_get_pleasure_patterns_power_fantasy(self, corpus_manager):
        """测试获取权力幻想模式"""
        log_info("🧪测试", "开始测试获取权力幻想模式")
        
        patterns = await corpus_manager.get_pleasure_patterns_by_type(
            "power_fantasy", count=2, min_quality=0.6
        )
        
        assert len(patterns) > 0, "未获取到权力幻想模式"
        
        # 检查模式特征
        for pattern in patterns:
            assert pattern.quality >= 0.6, "模式质量不符合要求"
            assert "权力" in pattern.content or "威压" in pattern.content or "统治" in pattern.content, \
                "权力幻想模式内容不符合预期"
        
        log_success("🧪测试", "权力幻想模式获取测试通过", 获取数量=len(patterns))
    
    @pytest.mark.asyncio
    async def test_get_pleasure_patterns_upgrade(self, corpus_manager):
        """测试获取升级进化模式"""
        log_info("🧪测试", "开始测试获取升级进化模式")
        
        patterns = await corpus_manager.get_pleasure_patterns_by_type(
            "upgrade", count=3, min_quality=0.5
        )
        
        assert len(patterns) > 0, "未获取到升级进化模式"
        
        # 检查模式特征
        for pattern in patterns:
            assert "突破" in pattern.content or "升级" in pattern.content or "进化" in pattern.content or \
                   "觉醒" in pattern.content, "升级模式内容不符合预期"
        
        log_success("🧪测试", "升级进化模式获取测试通过", 获取数量=len(patterns))
    
    @pytest.mark.asyncio
    async def test_get_rhythm_template(self, corpus_manager):
        """测试获取节奏模板"""
        log_info("🧪测试", "开始测试获取节奏模板")
        
        # 测试获取经典三段式模板
        template = await corpus_manager.get_rhythm_template("经典三段式")
        
        if template:
            assert isinstance(template, RhythmTemplateData), "模板数据类型错误"
            assert template.name, "模板名称为空"
            assert isinstance(template.structure_breakdown, dict), "结构分解应为字典"
            assert isinstance(template.emotion_curve, list), "情感曲线应为列表"
            assert isinstance(template.techniques, list), "技巧列表应为列表"
            
            log_success("🧪测试", "节奏模板获取测试通过", 模板名称=template.name)
        else:
            log_error("🧪测试", "未找到经典三段式模板")
    
    @pytest.mark.asyncio
    async def test_get_enhancement_expressions(self, corpus_manager):
        """测试获取强化表达"""
        log_info("🧪测试", "开始测试获取强化表达")
        
        # 测试获取震惊反应表达
        expressions = await corpus_manager.get_enhancement_expressions("震惊反应", count=5)
        
        if expressions:
            assert len(expressions) > 0, "未获取到强化表达"
            assert all(isinstance(expr, str) for expr in expressions), "表达应为字符串"
            
            # 检查表达内容
            shock_keywords = ["震惊", "惊讶", "不敢相信", "目瞪口呆", "惊呆"]
            has_shock_content = any(
                any(keyword in expr for keyword in shock_keywords) 
                for expr in expressions
            )
            
            log_success("🧪测试", "强化表达获取测试通过", 
                       获取数量=len(expressions), 包含震惊内容=has_shock_content)
        else:
            log_error("🧪测试", "未获取到震惊反应表达")
    
    @pytest.mark.asyncio
    async def test_corpus_key_mapping(self, corpus_manager):
        """测试语料库键名映射"""
        log_info("🧪测试", "开始测试语料库键名映射")
        
        # 测试各种爽感类型的映射
        test_cases = [
            ("face_slapping", "face_slapping"),
            ("power_fantasy", "power_fantasy"),
            ("upgrade", "upgrade"),
            ("recognition", "upgrade"),  # 认可类型映射到升级
            ("revenge", "face_slapping"),  # 复仇类型映射到打脸
            ("mystery", "face_slapping"),  # 解谜类型映射到打脸
            ("unknown_type", "face_slapping")  # 未知类型默认映射到打脸
        ]
        
        for input_type, expected_key in test_cases:
            actual_key = corpus_manager._get_corpus_key_by_pleasure_type(input_type)
            assert actual_key == expected_key, f"类型 {input_type} 映射错误，期望 {expected_key}，实际 {actual_key}"
        
        log_success("🧪测试", "语料库键名映射测试通过", 测试用例数=len(test_cases))


@pytest.mark.asyncio
async def test_pleasure_corpus_manager_integration():
    """🔗 [集成测试] 爽感语料库管理器集成测试"""
    log_info("🧪测试", "开始爽感语料库管理器集成测试")
    
    try:
        # 创建管理器
        corpus_root = str(Path(__file__).parent.parent / "data" / "corpus")
        manager = create_pleasure_corpus_manager(corpus_root)
        
        # 初始化
        success = await manager.initialize()
        assert success, "初始化失败"
        
        # 综合测试：获取不同类型的数据
        face_slapping_patterns = await manager.get_pleasure_patterns_by_type("face_slapping", count=2)
        power_fantasy_patterns = await manager.get_pleasure_patterns_by_type("power_fantasy", count=2)
        upgrade_patterns = await manager.get_pleasure_patterns_by_type("upgrade", count=2)
        
        rhythm_template = await manager.get_rhythm_template("经典三段式")
        enhancement_expressions = await manager.get_enhancement_expressions("震惊反应", count=3)
        
        # 验证结果
        total_patterns = len(face_slapping_patterns) + len(power_fantasy_patterns) + len(upgrade_patterns)
        assert total_patterns > 0, "未获取到任何爽感模式"
        
        log_success("🧪测试", "爽感语料库管理器集成测试通过",
                   打脸模式数=len(face_slapping_patterns),
                   权力幻想模式数=len(power_fantasy_patterns),
                   升级模式数=len(upgrade_patterns),
                   节奏模板=rhythm_template.name if rhythm_template else "无",
                   强化表达数=len(enhancement_expressions))
        
        return True
        
    except Exception as e:
        log_error("🧪测试", "爽感语料库管理器集成测试失败", error=e)
        return False


if __name__ == "__main__":
    """直接运行测试"""
    async def run_tests():
        log_info("🧪测试", "开始运行爽感语料库管理器测试")
        
        # 运行集成测试
        success = await test_pleasure_corpus_manager_integration()
        
        if success:
            log_success("🧪测试", "所有测试通过！")
        else:
            log_error("🧪测试", "测试失败！")
        
        return success
    
    # 运行测试
    result = asyncio.run(run_tests())
    exit(0 if result else 1)
