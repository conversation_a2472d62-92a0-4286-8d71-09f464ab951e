# 史诗任务9：AI智能体编排与文学风格增强系统 - 详细开发计划

## 🎯 项目背景分析

### 当前项目状态评估
基于对《想法.md》和《指导.md》的深入分析，我们的项目已经具备了强大的技术基础：

**✅ 已完成的技术能力（史诗任务1-8）**：
- 智谱AI集成 (任务1)
- 故事圣经管理 (任务2) 
- 情感基因库 (任务3)
- 熵增扰动器 (任务4)
- RAG记忆引擎 (任务5)
- 世界知识图谱 (任务6)
- 去AI化引擎 (任务7)
- 智能剧情推荐与冲突检测 (任务8)

**🎯 关键洞察**：
1. **技术不需要重做**：现有模块是实现Agent模式的完美底层能力
2. **缺失的是编排层**：需要一个"大脑"来协调所有模块
3. **文学灵魂待注入**：技术很强，但缺乏独特的文学风格和情感价值
4. **爽感机制是核心**：网文的成功关键在于情绪的极致体验

### PRD核心需求映射
**PRD要求**：从"一句话"到"一部小说"的**分步骤创作流程**
**我们的解决方案**：AI智能体编排器 + 现有技术模块的角色化 + **用户确认机制**

**PRD中的AI角色** → **我们的技术实现**：
- 概念架构师 → ZhipuClient + 专用Prompt
- 世界构建师 → ZhipuClient + BibleRepository  
- 角色设计师 → ZhipuClient + WorldGraph
- 首席编剧 → PlotRecommendationEngine
- 导演AI → PlotPathPlanner + ConflictDetection
- 作家AI → ZhipuClient + PromptSynthesizer
- 编剧AI → EntropyInjector + EmotionalGeneProcessor
- 编辑AI → DeAIProcessor + 新增StyleEnhancer

## 🔄 分步骤创作流程设计

### 用户创作流程（非全自动）
我们的系统采用**分步骤、用户参与**的创作模式，而不是一键全自动生成：

**第一步：创意扩展阶段**
1. 用户输入一句话创意
2. 系统调用"概念架构师"AI扩展创意
3. **用户审核**：查看扩展后的创意概念
4. **用户确认**：可以修改、重新生成或确认进入下一步

**第二步：世界观构建阶段**
1. 系统基于确认的创意调用"世界构建师"AI
2. 生成详细的世界观设定
3. **用户审核**：查看世界观文档
4. **用户确认**：可以修改世界观细节或确认进入下一步

**第三步：角色创建阶段**
1. 系统基于世界观调用"角色设计师"AI
2. 创建核心角色和人物关系
3. **用户审核**：查看角色设定和关系图
4. **用户确认**：可以修改角色属性或确认进入下一步

**第四步：情节大纲阶段**
1. 系统调用"首席编剧"AI生成情节大纲
2. 规划章节结构和主要情节点
3. **用户审核**：查看完整大纲
4. **用户确认**：可以调整大纲结构或确认进入写作阶段

**第五步：章节创作阶段**
1. 用户选择要生成的章节
2. 系统启动章节生产流水线（导演→作家→编剧→编辑）
3. **用户审核**：查看生成的章节内容
4. **用户确认**：可以重新生成、修改或确认该章节

**核心特点**：
- ✅ **用户控制**：每个阶段都需要用户确认才能继续
- ✅ **可修改**：用户可以在任何阶段提出修改意见
- ✅ **可回退**：支持回到之前的阶段重新生成
- ✅ **渐进式**：逐步完善，而不是一次性生成

## 🏗️ 详细开发计划

### 第一阶段：核心编排引擎 (预计2周)

#### 任务9.1.1：创建AI智能体编排器核心
**文件位置**：`backend/app/core/agent_orchestrator.py`

**核心类设计**：
```python
@dataclass
class PhaseStartRequest:
    """阶段启动请求"""
    project_id: str             # 项目ID
    phase: str                  # 阶段名称：concept_expansion, world_building, character_creation, plot_outline, chapter_production
    input_data: Dict            # 该阶段的输入数据
    user_preferences: Dict      # 用户偏好设置

@dataclass
class PhaseResult:
    """阶段结果"""
    project_id: str             # 项目ID
    phase: str                  # 阶段名称
    status: str                 # 状态：processing, completed, failed
    result_data: Dict           # 生成的结果数据
    created_at: datetime        # 创建时间
    requires_user_confirmation: bool  # 是否需要用户确认

@dataclass
class PhaseConfirmationRequest:
    """阶段确认请求"""
    project_id: str             # 项目ID
    phase: str                  # 阶段名称
    action: str                 # 动作：confirm, modify, regenerate, rollback
    modifications: Optional[Dict] = None  # 用户修改意见

@dataclass
class NovelProject:
    """小说项目"""
    project_id: str             # 项目ID
    user_id: str               # 用户ID
    title: str                  # 小说标题
    core_idea: str              # 核心创意
    genre: str                  # 小说类型
    current_phase: str          # 当前阶段
    phase_history: List[PhaseResult]  # 阶段历史
    created_at: datetime        # 创建时间
    updated_at: datetime        # 更新时间
    
class AIAgentOrchestrator:
    """AI智能体编排器 - 小说创作流水线的总导演"""
    
    def __init__(self):
        # 注入所有现有服务
        self.zhipu_client = None        # 任务1
        self.bible_repo = None          # 任务2
        self.emotion_processor = None   # 任务3
        self.entropy_injector = None    # 任务4
        self.prompt_synthesizer = None  # 任务5
        self.world_graph_client = None  # 任务6
        self.deai_processor = None      # 任务7
        self.plot_engine = None         # 任务8
        
    async def start_creation_phase(
        self,
        request: NovelCreationRequest,
        phase: str
    ) -> PhaseResult:
        """
        🎬 [编排器] 启动指定的创作阶段
        每个阶段完成后需要用户确认才能进入下一阶段
        """

    async def get_phase_result(self, project_id: str, phase: str) -> PhaseResult:
        """
        📋 [编排器] 获取指定阶段的结果，供用户审核
        """

    async def confirm_phase_and_proceed(
        self,
        project_id: str,
        phase: str,
        user_modifications: Optional[Dict] = None
    ) -> bool:
        """
        ✅ [编排器] 用户确认阶段结果并进入下一阶段
        用户可以提供修改意见，系统会据此调整
        """
        
    async def _phase_1_concept_expansion(self, idea: str, genre: str) -> ConceptExpansion:
        """
        🧠 [概念架构师] 第一阶段：核心创意扩展
        调用ZhipuClient，使用专门的概念扩展Prompt
        """
        
    async def _phase_2_world_building(self, concept: ConceptExpansion) -> WorldSetting:
        """
        🌍 [世界构建师] 第二阶段：世界观构建
        调用ZhipuClient生成世界观，存入BibleRepository
        """
        
    async def _phase_3_character_creation(self, world: WorldSetting) -> List[Character]:
        """
        👥 [角色设计师] 第三阶段：核心角色创建
        调用ZhipuClient创建角色，存入WorldGraph
        """
        
    async def _phase_4_plot_outline(self, world: WorldSetting, characters: List[Character]) -> PlotOutline:
        """
        📝 [首席编剧] 第四阶段：情节大纲生成
        调用PlotRecommendationEngine生成大纲
        """
        
    async def _phase_5_chapter_production(self, outline: PlotOutline) -> AsyncGenerator[Chapter, None]:
        """
        🎭 [章节生产流水线] 第五阶段：逐章生成
        这是一个复杂的子流水线，包含导演→作家→编剧→编辑四个步骤
        """
```

**关键技术点**：
- **分阶段处理**：每个阶段独立执行，等待用户确认
- **状态管理**：每个阶段的结果都要持久化存储，支持用户修改
- **用户确认机制**：每个阶段完成后暂停，等待用户审核和确认
- **错误恢复**：支持从任意阶段重新开始或回退
- **并发控制**：避免同一用户多个创作任务冲突

#### 任务9.1.2：章节生产流水线详细实现
**文件位置**：`backend/app/core/chapter_production_pipeline.py`

```python
class ChapterProductionPipeline:
    """章节生产流水线 - 导演→作家→编剧→编辑的四步骤流程"""
    
    async def produce_chapter(
        self, 
        chapter_outline: ChapterOutline,
        story_context: StoryContext
    ) -> Chapter:
        """生产单个章节的完整流程"""
        
        # 步骤1：导演AI - 将大纲分解为详细节拍表
        beat_sheet = await self._director_phase(chapter_outline, story_context)
        
        # 步骤2：作家AI - 根据节拍表撰写初稿
        draft = await self._writer_phase(beat_sheet, story_context)
        
        # 步骤3：编剧AI - 增加戏剧性和悬念
        enhanced_draft = await self._scriptwriter_phase(draft, story_context)
        
        # 步骤4：编辑AI - 终审润色和字数控制
        final_chapter = await self._editor_phase(enhanced_draft, story_context)
        
        return final_chapter
        
    async def _director_phase(self, outline: ChapterOutline, context: StoryContext) -> BeatSheet:
        """
        🎬 [导演AI] 将章节大纲分解为详细的节拍表
        - 调用PlotPathPlanner分析情节发展路径
        - 调用ConflictDetection检测潜在冲突点
        - 根据需要提议新角色引入
        """
        
    async def _writer_phase(self, beat_sheet: BeatSheet, context: StoryContext) -> ChapterDraft:
        """
        ✍️ [作家AI] 根据节拍表撰写章节初稿
        - 调用PromptSynthesizer合成动态提示词
        - 调用ZhipuClient生成章节内容
        - 确保与故事上下文的一致性
        """
        
    async def _scriptwriter_phase(self, draft: ChapterDraft, context: StoryContext) -> EnhancedDraft:
        """
        🎭 [编剧AI] 为初稿增加戏剧性、悬念和钩子
        - 调用EntropyInjector增加意外元素
        - 调用EmotionalGeneProcessor增强情感描写
        - 添加章节末尾的悬念钩子
        """
        
    async def _editor_phase(self, draft: EnhancedDraft, context: StoryContext) -> Chapter:
        """
        📝 [编辑AI] 终审、润色和字数控制
        - 调用DeAIProcessor去除AI痕迹
        - 调用StyleEnhancer应用文体风格
        - 控制章节字数在目标范围内
        """
```

### 第二阶段：文学风格增强引擎 (预计3周)

#### 任务9.2.1：语言风格注入器
**文件位置**：`backend/app/core/style_injector.py`

**语料库设计**：
```python
# backend/app/data/language_libraries/
├── dialect_library.json          # 方言库
├── sarcasm_library.json          # 阴阳怪气语句库  
├── ancient_insults.json          # 古风骂人话库
├── creative_phrases.json         # 创意表达库
└── poem_adaptations.json         # 诗词改编库

# 示例：sarcasm_library.json
{
  "categories": {
    "动物类": [
      "狗掀门帘子全靠一张嘴",
      "猪鼻子插葱装象",
      "癞蛤蟆想吃天鹅肉"
    ],
    "生活类": [
      "屋里挂葫芦把自己当爷了", 
      "用纱布擦屁股给我漏了一手",
      "领居门口晒花椒麻了隔壁"
    ],
    "文化类": [
      "莎士比亚（傻B）",
      "碳酸钠（纯碱）", 
      "端起碗来就是团圆饭"
    ]
  },
  "usage_contexts": {
    "愤怒": ["动物类", "生活类"],
    "嘲讽": ["文化类", "生活类"],
    "轻蔑": ["动物类"]
  }
}
```

**核心实现**：
```python
class StyleInjector:
    """语言风格注入器 - 让AI说人话，说有趣的话"""
    
    def __init__(self):
        self.libraries = self._load_all_libraries()
        self.replacement_patterns = self._compile_patterns()
        
    async def enhance_dialogue(
        self, 
        text: str, 
        character: Character, 
        emotion: str,
        context: str
    ) -> str:
        """
        🎨 [风格注入] 根据角色特征和情绪增强对话
        
        处理流程：
        1. 分析角色性格标签（如"暴躁"、"傲娇"）
        2. 识别当前情绪状态
        3. 从对应语料库中选择合适的表达
        4. 智能替换或增强原始对话
        """
        
    async def inject_creative_expressions(self, text: str, scene_type: str) -> str:
        """
        💡 [创意表达] 注入创意表达和改编诗词
        
        特殊场景处理：
        - 穿越场景：触发诗词改编（如"床前明月光，地上鞋两双"）
        - 装逼场景：使用古风表达
        - 吵架场景：使用古风骂人话
        """
        
    def _intelligent_replacement(self, original: str, candidates: List[str], context: str) -> str:
        """
        🧠 [智能替换] 不是简单替换，而是根据语境选择最合适的表达
        
        考虑因素：
        - 语言风格的一致性
        - 角色身份的匹配度  
        - 情节紧张度的适配
        - 读者期待的满足
        """
```

#### 任务9.2.2：性格驱动系统
**文件位置**：`backend/app/core/personality_driver.py`

**十二星座性格库**：
```json
{
  "白羊座": {
    "核心特征": ["冲动", "直率", "热情", "好胜", "急躁"],
    "语言特点": {
      "语速": "快",
      "用词": "直接、简短",
      "口头禅": ["我觉得", "必须", "赶紧", "不行"],
      "感叹词": ["哎呀", "天哪", "真的吗"]
    },
    "行为模式": {
      "决策风格": "快速决断，不喜欢拖延",
      "冲突处理": "直接对抗，不绕弯子",
      "情感表达": "直接外露，不掩饰"
    },
    "对话模板": {
      "同意": "对！就是这样！",
      "反对": "不行，我不同意！",
      "疑问": "什么？你确定吗？",
      "愤怒": "太过分了！我忍不了！"
    }
  },
  "处女座": {
    "核心特征": ["完美主义", "细致", "挑剔", "理性", "谨慎"],
    "语言特点": {
      "语速": "中等偏慢",
      "用词": "精确、专业",
      "口头禅": ["准确地说", "具体来讲", "我觉得应该", "这样不对"],
      "感叹词": ["嗯", "是的", "确实"]
    },
    "行为模式": {
      "决策风格": "深思熟虑，需要充分信息",
      "冲突处理": "理性分析，寻找最优解",
      "情感表达": "内敛含蓄，通过行动表达"
    }
  }
}
```

#### 任务9.2.3：网文爽感机制引擎
**文件位置**：`backend/app/core/pleasure_engine.py`

**核心理念**：网文的"爽"来自于情绪的极致体验 - 要么感人到极致，要么爽到极致

```python
class PleasureEngine:
    """网文爽感机制引擎 - 创造极致的情绪体验"""
    
    def __init__(self):
        self.emotion_wave_manager = EmotionWaveManager()
        self.foreshadowing_system = ForeshadowingSystem()  
        self.power_display_enhancer = PowerDisplayEnhancer()
        
    async def design_pleasure_arc(self, plot_outline: PlotOutline) -> PleasureArc:
        """
        🎢 [爽感设计] 为整个故事设计情绪过山车
        
        核心原则：
        1. 波峰波谷：极致的压抑后必有极致的爆发
        2. 节奏控制：张弛有度，不能一直爽或一直虐
        3. 层层递进：每次爽点都要比上次更强烈
        """
        
class EmotionWaveManager:
    """情绪波动管理器"""
    
    async def create_depression_phase(self, context: StoryContext) -> DepressionPlan:
        """
        😢 [压抑阶段] 创造压抑感的铺垫
        
        技术实现：
        - 利用ConflictDetection系统识别并强化敌对关系
        - 通过WorldGraph追踪"看不起主角"的角色实体
        - 连续几个章节不断升级冲突等级
        - 让主角处于劣势，但不能绝望（要留希望）
        """
        
    async def trigger_explosion_phase(self, depression_context: DepressionPlan) -> ExplosionEvent:
        """
        🔥 [爆发阶段] 触发极致的情绪释放
        
        技术实现：
        - RAG引擎检索所有受辱记忆片段
        - EmotionalGeneProcessor调取"震惊"、"不可思议"等反应
        - StyleInjector注入霸气的对话和描写
        - PowerDisplayEnhancer创造震撼的力量展示
        """

class ForeshadowingSystem:
    """伏笔回收系统 - 智力上的满足感"""
    
    async def plant_foreshadowing(
        self, 
        chapter_content: str, 
        future_plot_hints: List[PlotHint]
    ) -> EnhancedChapter:
        """
        🌱 [埋伏笔] 在看似无关的地方埋入伏笔
        
        伏笔类型：
        1. 物品伏笔：看似无用的道具（如"平平无奇的黑石头"）
        2. 人物伏笔：一笔带过的路人（实际是重要角色）
        3. 信息伏笔：随口提及的传说（实际是关键线索）
        4. 能力伏笔：主角展现的小技能（实际是强大能力的一角）
        """
        
    async def identify_payoff_opportunities(self, current_context: StoryContext) -> List[PayoffOpportunity]:
        """
        🔍 [寻找回收机会] 智能识别伏笔回收的最佳时机
        
        回收时机：
        1. 主角陷入绝境时（绝地反击）
        2. 敌人得意忘形时（意外打脸）
        3. 真相即将揭晓时（恍然大悟）
        4. 力量需要证明时（底牌展现）
        """

class PowerDisplayEnhancer:
    """力量展示增强器 - 创造震撼的冲击力"""
    
    async def enhance_power_scene(self, scene: PowerScene) -> EnhancedPowerScene:
        """
        💪 [力量展示] 不是写主角多强，而是写别人多震惊
        
        描写策略：
        1. 侧面烘托：通过敌人和旁观者的反应来展现力量
        2. 独特视角：从意想不到的角度描写（如瞳孔倒影）
        3. 时间扭曲：利用TimeDistortionProcessor描写"永恒的一瞬"
        4. 感官冲击：调用EmotionalGeneProcessor的感官数据
        """
        
    async def create_witness_reactions(self, power_event: PowerEvent, witnesses: List[Character]) -> str:
        """
        😱 [旁观者反应] 通过旁观者的震惊来侧面烘托主角的强大
        
        反应层次：
        1. 生理反应：瞳孔放大、呼吸停滞、冷汗直流
        2. 心理反应：不敢置信、恐惧、敬畏
        3. 行为反应：后退、跪拜、逃跑
        4. 语言反应：结巴、失语、惊呼
        """
```

### 第三阶段：系统集成与优化 (预计2周)

#### 任务9.3.1：API路由集成
**文件位置**：`backend/app/routers/novel_creation.py`

```python
@router.post("/start-phase", response_model=PhaseStartResponse)
async def start_creation_phase(
    request: PhaseStartRequest,
    current_user: User = Depends(get_current_user),
    orchestrator: AIAgentOrchestrator = Depends(get_orchestrator)
) -> PhaseStartResponse:
    """
    🎬 [API] 启动指定的创作阶段
    支持的阶段：concept_expansion, world_building, character_creation, plot_outline, chapter_production
    """

@router.get("/phase-result/{project_id}/{phase}")
async def get_phase_result(
    project_id: str,
    phase: str,
    current_user: User = Depends(get_current_user)
) -> PhaseResultResponse:
    """📋 [API] 获取指定阶段的结果，供用户审核"""

@router.post("/confirm-phase", response_model=ConfirmationResponse)
async def confirm_phase_and_proceed(
    request: PhaseConfirmationRequest,
    current_user: User = Depends(get_current_user),
    orchestrator: AIAgentOrchestrator = Depends(get_orchestrator)
) -> ConfirmationResponse:
    """✅ [API] 用户确认阶段结果并进入下一阶段"""

@router.post("/enhance-style", response_model=StyleEnhancementResponse)
async def enhance_text_style(
    request: StyleEnhancementRequest,
    style_injector: StyleInjector = Depends(get_style_injector)
) -> StyleEnhancementResponse:
    """🎨 [API] 文本风格增强"""
```

#### 任务9.3.2：前端界面开发
**文件位置**：`frontend/src/pages/novel-creation/`

**核心组件**：
1. **创作启动器** (`CreationLauncher.tsx`)
   - 一句话创意输入框
   - 小说类型选择器
   - 风格偏好配置面板

2. **创作进度监控器** (`CreationProgressMonitor.tsx`)
   - 实时显示当前AI智能体工作状态
   - 进度条和预计完成时间
   - 各阶段结果预览

3. **风格配置面板** (`StyleConfigPanel.tsx`)
   - 语言风格选择（方言、古风等）
   - 性格特征配置（十二星座等）
   - 爽感强度调节器

4. **伏笔管理器** (`ForeshadowingManager.tsx`)
   - 显示已埋伏笔列表
   - 标记回收机会
   - 伏笔效果预览

## 🎯 预期成果

### 技术成果
1. **分步骤的小说创作流水线**：实现"一句话到小说"的**用户参与式**创作流程
2. **独特的文学风格系统**：融入方言、古风、创意表达等特色
3. **强大的爽感生成机制**：通过情绪波动和伏笔回收创造极致体验
4. **智能的角色驱动系统**：基于性格特征的真实角色行为
5. **用户确认与修改机制**：每个阶段都支持用户审核、修改和重新生成

### 产品价值
1. **差异化竞争优势**：独特的文学风格和爽感机制
2. **用户粘性提升**：极致的情感体验带来强烈的使用欲望
3. **内容质量保证**：多层次的AI协作确保内容的丰富性和一致性
4. **商业化潜力**：可扩展到不同文体和用户群体

这个史诗任务9将是整个项目的集大成者，它不仅整合了所有技术模块，更重要的是注入了文学的灵魂和情感的力量！🚀
