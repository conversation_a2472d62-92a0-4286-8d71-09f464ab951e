"""
🎬 [编排] AI智能体编排系统的数据模型
支持分步骤、用户参与的小说创作流程
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field
from uuid import uuid4

# 导入文学风格相关枚举
from app.core.style_injector import StyleType
from app.core.personality_driver import ZodiacSign, PersonalityTrait
from app.core.pleasure_engine import PleasureType, EmotionalIntensity
from app.core.literary_style_engine import StyleEnhancementMode


class CreationPhase(str, Enum):
    """创作阶段枚举"""
    CONCEPT_EXPANSION = "concept_expansion"      # 创意扩展
    WORLD_BUILDING = "world_building"           # 世界观构建
    CHARACTER_CREATION = "character_creation"   # 角色创建
    PLOT_OUTLINE = "plot_outline"              # 情节大纲
    CHAPTER_PRODUCTION = "chapter_production"   # 章节创作


class PhaseStatus(str, Enum):
    """阶段状态枚举"""
    NOT_STARTED = "not_started"        # 未开始
    PROCESSING = "processing"          # 处理中
    COMPLETED = "completed"            # 已完成
    FAILED = "failed"                  # 失败
    WAITING_CONFIRMATION = "waiting_confirmation"  # 等待用户确认


class UserAction(str, Enum):
    """用户操作枚举"""
    CONFIRM = "confirm"                # 确认
    MODIFY = "modify"                  # 修改
    REGENERATE = "regenerate"          # 重新生成
    ROLLBACK = "rollback"              # 回退


class AIAgentRole(str, Enum):
    """AI智能体角色枚举"""
    CONCEPT_ARCHITECT = "concept_architect"    # 概念架构师
    WORLD_BUILDER = "world_builder"           # 世界构建师
    CHARACTER_DESIGNER = "character_designer"  # 角色设计师
    CHIEF_WRITER = "chief_writer"             # 首席编剧
    DIRECTOR = "director"                     # 导演AI
    WRITER = "writer"                         # 作家AI
    SCRIPTWRITER = "scriptwriter"             # 编剧AI
    EDITOR = "editor"                         # 编辑AI


# ==================== 请求模型 ====================

class PhaseStartRequest(BaseModel):
    """阶段启动请求"""
    project_id: str = Field(..., description="项目ID")
    phase: CreationPhase = Field(..., description="要启动的创作阶段")
    input_data: Dict[str, Any] = Field(default_factory=dict, description="该阶段的输入数据")
    user_preferences: Dict[str, Any] = Field(default_factory=dict, description="用户偏好设置")
    
    class Config:
        json_encoders = {
            CreationPhase: lambda v: v.value
        }


class PhaseConfirmationRequest(BaseModel):
    """阶段确认请求"""
    project_id: str = Field(..., description="项目ID")
    phase: CreationPhase = Field(..., description="阶段名称")
    action: UserAction = Field(..., description="用户操作")
    modifications: Optional[Dict[str, Any]] = Field(None, description="用户修改意见")
    feedback: Optional[str] = Field(None, description="用户反馈")
    
    class Config:
        json_encoders = {
            CreationPhase: lambda v: v.value,
            UserAction: lambda v: v.value
        }


class NovelProjectCreateRequest(BaseModel):
    """小说项目创建请求"""
    title: str = Field(..., description="小说标题")
    core_idea: str = Field(..., description="核心创意（一句话）")
    genre: str = Field(..., description="小说类型")
    target_length: int = Field(default=100000, description="目标字数")
    style_preferences: Dict[str, Any] = Field(default_factory=dict, description="风格偏好")


# ==================== 响应模型 ====================

class PhaseResult(BaseModel):
    """阶段结果"""
    project_id: str = Field(..., description="项目ID")
    phase: CreationPhase = Field(..., description="阶段名称")
    status: PhaseStatus = Field(..., description="阶段状态")
    agent_role: Optional[AIAgentRole] = Field(None, description="执行的AI角色")
    result_data: Dict[str, Any] = Field(default_factory=dict, description="生成的结果数据")
    processing_time: Optional[float] = Field(None, description="处理耗时（秒）")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    requires_user_confirmation: bool = Field(True, description="是否需要用户确认")
    error_message: Optional[str] = Field(None, description="错误信息")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            CreationPhase: lambda v: v.value,
            PhaseStatus: lambda v: v.value,
            AIAgentRole: lambda v: v.value if v else None
        }


class NovelProject(BaseModel):
    """小说项目"""
    project_id: str = Field(..., description="项目ID")
    user_id: str = Field(..., description="用户ID")
    title: str = Field(..., description="小说标题")
    core_idea: str = Field(..., description="核心创意")
    genre: str = Field(..., description="小说类型")
    target_length: int = Field(..., description="目标字数")
    style_preferences: Dict[str, Any] = Field(default_factory=dict, description="风格偏好")
    current_phase: CreationPhase = Field(CreationPhase.CONCEPT_EXPANSION, description="当前阶段")
    phase_history: List[PhaseResult] = Field(default_factory=list, description="阶段历史")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            CreationPhase: lambda v: v.value
        }


class PhaseStartResponse(BaseModel):
    """阶段启动响应"""
    success: bool = Field(..., description="是否成功启动")
    project_id: str = Field(..., description="项目ID")
    phase: CreationPhase = Field(..., description="启动的阶段")
    estimated_time: Optional[int] = Field(None, description="预计完成时间（分钟）")
    message: str = Field(..., description="响应消息")
    
    class Config:
        json_encoders = {
            CreationPhase: lambda v: v.value
        }


class PhaseResultResponse(BaseModel):
    """阶段结果响应"""
    phase_result: PhaseResult = Field(..., description="阶段结果")
    next_phase: Optional[CreationPhase] = Field(None, description="下一个阶段")
    can_proceed: bool = Field(..., description="是否可以继续下一阶段")
    
    class Config:
        json_encoders = {
            CreationPhase: lambda v: v.value if v else None
        }


class ConfirmationResponse(BaseModel):
    """确认响应"""
    success: bool = Field(..., description="操作是否成功")
    project_id: str = Field(..., description="项目ID")
    action_taken: UserAction = Field(..., description="执行的操作")
    current_phase: CreationPhase = Field(..., description="当前阶段")
    next_phase: Optional[CreationPhase] = Field(None, description="下一个阶段")
    message: str = Field(..., description="响应消息")
    
    class Config:
        json_encoders = {
            UserAction: lambda v: v.value,
            CreationPhase: lambda v: v.value,
        }


class NovelProjectResponse(BaseModel):
    """小说项目响应"""
    project: NovelProject = Field(..., description="项目信息")
    current_phase_result: Optional[PhaseResult] = Field(None, description="当前阶段结果")
    available_actions: List[UserAction] = Field(default_factory=list, description="可用操作")
    
    class Config:
        json_encoders = {
            UserAction: lambda v: v.value
        }


# ==================== 内部数据模型 ====================

class ConceptExpansion(BaseModel):
    """创意扩展结果"""
    original_idea: str = Field(..., description="原始创意")
    expanded_concept: str = Field(..., description="扩展后的概念")
    core_themes: List[str] = Field(default_factory=list, description="核心主题")
    genre_elements: List[str] = Field(default_factory=list, description="类型元素")
    potential_conflicts: List[str] = Field(default_factory=list, description="潜在冲突")
    target_audience: str = Field(..., description="目标读者")


class WorldSetting(BaseModel):
    """世界观设定"""
    world_name: str = Field(..., description="世界名称")
    world_type: str = Field(..., description="世界类型")
    background_setting: str = Field(..., description="背景设定")
    power_system: Optional[str] = Field(None, description="力量体系")
    social_structure: str = Field(..., description="社会结构")
    key_locations: List[Dict[str, str]] = Field(default_factory=list, description="关键地点")
    world_rules: List[str] = Field(default_factory=list, description="世界规则")
    cultural_elements: List[str] = Field(default_factory=list, description="文化元素")


class Character(BaseModel):
    """角色信息"""
    character_id: str = Field(default_factory=lambda: str(uuid4()), description="角色ID")
    name: str = Field(..., description="角色姓名")
    role: str = Field(..., description="角色定位")
    age: Optional[int] = Field(None, description="年龄")
    personality: List[str] = Field(default_factory=list, description="性格特征")
    background: str = Field(..., description="背景故事")
    abilities: List[str] = Field(default_factory=list, description="能力特长")
    goals: List[str] = Field(default_factory=list, description="目标动机")
    relationships: List[Dict[str, str]] = Field(default_factory=list, description="人物关系")
    appearance: Optional[str] = Field(None, description="外貌描述")


class PlotOutline(BaseModel):
    """情节大纲"""
    story_structure: str = Field(..., description="故事结构")
    main_plotline: str = Field(..., description="主线剧情")
    subplots: List[str] = Field(default_factory=list, description="支线剧情")
    chapter_outlines: List[Dict[str, Any]] = Field(default_factory=list, description="章节大纲")
    key_events: List[Dict[str, Any]] = Field(default_factory=list, description="关键事件")
    climax_points: List[str] = Field(default_factory=list, description="高潮点")
    resolution: str = Field(..., description="结局设定")


class ChapterOutline(BaseModel):
    """章节大纲"""
    chapter_number: int = Field(..., description="章节号")
    chapter_title: str = Field(..., description="章节标题")
    main_events: List[str] = Field(default_factory=list, description="主要事件")
    character_arcs: List[Dict[str, str]] = Field(default_factory=list, description="角色发展")
    emotional_beats: List[str] = Field(default_factory=list, description="情感节拍")
    target_word_count: int = Field(default=3000, description="目标字数")
    cliffhanger: Optional[str] = Field(None, description="悬念设置")


class StoryContext(BaseModel):
    """故事上下文"""
    project_id: str = Field(..., description="项目ID")
    world_setting: WorldSetting = Field(..., description="世界观设定")
    characters: List[Character] = Field(default_factory=list, description="角色列表")
    plot_outline: PlotOutline = Field(..., description="情节大纲")
    previous_chapters: List[Dict[str, Any]] = Field(default_factory=list, description="前面章节")
    current_chapter_number: int = Field(..., description="当前章节号")


# ==================== 文学风格增强相关模型 ====================

class LiteraryStylePreferences(BaseModel):
    """文学风格偏好设置"""
    # 语言风格配置
    style_types: List[StyleType] = Field(default_factory=list, description="语言风格类型列表")
    style_intensity: float = Field(default=0.5, ge=0.0, le=1.0, description="语言风格强度")

    # 性格驱动配置
    zodiac_sign: Optional[ZodiacSign] = Field(None, description="角色星座")
    personality_traits: List[PersonalityTrait] = Field(default_factory=list, description="性格特征列表")
    personality_intensity: float = Field(default=0.5, ge=0.0, le=1.0, description="性格驱动强度")

    # 爽感机制配置
    pleasure_types: List[PleasureType] = Field(default_factory=list, description="爽感类型列表")
    pleasure_intensity: EmotionalIntensity = Field(default=EmotionalIntensity.MEDIUM, description="爽感强度等级")
    rhythm_template: Optional[str] = Field(None, description="节奏模板名称")

    # 整体配置
    enhancement_mode: StyleEnhancementMode = Field(default=StyleEnhancementMode.MODERATE, description="增强模式")
    preserve_meaning: bool = Field(default=True, description="是否保持原意")
    target_audience: str = Field(default="通用读者", description="目标读者群体")


class StyleEnhancementRequest(BaseModel):
    """风格增强请求"""
    project_id: str = Field(..., description="项目ID")
    text: str = Field(..., description="待增强的文本")
    style_preferences: LiteraryStylePreferences = Field(..., description="风格偏好设置")
    preset_name: Optional[str] = Field(None, description="预设配置名称（可选）")
    custom_overrides: Dict[str, Any] = Field(default_factory=dict, description="自定义覆盖参数")


class StyleEnhancementResult(BaseModel):
    """风格增强结果"""
    success: bool = Field(..., description="增强是否成功")
    original_text: str = Field(..., description="原始文本")
    enhanced_text: str = Field(..., description="增强后文本")

    # 评估分数
    overall_enhancement_score: float = Field(..., description="综合增强评分")
    style_consistency_score: float = Field(..., description="风格一致性评分")
    readability_score: float = Field(..., description="可读性评分")

    # 处理详情
    processing_pipeline: List[str] = Field(..., description="处理流水线步骤")
    enhancement_details: Dict[str, Any] = Field(..., description="增强详情")
    performance_metrics: Dict[str, Any] = Field(..., description="性能指标")


class StyleEnhancementResponse(BaseModel):
    """风格增强响应"""
    result: StyleEnhancementResult = Field(..., description="增强结果")
    available_presets: List[str] = Field(..., description="可用预设配置列表")
    recommendations: List[str] = Field(default_factory=list, description="改进建议")
