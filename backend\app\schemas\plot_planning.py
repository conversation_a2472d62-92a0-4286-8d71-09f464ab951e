"""
🎯 [情节规划] 情节发展路径规划API数据模式

定义情节路径生成、评估和建议相关的Pydantic模型，
用于API请求和响应的数据验证和序列化。
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

from app.core.plot_path_planner import PathType, PathPriority, PlotEvent, PlotPath


class PathTypeEnum(str, Enum):
    """路径类型枚举（用于API）"""
    CONFLICT_ESCALATION = "conflict_escalation"
    RELATIONSHIP_DEVELOPMENT = "relationship_development"
    CHARACTER_GROWTH = "character_growth"
    MYSTERY_REVELATION = "mystery_revelation"
    WORLD_EXPLORATION = "world_exploration"
    EMOTIONAL_JOURNEY = "emotional_journey"


class PathPriorityEnum(str, Enum):
    """路径优先级枚举（用于API）"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class PlotEventRequest(BaseModel):
    """情节事件请求模型"""
    title: str = Field(..., description="事件标题")
    description: str = Field(..., description="事件描述")
    event_type: str = Field(..., description="事件类型")
    involved_entities: List[str] = Field(default=[], description="涉及的实体ID列表")
    involved_relationships: List[str] = Field(default=[], description="涉及的关系ID列表")
    prerequisites: List[str] = Field(default=[], description="前置条件")
    consequences: List[str] = Field(default=[], description="后果影响")
    emotional_impact: float = Field(5.0, ge=0.0, le=10.0, description="情感冲击力")
    tension_level: float = Field(5.0, ge=0.0, le=10.0, description="紧张程度")
    complexity: float = Field(3.0, ge=0.0, le=10.0, description="复杂度")
    estimated_chapters: int = Field(1, ge=1, le=10, description="预估章节数")


class PlotEventResponse(BaseModel):
    """情节事件响应模型"""
    id: str
    title: str
    description: str
    event_type: str
    involved_entities: List[str]
    involved_relationships: List[str]
    prerequisites: List[str]
    consequences: List[str]
    emotional_impact: float
    tension_level: float
    complexity: float
    estimated_chapters: int
    created_at: datetime

    class Config:
        from_attributes = True


class PlotPathRequest(BaseModel):
    """情节路径请求模型"""
    title: str = Field(..., description="路径标题")
    description: str = Field(..., description="路径描述")
    path_type: PathTypeEnum = Field(..., description="路径类型")
    priority: PathPriorityEnum = Field(PathPriorityEnum.MEDIUM, description="优先级")
    events: List[PlotEventRequest] = Field(..., description="路径中的事件序列")
    target_audience: List[str] = Field(default=["通用读者"], description="目标受众")
    genre_tags: List[str] = Field(default=[], description="类型标签")


class PlotPathResponse(BaseModel):
    """情节路径响应模型"""
    id: str
    title: str
    description: str
    path_type: PathTypeEnum
    priority: PathPriorityEnum
    events: List[PlotEventResponse]
    
    # 评估指标
    feasibility_score: float = Field(description="可行性评分")
    appeal_score: float = Field(description="吸引力评分")
    coherence_score: float = Field(description="连贯性评分")
    originality_score: float = Field(description="原创性评分")
    overall_score: float = Field(description="综合评分")
    
    # 风险评估
    risk_factors: List[str] = Field(description="风险因素")
    potential_conflicts: List[str] = Field(description="潜在冲突")
    
    # 元数据
    estimated_total_chapters: int = Field(description="预估总章节数")
    target_audience: List[str] = Field(description="目标受众")
    genre_tags: List[str] = Field(description="类型标签")
    
    created_at: datetime
    last_updated: datetime

    class Config:
        from_attributes = True


class PlotPathGenerationRequest(BaseModel):
    """情节路径生成请求模型"""
    story_id: str = Field(..., description="故事ID")
    current_chapter: int = Field(..., ge=1, description="当前章节号")
    target_paths: int = Field(5, ge=1, le=20, description="目标路径数量")
    focus_types: Optional[List[PathTypeEnum]] = Field(None, description="重点关注的路径类型")

    class Config:
        schema_extra = {
            "example": {
                "story_id": "story-123",
                "current_chapter": 5,
                "target_paths": 5,
                "focus_types": ["conflict_escalation", "character_growth"]
            }
        }


class PlotPathGenerationResponse(BaseModel):
    """情节路径生成响应模型"""
    story_id: str
    current_chapter: int
    analysis_timestamp: datetime
    
    # 当前状态分析
    current_state_summary: str = Field(description="当前状态摘要")
    active_conflicts: List[str] = Field(description="活跃冲突")
    character_motivations: Dict[str, List[str]] = Field(description="角色动机")
    unresolved_threads: List[str] = Field(description="未解决的线索")
    
    # 生成的路径
    recommended_paths: List[PlotPathResponse] = Field(description="推荐路径")
    alternative_paths: List[PlotPathResponse] = Field(description="备选路径")
    
    # 整体建议
    next_chapter_suggestions: List[str] = Field(description="下一章建议")
    long_term_strategy: str = Field(description="长期策略")
    pacing_recommendations: str = Field(description="节奏建议")
    
    # 统计信息
    total_paths_generated: int = Field(description="总生成路径数")
    analysis_confidence: float = Field(ge=0.0, le=1.0, description="分析置信度")

    class Config:
        schema_extra = {
            "example": {
                "story_id": "story-123",
                "current_chapter": 5,
                "current_state_summary": "故事进行到第5章，主要冲突逐渐升级",
                "recommended_paths": [],
                "analysis_confidence": 0.85
            }
        }


class PlotPathEvaluationRequest(BaseModel):
    """情节路径评估请求模型"""
    story_id: str = Field(..., description="故事ID")
    path: PlotPathRequest = Field(..., description="待评估的路径")

    class Config:
        schema_extra = {
            "example": {
                "story_id": "story-123",
                "path": {
                    "title": "主角成长路径",
                    "description": "主角通过挑战获得成长",
                    "path_type": "character_growth",
                    "events": []
                }
            }
        }


class PlotPathEvaluationResponse(BaseModel):
    """情节路径评估响应模型"""
    story_id: str
    evaluated_path: PlotPathResponse = Field(description="评估后的路径")
    evaluation_summary: str = Field(description="评估摘要")
    strengths: List[str] = Field(description="路径优势")
    weaknesses: List[str] = Field(description="路径劣势")
    improvement_suggestions: List[str] = Field(description="改进建议")
    compatibility_score: float = Field(ge=0.0, le=1.0, description="与故事的兼容性评分")
    evaluation_timestamp: datetime = Field(description="评估时间戳")

    class Config:
        schema_extra = {
            "example": {
                "story_id": "story-123",
                "evaluation_summary": "路径综合评分: 8.2/10.0",
                "strengths": ["情节连贯性强", "角色发展合理"],
                "weaknesses": ["节奏略显缓慢"],
                "compatibility_score": 0.82
            }
        }


class PlotSuggestionsResponse(BaseModel):
    """情节建议响应模型"""
    story_id: str
    current_chapter: int
    suggestions: List[str] = Field(description="下一章建议")
    pacing_advice: str = Field(description="节奏建议")
    character_focus_suggestions: List[str] = Field(description="角色重点建议")
    conflict_development_tips: List[str] = Field(description="冲突发展提示")
    world_building_opportunities: List[str] = Field(description="世界构建机会")
    confidence_level: float = Field(ge=0.0, le=1.0, description="建议置信度")
    generation_timestamp: datetime = Field(description="生成时间戳")

    class Config:
        schema_extra = {
            "example": {
                "story_id": "story-123",
                "current_chapter": 5,
                "suggestions": [
                    "考虑在下一章引入新的冲突元素",
                    "深化主角与反派的对立关系"
                ],
                "confidence_level": 0.78
            }
        }


class PathGenerationHistoryItem(BaseModel):
    """路径生成历史项模型"""
    generation_id: str
    generation_timestamp: datetime
    current_chapter: int
    paths_generated: int
    analysis_confidence: float
    user_feedback: Optional[str] = None

    class Config:
        from_attributes = True


class PathHistoryResponse(BaseModel):
    """路径历史响应模型"""
    story_id: str
    total_generations: int = Field(description="总生成次数")
    generations: List[PathGenerationHistoryItem] = Field(description="生成历史列表")
    skip: int = Field(description="跳过的记录数")
    limit: int = Field(description="返回的记录数")

    class Config:
        schema_extra = {
            "example": {
                "story_id": "story-123",
                "total_generations": 15,
                "generations": [],
                "skip": 0,
                "limit": 10
            }
        }
