// 应用首页组件
import { useState, useEffect } from 'react';
import { useTheme } from '../../components/ThemeProvider';

interface HomePageProps {
  onStartCreating: () => void;
  onViewProjects: () => void;
  isConnected: boolean | null;
  connectionLatency?: number; // 添加连接延迟参数
  isLoggedIn: boolean; // 添加登录状态
  onLogin: () => void; // 添加登录处理函数
  onLogout: () => void; // 添加登出处理函数
  isLoading: boolean; // 添加加载状态
}

export const HomePage: React.FC<HomePageProps> = ({
  onStartCreating,
  onViewProjects,
  isConnected,
  connectionLatency,
  isLoggedIn,
  onLogin,
  onLogout,
  isLoading
}) => {
  const [stats, setStats] = useState({
    totalProjects: 0,
    totalChapters: 0,
    totalWords: 0
  });
  const { isDark, toggleDarkMode } = useTheme();

  useEffect(() => {
    console.log('📍 [页面位置] 当前位置: 应用首页 (HomePage)');
    console.log('🌙 [HomePage] 夜间模式状态:', isDark);
    
    // 模拟获取统计数据
    // 在实际应用中，这里应该调用API获取用户的创作统计
    setStats({
      totalProjects: 0,
      totalChapters: 0,
      totalWords: 0
    });
  }, []);

  const features = [
    {
      icon: '🤖',
      title: '多AI模型支持',
      description: '支持智谱AI、Kimi等多种先进AI模型，智能选择最佳服务'
    },
    {
      icon: '📚',
      title: '故事圣经生成',
      description: '基于创意概念，AI自动生成完整的世界观、角色设定和情节大纲'
    },
    {
      icon: '✍️',
      title: '章节智能创作',
      description: '根据故事圣经，逐章生成高质量的小说内容，保持情节连贯性'
    },
    {
      icon: '🌓',
      title: '沉浸式阅读',
      description: '专业的阅读界面，支持日夜间模式和字体调节，提供最佳阅读体验'
    },
    {
      icon: '💾',
      title: '云端同步',
      description: '所有创作内容自动保存，支持多设备同步，永不丢失您的灵感'
    },
    {
      icon: '📊',
      title: '创作统计',
      description: '详细的创作数据分析，追踪您的写作进度和成长轨迹'
    }
  ];

  const quickActions = [
    {
      title: '开始新创作',
      description: '从一个创意开始，让AI帮您构建完整的小说世界',
      icon: '✨',
      action: onStartCreating,
      primary: true
    },
    {
      title: '查看作品',
      description: '管理和继续您之前的创作项目',
      icon: '📖',
      action: onViewProjects,
      primary: false
    },
    {
      title: 'API测试',
      description: '测试后端API连接和功能验证（开发工具）',
      icon: '🔧',
      action: () => window.location.href = '/api-test',
      primary: false
    }
  ];

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDark 
        ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-gray-100' 
        : 'bg-gradient-to-br from-blue-50 via-white to-purple-50'
    }`}>
      {/* 顶部导航 */}
      <header className={`backdrop-blur-sm border-b sticky top-0 z-50 transition-colors duration-300 ${
        isDark 
          ? 'bg-gray-800/80 border-gray-700' 
          : 'bg-white/80 border-gray-200'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                isDark 
                  ? 'bg-gradient-to-r from-purple-600 to-blue-600' 
                  : 'bg-gradient-to-r from-blue-600 to-purple-600'
              }`}>
                <span className="text-white font-bold text-sm">AI</span>
              </div>
              <h1 className={`text-xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                小说工作流
              </h1>
            </div>
            
            {/* 右侧按钮组 */}
            <div className="flex items-center space-x-3">
              {/* 用户信息显示（仅在登录时显示） */}
              {isLoggedIn && (
                <div className={`text-xs px-2 py-1 rounded ${
                  isDark ? 'bg-green-800 text-green-200' : 'bg-green-100 text-green-700'
                }`}>
                  已登录
                </div>
              )}

              {/* 日夜间模式切换 */}
              <button
                onClick={toggleDarkMode}
                className={`p-2 rounded-full transition-colors ${
                  isDark 
                    ? 'hover:bg-gray-700 text-yellow-400' 
                    : 'hover:bg-gray-100 text-gray-600'
                }`}
                title={isDark ? '切换到日间模式' : '切换到夜间模式'}
              >
                {isDark ? (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path fillRule="evenodd" d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z" clipRule="evenodd" />
                  </svg>
                )}
              </button>

              {/* 连接状态指示（带延迟显示） */}
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  isConnected === true ? 'bg-green-500' :
                  isConnected === false ? 'bg-red-500' : 'bg-yellow-500'
                }`}></div>
                <span className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  {isConnected === true ? (
                    <>
                      服务正常
                      {connectionLatency && (
                        <span className={`ml-1 text-xs ${
                          isDark ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          ({connectionLatency}ms)
                        </span>
                      )}
                    </>
                  ) : isConnected === false ? '连接失败' : '连接中...'}
                </span>
              </div>

              {/* 登录/登出按钮 */}
              {isLoggedIn ? (
                <button
                  onClick={onLogout}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    isDark
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'bg-red-600 hover:bg-red-700 text-white'
                  }`}
                >
                  登出
                </button>
              ) : (
                <button
                  onClick={onLogin}
                  disabled={isLoading}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2 ${
                    isLoading
                      ? isDark
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : isDark
                        ? 'bg-purple-600 hover:bg-purple-700 text-white'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                  }`}
                >
                  {isLoading && (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  )}
                  <span>{isLoading ? '登录中...' : '登录'}</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero区域 */}
        <div className="text-center mb-16">
          <h1 className={`text-4xl sm:text-6xl font-bold mb-6 ${isDark ? 'text-white' : 'text-gray-900'}`}>
            <span className={`bg-gradient-to-r bg-clip-text text-transparent ${
              isDark 
                ? 'from-purple-400 to-blue-400' 
                : 'from-blue-600 to-purple-600'
            }`}>
              AI 驱动的
            </span>
            <br />
            智能小说创作平台
          </h1>
          <p className={`text-xl mb-8 max-w-3xl mx-auto ${
            isDark ? 'text-gray-300' : 'text-gray-600'
          }`}>
            从一句话的灵感出发，让AI帮您构建完整的小说世界。支持多种AI模型，提供专业的创作工具和沉浸式阅读体验。
          </p>
          
          {/* 统计数据 */}
          <div className="grid grid-cols-3 gap-6 max-w-md mx-auto mb-12">
            <div className="text-center">
              <div className={`text-2xl font-bold ${
                isDark ? 'text-purple-400' : 'text-blue-600'
              }`}>{stats.totalProjects}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>创作项目</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${
                isDark ? 'text-blue-400' : 'text-purple-600'
              }`}>{stats.totalChapters}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>完成章节</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${
                isDark ? 'text-green-400' : 'text-green-600'
              }`}>{stats.totalWords.toLocaleString()}</div>
              <div className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>创作字数</div>
            </div>
          </div>

          {/* 快捷操作 */}
          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {quickActions.map((action, index) => (
              <button
                key={index}
                onClick={action.action}
                className={`group p-6 rounded-2xl text-left transition-all duration-200 transform hover:scale-105 ${
                  action.primary
                    ? isDark
                      ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg hover:shadow-xl'
                      : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg hover:shadow-xl'
                    : isDark
                      ? 'bg-gray-800 border-2 border-gray-700 hover:border-gray-600 shadow-sm hover:shadow-md text-gray-100'
                      : 'bg-white border-2 border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md'
                }`}
              >
                <div className="flex items-center space-x-4">
                  <div className={`text-3xl ${action.primary ? '' : 'group-hover:scale-110 transition-transform'}`}>
                    {action.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className={`text-lg font-semibold mb-1 ${
                      action.primary ? 'text-white' : isDark ? 'text-gray-100' : 'text-gray-900'
                    }`}>
                      {action.title}
                    </h3>
                    <p className={`text-sm ${
                      action.primary 
                        ? isDark ? 'text-purple-100' : 'text-blue-100'
                        : isDark ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      {action.description}
                    </p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* 功能特性 */}
        <div className="mb-16">
          <h2 className={`text-3xl font-bold text-center mb-12 ${
            isDark ? 'text-white' : 'text-gray-900'
          }`}>
            为什么选择我们的AI小说创作平台？
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div 
                key={index}
                className={`p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow border ${
                  isDark 
                    ? 'bg-gray-800 border-gray-700 hover:border-gray-600' 
                    : 'bg-white border-gray-100'
                }`}
              >
                <div className="text-3xl mb-4">{feature.icon}</div>
                <h3 className={`text-lg font-semibold mb-2 ${
                  isDark ? 'text-gray-100' : 'text-gray-900'
                }`}>
                  {feature.title}
                </h3>
                <p className={`text-sm leading-relaxed ${
                  isDark ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* 工作流程 */}
        <div className="text-center">
          <h2 className={`text-3xl font-bold mb-12 ${
            isDark ? 'text-white' : 'text-gray-900'
          }`}>
            简单三步，开启创作之旅
          </h2>
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {[
              {
                step: '01',
                title: '输入创意',
                description: '只需描述您的故事概念，AI将理解您的创意'
              },
              {
                step: '02',
                title: '生成故事圣经',
                description: 'AI自动构建完整的世界观、角色和情节大纲'
              },
              {
                step: '03',
                title: '智能创作',
                description: '逐章生成高质量内容，享受专业阅读体验'
              }
            ].map((item, index) => (
              <div key={index} className="relative">
                <div className={`text-white rounded-full w-16 h-16 flex items-center justify-center text-lg font-bold mx-auto mb-4 ${
                  isDark 
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600' 
                    : 'bg-gradient-to-r from-blue-600 to-purple-600'
                }`}>
                  {item.step}
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${
                  isDark ? 'text-gray-100' : 'text-gray-900'
                }`}>
                  {item.title}
                </h3>
                <p className={isDark ? 'text-gray-400' : 'text-gray-600'}>
                  {item.description}
                </p>
                
                {/* 连接线 */}
                {index < 2 && (
                  <div className={`hidden md:block absolute top-8 left-full w-full h-0.5 transform -translate-x-8 ${
                    isDark 
                      ? 'bg-gradient-to-r from-purple-300 to-blue-300' 
                      : 'bg-gradient-to-r from-blue-200 to-purple-200'
                  }`}></div>
                )}
              </div>
            ))}
          </div>
        </div>
      </main>

      {/* 底部 */}
      <footer className={`border-t py-8 transition-colors duration-300 ${
        isDark 
          ? 'bg-gray-800 border-gray-700' 
          : 'bg-gray-50 border-gray-200'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              AI小说工作流 - 让创作更简单，让故事更精彩
            </p>
            <p className={`text-xs mt-2 ${isDark ? 'text-gray-500' : 'text-gray-400'}`}>
              Powered by 智谱AI & Kimi
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};