/**
 * 🔧 [调试] API测试页面
 * 用于测试后端API连接和功能验证
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Label } from '../../components/ui/Label';
import { Textarea } from '../../components/ui/Textarea';
import { Select } from '../../components/ui/Select';
import { Badge } from '../../components/ui/Badge';
import { Separator } from '../../components/ui/Separator';
import { SimpleApiService } from '../../services/api/client';
import { debugLog } from '../../config/env';
import {
  StoryBibleRequest,
  StoryBibleResponse,
  ChapterGenerationRequest,
  ChapterResponse,
  TaskStatusResponse,
  HealthCheckResponse,
  AIProvider,
  StoryGenre,
  GenerationStatus
} from '../../types/backend';

/**
 * API测试页面组件
 */
export default function ApiTestPage() {
  // 系统状态
  const [healthStatus, setHealthStatus] = useState<{
    connected: boolean;
    latency?: number;
    details?: HealthCheckResponse;
  } | null>(null);

  // 故事圣经生成状态
  const [bibleRequest, setBibleRequest] = useState<StoryBibleRequest>({
    title: '测试小说',
    genre: StoryGenre.FANTASY,
    theme: '一个关于魔法与冒险的故事',
    protagonist: '年轻的魔法师艾莉丝，勇敢而聪明',
    setting: '中世纪魔法王国，充满神秘与危险',
    plot_outline: '主角在魔法学院学习，遇到各种挑战，最终成长为强大的魔法师',
    target_audience: '青少年读者',
    writing_style: '轻松幽默，富有想象力',
    ai_provider: AIProvider.ZHIPU,
    temperature: 0.8,
    max_tokens: 3000
  });

  const [bibleResponse, setBibleResponse] = useState<StoryBibleResponse | null>(null);
  const [bibleLoading, setBibleLoading] = useState(false);

  // 章节生成状态
  const [chapterRequest, setChapterRequest] = useState<ChapterGenerationRequest>({
    story_bible_id: '',
    chapter_number: 1,
    chapter_title: '第一章：魔法学院',
    chapter_outline: '艾莉丝第一天到达魔法学院，遇到室友和老师',
    previous_chapter_summary: '',
    character_development: '艾莉丝从紧张变为兴奋',
    plot_requirements: '介绍学院环境和主要角色',
    target_word_count: 2000,
    ai_provider: AIProvider.ZHIPU,
    temperature: 0.8,
    max_tokens: 4000
  });

  const [chapterResponse, setChapterResponse] = useState<ChapterResponse | null>(null);
  const [chapterLoading, setChapterLoading] = useState(false);

  // 任务状态查询
  const [taskId, setTaskId] = useState('');
  const [taskStatus, setTaskStatus] = useState<TaskStatusResponse | null>(null);
  const [taskLoading, setTaskLoading] = useState(false);

  // 页面加载时检查健康状态
  useEffect(() => {
    checkHealth();
  }, []);

  /**
   * 检查后端健康状态
   */
  const checkHealth = async () => {
    try {
      debugLog('调试', '开始健康检查');
      const result = await SimpleApiService.healthCheck();
      const rootInfo = await SimpleApiService.getRootInfo();
      
      setHealthStatus({
        connected: result.connected,
        latency: result.latency,
        details: rootInfo as any
      });
      
      debugLog('调试', '健康检查完成', { connected: result.connected, latency: result.latency });
    } catch (error) {
      debugLog('错误', '健康检查失败', { error });
      setHealthStatus({ connected: false });
    }
  };

  /**
   * 生成故事圣经
   */
  const generateBible = async () => {
    setBibleLoading(true);
    try {
      debugLog('调试', '开始生成故事圣经', bibleRequest);
      const response = await SimpleApiService.generateStoryBible(bibleRequest);
      setBibleResponse(response);
      
      // 自动设置章节请求的故事圣经ID
      setChapterRequest(prev => ({
        ...prev,
        story_bible_id: response.id
      }));
      
      debugLog('调试', '故事圣经生成任务创建成功', { task_id: response.id });
    } catch (error) {
      debugLog('错误', '故事圣经生成失败', { error });
    } finally {
      setBibleLoading(false);
    }
  };

  /**
   * 生成章节
   */
  const generateChapter = async () => {
    if (!chapterRequest.story_bible_id) {
      alert('请先生成故事圣经');
      return;
    }

    setChapterLoading(true);
    try {
      debugLog('调试', '开始生成章节', chapterRequest);
      const response = await SimpleApiService.generateChapter(chapterRequest);
      setChapterResponse(response);
      debugLog('调试', '章节生成任务创建成功', { task_id: response.id });
    } catch (error) {
      debugLog('错误', '章节生成失败', { error });
    } finally {
      setChapterLoading(false);
    }
  };

  /**
   * 查询任务状态
   */
  const queryTaskStatus = async () => {
    if (!taskId) {
      alert('请输入任务ID');
      return;
    }

    setTaskLoading(true);
    try {
      debugLog('调试', '查询任务状态', { task_id: taskId });
      const response = await SimpleApiService.getTaskStatus(taskId);
      setTaskStatus(response);
      debugLog('调试', '任务状态查询完成', response);
    } catch (error) {
      debugLog('错误', '任务状态查询失败', { error });
    } finally {
      setTaskLoading(false);
    }
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status: GenerationStatus) => {
    switch (status) {
      case GenerationStatus.PENDING:
        return 'bg-yellow-500';
      case GenerationStatus.GENERATING:
        return 'bg-blue-500';
      case GenerationStatus.COMPLETED:
        return 'bg-green-500';
      case GenerationStatus.FAILED:
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">🔧 API测试页面</h1>
        <p className="text-muted-foreground">测试后端API连接和功能验证</p>
      </div>

      {/* 健康检查 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🩺 系统健康检查
            <Badge variant={healthStatus?.connected ? "default" : "destructive"}>
              {healthStatus?.connected ? "已连接" : "未连接"}
            </Badge>
          </CardTitle>
          <CardDescription>检查后端服务状态和连接性</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={checkHealth}>重新检查</Button>
            {healthStatus?.latency && (
              <Badge variant="outline">延迟: {healthStatus.latency}ms</Badge>
            )}
          </div>
          
          {healthStatus?.details && (
            <div className="bg-muted p-4 rounded-lg">
              <pre className="text-sm">
                {JSON.stringify(healthStatus.details, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>

      <Separator />

      {/* 故事圣经生成测试 */}
      <Card>
        <CardHeader>
          <CardTitle>📝 故事圣经生成测试</CardTitle>
          <CardDescription>测试故事圣经生成API</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">小说标题</Label>
              <Input
                id="title"
                value={bibleRequest.title}
                onChange={(e) => setBibleRequest(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="genre">小说类型</Label>
              <Select
                value={bibleRequest.genre}
                onChange={(value) => setBibleRequest(prev => ({ ...prev, genre: value as StoryGenre }))}
                options={[
                  { value: StoryGenre.FANTASY, label: '奇幻' },
                  { value: StoryGenre.ROMANCE, label: '言情' },
                  { value: StoryGenre.URBAN, label: '都市' },
                  { value: StoryGenre.HISTORICAL, label: '历史' },
                  { value: StoryGenre.MYSTERY, label: '悬疑' },
                  { value: StoryGenre.SCIFI, label: '科幻' },
                  { value: StoryGenre.MARTIAL_ARTS, label: '武侠' },
                  { value: StoryGenre.THRILLER, label: '惊悚' }
                ]}
                placeholder="选择小说类型"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="theme">小说主题</Label>
            <Textarea
              id="theme"
              value={bibleRequest.theme}
              onChange={(e) => setBibleRequest(prev => ({ ...prev, theme: e.target.value }))}
              rows={2}
            />
          </div>

          <div>
            <Label htmlFor="protagonist">主角设定</Label>
            <Textarea
              id="protagonist"
              value={bibleRequest.protagonist}
              onChange={(e) => setBibleRequest(prev => ({ ...prev, protagonist: e.target.value }))}
              rows={2}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="ai_provider">AI提供商</Label>
              <Select
                value={bibleRequest.ai_provider}
                onChange={(value) => setBibleRequest(prev => ({ ...prev, ai_provider: value as AIProvider }))}
                options={[
                  { value: AIProvider.ZHIPU, label: '智谱AI' },
                  { value: AIProvider.KIMI, label: 'Kimi' }
                ]}
                placeholder="选择AI提供商"
              />
            </div>
            <div>
              <Label htmlFor="temperature">温度参数</Label>
              <Input
                id="temperature"
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={bibleRequest.temperature}
                onChange={(e) => setBibleRequest(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
              />
            </div>
          </div>

          <Button onClick={generateBible} disabled={bibleLoading}>
            {bibleLoading ? '生成中...' : '生成故事圣经'}
          </Button>

          {bibleResponse && (
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-semibold mb-2">生成结果:</h4>
              <div className="space-y-2">
                <div>任务ID: <code>{bibleResponse.id}</code></div>
                <div>状态: <Badge className={getStatusColor(bibleResponse.status)}>{bibleResponse.status}</Badge></div>
                <div>创建时间: {new Date(bibleResponse.created_at).toLocaleString()}</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Separator />

      {/* 章节生成测试 */}
      <Card>
        <CardHeader>
          <CardTitle>📖 章节生成测试</CardTitle>
          <CardDescription>测试章节生成API</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="story_bible_id">故事圣经ID</Label>
              <Input
                id="story_bible_id"
                value={chapterRequest.story_bible_id}
                onChange={(e) => setChapterRequest(prev => ({ ...prev, story_bible_id: e.target.value }))}
                placeholder="从上面的故事圣经生成结果中获取"
              />
            </div>
            <div>
              <Label htmlFor="chapter_number">章节号</Label>
              <Input
                id="chapter_number"
                type="number"
                min="1"
                value={chapterRequest.chapter_number}
                onChange={(e) => setChapterRequest(prev => ({ ...prev, chapter_number: parseInt(e.target.value) }))}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="chapter_title">章节标题</Label>
            <Input
              id="chapter_title"
              value={chapterRequest.chapter_title}
              onChange={(e) => setChapterRequest(prev => ({ ...prev, chapter_title: e.target.value }))}
            />
          </div>

          <div>
            <Label htmlFor="chapter_outline">章节大纲</Label>
            <Textarea
              id="chapter_outline"
              value={chapterRequest.chapter_outline}
              onChange={(e) => setChapterRequest(prev => ({ ...prev, chapter_outline: e.target.value }))}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="target_word_count">目标字数</Label>
              <Input
                id="target_word_count"
                type="number"
                min="500"
                max="10000"
                value={chapterRequest.target_word_count}
                onChange={(e) => setChapterRequest(prev => ({ ...prev, target_word_count: parseInt(e.target.value) }))}
              />
            </div>
            <div>
              <Label htmlFor="chapter_ai_provider">AI提供商</Label>
              <Select
                value={chapterRequest.ai_provider}
                onChange={(value) => setChapterRequest(prev => ({ ...prev, ai_provider: value as AIProvider }))}
                options={[
                  { value: AIProvider.ZHIPU, label: '智谱AI' },
                  { value: AIProvider.KIMI, label: 'Kimi' }
                ]}
                placeholder="选择AI提供商"
              />
            </div>
          </div>

          <Button onClick={generateChapter} disabled={chapterLoading}>
            {chapterLoading ? '生成中...' : '生成章节'}
          </Button>

          {chapterResponse && (
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-semibold mb-2">生成结果:</h4>
              <div className="space-y-2">
                <div>任务ID: <code>{chapterResponse.id}</code></div>
                <div>状态: <Badge className={getStatusColor(chapterResponse.status)}>{chapterResponse.status}</Badge></div>
                <div>章节号: {chapterResponse.request_data.chapter_number}</div>
                <div>章节标题: {chapterResponse.request_data.chapter_title}</div>
                <div>创建时间: {new Date(chapterResponse.created_at).toLocaleString()}</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Separator />

      {/* 任务状态查询 */}
      <Card>
        <CardHeader>
          <CardTitle>📋 任务状态查询</CardTitle>
          <CardDescription>查询生成任务的当前状态</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="task_id">任务ID</Label>
            <Input
              id="task_id"
              value={taskId}
              onChange={(e) => setTaskId(e.target.value)}
              placeholder="输入要查询的任务ID"
            />
          </div>

          <Button onClick={queryTaskStatus} disabled={taskLoading}>
            {taskLoading ? '查询中...' : '查询状态'}
          </Button>

          {taskStatus && (
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-semibold mb-2">任务状态:</h4>
              <div className="space-y-2">
                <div>任务ID: <code>{taskStatus.id}</code></div>
                <div>任务类型: <Badge variant="outline">{taskStatus.task_type}</Badge></div>
                <div>状态: <Badge className={getStatusColor(taskStatus.status)}>{taskStatus.status}</Badge></div>
                <div>AI提供商: {taskStatus.ai_provider}</div>
                <div>创建时间: {new Date(taskStatus.created_at).toLocaleString()}</div>
                <div>更新时间: {new Date(taskStatus.updated_at).toLocaleString()}</div>
                {taskStatus.progress && (
                  <div>进度: {taskStatus.progress}%</div>
                )}
                {taskStatus.current_step && (
                  <div>当前步骤: {taskStatus.current_step}</div>
                )}
                {taskStatus.error_message && (
                  <div className="text-red-600">错误信息: {taskStatus.error_message}</div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>📖 使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>1. 首先检查系统健康状态，确保后端服务正常运行</p>
            <p>2. 填写故事圣经信息并点击"生成故事圣经"，获取任务ID</p>
            <p>3. 使用获取的任务ID查询生成状态，等待生成完成</p>
            <p>4. 故事圣经生成完成后，可以使用其ID生成章节</p>
            <p>5. 所有操作都会在浏览器控制台输出详细日志</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
