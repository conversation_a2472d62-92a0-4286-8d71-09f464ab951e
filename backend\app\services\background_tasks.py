"""
⚡ [任务] 后台任务处理模块
处理故事圣经和章节生成的后台任务
"""

from datetime import datetime
from typing import TYPE_CHECKING

from app.schemas.generation import (
    StoryBibleRequest, ChapterGenerationRequest, GenerationStatus
)
from app.services.generation_service import (
    generate_story_bible_content, generate_chapter_content, generate_chapter_content_with_rag
)
from app.core.config import log_info, log_error, log_success

if TYPE_CHECKING:
    from app.repositories.bible_repo import StoryBibleRepository
    from app.repositories.chapter_repo import ChapterRepository


async def background_generate_story_bible(
    task_id: str, 
    request: StoryBibleRequest, 
    bible_repo: "StoryBibleRepository"
) -> None:
    """
    📝 [生成] 后台生成故事圣经任务
    
    在后台异步执行故事圣经的AI生成，并更新数据库状态
    
    Args:
        task_id: 任务ID
        request: 故事圣经生成请求
        bible_repo: 故事圣经仓库
    """
    try:
        log_info("生成", "开始后台生成故事圣经", 任务ID=task_id)
        
        # 更新状态为生成中
        await bible_repo.update_bible_status(task_id, GenerationStatus.GENERATING)
        
        # 执行生成
        start_time = datetime.now()
        content = await generate_story_bible_content(request)
        end_time = datetime.now()
        generation_time = (end_time - start_time).total_seconds()
        
        # 更新任务为完成状态
        await bible_repo.update_bible_status(
            task_id,
            GenerationStatus.COMPLETED,
            generated_content=content,
            content_length=len(content),
            generation_time=generation_time,
            token_usage=len(content.split()),  # 简单估算token使用量
            completed_at=end_time
        )
        
        log_success("生成", "故事圣经生成完成", 
                   任务ID=task_id, 
                   内容长度=len(content),
                   生成时间=f"{generation_time:.2f}秒")
        
    except Exception as e:
        log_error("生成", "故事圣经生成失败", error=e, 任务ID=task_id)
        await bible_repo.update_bible_status(
            task_id,
            GenerationStatus.FAILED,
            error_message=str(e)
        )


async def background_generate_chapter(
    task_id: str, 
    request: ChapterGenerationRequest, 
    chapter_repo: "ChapterRepository"
) -> None:
    """
    📝 [生成] 后台生成章节任务
    
    在后台异步执行章节的AI生成，并更新数据库状态
    
    Args:
        task_id: 任务ID
        request: 章节生成请求
        chapter_repo: 章节仓库
    """
    try:
        log_info("生成", "开始后台生成章节", 
                任务ID=task_id, 
                章节号=request.chapter_number)
        
        # 更新状态为生成中
        await chapter_repo.update_chapter_status(task_id, GenerationStatus.GENERATING)
        
        # 获取故事圣经
        from app.core.database import db_manager
        from app.repositories.bible_repo import StoryBibleRepository

        # 这里需要获取数据库会话来查询故事圣经和情感基因
        # 注意：在后台任务中需要手动管理数据库会话
        async with db_manager.get_session() as session:
            bible_repo = StoryBibleRepository(session)

            story_bible = await bible_repo.get_bible_by_id(request.story_bible_id)
            if not story_bible:
                raise ValueError(f"未找到故事圣经: {request.story_bible_id}")

            if story_bible.status != GenerationStatus.COMPLETED:
                raise ValueError("故事圣经尚未完成生成")

            # 创建情感基因仓库（如果启用了情感增强）
            gene_repository = None
            if request.enable_emotion_enhancement:
                from app.repositories.emotional_gene_repo import EmotionalGeneRepository
                gene_repository = EmotionalGeneRepository(session)

            # 🧠 [RAG] 集成动态提示词合成器
            log_info("生成", "开始构建RAG上下文简报",
                    任务ID=task_id,
                    故事圣经ID=request.story_bible_id,
                    章节号=request.chapter_number)

            # 🌐 [知识图谱] 创建提示词合成器（集成世界图谱客户端）
            from app.repositories.chapter_repo import ChapterRepository
            from app.services.vector_store import get_vector_store
            from app.core.prompt_synthesizer import PromptSynthesizer
            from app.services.world_graph_client import create_world_graph_client

            chapter_repo_for_synthesizer = ChapterRepository(session)
            vector_store = await get_vector_store()
            world_graph_client = create_world_graph_client(session)

            synthesizer = PromptSynthesizer(
                bible_repo,
                chapter_repo_for_synthesizer,
                vector_store,
                world_graph_client  # 🌐 集成世界图谱客户端
            )

            # 构建上下文简报
            context_briefing = await synthesizer.build_context_briefing(
                task_description=f"创作第{request.chapter_number}章：{request.chapter_title}",
                story_bible_id=request.story_bible_id,
                chapter_number=request.chapter_number,
                max_memory_results=10,
                max_previous_chapters=5
            )

            log_success("生成", "RAG上下文简报构建完成",
                       任务ID=task_id,
                       质量评分=f"{context_briefing.context_quality_score:.3f}",
                       检索耗时=f"{context_briefing.retrieval_time:.3f}秒",
                       上下文长度=len(context_briefing.enhanced_prompt))

            # 执行RAG增强的生成
            start_time = datetime.now()
            content = await generate_chapter_content_with_rag(
                request,
                context_briefing,
                gene_repository
            )
            end_time = datetime.now()
            generation_time = (end_time - start_time).total_seconds()

            # 🌐 [事件处理] 自动更新世界知识图谱
            try:
                log_info("生成", "开始处理章节事件，更新知识图谱",
                        任务ID=task_id,
                        章节号=request.chapter_number)

                from app.core.event_processor import EventProcessor
                from app.core.database import db_manager

                # 创建事件处理器
                event_processor = EventProcessor(db_manager)

                # 处理章节事件，自动更新知识图谱
                events = await event_processor.process_chapter_events(
                    chapter_text=content,
                    story_id=request.story_bible_id,
                    chapter_number=request.chapter_number
                )

                log_success("生成", "章节事件处理完成",
                           任务ID=task_id,
                           章节号=request.chapter_number,
                           处理事件数=len(events))

            except Exception as e:
                # 事件处理失败不影响章节生成的成功状态
                log_error("生成", "章节事件处理失败", error=e,
                         任务ID=task_id,
                         章节号=request.chapter_number)

            # 更新任务为完成状态
            await chapter_repo.update_chapter_status(
                task_id,
                GenerationStatus.COMPLETED,
                generated_content=content,
                generation_time=generation_time,
                token_usage=len(content.split()),  # 简单估算token使用量
                actual_word_count=len(content),
                completed_at=end_time
            )

            log_success("生成", "章节生成完成",
                       任务ID=task_id,
                       章节号=request.chapter_number,
                       内容长度=len(content),
                       生成时间=f"{generation_time:.2f}秒")
        
    except Exception as e:
        log_error("生成", "章节生成失败", error=e, 任务ID=task_id)
        await chapter_repo.update_chapter_status(
            task_id,
            GenerationStatus.FAILED,
            error_message=str(e)
        )


def format_bible_response(bible) -> dict:
    """
    🎨 [UI] 格式化故事圣经响应数据
    
    Args:
        bible: 故事圣经数据库对象
        
    Returns:
        dict: 格式化的响应数据
    """
    return {
        "id": bible.id,
        "title": bible.title,
        "genre": bible.genre,
        "theme": bible.theme,
        "protagonist": bible.protagonist,
        "setting": bible.setting,
        "plot_outline": bible.plot_outline,
        "target_audience": bible.target_audience,
        "writing_style": bible.writing_style,
        "status": bible.status,
        "ai_provider": bible.ai_provider,
        "temperature": bible.temperature,
        "max_tokens": bible.max_tokens,
        "generated_content": bible.generated_content,
        "content_length": bible.content_length,
        "generation_time": bible.generation_time,
        "token_usage": bible.token_usage,
        "error_message": bible.error_message,
        "created_at": bible.created_at.isoformat(),
        "updated_at": bible.updated_at.isoformat(),
        "completed_at": bible.completed_at.isoformat() if bible.completed_at else None
    }


def format_chapter_response(chapter) -> dict:
    """
    🎨 [UI] 格式化章节响应数据
    
    Args:
        chapter: 章节数据库对象
        
    Returns:
        dict: 格式化的响应数据
    """
    return {
        "id": chapter.id,
        "story_bible_id": chapter.story_bible_id,
        "chapter_number": chapter.chapter_number,
        "chapter_title": chapter.chapter_title,
        "chapter_outline": chapter.chapter_outline,
        "previous_chapter_summary": chapter.previous_chapter_summary,
        "character_development": chapter.character_development,
        "plot_requirements": chapter.plot_requirements,
        "target_word_count": chapter.target_word_count,
        "status": chapter.status,
        "ai_provider": chapter.ai_provider,
        "temperature": chapter.temperature,
        "max_tokens": chapter.max_tokens,
        "generated_content": chapter.generated_content,
        "content_length": chapter.content_length,
        "actual_word_count": chapter.actual_word_count,
        "generation_time": chapter.generation_time,
        "token_usage": chapter.token_usage,
        "error_message": chapter.error_message,
        "created_at": chapter.created_at.isoformat(),
        "updated_at": chapter.updated_at.isoformat(),
        "completed_at": chapter.completed_at.isoformat() if chapter.completed_at else None
    }
