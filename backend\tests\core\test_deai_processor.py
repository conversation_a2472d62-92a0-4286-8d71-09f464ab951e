"""
🧪 [测试] 去AI化处理器测试模块

测试去AI化处理器的各项功能，包括：
1. AI痕迹检测功能
2. 自适应处理强度调节
3. 多模块协同去AI化处理
4. 处理效果评估

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import pytest
from unittest.mock import Mock, patch

from app.core.deai_processor import (
    DeAIProcessor,
    AITraceDetector,
    AITraceLevel,
    ProcessingMode,
    create_deai_processor,
    quick_deai_processing,
    analyze_ai_traces
)


class TestAITraceDetector:
    """AI痕迹检测器测试类"""
    
    def test_detector_initialization(self):
        """测试检测器初始化"""
        detector = AITraceDetector()
        
        assert detector.ai_patterns is not None
        assert detector.human_patterns is not None
        assert len(detector.ai_patterns) > 0
        assert len(detector.human_patterns) > 0
    
    def test_high_ai_trace_detection(self):
        """测试高AI痕迹文本检测"""
        detector = AITraceDetector()
        
        # 典型的AI生成文本特征
        ai_text = """
        不仅如此，而且更重要的是，这个完美的解决方案毫无疑问地展现了绝对的优势。
        首先，它具有非常出色的性能；其次，它拥有十分完善的功能；最后，它提供了极其便利的操作体验。
        总的来说，这是一个无瑕的产品，完全满足了用户的所有需求。
        """
        
        result = detector.analyze_ai_traces(ai_text)
        
        assert result["ai_percentage"] > 50  # 应该检测到高AI痕迹
        assert result["ai_level"] in [AITraceLevel.HIGH, AITraceLevel.VERY_HIGH]
        assert result["total_ai_matches"] > 5
    
    def test_low_ai_trace_detection(self):
        """测试低AI痕迹文本检测"""
        detector = AITraceDetector()
        
        # 更自然的人类文本
        human_text = """
        嗯，这个东西好像还行吧。用起来感觉稍微有点别扭，不过大概能满足需求。
        或许是我还没习惯，总觉得有些地方怎么说呢...不太顺手。
        """
        
        result = detector.analyze_ai_traces(human_text)
        
        assert result["ai_percentage"] < 30  # 应该检测到低AI痕迹
        assert result["ai_level"] in [AITraceLevel.VERY_LOW, AITraceLevel.LOW]
        assert result["total_human_matches"] > 0
    
    def test_ai_level_determination(self):
        """测试AI等级判定"""
        detector = AITraceDetector()
        
        # 测试不同百分比对应的等级
        assert detector._determine_ai_level(90) == AITraceLevel.VERY_HIGH
        assert detector._determine_ai_level(70) == AITraceLevel.HIGH
        assert detector._determine_ai_level(50) == AITraceLevel.MEDIUM
        assert detector._determine_ai_level(30) == AITraceLevel.LOW
        assert detector._determine_ai_level(10) == AITraceLevel.VERY_LOW


class TestDeAIProcessor:
    """去AI化处理器测试类"""
    
    def test_processor_initialization(self):
        """测试处理器初始化"""
        processor = DeAIProcessor()
        
        assert processor.ai_detector is not None
        assert processor.attention_defocus is not None
        assert processor.emotional_downgrader is not None
        assert processor.behavior_injector is not None
        assert processor.time_distorter is not None
        assert processor.entropy_injector is not None
    
    def test_processing_intensity_determination(self):
        """测试处理强度确定"""
        processor = DeAIProcessor()
        
        # 测试保守模式
        intensity = processor._determine_processing_intensity(
            ProcessingMode.CONSERVATIVE, 
            AITraceLevel.HIGH
        )
        assert intensity == 0.3
        
        # 测试自适应模式
        intensity = processor._determine_processing_intensity(
            ProcessingMode.ADAPTIVE, 
            AITraceLevel.VERY_HIGH
        )
        assert intensity == 1.0
        
        # 测试自定义强度
        intensity = processor._determine_processing_intensity(
            ProcessingMode.BALANCED, 
            AITraceLevel.MEDIUM,
            custom_intensity=0.7
        )
        assert intensity == 0.7
    
    def test_primary_emotion_detection(self):
        """测试主导情绪检测"""
        processor = DeAIProcessor()
        
        # 测试愤怒情绪
        angry_text = "他非常愤怒，气愤地说道..."
        emotion = processor._detect_primary_emotion(angry_text)
        assert emotion == "愤怒"
        
        # 测试悲伤情绪
        sad_text = "她感到非常悲伤和失落..."
        emotion = processor._detect_primary_emotion(sad_text)
        assert emotion == "悲伤"
        
        # 测试无明显情绪
        neutral_text = "今天天气不错，阳光明媚。"
        emotion = processor._detect_primary_emotion(neutral_text)
        assert emotion is None
    
    def test_time_content_detection(self):
        """测试时间内容检测"""
        processor = DeAIProcessor()
        
        # 包含时间内容
        time_text = "几分钟后，他突然意识到..."
        assert processor._has_time_content(time_text) is True
        
        # 不包含时间内容
        no_time_text = "这是一个普通的描述。"
        assert processor._has_time_content(no_time_text) is False
    
    @patch('app.core.deai_processor.create_attention_defocus_system')
    @patch('app.core.deai_processor.create_emotional_downgrader')
    @patch('app.core.deai_processor.create_irrational_behavior_injector')
    @patch('app.core.deai_processor.create_time_perception_distorter')
    @patch('app.core.deai_processor.create_entropy_injector')
    def test_adaptive_deai_processing(self, mock_entropy, mock_time, mock_behavior, mock_emotional, mock_attention):
        """测试自适应去AI化处理"""
        # 模拟各个模块
        mock_attention_system = Mock()
        mock_attention_system.apply_emotional_filter.return_value = Mock(
            filtered_text="失焦处理后的文本",
            compression_ratio=0.8,
            filtered_elements=[]
        )
        mock_attention.return_value = mock_attention_system
        
        mock_emotional_system = Mock()
        mock_emotional_system.make_expression_inadequate.return_value = Mock(
            processed_text="情感降级后的文本",
            inadequacy_score=0.7,
            modes_applied=[],
            modifications_count=2
        )
        mock_emotional.return_value = mock_emotional_system
        
        mock_behavior_system = Mock()
        mock_behavior_system.inject_micro_behavior.return_value = Mock(
            processed_text="行为注入后的文本",
            behaviors_injected=[],
            injection_points=[]
        )
        mock_behavior.return_value = mock_behavior_system
        
        mock_time_system = Mock()
        mock_time_system.distort_time_perception.return_value = Mock(
            distorted_text="时间扭曲后的文本",
            distortion_mode=Mock(value="accelerated"),
            time_modifications=[]
        )
        mock_time.return_value = mock_time_system
        
        mock_entropy_system = Mock()
        mock_entropy_system.process_text.return_value = Mock(
            processed_text="熵增处理后的文本",
            modifications_applied=3,
            disruption_techniques=[],
            ai_trace_reduction=25.0
        )
        mock_entropy.return_value = mock_entropy_system
        
        # 测试处理
        processor = DeAIProcessor()
        
        # 高AI痕迹文本
        ai_text = """
        不仅如此，而且更重要的是，这个完美的解决方案毫无疑问地展现了绝对的优势。
        首先，它具有非常出色的性能；其次，它拥有十分完善的功能。
        """
        
        result = processor.adaptive_deai_processing(
            text=ai_text,
            processing_mode=ProcessingMode.ADAPTIVE
        )
        
        assert result is not None
        assert result.original_text == ai_text
        assert result.processed_text != ai_text
        assert result.ai_trace_before > 0
        assert result.ai_reduction_percentage >= 0
        assert len(result.modules_applied) > 0
        assert result.quality_score >= 0
    
    def test_quality_score_calculation(self):
        """测试质量评分计算"""
        processor = DeAIProcessor()
        
        # 测试高质量处理
        score = processor._calculate_quality_score(
            ai_reduction_percentage=80.0,
            modules_count=4,
            processing_details={
                "模块1": {"效果": "良好"},
                "模块2": {"效果": "优秀"},
                "模块3": {"效果": "一般"},
                "模块4": {"效果": "良好"}
            }
        )
        
        assert 0.0 <= score <= 1.0
        assert score > 0.8  # 高质量处理应该有高分
        
        # 测试低质量处理
        score = processor._calculate_quality_score(
            ai_reduction_percentage=10.0,
            modules_count=1,
            processing_details={
                "模块1": {"error": "处理失败"}
            }
        )
        
        assert 0.0 <= score <= 1.0
        assert score < 0.3  # 低质量处理应该有低分


class TestFactoryFunctions:
    """工厂函数测试类"""
    
    def test_create_deai_processor(self):
        """测试创建去AI化处理器"""
        processor = create_deai_processor()
        
        assert isinstance(processor, DeAIProcessor)
        assert processor.ai_detector is not None
    
    @patch('app.core.deai_processor.create_deai_processor')
    def test_quick_deai_processing(self, mock_create):
        """测试快速去AI化处理"""
        mock_processor = Mock()
        mock_processor.adaptive_deai_processing.return_value = Mock(
            processed_text="处理后文本",
            ai_reduction_percentage=50.0
        )
        mock_create.return_value = mock_processor
        
        result = quick_deai_processing("测试文本", intensity=0.5)
        
        assert result is not None
        mock_processor.adaptive_deai_processing.assert_called_once()
    
    def test_analyze_ai_traces(self):
        """测试AI痕迹分析"""
        result = analyze_ai_traces("测试文本")
        
        assert "ai_percentage" in result
        assert "ai_level" in result
        assert "total_ai_matches" in result
        assert "total_human_matches" in result


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
