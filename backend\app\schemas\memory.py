"""
🧠 [记忆] 记忆嵌入相关的数据模型
定义记忆嵌入工作流的请求和响应数据结构
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class MemoryType(str, Enum):
    """记忆类型枚举"""
    CHAPTER = "chapter"       # 章节记忆
    CHARACTER = "character"   # 角色记忆
    PLOT = "plot"            # 情节记忆
    SETTING = "setting"      # 场景记忆
    DIALOGUE = "dialogue"    # 对话记忆
    EMOTION = "emotion"      # 情感记忆


class MemoryEmbedRequest(BaseModel):
    """记忆嵌入请求模型"""
    story_bible_id: str = Field(..., description="故事圣经ID")
    chapter_number: int = Field(..., ge=1, description="章节号")
    content: Optional[str] = Field(None, description="自定义内容，如果不提供则使用章节生成内容")
    summary: Optional[str] = Field(None, description="自定义摘要，如果不提供则自动生成")
    memory_type: MemoryType = Field(default=MemoryType.CHAPTER, description="记忆类型")
    importance_score: float = Field(default=0.5, ge=0.0, le=1.0, description="重要性分数")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")


class MemorySearchRequest(BaseModel):
    """记忆搜索请求模型"""
    query: str = Field(..., description="搜索查询文本", min_length=1, max_length=500)
    story_id: Optional[int] = Field(None, description="限制搜索的故事ID")
    memory_type: Optional[MemoryType] = Field(None, description="限制搜索的记忆类型")
    n_results: int = Field(default=5, ge=1, le=20, description="返回结果数量")
    min_similarity: float = Field(default=0.0, ge=0.0, le=1.0, description="最小相似度阈值")


class MemoryDocument(BaseModel):
    """记忆文档响应模型"""
    id: str = Field(..., description="记忆文档ID")
    content: str = Field(..., description="记忆内容")
    summary: str = Field(..., description="记忆摘要")
    story_id: int = Field(..., description="所属故事ID")
    chapter_id: Optional[int] = Field(None, description="所属章节ID")
    memory_type: MemoryType = Field(..., description="记忆类型")
    importance_score: float = Field(..., description="重要性分数")
    created_at: str = Field(..., description="创建时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")


class MemorySearchResult(BaseModel):
    """记忆搜索结果模型"""
    document: MemoryDocument = Field(..., description="匹配的记忆文档")
    similarity_score: float = Field(..., description="相似度分数")
    distance: float = Field(..., description="向量距离")


class MemoryEmbedResponse(BaseModel):
    """记忆嵌入响应模型"""
    success: bool = Field(..., description="是否成功")
    memory_id: str = Field(..., description="记忆文档ID")
    story_id: int = Field(..., description="故事ID")
    chapter_id: Optional[int] = Field(None, description="章节ID")
    summary: str = Field(..., description="生成的摘要")
    embedding_dimension: int = Field(..., description="向量维度")
    message: str = Field(..., description="处理消息")


class MemorySearchResponse(BaseModel):
    """记忆搜索响应模型"""
    success: bool = Field(..., description="是否成功")
    query: str = Field(..., description="搜索查询")
    total_results: int = Field(..., description="总结果数量")
    results: List[MemorySearchResult] = Field(..., description="搜索结果列表")
    average_similarity: float = Field(..., description="平均相似度")
    search_time_ms: float = Field(..., description="搜索耗时（毫秒）")


class MemoryStatsResponse(BaseModel):
    """记忆统计响应模型"""
    success: bool = Field(..., description="是否成功")
    story_id: int = Field(..., description="故事ID")
    total_memories: int = Field(..., description="总记忆数量")
    memory_types: Dict[str, int] = Field(..., description="各类型记忆数量")
    average_importance: float = Field(..., description="平均重要性分数")
    latest_memory_time: Optional[str] = Field(None, description="最新记忆时间")
    embedding_dimension: int = Field(..., description="向量维度")


class MemoryDeleteResponse(BaseModel):
    """记忆删除响应模型"""
    success: bool = Field(..., description="是否成功")
    story_id: int = Field(..., description="故事ID")
    deleted_count: int = Field(..., description="删除的记忆数量")
    message: str = Field(..., description="处理消息")


class AutoSummaryRequest(BaseModel):
    """自动摘要请求模型"""
    content: str = Field(..., description="待摘要的内容", min_length=10, max_length=10000)
    max_length: int = Field(default=100, ge=20, le=500, description="摘要最大长度")
    focus_keywords: Optional[List[str]] = Field(None, description="重点关键词")


class AutoSummaryResponse(BaseModel):
    """自动摘要响应模型"""
    success: bool = Field(..., description="是否成功")
    summary: str = Field(..., description="生成的摘要")
    original_length: int = Field(..., description="原文长度")
    summary_length: int = Field(..., description="摘要长度")
    compression_ratio: float = Field(..., description="压缩比例")
    keywords: List[str] = Field(..., description="提取的关键词")
