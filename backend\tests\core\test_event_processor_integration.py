"""
🧪 [集成测试] 动态事件处理器集成测试
测试完整的事件处理流程，包括数据库操作
"""

import pytest
import json
from unittest.mock import patch, AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.event_processor import EventProcessor, get_event_processor
from app.models.story_bible import StoryBible, AIProvider
from app.models.world_graph import Entity, EntityRelationship, EntityType
from app.services.zhipu_client import ChatCompletionResponse
from app.core.config import log_debug


class TestEventProcessorIntegration:
    """事件处理器集成测试类"""
    
    @pytest.fixture
    async def test_story(self, async_db_session: AsyncSession):
        """创建测试故事"""
        story = StoryBible(
            id="test_story_integration",
            title="集成测试小说",
            genre="fantasy",
            theme="测试主题",
            protagonist="张三",
            setting="测试背景",
            plot_outline="测试大纲",
            target_audience="adult",
            ai_provider=AIProvider.ZHIPU
        )
        
        async_db_session.add(story)
        await async_db_session.commit()
        await async_db_session.refresh(story)
        
        log_debug("集成测试", "测试故事创建完成", story_id=story.id)
        return story
    
    @pytest.fixture
    async def existing_entity(self, async_db_session: AsyncSession, test_story):
        """创建已存在的实体（张三）"""
        entity = Entity(
            id="zhang_san_001",
            story_id=test_story.id,
            name="张三",
            type=EntityType.CHARACTER,
            description="主角",
            properties={"年龄": 25, "职业": "剑客"},
            first_mentioned_chapter=0,
            is_active=True
        )
        
        async_db_session.add(entity)
        await async_db_session.commit()
        await async_db_session.refresh(entity)
        
        log_debug("集成测试", "已存在实体创建完成", entity_id=entity.id)
        return entity
    
    @pytest.fixture
    def sample_chapter_text(self):
        """示例章节文本"""
        return """
        张三在森林中遇到了一位神秘的老者王五。老者告诉张三，他是一位隐居的武林高手。
        王五看中了张三的天赋，决定收他为徒。张三激动地接受了，从此王五成为了张三的师父。
        王五送给张三一本《九阳神功》秘籍，这是他多年来珍藏的武功秘籍。
        """
    
    @pytest.fixture
    def mock_ai_response(self):
        """模拟AI响应"""
        return json.dumps([
            {
                "event": "create_entity",
                "data": {
                    "name": "王五",
                    "type": "character",
                    "description": "神秘的武林高手，隐居在森林中",
                    "properties": {"年龄": 60, "职业": "武林高手", "居住地": "森林"},
                    "first_mentioned_chapter": 2
                }
            },
            {
                "event": "create_entity",
                "data": {
                    "name": "九阳神功",
                    "type": "item",
                    "description": "珍贵的武功秘籍",
                    "properties": {"类型": "秘籍", "品质": "高级"},
                    "first_mentioned_chapter": 2
                }
            },
            {
                "event": "create_relationship",
                "data": {
                    "source_entity": "王五",
                    "target_entity": "张三",
                    "relationship_type": "师父",
                    "description": "王五是张三的师父",
                    "properties": {"关系建立时间": "第2章"},
                    "established_chapter": 2
                }
            },
            {
                "event": "create_relationship",
                "data": {
                    "source_entity": "张三",
                    "target_entity": "九阳神功",
                    "relationship_type": "持有",
                    "description": "张三获得了九阳神功秘籍",
                    "properties": {"获得方式": "师父赠送"},
                    "established_chapter": 2
                }
            }
        ])
    
    @pytest.mark.asyncio
    async def test_process_chapter_events_complete_flow(
        self, 
        async_db_session: AsyncSession,
        test_story,
        existing_entity,
        sample_chapter_text,
        mock_ai_response
    ):
        """测试完整的章节事件处理流程"""
        # Mock智谱AI响应
        mock_response = ChatCompletionResponse(
            id="integration_test_id",
            object="chat.completion",
            created=1234567890,
            model="glm-4.5-flash",
            choices=[{
                "message": {"content": mock_ai_response},
                "finish_reason": "stop"
            }],
            usage={"total_tokens": 200}
        )
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            # 创建事件处理器并处理章节事件
            processor = EventProcessor()
            result = await processor.process_chapter_events(
                chapter_text=sample_chapter_text,
                story_id=test_story.id,
                chapter_number=2,
                db=async_db_session
            )
            
            # 验证处理结果
            assert result["story_id"] == test_story.id
            assert result["chapter_number"] == 2
            assert result["total_events"] == 4
            assert result["processed_events"] == 4
            assert result["successful_events"] >= 2  # 至少成功处理2个事件
            assert len(result["event_details"]) == 4
            
            log_debug("集成测试", "事件处理结果", result=result)
    
    @pytest.mark.asyncio
    async def test_create_new_entities(
        self,
        async_db_session: AsyncSession,
        test_story,
        existing_entity,
        sample_chapter_text,
        mock_ai_response
    ):
        """测试创建新实体"""
        # Mock智谱AI响应
        mock_response = ChatCompletionResponse(
            id="create_entity_test",
            object="chat.completion",
            created=1234567890,
            model="glm-4.5-flash",
            choices=[{
                "message": {"content": mock_ai_response},
                "finish_reason": "stop"
            }],
            usage={"total_tokens": 150}
        )
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            processor = EventProcessor()
            result = await processor.process_chapter_events(
                chapter_text=sample_chapter_text,
                story_id=test_story.id,
                chapter_number=2,
                db=async_db_session
            )
            
            # 验证新实体是否创建成功
            from sqlalchemy import select
            
            # 检查王五是否创建
            wang_wu_result = await async_db_session.execute(
                select(Entity).filter(
                    Entity.story_id == test_story.id,
                    Entity.name == "王五",
                    Entity.is_active == True
                )
            )
            wang_wu = wang_wu_result.scalar_one_or_none()
            
            if wang_wu:
                assert wang_wu.name == "王五"
                assert wang_wu.type == EntityType.CHARACTER
                assert wang_wu.description == "神秘的武林高手，隐居在森林中"
                assert wang_wu.properties.get("年龄") == 60
                log_debug("集成测试", "王五实体创建验证成功", entity_id=wang_wu.id)
            
            # 检查九阳神功是否创建
            jiuyang_result = await async_db_session.execute(
                select(Entity).filter(
                    Entity.story_id == test_story.id,
                    Entity.name == "九阳神功",
                    Entity.is_active == True
                )
            )
            jiuyang = jiuyang_result.scalar_one_or_none()
            
            if jiuyang:
                assert jiuyang.name == "九阳神功"
                assert jiuyang.type == EntityType.ITEM
                assert jiuyang.properties.get("类型") == "秘籍"
                log_debug("集成测试", "九阳神功实体创建验证成功", entity_id=jiuyang.id)
    
    @pytest.mark.asyncio
    async def test_create_relationships(
        self,
        async_db_session: AsyncSession,
        test_story,
        existing_entity,
        sample_chapter_text,
        mock_ai_response
    ):
        """测试创建关系"""
        # Mock智谱AI响应
        mock_response = ChatCompletionResponse(
            id="create_relationship_test",
            object="chat.completion",
            created=1234567890,
            model="glm-4.5-flash",
            choices=[{
                "message": {"content": mock_ai_response},
                "finish_reason": "stop"
            }],
            usage={"total_tokens": 180}
        )
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            processor = EventProcessor()
            result = await processor.process_chapter_events(
                chapter_text=sample_chapter_text,
                story_id=test_story.id,
                chapter_number=2,
                db=async_db_session
            )
            
            # 验证关系是否创建成功
            from sqlalchemy import select
            
            # 检查师父关系
            relationship_result = await async_db_session.execute(
                select(EntityRelationship).filter(
                    EntityRelationship.relationship_type == "师父"
                )
            )
            relationships = relationship_result.scalars().all()
            
            if relationships:
                master_relationship = relationships[0]
                assert master_relationship.relationship_type == "师父"
                assert master_relationship.description == "王五是张三的师父"
                log_debug("集成测试", "师父关系创建验证成功", 
                         relationship_id=master_relationship.id)
    
    @pytest.mark.asyncio
    async def test_error_handling_invalid_story(self, async_db_session: AsyncSession):
        """测试无效故事ID的错误处理"""
        processor = EventProcessor()
        
        result = await processor.process_chapter_events(
            chapter_text="测试文本",
            story_id="nonexistent_story",
            chapter_number=1,
            db=async_db_session
        )
        
        # 应该能正常处理，但可能没有成功的事件
        assert result["story_id"] == "nonexistent_story"
        assert result["chapter_number"] == 1
        assert isinstance(result["total_events"], int)
    
    @pytest.mark.asyncio
    async def test_singleton_pattern(self):
        """测试单例模式"""
        processor1 = await get_event_processor()
        processor2 = await get_event_processor()
        
        assert processor1 is processor2
        assert isinstance(processor1, EventProcessor)
