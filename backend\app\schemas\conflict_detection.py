"""
⚔️ [冲突检测] 冲突检测数据模型
定义冲突检测相关的Pydantic模型，用于API请求和响应的数据验证
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class ConflictTypeEnum(str, Enum):
    """冲突类型枚举"""
    RELATIONSHIP_CONTRADICTION = "relationship_contradiction"
    TIMELINE_INCONSISTENCY = "timeline_inconsistency"
    CHARACTER_ABILITY_CONFLICT = "character_ability_conflict"
    WORLD_SETTING_CONFLICT = "world_setting_conflict"
    PERSONALITY_INCONSISTENCY = "personality_inconsistency"
    LOCATION_CONFLICT = "location_conflict"
    ITEM_OWNERSHIP_CONFLICT = "item_ownership_conflict"
    KNOWLEDGE_INCONSISTENCY = "knowledge_inconsistency"


class ConflictSeverityEnum(str, Enum):
    """冲突严重程度枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ConflictDetectionRequest(BaseModel):
    """冲突检测请求模型"""
    story_id: str = Field(..., description="故事ID")
    current_chapter: int = Field(..., ge=1, description="当前章节号")
    conflict_types: Optional[List[ConflictTypeEnum]] = Field(
        None, 
        description="要检测的冲突类型列表，为空则检测所有类型"
    )
    min_severity: Optional[ConflictSeverityEnum] = Field(
        None,
        description="最低严重程度过滤"
    )
    max_results: Optional[int] = Field(
        None,
        ge=1,
        le=100,
        description="最大返回结果数量"
    )
    
    class Config:
        schema_extra = {
            "example": {
                "story_id": "story_001",
                "current_chapter": 5,
                "conflict_types": ["relationship_contradiction", "timeline_inconsistency"],
                "min_severity": "medium",
                "max_results": 20
            }
        }


class ConflictDetectionItem(BaseModel):
    """单个冲突检测结果模型"""
    id: str = Field(..., description="冲突ID")
    conflict_type: ConflictTypeEnum = Field(..., description="冲突类型")
    severity: ConflictSeverityEnum = Field(..., description="严重程度")
    title: str = Field(..., description="冲突标题")
    description: str = Field(..., description="冲突描述")
    
    # 冲突详情
    involved_entities: List[str] = Field(..., description="涉及的实体ID列表")
    involved_relationships: List[str] = Field(..., description="涉及的关系ID列表")
    conflicting_chapters: List[int] = Field(..., description="发生冲突的章节列表")
    
    # 冲突分析
    evidence: List[str] = Field(..., description="冲突证据列表")
    impact_analysis: str = Field(..., description="影响分析")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="检测置信度")
    
    # 修复建议
    repair_suggestions: List[str] = Field(..., description="修复建议列表")
    alternative_solutions: List[str] = Field(..., description="替代方案列表")
    
    # 元数据
    detected_at: datetime = Field(..., description="检测时间")
    last_updated: datetime = Field(..., description="最后更新时间")
    
    class Config:
        schema_extra = {
            "example": {
                "id": "conflict_001",
                "conflict_type": "relationship_contradiction",
                "severity": "high",
                "title": "张三与李四的关系转变过于突然",
                "description": "从敌对关系突然转变为友好关系，缺乏足够的铺垫",
                "involved_entities": ["char_001", "char_002"],
                "involved_relationships": ["rel_001", "rel_002"],
                "conflicting_chapters": [3, 5],
                "evidence": [
                    "第3章建立敌对关系",
                    "第5章转变为友好关系",
                    "章节间隔仅2章，转变过于突然"
                ],
                "impact_analysis": "关系转变缺乏合理铺垫，可能影响读者的代入感和故事的可信度",
                "confidence_score": 0.8,
                "repair_suggestions": [
                    "在关系转变前增加铺垫情节",
                    "添加促成关系转变的关键事件"
                ],
                "alternative_solutions": [
                    "将关系转变分解为多个阶段",
                    "增加过渡性的中性关系阶段"
                ],
                "detected_at": "2024-01-01T12:00:00",
                "last_updated": "2024-01-01T12:00:00"
            }
        }


class ConflictAnalysisResponse(BaseModel):
    """冲突分析结果模型"""
    story_consistency_score: float = Field(..., ge=0.0, le=1.0, description="故事一致性评分")
    logical_integrity_score: float = Field(..., ge=0.0, le=1.0, description="逻辑完整性评分")
    overall_health_score: float = Field(..., ge=0.0, le=1.0, description="整体健康度评分")
    conflict_trend: str = Field(..., description="冲突趋势 (improving/stable/worsening)")
    risk_areas: List[str] = Field(..., description="风险区域列表")
    conflicts_by_type: Dict[str, int] = Field(..., description="按类型统计的冲突数量")
    conflicts_by_severity: Dict[str, int] = Field(..., description="按严重程度统计的冲突数量")
    
    class Config:
        schema_extra = {
            "example": {
                "story_consistency_score": 0.75,
                "logical_integrity_score": 0.82,
                "overall_health_score": 0.785,
                "conflict_trend": "stable",
                "risk_areas": ["关系矛盾频发", "第5章冲突集中"],
                "conflicts_by_type": {
                    "relationship_contradiction": 3,
                    "timeline_inconsistency": 1,
                    "character_ability_conflict": 2
                },
                "conflicts_by_severity": {
                    "critical": 0,
                    "high": 2,
                    "medium": 3,
                    "low": 1
                }
            }
        }


class ConflictDetectionResponse(BaseModel):
    """冲突检测响应模型"""
    story_id: str = Field(..., description="故事ID")
    analysis_chapter: int = Field(..., description="分析章节")
    conflicts: List[ConflictDetectionItem] = Field(..., description="检测到的冲突列表")
    total_conflicts: int = Field(..., description="总冲突数量")
    filtered_conflicts: int = Field(..., description="过滤后的冲突数量")
    analysis_summary: ConflictAnalysisResponse = Field(..., description="分析摘要")
    
    class Config:
        schema_extra = {
            "example": {
                "story_id": "story_001",
                "analysis_chapter": 5,
                "conflicts": [],  # 实际包含ConflictDetectionItem列表
                "total_conflicts": 6,
                "filtered_conflicts": 4,
                "analysis_summary": {
                    "story_consistency_score": 0.75,
                    "logical_integrity_score": 0.82,
                    "overall_health_score": 0.785,
                    "conflict_trend": "stable",
                    "risk_areas": ["关系矛盾频发"],
                    "conflicts_by_type": {"relationship_contradiction": 3},
                    "conflicts_by_severity": {"high": 2, "medium": 2}
                }
            }
        }


class ConflictSummaryResponse(BaseModel):
    """冲突检测摘要响应模型"""
    story_id: str = Field(..., description="故事ID")
    analysis_chapter: int = Field(..., description="分析章节")
    overall_status: str = Field(..., description="整体状态 (healthy/caution/warning/critical)")
    total_conflicts: int = Field(..., description="总冲突数量")
    critical_conflicts: int = Field(..., description="严重冲突数量")
    high_conflicts: int = Field(..., description="高级别冲突数量")
    medium_conflicts: int = Field(..., description="中等冲突数量")
    low_conflicts: int = Field(..., description="低级别冲突数量")
    health_score: float = Field(..., ge=0.0, le=1.0, description="健康度评分")
    top_risk_areas: List[str] = Field(..., description="主要风险区域")
    conflict_trend: str = Field(..., description="冲突趋势")
    
    @validator('overall_status')
    def validate_status(cls, v):
        valid_statuses = ['healthy', 'caution', 'warning', 'critical']
        if v not in valid_statuses:
            raise ValueError(f'overall_status must be one of {valid_statuses}')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "story_id": "story_001",
                "analysis_chapter": 5,
                "overall_status": "caution",
                "total_conflicts": 6,
                "critical_conflicts": 0,
                "high_conflicts": 2,
                "medium_conflicts": 3,
                "low_conflicts": 1,
                "health_score": 0.785,
                "top_risk_areas": ["关系矛盾频发", "第5章冲突集中"],
                "conflict_trend": "stable"
            }
        }


class ConflictTypeInfo(BaseModel):
    """冲突类型信息模型"""
    type: ConflictTypeEnum = Field(..., description="冲突类型")
    name: str = Field(..., description="冲突类型名称")
    description: str = Field(..., description="冲突类型描述")
    severity_levels: List[ConflictSeverityEnum] = Field(..., description="支持的严重程度级别")
    
    class Config:
        schema_extra = {
            "example": {
                "type": "relationship_contradiction",
                "name": "关系矛盾",
                "description": "检测角色关系的前后矛盾，如敌人突然变朋友但缺乏铺垫",
                "severity_levels": ["low", "medium", "high", "critical"]
            }
        }


class ConflictResolutionSuggestion(BaseModel):
    """冲突解决建议模型"""
    conflict_id: str = Field(..., description="冲突ID")
    priority: int = Field(..., ge=1, le=5, description="优先级 (1-5, 5最高)")
    suggested_actions: List[str] = Field(..., description="建议的解决行动")
    estimated_effort: str = Field(..., description="预估工作量 (low/medium/high)")
    potential_impact: str = Field(..., description="潜在影响描述")
    
    class Config:
        schema_extra = {
            "example": {
                "conflict_id": "conflict_001",
                "priority": 4,
                "suggested_actions": [
                    "在第4章添加角色互动情节",
                    "通过对话展现角色态度变化"
                ],
                "estimated_effort": "medium",
                "potential_impact": "显著提升故事的逻辑连贯性和可信度"
            }
        }
