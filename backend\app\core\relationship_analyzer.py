"""
🕸️ [关系分析] 角色关系网络分析与可视化核心模块

基于图论算法分析角色间的复杂关系网络，提供关系强度分析、
社区检测、影响力评估和可视化数据生成。

主要功能：
1. 构建角色关系图谱
2. 计算关系强度和影响力指标
3. 检测角色社区和派系
4. 生成可视化数据
5. 分析关系演化趋势
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import List, Dict, Any, Optional, Tuple, Set
import uuid
import math
from collections import defaultdict, deque

from app.schemas.world_graph import WorldGraphResponse, EntityResponse, RelationshipResponse
from app.models.world_graph import EntityType, RelationshipStatus
from app.core.config import log_info, log_debug, log_error


class CentralityType(Enum):
    """中心性类型枚举"""
    DEGREE = "degree"                    # 度中心性
    BETWEENNESS = "betweenness"         # 介数中心性
    CLOSENESS = "closeness"             # 接近中心性
    EIGENVECTOR = "eigenvector"         # 特征向量中心性
    PAGERANK = "pagerank"               # PageRank中心性


class CommunityAlgorithm(Enum):
    """社区检测算法枚举"""
    LOUVAIN = "louvain"                 # Louvain算法
    LABEL_PROPAGATION = "label_propagation"  # 标签传播算法
    MODULARITY = "modularity"           # 模块度优化
    CONNECTED_COMPONENTS = "connected_components"  # 连通分量


@dataclass
class NodeMetrics:
    """节点指标数据类"""
    node_id: str
    name: str
    
    # 中心性指标
    degree_centrality: float          # 度中心性
    betweenness_centrality: float     # 介数中心性
    closeness_centrality: float       # 接近中心性
    eigenvector_centrality: float     # 特征向量中心性
    pagerank: float                   # PageRank值
    
    # 网络位置指标
    clustering_coefficient: float     # 聚类系数
    eccentricity: int                # 偏心率
    
    # 影响力指标
    influence_score: float           # 综合影响力评分
    importance_rank: int             # 重要性排名
    
    # 社区信息
    community_id: Optional[str]      # 所属社区ID
    community_role: str              # 社区角色（核心/边缘/桥梁）
    
    # 关系统计
    total_connections: int           # 总连接数
    strong_connections: int          # 强连接数
    weak_connections: int            # 弱连接数


@dataclass
class EdgeMetrics:
    """边指标数据类"""
    edge_id: str
    source_id: str
    target_id: str
    relationship_type: str
    
    # 强度指标
    strength: float                  # 关系强度
    weight: float                    # 边权重
    
    # 结构指标
    betweenness: float              # 边介数
    bridge_score: float             # 桥梁评分
    
    # 稳定性指标
    stability: float                # 关系稳定性
    evolution_trend: str            # 演化趋势（增强/减弱/稳定）
    
    # 元数据
    established_chapter: int        # 建立章节
    last_updated_chapter: int       # 最后更新章节
    duration: int                   # 持续章节数


@dataclass
class Community:
    """社区数据类"""
    id: str
    name: str
    members: List[str]              # 成员节点ID列表
    
    # 社区指标
    size: int                       # 社区大小
    density: float                  # 内部密度
    modularity: float               # 模块度
    cohesion: float                 # 凝聚力
    
    # 角色分析
    core_members: List[str]         # 核心成员
    peripheral_members: List[str]   # 边缘成员
    bridge_members: List[str]       # 桥梁成员
    
    # 特征描述
    dominant_relationship_types: List[str]  # 主导关系类型
    community_theme: str            # 社区主题
    
    # 演化信息
    formation_chapter: int          # 形成章节
    stability_score: float          # 稳定性评分


@dataclass
class NetworkVisualizationData:
    """网络可视化数据类"""
    nodes: List[Dict[str, Any]]     # 节点数据
    edges: List[Dict[str, Any]]     # 边数据
    communities: List[Dict[str, Any]]  # 社区数据
    
    # 布局信息
    layout_algorithm: str           # 布局算法
    node_positions: Dict[str, Tuple[float, float]]  # 节点位置
    
    # 视觉配置
    node_size_range: Tuple[float, float]    # 节点大小范围
    edge_width_range: Tuple[float, float]   # 边宽度范围
    color_scheme: Dict[str, str]            # 颜色方案
    
    # 交互配置
    zoom_level: float               # 缩放级别
    center_position: Tuple[float, float]    # 中心位置


@dataclass
class RelationshipAnalysisResult:
    """关系分析结果数据类"""
    story_id: str
    analysis_timestamp: datetime
    
    # 网络概览
    total_nodes: int                # 总节点数
    total_edges: int                # 总边数
    network_density: float          # 网络密度
    average_clustering: float       # 平均聚类系数
    diameter: int                   # 网络直径
    
    # 节点分析
    node_metrics: List[NodeMetrics] # 节点指标
    top_influential_characters: List[str]  # 最具影响力角色
    
    # 边分析
    edge_metrics: List[EdgeMetrics] # 边指标
    strongest_relationships: List[str]     # 最强关系
    bridge_relationships: List[str]        # 桥梁关系
    
    # 社区分析
    communities: List[Community]    # 检测到的社区
    community_count: int            # 社区数量
    modularity_score: float         # 整体模块度
    
    # 可视化数据
    visualization_data: NetworkVisualizationData
    
    # 分析洞察
    key_insights: List[str]         # 关键洞察
    relationship_patterns: List[str] # 关系模式
    evolution_trends: List[str]     # 演化趋势
    
    # 建议
    story_development_suggestions: List[str]  # 故事发展建议
    character_development_opportunities: List[str]  # 角色发展机会
    
    # 统计信息
    analysis_confidence: float      # 分析置信度
    computation_time: float         # 计算时间


class RelationshipNetworkAnalyzer:
    """
    🕸️ [关系分析] 角色关系网络分析器核心类
    
    使用图论算法分析角色关系网络，提供多维度的网络分析和可视化支持。
    """
    
    def __init__(self):
        """初始化关系网络分析器"""
        log_info("关系分析", "初始化关系网络分析器")
        
        # 分析配置
        self.centrality_algorithms = {
            CentralityType.DEGREE: self._calculate_degree_centrality,
            CentralityType.BETWEENNESS: self._calculate_betweenness_centrality,
            CentralityType.CLOSENESS: self._calculate_closeness_centrality,
            CentralityType.EIGENVECTOR: self._calculate_eigenvector_centrality,
            CentralityType.PAGERANK: self._calculate_pagerank
        }
        
        self.community_algorithms = {
            CommunityAlgorithm.CONNECTED_COMPONENTS: self._detect_connected_components,
            CommunityAlgorithm.LABEL_PROPAGATION: self._detect_communities_label_propagation,
            CommunityAlgorithm.MODULARITY: self._detect_communities_modularity
        }
        
        # 权重配置
        self.relationship_weights = {
            "朋友": 0.8,
            "敌人": 0.9,
            "恋人": 1.0,
            "家人": 0.9,
            "师父": 0.7,
            "徒弟": 0.7,
            "同事": 0.5,
            "竞争者": 0.6,
            "拥有": 0.3,
            "位于": 0.2
        }
        
        log_info("关系分析", "关系网络分析器初始化完成",
                中心性算法数=len(self.centrality_algorithms),
                社区检测算法数=len(self.community_algorithms))
    
    async def analyze_relationship_network(
        self,
        world_graph: WorldGraphResponse,
        analysis_options: Optional[Dict[str, Any]] = None
    ) -> RelationshipAnalysisResult:
        """
        🕸️ [关系分析] 分析角色关系网络
        
        Args:
            world_graph: 世界知识图谱
            analysis_options: 分析选项配置
            
        Returns:
            RelationshipAnalysisResult: 关系分析结果
        """
        start_time = datetime.now()
        log_info("关系分析", "开始分析角色关系网络",
                故事ID=world_graph.story_id,
                节点数=len(world_graph.entities),
                边数=len(world_graph.relationships))
        
        try:
            # 1. 构建网络图
            network_graph = await self._build_network_graph(world_graph)
            
            # 2. 计算网络基础指标
            network_metrics = await self._calculate_network_metrics(network_graph)
            
            # 3. 计算节点中心性指标
            node_metrics = await self._calculate_node_metrics(network_graph, world_graph)
            
            # 4. 计算边指标
            edge_metrics = await self._calculate_edge_metrics(network_graph, world_graph)
            
            # 5. 检测社区结构
            communities = await self._detect_communities(network_graph, world_graph)
            
            # 6. 生成可视化数据
            visualization_data = await self._generate_visualization_data(
                network_graph, node_metrics, edge_metrics, communities
            )
            
            # 7. 生成分析洞察
            insights = await self._generate_insights(
                network_metrics, node_metrics, edge_metrics, communities
            )
            
            # 8. 计算分析时间
            computation_time = (datetime.now() - start_time).total_seconds()
            
            # 9. 构建结果
            result = RelationshipAnalysisResult(
                story_id=world_graph.story_id,
                analysis_timestamp=datetime.now(),
                total_nodes=len(network_graph["nodes"]),
                total_edges=len(network_graph["edges"]),
                network_density=network_metrics["density"],
                average_clustering=network_metrics["average_clustering"],
                diameter=network_metrics["diameter"],
                node_metrics=node_metrics,
                top_influential_characters=self._get_top_influential(node_metrics, 5),
                edge_metrics=edge_metrics,
                strongest_relationships=self._get_strongest_relationships(edge_metrics, 5),
                bridge_relationships=self._get_bridge_relationships(edge_metrics, 3),
                communities=communities,
                community_count=len(communities),
                modularity_score=network_metrics.get("modularity", 0.0),
                visualization_data=visualization_data,
                key_insights=insights["key_insights"],
                relationship_patterns=insights["patterns"],
                evolution_trends=insights["trends"],
                story_development_suggestions=insights["story_suggestions"],
                character_development_opportunities=insights["character_opportunities"],
                analysis_confidence=self._calculate_analysis_confidence(world_graph, node_metrics),
                computation_time=computation_time
            )
            
            log_info("关系分析", "关系网络分析完成",
                    故事ID=world_graph.story_id,
                    社区数量=len(communities),
                    计算时间=f"{computation_time:.2f}秒",
                    分析置信度=result.analysis_confidence)
            
            return result
            
        except Exception as e:
            log_error("关系分析", "关系网络分析失败", error=str(e))
            raise

    async def _build_network_graph(
        self,
        world_graph: WorldGraphResponse
    ) -> Dict[str, Any]:
        """构建网络图数据结构"""
        log_debug("关系分析", "构建网络图数据结构")

        # 只包含角色节点
        character_entities = [
            entity for entity in world_graph.entities
            if entity.type == EntityType.CHARACTER
        ]

        # 构建节点
        nodes = {}
        for entity in character_entities:
            nodes[entity.id] = {
                "id": entity.id,
                "name": entity.name,
                "properties": entity.properties or {},
                "importance_score": entity.importance_score,
                "first_mentioned_chapter": entity.first_mentioned_chapter,
                "last_mentioned_chapter": entity.last_mentioned_chapter
            }

        # 构建边（只包含角色间的关系）
        edges = []
        adjacency_list = defaultdict(list)

        for rel in world_graph.relationships:
            if (rel.source_entity_id in nodes and
                rel.target_entity_id in nodes and
                rel.status == RelationshipStatus.ACTIVE):

                weight = self.relationship_weights.get(rel.relationship_type, 0.5)

                edge = {
                    "id": rel.id,
                    "source": rel.source_entity_id,
                    "target": rel.target_entity_id,
                    "type": rel.relationship_type,
                    "weight": weight,
                    "strength": rel.strength,
                    "established_chapter": rel.established_chapter,
                    "last_updated_chapter": rel.last_updated_chapter
                }

                edges.append(edge)
                adjacency_list[rel.source_entity_id].append(rel.target_entity_id)
                adjacency_list[rel.target_entity_id].append(rel.source_entity_id)  # 无向图

        return {
            "nodes": nodes,
            "edges": edges,
            "adjacency_list": adjacency_list
        }

    async def _calculate_network_metrics(
        self,
        network_graph: Dict[str, Any]
    ) -> Dict[str, float]:
        """计算网络基础指标"""
        log_debug("关系分析", "计算网络基础指标")

        nodes = network_graph["nodes"]
        edges = network_graph["edges"]
        adjacency_list = network_graph["adjacency_list"]

        n_nodes = len(nodes)
        n_edges = len(edges)

        # 网络密度
        max_edges = n_nodes * (n_nodes - 1) / 2
        density = n_edges / max_edges if max_edges > 0 else 0.0

        # 平均聚类系数
        clustering_coefficients = []
        for node_id in nodes:
            neighbors = set(adjacency_list[node_id])
            if len(neighbors) < 2:
                clustering_coefficients.append(0.0)
                continue

            # 计算邻居间的连接数
            neighbor_connections = 0
            for neighbor1 in neighbors:
                for neighbor2 in neighbors:
                    if neighbor1 != neighbor2 and neighbor2 in adjacency_list[neighbor1]:
                        neighbor_connections += 1

            neighbor_connections //= 2  # 无向图，避免重复计算
            max_neighbor_connections = len(neighbors) * (len(neighbors) - 1) / 2
            clustering = neighbor_connections / max_neighbor_connections if max_neighbor_connections > 0 else 0.0
            clustering_coefficients.append(clustering)

        average_clustering = sum(clustering_coefficients) / len(clustering_coefficients) if clustering_coefficients else 0.0

        # 网络直径（最短路径的最大值）
        diameter = self._calculate_network_diameter(adjacency_list)

        return {
            "density": density,
            "average_clustering": average_clustering,
            "diameter": diameter
        }

    def _calculate_network_diameter(self, adjacency_list: Dict[str, List[str]]) -> int:
        """计算网络直径"""
        if not adjacency_list:
            return 0

        max_distance = 0
        nodes = list(adjacency_list.keys())

        for start_node in nodes:
            distances = self._bfs_shortest_paths(adjacency_list, start_node)
            for distance in distances.values():
                if distance != float('inf') and distance > max_distance:
                    max_distance = distance

        return max_distance

    def _bfs_shortest_paths(self, adjacency_list: Dict[str, List[str]], start_node: str) -> Dict[str, int]:
        """使用BFS计算从起始节点到所有其他节点的最短路径"""
        distances = {node: float('inf') for node in adjacency_list}
        distances[start_node] = 0

        queue = deque([start_node])

        while queue:
            current = queue.popleft()
            current_distance = distances[current]

            for neighbor in adjacency_list[current]:
                if distances[neighbor] == float('inf'):
                    distances[neighbor] = current_distance + 1
                    queue.append(neighbor)

        return distances

    async def _calculate_node_metrics(
        self,
        network_graph: Dict[str, Any],
        world_graph: WorldGraphResponse
    ) -> List[NodeMetrics]:
        """计算节点指标"""
        log_debug("关系分析", "计算节点指标")

        nodes = network_graph["nodes"]
        adjacency_list = network_graph["adjacency_list"]

        node_metrics = []

        # 计算各种中心性指标
        degree_centrality = self._calculate_degree_centrality(adjacency_list)
        betweenness_centrality = self._calculate_betweenness_centrality(adjacency_list)
        closeness_centrality = self._calculate_closeness_centrality(adjacency_list)

        for node_id, node_data in nodes.items():
            # 计算聚类系数
            neighbors = set(adjacency_list[node_id])
            clustering_coefficient = 0.0
            if len(neighbors) >= 2:
                neighbor_connections = 0
                for neighbor1 in neighbors:
                    for neighbor2 in neighbors:
                        if neighbor1 != neighbor2 and neighbor2 in adjacency_list[neighbor1]:
                            neighbor_connections += 1
                neighbor_connections //= 2
                max_connections = len(neighbors) * (len(neighbors) - 1) / 2
                clustering_coefficient = neighbor_connections / max_connections if max_connections > 0 else 0.0

            # 计算影响力评分
            influence_score = (
                degree_centrality.get(node_id, 0.0) * 0.3 +
                betweenness_centrality.get(node_id, 0.0) * 0.3 +
                closeness_centrality.get(node_id, 0.0) * 0.2 +
                node_data["importance_score"] * 0.2
            )

            # 统计连接类型
            strong_connections = 0
            weak_connections = 0
            for edge in network_graph["edges"]:
                if edge["source"] == node_id or edge["target"] == node_id:
                    if edge["weight"] >= 0.7:
                        strong_connections += 1
                    else:
                        weak_connections += 1

            metrics = NodeMetrics(
                node_id=node_id,
                name=node_data["name"],
                degree_centrality=degree_centrality.get(node_id, 0.0),
                betweenness_centrality=betweenness_centrality.get(node_id, 0.0),
                closeness_centrality=closeness_centrality.get(node_id, 0.0),
                eigenvector_centrality=0.0,  # 简化实现
                pagerank=0.0,  # 简化实现
                clustering_coefficient=clustering_coefficient,
                eccentricity=0,  # 简化实现
                influence_score=influence_score,
                importance_rank=0,  # 稍后排序时设置
                community_id=None,  # 稍后社区检测时设置
                community_role="unknown",
                total_connections=len(adjacency_list[node_id]),
                strong_connections=strong_connections,
                weak_connections=weak_connections
            )

            node_metrics.append(metrics)

        # 设置重要性排名
        node_metrics.sort(key=lambda x: x.influence_score, reverse=True)
        for i, metrics in enumerate(node_metrics):
            metrics.importance_rank = i + 1

        return node_metrics


    def _calculate_degree_centrality(self, adjacency_list: Dict[str, List[str]]) -> Dict[str, float]:
        """计算度中心性"""
        if not adjacency_list:
            return {}

        max_degree = max(len(neighbors) for neighbors in adjacency_list.values()) if adjacency_list else 1
        return {
            node_id: len(neighbors) / max_degree if max_degree > 0 else 0.0
            for node_id, neighbors in adjacency_list.items()
        }

    def _calculate_betweenness_centrality(self, adjacency_list: Dict[str, List[str]]) -> Dict[str, float]:
        """计算介数中心性（简化版本）"""
        if not adjacency_list:
            return {}

        betweenness = {node: 0.0 for node in adjacency_list}
        nodes = list(adjacency_list.keys())

        # 简化实现：只计算部分路径
        for start in nodes[:min(10, len(nodes))]:  # 限制计算量
            for end in nodes:
                if start != end:
                    paths = self._find_shortest_paths(adjacency_list, start, end)
                    if len(paths) > 1:
                        for path in paths:
                            for node in path[1:-1]:  # 排除起点和终点
                                betweenness[node] += 1.0 / len(paths)

        # 归一化
        max_betweenness = max(betweenness.values()) if betweenness.values() else 1
        if max_betweenness > 0:
            betweenness = {node: score / max_betweenness for node, score in betweenness.items()}

        return betweenness

    def _calculate_closeness_centrality(self, adjacency_list: Dict[str, List[str]]) -> Dict[str, float]:
        """计算接近中心性"""
        if not adjacency_list:
            return {}

        closeness = {}

        for node in adjacency_list:
            distances = self._bfs_shortest_paths(adjacency_list, node)
            finite_distances = [d for d in distances.values() if d != float('inf')]

            if finite_distances:
                avg_distance = sum(finite_distances) / len(finite_distances)
                closeness[node] = 1.0 / avg_distance if avg_distance > 0 else 0.0
            else:
                closeness[node] = 0.0

        # 归一化
        max_closeness = max(closeness.values()) if closeness.values() else 1
        if max_closeness > 0:
            closeness = {node: score / max_closeness for node, score in closeness.items()}

        return closeness

    def _calculate_eigenvector_centrality(self, adjacency_list: Dict[str, List[str]]) -> Dict[str, float]:
        """计算特征向量中心性（简化实现）"""
        if not adjacency_list:
            return {}

        # 简化实现：返回度中心性作为近似
        return self._calculate_degree_centrality(adjacency_list)

    def _calculate_pagerank(self, adjacency_list: Dict[str, List[str]]) -> Dict[str, float]:
        """计算PageRank中心性（简化实现）"""
        if not adjacency_list:
            return {}

        # 简化实现：返回度中心性作为近似
        return self._calculate_degree_centrality(adjacency_list)

    def _detect_connected_components(self, adjacency_list: Dict[str, List[str]]) -> List[List[str]]:
        """检测连通分量"""
        if not adjacency_list:
            return []

        visited = set()
        components = []

        for node in adjacency_list:
            if node not in visited:
                component = []
                queue = deque([node])
                visited.add(node)

                while queue:
                    current = queue.popleft()
                    component.append(current)

                    for neighbor in adjacency_list[current]:
                        if neighbor not in visited:
                            visited.add(neighbor)
                            queue.append(neighbor)

                if len(component) >= 2:
                    components.append(component)

        return components

    def _detect_communities_label_propagation(self, adjacency_list: Dict[str, List[str]]) -> List[List[str]]:
        """标签传播社区检测（简化实现）"""
        # 简化实现：使用连通分量
        return self._detect_connected_components(adjacency_list)

    def _detect_communities_modularity(self, adjacency_list: Dict[str, List[str]]) -> List[List[str]]:
        """模块度优化社区检测（简化实现）"""
        # 简化实现：使用连通分量
        return self._detect_connected_components(adjacency_list)

    def _find_shortest_paths(self, adjacency_list: Dict[str, List[str]], start: str, end: str) -> List[List[str]]:
        """查找最短路径（简化版本，只返回一条路径）"""
        if start == end:
            return [[start]]

        queue = deque([(start, [start])])
        visited = {start}

        while queue:
            current, path = queue.popleft()

            for neighbor in adjacency_list[current]:
                if neighbor == end:
                    return [path + [neighbor]]

                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append((neighbor, path + [neighbor]))

        return []  # 无路径

    async def _calculate_edge_metrics(
        self,
        network_graph: Dict[str, Any],
        world_graph: WorldGraphResponse
    ) -> List[EdgeMetrics]:
        """计算边指标"""
        log_debug("关系分析", "计算边指标")

        edge_metrics = []

        for edge in network_graph["edges"]:
            # 计算持续时间
            duration = edge["last_updated_chapter"] - edge["established_chapter"] + 1

            # 简化的桥梁评分计算
            bridge_score = edge["weight"] * 0.5  # 简化实现

            metrics = EdgeMetrics(
                edge_id=edge["id"],
                source_id=edge["source"],
                target_id=edge["target"],
                relationship_type=edge["type"],
                strength=edge["strength"],
                weight=edge["weight"],
                betweenness=0.0,  # 简化实现
                bridge_score=bridge_score,
                stability=min(1.0, duration / 5.0),  # 基于持续时间的稳定性
                evolution_trend="稳定",  # 简化实现
                established_chapter=edge["established_chapter"],
                last_updated_chapter=edge["last_updated_chapter"],
                duration=duration
            )

            edge_metrics.append(metrics)

        return edge_metrics

    async def _detect_communities(
        self,
        network_graph: Dict[str, Any],
        world_graph: WorldGraphResponse
    ) -> List[Community]:
        """检测社区结构"""
        log_debug("关系分析", "检测社区结构")

        # 使用连通分量作为简化的社区检测
        adjacency_list = network_graph["adjacency_list"]
        communities = []
        visited = set()
        community_id = 0

        for node_id in network_graph["nodes"]:
            if node_id not in visited:
                # BFS找到连通分量
                component = []
                queue = deque([node_id])
                visited.add(node_id)

                while queue:
                    current = queue.popleft()
                    component.append(current)

                    for neighbor in adjacency_list[current]:
                        if neighbor not in visited:
                            visited.add(neighbor)
                            queue.append(neighbor)

                if len(component) >= 2:  # 只考虑有多个成员的社区
                    community = Community(
                        id=f"community-{community_id}",
                        name=f"社区 {community_id + 1}",
                        members=component,
                        size=len(component),
                        density=self._calculate_community_density(component, network_graph),
                        modularity=0.0,  # 简化实现
                        cohesion=0.8,    # 简化实现
                        core_members=component[:max(1, len(component)//2)],
                        peripheral_members=component[len(component)//2:],
                        bridge_members=[],
                        dominant_relationship_types=["朋友"],  # 简化实现
                        community_theme="角色群体",
                        formation_chapter=1,  # 简化实现
                        stability_score=0.8   # 简化实现
                    )

                    communities.append(community)
                    community_id += 1

        return communities

    def _calculate_community_density(self, members: List[str], network_graph: Dict[str, Any]) -> float:
        """计算社区内部密度"""
        if len(members) < 2:
            return 0.0

        internal_edges = 0
        for edge in network_graph["edges"]:
            if edge["source"] in members and edge["target"] in members:
                internal_edges += 1

        max_edges = len(members) * (len(members) - 1) / 2
        return internal_edges / max_edges if max_edges > 0 else 0.0


    async def _generate_visualization_data(
        self,
        network_graph: Dict[str, Any],
        node_metrics: List[NodeMetrics],
        edge_metrics: List[EdgeMetrics],
        communities: List[Community]
    ) -> NetworkVisualizationData:
        """生成可视化数据"""
        log_debug("关系分析", "生成可视化数据")

        # 构建节点数据
        nodes_data = []
        node_positions = {}

        for metrics in node_metrics:
            node_data = network_graph["nodes"][metrics.node_id]

            # 简化的布局算法：基于影响力的圆形布局
            angle = (metrics.importance_rank - 1) * 2 * math.pi / len(node_metrics)
            radius = 100 + metrics.influence_score * 50
            x = radius * math.cos(angle)
            y = radius * math.sin(angle)

            node_positions[metrics.node_id] = (x, y)

            nodes_data.append({
                "id": metrics.node_id,
                "name": metrics.name,
                "size": 10 + metrics.influence_score * 20,
                "color": self._get_node_color(metrics),
                "x": x,
                "y": y,
                "metrics": {
                    "degree_centrality": metrics.degree_centrality,
                    "betweenness_centrality": metrics.betweenness_centrality,
                    "influence_score": metrics.influence_score,
                    "total_connections": metrics.total_connections
                }
            })

        # 构建边数据
        edges_data = []
        for metrics in edge_metrics:
            edges_data.append({
                "id": metrics.edge_id,
                "source": metrics.source_id,
                "target": metrics.target_id,
                "type": metrics.relationship_type,
                "width": 1 + metrics.strength * 5,
                "color": self._get_edge_color(metrics.relationship_type),
                "strength": metrics.strength,
                "weight": metrics.weight
            })

        # 构建社区数据
        communities_data = []
        for community in communities:
            communities_data.append({
                "id": community.id,
                "name": community.name,
                "members": community.members,
                "size": community.size,
                "color": self._get_community_color(community.id)
            })

        return NetworkVisualizationData(
            nodes=nodes_data,
            edges=edges_data,
            communities=communities_data,
            layout_algorithm="force_directed",
            node_positions=node_positions,
            node_size_range=(10, 30),
            edge_width_range=(1, 6),
            color_scheme={
                "primary": "#3498db",
                "secondary": "#e74c3c",
                "accent": "#f39c12"
            },
            zoom_level=1.0,
            center_position=(0, 0)
        )

    def _get_node_color(self, metrics: NodeMetrics) -> str:
        """根据节点指标获取颜色"""
        if metrics.influence_score > 0.8:
            return "#e74c3c"  # 红色 - 高影响力
        elif metrics.influence_score > 0.6:
            return "#f39c12"  # 橙色 - 中等影响力
        else:
            return "#3498db"  # 蓝色 - 低影响力

    def _get_edge_color(self, relationship_type: str) -> str:
        """根据关系类型获取边颜色"""
        color_map = {
            "朋友": "#2ecc71",    # 绿色
            "敌人": "#e74c3c",    # 红色
            "恋人": "#e91e63",    # 粉色
            "家人": "#9b59b6",    # 紫色
            "师父": "#34495e",    # 深灰色
            "徒弟": "#95a5a6",    # 浅灰色
            "同事": "#3498db",    # 蓝色
            "竞争者": "#f39c12"   # 橙色
        }
        return color_map.get(relationship_type, "#bdc3c7")  # 默认灰色

    def _get_community_color(self, community_id: str) -> str:
        """根据社区ID获取颜色"""
        colors = ["#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6", "#1abc9c"]
        hash_value = hash(community_id) % len(colors)
        return colors[hash_value]

    async def _generate_insights(
        self,
        network_metrics: Dict[str, float],
        node_metrics: List[NodeMetrics],
        edge_metrics: List[EdgeMetrics],
        communities: List[Community]
    ) -> Dict[str, List[str]]:
        """生成分析洞察"""
        log_debug("关系分析", "生成分析洞察")

        key_insights = []
        patterns = []
        trends = []
        story_suggestions = []
        character_opportunities = []

        # 网络结构洞察
        if network_metrics["density"] > 0.7:
            key_insights.append("角色关系网络密度很高，角色间联系紧密")
        elif network_metrics["density"] < 0.3:
            key_insights.append("角色关系网络较为稀疏，存在孤立的角色群体")

        # 影响力分析
        if node_metrics:
            top_character = node_metrics[0]
            key_insights.append(f"{top_character.name}是网络中最具影响力的角色")

            if top_character.influence_score > 0.8:
                story_suggestions.append(f"考虑围绕{top_character.name}展开更多情节线")

        # 社区结构分析
        if len(communities) > 1:
            patterns.append(f"检测到{len(communities)}个角色社区，存在明显的派系分化")
            story_suggestions.append("可以利用不同社区间的冲突来推动情节发展")

        # 关系强度分析
        strong_relationships = [e for e in edge_metrics if e.strength > 0.8]
        if strong_relationships:
            patterns.append(f"存在{len(strong_relationships)}个强关系，这些关系对故事发展很重要")

        # 角色发展机会
        for metrics in node_metrics:
            if metrics.total_connections < 2:
                character_opportunities.append(f"{metrics.name}的关系网络较为单薄，可以增加更多互动")
            elif metrics.influence_score < 0.3 and metrics.importance_rank <= 5:
                character_opportunities.append(f"{metrics.name}具有发展潜力，但影响力有待提升")

        return {
            "key_insights": key_insights,
            "patterns": patterns,
            "trends": trends,
            "story_suggestions": story_suggestions,
            "character_opportunities": character_opportunities
        }

    def _get_top_influential(self, node_metrics: List[NodeMetrics], count: int) -> List[str]:
        """获取最具影响力的角色"""
        sorted_metrics = sorted(node_metrics, key=lambda x: x.influence_score, reverse=True)
        return [metrics.name for metrics in sorted_metrics[:count]]

    def _get_strongest_relationships(self, edge_metrics: List[EdgeMetrics], count: int) -> List[str]:
        """获取最强的关系"""
        sorted_edges = sorted(edge_metrics, key=lambda x: x.strength, reverse=True)
        return [f"{edge.source_id}-{edge.target_id}({edge.relationship_type})" for edge in sorted_edges[:count]]

    def _get_bridge_relationships(self, edge_metrics: List[EdgeMetrics], count: int) -> List[str]:
        """获取桥梁关系"""
        sorted_edges = sorted(edge_metrics, key=lambda x: x.bridge_score, reverse=True)
        return [f"{edge.source_id}-{edge.target_id}({edge.relationship_type})" for edge in sorted_edges[:count]]

    def _calculate_analysis_confidence(
        self,
        world_graph: WorldGraphResponse,
        node_metrics: List[NodeMetrics]
    ) -> float:
        """计算分析置信度"""
        confidence = 0.6  # 基础置信度

        # 基于数据完整性调整
        character_count = len([e for e in world_graph.entities if e.type == EntityType.CHARACTER])
        if character_count >= 3:
            confidence += 0.2
        if len(world_graph.relationships) >= 2:
            confidence += 0.1

        # 基于网络连通性调整
        if node_metrics and node_metrics[0].total_connections > 0:
            confidence += 0.1

        return min(1.0, confidence)


def create_relationship_analyzer() -> RelationshipNetworkAnalyzer:
    """
    🕸️ [关系分析] 创建关系网络分析器实例
    
    Returns:
        RelationshipNetworkAnalyzer: 关系网络分析器实例
    """
    log_info("关系分析", "创建关系网络分析器实例")
    
    analyzer = RelationshipNetworkAnalyzer()
    
    log_info("关系分析", "关系网络分析器创建完成",
            支持的中心性算法=[ct.value for ct in CentralityType],
            支持的社区检测算法=[ca.value for ca in CommunityAlgorithm])
    
    return analyzer
