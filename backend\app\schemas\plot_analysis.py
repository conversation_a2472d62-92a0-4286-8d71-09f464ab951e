"""
🎭 [剧情分析] 剧情分析和推荐相关的Pydantic模型
定义API请求和响应的数据结构
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class PlotRecommendationRequest(BaseModel):
    """剧情推荐请求模型"""
    story_id: str = Field(..., description="故事ID")
    current_chapter: int = Field(..., description="当前章节号", ge=1)
    max_recommendations: int = Field(5, description="最大推荐数量", ge=1, le=20)
    
    class Config:
        json_schema_extra = {
            "example": {
                "story_id": "story-123",
                "current_chapter": 5,
                "max_recommendations": 5
            }
        }


class StoryAnalysisRequest(BaseModel):
    """故事状态分析请求模型"""
    story_id: str = Field(..., description="故事ID")
    current_chapter: int = Field(..., description="当前章节号", ge=1)
    
    class Config:
        json_schema_extra = {
            "example": {
                "story_id": "story-123",
                "current_chapter": 5
            }
        }


class PlotRecommendationResponse(BaseModel):
    """剧情推荐响应模型"""
    id: str = Field(..., description="推荐ID")
    title: str = Field(..., description="推荐标题")
    description: str = Field(..., description="推荐描述")
    pattern_type: str = Field(..., description="剧情模式类型")
    priority: str = Field(..., description="推荐优先级")
    
    # 评分系统
    feasibility_score: float = Field(..., description="可行性评分", ge=0.0, le=1.0)
    dramatic_impact: float = Field(..., description="戏剧效果评分", ge=0.0, le=1.0)
    character_development: float = Field(..., description="角色发展评分", ge=0.0, le=1.0)
    plot_advancement: float = Field(..., description="情节推进评分", ge=0.0, le=1.0)
    overall_score: float = Field(..., description="综合评分", ge=0.0, le=1.0)
    
    # 详细信息
    involved_entities: List[str] = Field(default_factory=list, description="涉及的实体ID列表")
    required_relationships: List[str] = Field(default_factory=list, description="需要的关系ID列表")
    potential_conflicts: List[str] = Field(default_factory=list, description="潜在冲突列表")
    execution_steps: List[str] = Field(default_factory=list, description="执行步骤列表")
    expected_outcomes: List[str] = Field(default_factory=list, description="预期结果列表")
    
    # 元数据
    reasoning: str = Field(..., description="推荐理由")
    created_at: datetime = Field(..., description="创建时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "story-123-conflict_escalation-5",
                "title": "张三与李四的冲突升级",
                "description": "将敌人关系进一步激化，通过具体事件或第三方介入使矛盾达到新的高度",
                "pattern_type": "conflict_escalation",
                "priority": "high",
                "feasibility_score": 0.85,
                "dramatic_impact": 0.9,
                "character_development": 0.7,
                "plot_advancement": 0.8,
                "overall_score": 0.81,
                "involved_entities": ["char-001", "char-002"],
                "required_relationships": ["rel-001"],
                "potential_conflicts": ["关系彻底破裂", "第三方被迫选边站队"],
                "execution_steps": [
                    "设计具体的冲突触发事件",
                    "展现双方的激烈对抗",
                    "考虑第三方角色的反应",
                    "为后续发展埋下伏笔"
                ],
                "expected_outcomes": ["矛盾公开化", "故事张力显著提升", "为后续情节发展创造机会"],
                "reasoning": "基于敌人关系的高强度(0.85)，适合进行冲突升级",
                "created_at": "2024-01-15T10:30:00Z"
            }
        }


class EntitySummary(BaseModel):
    """实体摘要模型"""
    id: str = Field(..., description="实体ID")
    name: str = Field(..., description="实体名称")
    importance_score: float = Field(..., description="重要性评分", ge=0.0, le=1.0)


class StoryAnalysisResponse(BaseModel):
    """故事状态分析响应模型"""
    story_id: str = Field(..., description="故事ID")
    current_chapter: int = Field(..., description="当前章节号")
    
    # 实体分析
    main_characters: List[EntitySummary] = Field(default_factory=list, description="主要角色列表")
    supporting_characters: List[EntitySummary] = Field(default_factory=list, description="配角列表")
    key_items: List[EntitySummary] = Field(default_factory=list, description="关键物品列表")
    important_scenes: List[EntitySummary] = Field(default_factory=list, description="重要场景列表")
    
    # 关系分析
    active_relationships_count: int = Field(..., description="活跃关系数量", ge=0)
    conflict_relationships_count: int = Field(..., description="冲突关系数量", ge=0)
    alliance_relationships_count: int = Field(..., description="联盟关系数量", ge=0)
    romantic_relationships_count: int = Field(..., description="浪漫关系数量", ge=0)
    
    # 状态评估
    tension_level: float = Field(..., description="当前张力水平", ge=0.0, le=1.0)
    character_development_stage: Dict[str, float] = Field(
        default_factory=dict, 
        description="角色发展阶段映射"
    )
    plot_complexity: float = Field(..., description="情节复杂度", ge=0.0, le=1.0)
    pacing_score: float = Field(..., description="节奏评分", ge=0.0, le=1.0)
    
    # 潜在机会
    underutilized_characters: List[str] = Field(
        default_factory=list, 
        description="未充分利用的角色ID列表"
    )
    dormant_relationships: List[str] = Field(
        default_factory=list, 
        description="休眠关系ID列表"
    )
    unresolved_conflicts: List[str] = Field(
        default_factory=list, 
        description="未解决冲突ID列表"
    )
    
    # 元数据
    created_at: datetime = Field(..., description="分析创建时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "story_id": "story-123",
                "current_chapter": 5,
                "main_characters": [
                    {
                        "id": "char-001",
                        "name": "张三",
                        "importance_score": 0.9
                    },
                    {
                        "id": "char-002",
                        "name": "李四",
                        "importance_score": 0.8
                    }
                ],
                "supporting_characters": [
                    {
                        "id": "char-003",
                        "name": "王五",
                        "importance_score": 0.5
                    }
                ],
                "key_items": [
                    {
                        "id": "item-001",
                        "name": "神秘宝剑",
                        "importance_score": 0.7
                    }
                ],
                "important_scenes": [
                    {
                        "id": "scene-001",
                        "name": "决斗场",
                        "importance_score": 0.6
                    }
                ],
                "active_relationships_count": 8,
                "conflict_relationships_count": 3,
                "alliance_relationships_count": 2,
                "romantic_relationships_count": 1,
                "tension_level": 0.75,
                "character_development_stage": {
                    "char-001": 0.6,
                    "char-002": 0.4
                },
                "plot_complexity": 0.65,
                "pacing_score": 0.8,
                "underutilized_characters": ["char-003"],
                "dormant_relationships": ["rel-005"],
                "unresolved_conflicts": ["rel-001", "rel-003"],
                "created_at": "2024-01-15T10:30:00Z"
            }
        }


class PlotPatternInfo(BaseModel):
    """剧情模式信息模型"""
    pattern_type: str = Field(..., description="模式类型")
    name: str = Field(..., description="模式名称")
    description: str = Field(..., description="模式描述")
    dramatic_tension: float = Field(..., description="戏剧张力评分", ge=0.0, le=1.0)
    complexity: float = Field(..., description="复杂度评分", ge=0.0, le=1.0)
    reader_appeal: float = Field(..., description="读者吸引力评分", ge=0.0, le=1.0)
    prerequisites: List[str] = Field(default_factory=list, description="前置条件列表")
    outcomes: List[str] = Field(default_factory=list, description="可能结果列表")
    
    class Config:
        json_schema_extra = {
            "example": {
                "pattern_type": "conflict_escalation",
                "name": "冲突升级",
                "description": "现有冲突进一步激化，矛盾达到新的高度",
                "dramatic_tension": 0.9,
                "complexity": 0.6,
                "reader_appeal": 0.8,
                "prerequisites": ["存在活跃的敌对关系", "角色间有未解决的分歧"],
                "outcomes": ["关系彻底破裂", "公开对抗", "第三方介入"]
            }
        }


class PlotPatternsResponse(BaseModel):
    """剧情模式列表响应模型"""
    patterns: List[PlotPatternInfo] = Field(default_factory=list, description="剧情模式列表")
    total_count: int = Field(..., description="模式总数", ge=0)
    
    class Config:
        json_schema_extra = {
            "example": {
                "patterns": [
                    {
                        "pattern_type": "conflict_escalation",
                        "name": "冲突升级",
                        "description": "现有冲突进一步激化，矛盾达到新的高度",
                        "dramatic_tension": 0.9,
                        "complexity": 0.6,
                        "reader_appeal": 0.8,
                        "prerequisites": ["存在活跃的敌对关系", "角色间有未解决的分歧"],
                        "outcomes": ["关系彻底破裂", "公开对抗", "第三方介入"]
                    }
                ],
                "total_count": 10
            }
        }
