"""
🎯 [情节规划] 情节发展路径规划器单元测试

测试情节路径生成、评估和建议功能的正确性和可靠性。
"""

import pytest
import asyncio
from datetime import datetime
from typing import List

from app.core.plot_path_planner import (
    create_plot_path_planner,
    PlotPathPlanner,
    PathType,
    PathPriority,
    PlotEvent,
    PlotPath,
    PathPlanningResult
)
from app.schemas.world_graph import WorldGraphResponse, EntityResponse, RelationshipResponse
from app.models.world_graph import EntityType, RelationshipStatus, RelationshipTypes


class TestPlotPathPlanner:
    """情节路径规划器测试类"""
    
    @pytest.fixture
    def planner(self) -> PlotPathPlanner:
        """创建情节路径规划器实例"""
        return create_plot_path_planner()
    
    @pytest.fixture
    def sample_world_graph(self) -> WorldGraphResponse:
        """创建示例世界知识图谱"""
        entities = [
            EntityResponse(
                id="char-001",
                story_id="test-story",
                name="张三",
                type=EntityType.CHARACTER,
                description="主角，年轻的剑客",
                properties={"age": 25, "weapon": "长剑", "personality": "善良勇敢"},
                importance_score=0.9,
                first_mentioned_chapter=1,
                last_mentioned_chapter=5,
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            EntityResponse(
                id="char-002",
                story_id="test-story",
                name="李四",
                type=EntityType.CHARACTER,
                description="反派，邪恶的法师",
                properties={"age": 40, "magic": "黑魔法", "personality": "邪恶残忍"},
                importance_score=0.8,
                first_mentioned_chapter=2,
                last_mentioned_chapter=5,
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            EntityResponse(
                id="item-001",
                story_id="test-story",
                name="神秘宝剑",
                type=EntityType.ITEM,
                description="拥有神秘力量的古老宝剑",
                properties={"power": "光明之力", "rarity": "传说级"},
                importance_score=0.7,
                first_mentioned_chapter=3,
                last_mentioned_chapter=5,
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
        ]
        
        relationships = [
            RelationshipResponse(
                id="rel-001",
                source_entity_id="char-001",
                target_entity_id="char-002",
                relationship_type=RelationshipTypes.ENEMY,
                strength=0.9,
                status=RelationshipStatus.ACTIVE,
                description="张三与李四是宿敌",
                established_chapter=2,
                last_updated_chapter=5,
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            RelationshipResponse(
                id="rel-002",
                source_entity_id="char-001",
                target_entity_id="item-001",
                relationship_type=RelationshipTypes.OWNS,
                strength=0.8,
                status=RelationshipStatus.ACTIVE,
                description="张三拥有神秘宝剑",
                established_chapter=3,
                last_updated_chapter=5,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
        ]
        
        return WorldGraphResponse(
            story_id="test-story",
            story_title="测试故事",
            entities=entities,
            relationships=relationships,
            entity_count=len(entities),
            relationship_count=len(relationships),
            entity_stats={"CHARACTER": 2, "ITEM": 1},
            relationship_stats={"敌人": 1, "拥有": 1}
        )
    
    def test_planner_initialization(self, planner: PlotPathPlanner):
        """测试规划器初始化"""
        assert planner is not None
        assert len(planner.path_generation_rules) == 6  # 6种路径类型
        assert len(planner.evaluation_weights) == 4     # 4个评估维度
        
        # 检查路径生成规则
        assert PathType.CONFLICT_ESCALATION in planner.path_generation_rules
        assert PathType.CHARACTER_GROWTH in planner.path_generation_rules
        assert PathType.MYSTERY_REVELATION in planner.path_generation_rules
        
        # 检查评估权重
        assert "feasibility" in planner.evaluation_weights
        assert "appeal" in planner.evaluation_weights
        assert "coherence" in planner.evaluation_weights
        assert "originality" in planner.evaluation_weights
    
    @pytest.mark.asyncio
    async def test_analyze_current_state(
        self, 
        planner: PlotPathPlanner, 
        sample_world_graph: WorldGraphResponse
    ):
        """测试当前状态分析"""
        current_state = await planner._analyze_current_state(sample_world_graph, 5)
        
        assert "summary" in current_state
        assert "conflicts" in current_state
        assert "unresolved_threads" in current_state
        assert "chapter" in current_state
        
        assert current_state["chapter"] == 5
        assert len(current_state["conflicts"]) >= 1  # 至少有一个敌人关系
        assert "张三与李四的敌人关系" in current_state["conflicts"][0]
    
    @pytest.mark.asyncio
    async def test_analyze_character_motivations(
        self, 
        planner: PlotPathPlanner, 
        sample_world_graph: WorldGraphResponse
    ):
        """测试角色动机分析"""
        motivations = await planner._analyze_character_motivations(sample_world_graph)
        
        assert "char-001" in motivations  # 张三
        assert "char-002" in motivations  # 李四
        
        # 检查张三的动机
        zhang_motivations = motivations["char-001"]
        assert len(zhang_motivations) > 0
        assert any("保护" in m or "击败" in m for m in zhang_motivations)
        
        # 检查李四的动机
        li_motivations = motivations["char-002"]
        assert len(li_motivations) > 0
    
    @pytest.mark.asyncio
    async def test_generate_plot_paths(
        self, 
        planner: PlotPathPlanner, 
        sample_world_graph: WorldGraphResponse
    ):
        """测试情节路径生成"""
        result = await planner.generate_plot_paths(
            world_graph=sample_world_graph,
            current_chapter=5,
            target_paths=3
        )
        
        # 检查结果结构
        assert isinstance(result, PathPlanningResult)
        assert result.story_id == "test-story"
        assert result.current_chapter == 5
        assert result.analysis_timestamp is not None
        
        # 检查状态分析
        assert result.current_state_summary
        assert isinstance(result.active_conflicts, list)
        assert isinstance(result.character_motivations, dict)
        assert isinstance(result.unresolved_threads, list)
        
        # 检查生成的路径
        assert len(result.recommended_paths) <= 3
        assert len(result.alternative_paths) >= 0
        
        # 检查建议
        assert isinstance(result.next_chapter_suggestions, list)
        assert result.long_term_strategy
        assert result.pacing_recommendations
        
        # 检查统计信息
        assert result.total_paths_generated > 0
        assert 0.0 <= result.analysis_confidence <= 1.0
    
    @pytest.mark.asyncio
    async def test_generate_focused_paths(
        self, 
        planner: PlotPathPlanner, 
        sample_world_graph: WorldGraphResponse
    ):
        """测试重点类型路径生成"""
        focus_types = [PathType.CONFLICT_ESCALATION, PathType.CHARACTER_GROWTH]
        
        result = await planner.generate_plot_paths(
            world_graph=sample_world_graph,
            current_chapter=3,
            target_paths=2,
            focus_types=focus_types
        )
        
        # 检查生成的路径类型
        for path in result.recommended_paths:
            assert path.path_type in focus_types
    
    def test_path_evaluation_metrics(self, planner: PlotPathPlanner):
        """测试路径评估指标计算"""
        # 创建测试路径
        test_events = [
            PlotEvent(
                id="event-001",
                title="测试事件",
                description="测试事件描述",
                event_type="confrontation",
                involved_entities=["char-001", "char-002"],
                involved_relationships=[],
                prerequisites=[],
                consequences=[],
                emotional_impact=7.0,
                tension_level=8.0,
                complexity=5.0,
                estimated_chapters=2,
                created_at=datetime.now()
            )
        ]
        
        test_path = PlotPath(
            id="path-001",
            title="测试路径",
            description="测试路径描述",
            path_type=PathType.CONFLICT_ESCALATION,
            priority=PathPriority.HIGH,
            events=test_events,
            feasibility_score=0.0,
            appeal_score=0.0,
            coherence_score=0.0,
            originality_score=0.0,
            overall_score=0.0,
            risk_factors=[],
            potential_conflicts=[],
            estimated_total_chapters=2,
            target_audience=["通用读者"],
            genre_tags=["conflict_escalation"],
            created_at=datetime.now(),
            last_updated=datetime.now()
        )
        
        # 测试各项评分计算
        appeal_score = planner._calculate_appeal(test_path)
        assert 0.0 <= appeal_score <= 10.0
        
        coherence_score = planner._calculate_coherence(test_path)
        assert 0.0 <= coherence_score <= 10.0
        
        originality_score = planner._calculate_originality(test_path)
        assert 0.0 <= originality_score <= 10.0
    
    def test_risk_factor_identification(
        self, 
        planner: PlotPathPlanner, 
        sample_world_graph: WorldGraphResponse
    ):
        """测试风险因素识别"""
        # 创建高风险路径（事件过多）
        many_events = [
            PlotEvent(
                id=f"event-{i:03d}",
                title=f"事件{i}",
                description=f"事件{i}描述",
                event_type="test",
                involved_entities=["char-001"],
                involved_relationships=[],
                prerequisites=[],
                consequences=[],
                emotional_impact=5.0,
                tension_level=5.0,
                complexity=3.0,
                estimated_chapters=1,
                created_at=datetime.now()
            )
            for i in range(10)  # 10个事件，超过正常范围
        ]
        
        high_risk_path = PlotPath(
            id="high-risk-path",
            title="高风险路径",
            description="包含过多事件的路径",
            path_type=PathType.CONFLICT_ESCALATION,
            priority=PathPriority.MEDIUM,
            events=many_events,
            feasibility_score=0.0,
            appeal_score=0.0,
            coherence_score=0.0,
            originality_score=0.0,
            overall_score=0.0,
            risk_factors=[],
            potential_conflicts=[],
            estimated_total_chapters=10,
            target_audience=["通用读者"],
            genre_tags=["conflict_escalation"],
            created_at=datetime.now(),
            last_updated=datetime.now()
        )
        
        # 识别风险因素
        risk_factors = planner._identify_risk_factors(high_risk_path, sample_world_graph)
        
        assert len(risk_factors) > 0
        assert any("事件过多" in risk for risk in risk_factors)
    
    def test_confidence_calculation(
        self, 
        planner: PlotPathPlanner, 
        sample_world_graph: WorldGraphResponse
    ):
        """测试置信度计算"""
        # 创建高质量路径
        high_quality_path = PlotPath(
            id="high-quality-path",
            title="高质量路径",
            description="高质量路径描述",
            path_type=PathType.CHARACTER_GROWTH,
            priority=PathPriority.HIGH,
            events=[],
            feasibility_score=8.5,
            appeal_score=8.0,
            coherence_score=9.0,
            originality_score=7.5,
            overall_score=8.25,
            risk_factors=[],
            potential_conflicts=[],
            estimated_total_chapters=5,
            target_audience=["通用读者"],
            genre_tags=["character_growth"],
            created_at=datetime.now(),
            last_updated=datetime.now()
        )
        
        confidence = planner._calculate_confidence(sample_world_graph, [high_quality_path])
        
        assert 0.0 <= confidence <= 1.0
        assert confidence > 0.7  # 应该有较高的置信度


def test_create_plot_path_planner():
    """测试创建情节路径规划器函数"""
    planner = create_plot_path_planner()
    
    assert isinstance(planner, PlotPathPlanner)
    assert planner.path_generation_rules is not None
    assert planner.evaluation_weights is not None
