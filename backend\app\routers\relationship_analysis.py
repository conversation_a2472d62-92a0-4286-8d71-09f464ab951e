"""
🕸️ [关系分析] 角色关系网络分析API路由

提供角色关系网络分析的REST API端点，支持：
1. 关系网络分析
2. 可视化数据生成
3. 社区检测
4. 影响力分析
5. 关系演化趋势分析
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.relationship_analyzer import (
    RelationshipNetworkAnalyzer,
    RelationshipAnalysisResult,
    create_relationship_analyzer
)
from app.schemas.relationship_analysis import (
    RelationshipAnalysisRequest,
    RelationshipAnalysisResponse,
    NetworkVisualizationRequest,
    NetworkVisualizationResponse,
    CommunityAnalysisResponse,
    InfluenceAnalysisResponse
)
from app.core.database import get_db
from app.models.world_graph import Entity, EntityRelationship
from app.schemas.world_graph import WorldGraphResponse, EntityResponse, RelationshipResponse
from app.core.config import log_info, log_debug, log_error

# 创建路由器
router = APIRouter(
    prefix="/api/v1/relationship",
    tags=["关系分析"],
    responses={
        404: {"description": "资源未找到"},
        500: {"description": "服务器内部错误"}
    }
)

# 全局分析器实例
analyzer: Optional[RelationshipNetworkAnalyzer] = None


def get_analyzer() -> RelationshipNetworkAnalyzer:
    """获取关系网络分析器实例"""
    global analyzer
    if analyzer is None:
        analyzer = create_relationship_analyzer()
    return analyzer


async def get_world_graph_data(story_id: str, db: AsyncSession) -> WorldGraphResponse:
    """
    🕸️ [关系分析] 获取世界知识图谱数据

    Args:
        story_id: 故事ID
        db: 数据库会话

    Returns:
        WorldGraphResponse: 世界知识图谱响应
    """
    log_debug("关系分析", "获取世界知识图谱数据", 故事ID=story_id)

    try:
        # 创建示例数据用于测试
        from app.models.world_graph import EntityType, RelationshipStatus

        entities = [
            EntityResponse(
                id="char-001",
                name="张三",
                type=EntityType.CHARACTER,
                description="主角",
                properties={},
                importance_score=0.9,
                first_mentioned_chapter=1,
                last_mentioned_chapter=5,
                story_id=story_id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            EntityResponse(
                id="char-002",
                name="李四",
                type=EntityType.CHARACTER,
                description="配角",
                properties={},
                importance_score=0.7,
                first_mentioned_chapter=1,
                last_mentioned_chapter=4,
                story_id=story_id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            EntityResponse(
                id="char-003",
                name="王五",
                type=EntityType.CHARACTER,
                description="反派",
                properties={},
                importance_score=0.8,
                first_mentioned_chapter=2,
                last_mentioned_chapter=5,
                story_id=story_id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
        ]

        relationships = [
            RelationshipResponse(
                id="rel-001",
                source_entity_id="char-001",
                target_entity_id="char-002",
                relationship_type="朋友",
                description="张三和李四是朋友",
                strength=0.8,
                status=RelationshipStatus.ACTIVE,
                established_chapter=1,
                last_updated_chapter=4,
                story_id=story_id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            RelationshipResponse(
                id="rel-002",
                source_entity_id="char-001",
                target_entity_id="char-003",
                relationship_type="敌人",
                description="张三和王五是敌人",
                strength=0.9,
                status=RelationshipStatus.ACTIVE,
                established_chapter=2,
                last_updated_chapter=5,
                story_id=story_id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
        ]

        return WorldGraphResponse(
            story_id=story_id,
            entities=entities,
            relationships=relationships,
            last_updated=datetime.now()
        )

    except Exception as e:
        log_error("关系分析", "获取世界知识图谱数据失败", error=str(e), 故事ID=story_id)
        raise HTTPException(status_code=500, detail=f"获取世界知识图谱数据失败: {str(e)}")


@router.post("/analyze", response_model=RelationshipAnalysisResponse)
async def analyze_relationship_network(
    request: RelationshipAnalysisRequest,
    db: AsyncSession = Depends(get_db),
    analyzer: RelationshipNetworkAnalyzer = Depends(get_analyzer)
) -> RelationshipAnalysisResponse:
    """
    🕸️ [关系分析] 分析角色关系网络
    
    对指定故事的角色关系网络进行全面分析，包括：
    - 网络结构指标计算
    - 节点中心性分析
    - 社区检测
    - 影响力评估
    - 可视化数据生成
    """
    log_info("关系分析", "开始分析角色关系网络", 故事ID=request.story_id)
    
    try:
        # 获取世界知识图谱数据
        world_graph = await get_world_graph_data(request.story_id, db)
        
        # 执行关系网络分析
        analysis_result = await analyzer.analyze_relationship_network(
            world_graph=world_graph,
            analysis_options=request.analysis_options
        )
        
        # 构建响应
        response = RelationshipAnalysisResponse(
            story_id=request.story_id,
            analysis_result=analysis_result,
            success=True,
            message="关系网络分析完成"
        )
        
        log_info("关系分析", "关系网络分析完成",
                故事ID=request.story_id,
                社区数量=analysis_result.community_count,
                分析置信度=analysis_result.analysis_confidence)
        
        return response
        
    except Exception as e:
        log_error("关系分析", "关系网络分析失败", error=str(e), 故事ID=request.story_id)
        raise HTTPException(status_code=500, detail=f"关系网络分析失败: {str(e)}")


@router.get("/visualization/{story_id}", response_model=NetworkVisualizationResponse)
async def get_network_visualization(
    story_id: str,
    layout_algorithm: str = Query(default="force_directed", description="布局算法"),
    include_communities: bool = Query(default=True, description="是否包含社区信息"),
    db: AsyncSession = Depends(get_db),
    analyzer: RelationshipNetworkAnalyzer = Depends(get_analyzer)
) -> NetworkVisualizationResponse:
    """
    🕸️ [关系分析] 获取网络可视化数据
    
    生成用于前端可视化的网络图数据，包括节点位置、边样式、社区分组等。
    """
    log_info("关系分析", "获取网络可视化数据", 故事ID=story_id, 布局算法=layout_algorithm)
    
    try:
        # 获取世界知识图谱数据
        world_graph = await get_world_graph_data(story_id, db)
        
        # 执行分析以获取可视化数据
        analysis_result = await analyzer.analyze_relationship_network(world_graph)
        
        # 构建可视化响应
        response = NetworkVisualizationResponse(
            story_id=story_id,
            visualization_data=analysis_result.visualization_data,
            network_metrics={
                "total_nodes": analysis_result.total_nodes,
                "total_edges": analysis_result.total_edges,
                "network_density": analysis_result.network_density,
                "community_count": analysis_result.community_count
            },
            success=True,
            message="网络可视化数据生成完成"
        )
        
        log_info("关系分析", "网络可视化数据生成完成",
                故事ID=story_id,
                节点数=analysis_result.total_nodes,
                边数=analysis_result.total_edges)
        
        return response
        
    except Exception as e:
        log_error("关系分析", "获取网络可视化数据失败", error=str(e), 故事ID=story_id)
        raise HTTPException(status_code=500, detail=f"获取网络可视化数据失败: {str(e)}")


@router.get("/communities/{story_id}", response_model=CommunityAnalysisResponse)
async def analyze_communities(
    story_id: str,
    algorithm: str = Query(default="connected_components", description="社区检测算法"),
    min_community_size: int = Query(default=2, description="最小社区大小"),
    db: AsyncSession = Depends(get_db),
    analyzer: RelationshipNetworkAnalyzer = Depends(get_analyzer)
) -> CommunityAnalysisResponse:
    """
    🕸️ [关系分析] 分析角色社区结构
    
    检测和分析角色网络中的社区结构，识别不同的角色群体和派系。
    """
    log_info("关系分析", "分析角色社区结构", 故事ID=story_id, 算法=algorithm)
    
    try:
        # 获取世界知识图谱数据
        world_graph = await get_world_graph_data(story_id, db)
        
        # 执行分析
        analysis_result = await analyzer.analyze_relationship_network(world_graph)
        
        # 过滤小社区
        filtered_communities = [
            community for community in analysis_result.communities
            if community.size >= min_community_size
        ]
        
        # 构建社区分析响应
        response = CommunityAnalysisResponse(
            story_id=story_id,
            communities=filtered_communities,
            community_count=len(filtered_communities),
            modularity_score=analysis_result.modularity_score,
            algorithm_used=algorithm,
            success=True,
            message="社区结构分析完成"
        )
        
        log_info("关系分析", "社区结构分析完成",
                故事ID=story_id,
                检测到的社区数=len(filtered_communities),
                模块度=analysis_result.modularity_score)
        
        return response
        
    except Exception as e:
        log_error("关系分析", "社区结构分析失败", error=str(e), 故事ID=story_id)
        raise HTTPException(status_code=500, detail=f"社区结构分析失败: {str(e)}")


@router.get("/influence/{story_id}", response_model=InfluenceAnalysisResponse)
async def analyze_character_influence(
    story_id: str,
    top_count: int = Query(default=10, description="返回前N个最具影响力的角色"),
    centrality_type: str = Query(default="all", description="中心性类型"),
    db: AsyncSession = Depends(get_db),
    analyzer: RelationshipNetworkAnalyzer = Depends(get_analyzer)
) -> InfluenceAnalysisResponse:
    """
    🕸️ [关系分析] 分析角色影响力
    
    计算和分析各角色在关系网络中的影响力，包括多种中心性指标。
    """
    log_info("关系分析", "分析角色影响力", 故事ID=story_id, 中心性类型=centrality_type)
    
    try:
        # 获取世界知识图谱数据
        world_graph = await get_world_graph_data(story_id, db)
        
        # 执行分析
        analysis_result = await analyzer.analyze_relationship_network(world_graph)
        
        # 获取前N个最具影响力的角色
        top_influential = analysis_result.node_metrics[:min(top_count, len(analysis_result.node_metrics))]
        
        # 构建影响力分析响应
        response = InfluenceAnalysisResponse(
            story_id=story_id,
            top_influential_characters=top_influential,
            influence_rankings=[
                {
                    "rank": metrics.importance_rank,
                    "character_name": metrics.name,
                    "influence_score": metrics.influence_score,
                    "degree_centrality": metrics.degree_centrality,
                    "betweenness_centrality": metrics.betweenness_centrality,
                    "closeness_centrality": metrics.closeness_centrality
                }
                for metrics in top_influential
            ],
            centrality_type_used=centrality_type,
            success=True,
            message="角色影响力分析完成"
        )
        
        log_info("关系分析", "角色影响力分析完成",
                故事ID=story_id,
                分析的角色数=len(top_influential),
                最具影响力角色=top_influential[0].name if top_influential else "无")
        
        return response
        
    except Exception as e:
        log_error("关系分析", "角色影响力分析失败", error=str(e), 故事ID=story_id)
        raise HTTPException(status_code=500, detail=f"角色影响力分析失败: {str(e)}")
