// generating 页面的数据逻辑 hook
import { useState, useCallback, useEffect, useRef } from 'react';
import { useStoryStore } from '../stores';
import { useNotification } from '../components/NotificationProvider';
import { ApiAdapter } from '../services/apiAdapter';
import { useTaskMonitor } from './useTaskMonitor';
import { debugLog } from '../config/env';
import type { StoryBibleRequest, StoryBibleResponse, AIProvider, StoryGenre } from '../types/backend';
import { convertStoryBibleResponse } from '../types/story';

export interface UseGeneratingReturn {
  // 状态
  progress: number;
  currentText: string;
  status: string;
  isCompleted: boolean;
  error: string | null;
  isGenerating: boolean;

  // 任务相关
  currentTaskId: string | null;
  taskStatus?: string;
  isMonitoring: boolean;

  // 滚动控制
  isPanelVisible: boolean;
  setIsPanelVisible: (visible: boolean) => void;

  // 操作
  startGeneration: () => Promise<void>;
  handleComplete: (bible: any) => void;
  handleCancel: () => void;
  scrollToTop: () => void;
  scrollToBottom: () => void;
  scrollUp: () => void;
  scrollDown: () => void;
}

export const useGenerating = (): UseGeneratingReturn => {
  const { userPrompt, selectedAIProvider, setStoryBible } = useStoryStore();
  const { showNotification } = useNotification();

  // 生成状态
  const [progress, setProgress] = useState(0);
  const [currentText, setCurrentText] = useState('');
  const [status, setStatus] = useState('准备开始生成...');
  const [isCompleted, setIsCompleted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);

  // 任务监控
  const taskMonitor = useTaskMonitor({
    onComplete: (task) => {
      debugLog('生成', '故事圣经生成完成', { taskId: task.id });
      setStatus('生成完成！');
      setProgress(100);
      setIsCompleted(true);
      setIsGenerating(false);

      // 如果有生成内容，更新显示
      if (task.generated_content) {
        setCurrentText(task.generated_content);
      }

      showNotification('故事圣经生成完成！', 'success');
    },
    onError: (task, errorMsg) => {
      debugLog('错误', '故事圣经生成失败', { taskId: task.id, error: errorMsg });
      setError(errorMsg || '生成失败');
      setStatus('生成失败');
      setIsGenerating(false);
      showNotification('故事圣经生成失败：' + (errorMsg || '未知错误'), 'error');
    },
    onProgress: (task) => {
      debugLog('生成', '生成进度更新', {
        taskId: task.id,
        progress: task.progress,
        step: task.current_step
      });

      if (task.progress !== undefined) {
        setProgress(task.progress);
      }

      if (task.current_step) {
        setStatus(task.current_step);
      }

      // 如果有部分生成内容，实时更新显示
      if (task.generated_content && task.generated_content !== currentText) {
        setCurrentText(task.generated_content);
      }
    }
  });
  
  // 滚动控制状态
  const [isPanelVisible, setIsPanelVisible] = useState(false);
  
  // refs
  const autoScrollRef = useRef<boolean>(true);
  const lastContentLengthRef = useRef<number>(0);
  const hasInitialScrolled = useRef<boolean>(false);
  const lastScrollTimeRef = useRef<number>(0);

  // 获取AI提供商显示名称
  const getProviderDisplayName = useCallback((provider: string | null | undefined): string => {
    const names: Record<string, string> = {
      'zhipu': '智谱AI',
      'kimi': 'Kimi', 
      'openai': 'OpenAI',
      'claude': 'Claude'
    };
    
    if (!provider) {
      return '智能选择';
    }
    
    return names[provider] || provider;
  }, []);

  // 开始生成（新版本 - 使用任务监控）
  const startGeneration = useCallback(async () => {
    if (!userPrompt.trim()) {
      debugLog('生成', '用户输入为空，无法开始生成');
      setError('请先输入故事创意');
      return;
    }

    debugLog('生成', '开始生成故事圣经', {
      provider: selectedAIProvider,
      prompt: userPrompt.substring(0, 50) + '...'
    });

    setIsGenerating(true);
    setProgress(5);
    setStatus(`正在连接${getProviderDisplayName(selectedAIProvider)}...`);
    setError(null);
    setCurrentText('');
    setIsCompleted(false);
    setCurrentTaskId(null);

    // 重置自动滚动状态
    autoScrollRef.current = true;
    lastContentLengthRef.current = 0;
    hasInitialScrolled.current = false;
    lastScrollTimeRef.current = 0;

    try {
      // 构造故事圣经请求
      const request: StoryBibleRequest = {
        title: '基于创意的小说',
        genre: StoryGenre.FANTASY,
        theme: userPrompt,
        protagonist: '待定主角',
        setting: '待定设定',
        plot_outline: userPrompt,
        ai_provider: (selectedAIProvider as AIProvider) || AIProvider.ZHIPU,
        temperature: 0.8,
        max_tokens: 3000
      };

      debugLog('生成', '发送故事圣经生成请求', request);

      // 调用新版API
      const response = await ApiAdapter.generateStoryBibleV2(request);

      debugLog('生成', '故事圣经生成任务已创建', {
        taskId: response.id,
        status: response.status
      });

      setCurrentTaskId(response.id);
      setStatus('任务已创建，开始监控进度...');
      setProgress(10);

      // 开始监控任务
      taskMonitor.startMonitoring(response.id);

      showNotification('故事圣经生成任务已启动', 'info');

    } catch (err: any) {
      debugLog('错误', '启动生成失败', { error: err.message });
      setError(err.message || '生成过程中发生未知错误');
      setStatus('生成失败');
      setIsGenerating(false);

      showNotification('生成失败：' + (err.message || '未知错误'), 'error');
    }
  }, [userPrompt, selectedAIProvider, getProviderDisplayName, showNotification, taskMonitor]);

  // 处理完成
  const handleComplete = useCallback((bible: any) => {
    debugLog('生成', '用户确认使用生成内容');

    // 如果是新格式的任务响应，转换为旧格式
    if (taskMonitor.task && taskMonitor.isCompleted) {
      const convertedBible = convertStoryBibleResponse(taskMonitor.task as any);
      setStoryBible(convertedBible);
      window.location.href = `/bible/${convertedBible.id}`;
    } else {
      setStoryBible(bible);
      window.location.href = `/bible/${bible.id}`;
    }
  }, [setStoryBible, taskMonitor]);

  // 处理取消
  const handleCancel = useCallback(() => {
    debugLog('生成', '用户取消生成');

    // 停止任务监控
    taskMonitor.stopMonitoring();

    // 重置状态
    setIsGenerating(false);
    setCurrentTaskId(null);

    window.location.href = '/prompt';
  }, [taskMonitor]);

  // 滚动控制方法
  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
    debugLog('useGenerating', '手动滚动到顶部');
  }, []);

  const scrollToBottom = useCallback(() => {
    const scrollHeight = document.documentElement.scrollHeight;
    window.scrollTo({
      top: scrollHeight,
      behavior: 'smooth'
    });
    debugLog('useGenerating', '手动滚动到底部', { scrollHeight });
  }, []);

  const scrollUp = useCallback(() => {
    const currentScroll = window.pageYOffset;
    const scrollDistance = window.innerHeight * 0.7;
    window.scrollTo({
      top: Math.max(0, currentScroll - scrollDistance),
      behavior: 'smooth'
    });
    debugLog('useGenerating', '手动向上滚动', { 
      from: currentScroll, 
      to: Math.max(0, currentScroll - scrollDistance),
      distance: scrollDistance 
    });
  }, []);

  const scrollDown = useCallback(() => {
    const currentScroll = window.pageYOffset;
    const scrollDistance = window.innerHeight * 0.7;
    const scrollHeight = document.documentElement.scrollHeight;
    const targetScroll = Math.min(scrollHeight, currentScroll + scrollDistance);
    
    window.scrollTo({
      top: targetScroll,
      behavior: 'smooth'
    });
    debugLog('useGenerating', '手动向下滚动', { 
      from: currentScroll, 
      to: targetScroll,
      distance: scrollDistance 
    });
  }, []);

  // 自动开始生成
  useEffect(() => {
    if (userPrompt.trim()) {
      startGeneration();
    }
  }, []); // 只在组件挂载时执行一次

  return {
    // 状态
    progress,
    currentText,
    status,
    isCompleted,
    error,
    isGenerating,

    // 任务相关
    currentTaskId,
    taskStatus: taskMonitor.task?.status,
    isMonitoring: taskMonitor.isMonitoring,

    // 滚动控制
    isPanelVisible,
    setIsPanelVisible,

    // 操作
    startGeneration,
    handleComplete,
    handleCancel,
    scrollToTop,
    scrollToBottom,
    scrollUp,
    scrollDown
  };
};