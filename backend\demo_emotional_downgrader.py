"""
🎭 [演示] 情感表达降级器演示脚本

展示情感表达降级器的各项功能，包括过度描述检测、
不同降级模式的效果、预设配置的应用等。

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.emotional_downgrader import (
    create_emotional_downgrader,
    quick_emotional_downgrade,
    analyze_text_emotional_complexity,
    detect_ai_emotional_patterns,
    apply_preset_downgrade,
    InarticulationMode,
    PRESET_CONFIGURATIONS
)


def print_section_header(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🎭 {title}")
    print(f"{'='*60}")


def print_subsection_header(title: str):
    """打印子章节标题"""
    print(f"\n{'-'*40}")
    print(f"📝 {title}")
    print(f"{'-'*40}")


def demo_over_description_detection():
    """演示过度描述检测功能"""
    print_section_header("过度描述检测演示")
    
    test_texts = [
        {
            "name": "AI典型过度描述",
            "text": "他心中涌起复杂的情感，愤怒、失望、不甘心交织在一起，五味杂陈。这种难以名状的感觉如潮水般涌来，让他精确地感受到内心深处的痛苦。"
        },
        {
            "name": "过度文学化表达",
            "text": "痛苦如刀子一样刺痛着他的心，仿佛被雷击一般，宛如置身于情感的漩涡中，无法自拔。"
        },
        {
            "name": "正常人类表达",
            "text": "他有点不高兴，摇摇头就走了。算了，不想说什么。"
        }
    ]
    
    for test_case in test_texts:
        print_subsection_header(test_case["name"])
        print(f"原文: {test_case['text']}")
        
        result = detect_ai_emotional_patterns(test_case["text"])
        
        print(f"过度描述程度: {result['over_description_level']:.2f}")
        print(f"需要降级: {'是' if result['needs_downgrade'] else '否'}")
        print(f"检测到的模式数: {len(result['detected_patterns'])}")
        
        if result['detected_patterns']:
            print("检测到的AI模式:")
            for pattern_type, details in result['detected_patterns'].items():
                print(f"  - {pattern_type}: {details['matches'][:3]}...")


def demo_different_downgrade_modes():
    """演示不同降级模式的效果"""
    print_section_header("不同降级模式效果演示")
    
    original_text = "他心中涌起复杂的情感，精确地感受到这种强烈的痛苦，内心深处的挣扎让他难以言喻。"
    
    print(f"原文: {original_text}")
    print()
    
    downgrader = create_emotional_downgrader()
    
    modes = [
        (InarticulationMode.VOCABULARY_POOR, "词汇贫乏模式"),
        (InarticulationMode.EMOTIONAL_AVOIDANCE, "情感回避模式"),
        (InarticulationMode.BODY_SUBSTITUTION, "身体代替模式"),
        (InarticulationMode.VAGUE_EXPRESSION, "模糊表达模式"),
        (InarticulationMode.TOPIC_SHIFTING, "转移话题模式")
    ]
    
    for mode, mode_name in modes:
        print_subsection_header(mode_name)
        
        result = downgrader.make_expression_inadequate(
            original_text, 
            intensity=0.8,
            target_modes=[mode]
        )
        
        print(f"降级后: {result.downgraded_text}")
        print(f"原始表达: {result.original_expressions}")
        print(f"简化表达: {result.simplified_expressions}")
        print(f"压缩比例: {result.processing_details['compression_ratio']:.1%}")


def demo_intensity_effects():
    """演示不同强度的效果"""
    print_section_header("不同强度效果演示")
    
    text = "他心中涌起复杂的情感，精确地感受到这种强烈的痛苦，内心深处的挣扎让他难以言喻，五味杂陈。"
    
    print(f"原文: {text}")
    print()
    
    intensities = [0.3, 0.6, 0.9]
    
    for intensity in intensities:
        print_subsection_header(f"强度 {intensity}")
        
        result = quick_emotional_downgrade(text, intensity=intensity)
        
        print(f"降级后: {result}")
        
        # 分析复杂度变化
        original_analysis = analyze_text_emotional_complexity(text)
        processed_analysis = analyze_text_emotional_complexity(result)
        
        print(f"原始复杂度: {original_analysis['complexity_score']:.1f}")
        print(f"处理后复杂度: {processed_analysis['complexity_score']:.1f}")
        print(f"复杂度降低: {original_analysis['complexity_score'] - processed_analysis['complexity_score']:.1f}")


def demo_preset_configurations():
    """演示预设配置的效果"""
    print_section_header("预设配置效果演示")
    
    text = "他心中涌起复杂的情感，精确地感受到这种强烈的痛苦，内心深处的挣扎让他难以言喻，这种感觉如潮水般涌来。"
    
    print(f"原文: {text}")
    print()
    
    for preset_name, config in PRESET_CONFIGURATIONS.items():
        print_subsection_header(f"{preset_name}")
        print(f"配置描述: {config['description']}")
        print(f"强度: {config['intensity']}")
        print(f"模式: {[mode.value for mode in config['modes']]}")
        
        result = apply_preset_downgrade(text, preset_name)
        
        print(f"降级后: {result.downgraded_text}")
        print(f"应用的模式: {result.applied_modes}")
        print(f"压缩比例: {result.processing_details['compression_ratio']:.1%}")


def demo_emotional_complexity_analysis():
    """演示情感复杂度分析"""
    print_section_header("情感复杂度分析演示")
    
    test_texts = [
        {
            "name": "高复杂度AI文本",
            "text": "他心中涌起复杂的情感，精确地感受到这种强烈的痛苦，内心深处的挣扎让他难以言喻，愤怒、失望、不甘心交织在一起，如潮水般涌来。"
        },
        {
            "name": "中等复杂度文本",
            "text": "他感到有些失望，心情很复杂，说不上来是什么感觉。"
        },
        {
            "name": "简单自然文本",
            "text": "他有点不高兴，摇摇头就走了。"
        }
    ]
    
    for test_case in test_texts:
        print_subsection_header(test_case["name"])
        print(f"文本: {test_case['text']}")
        
        analysis = analyze_text_emotional_complexity(test_case["text"])
        
        print(f"复杂度评分: {analysis['complexity_score']:.1f}/100")
        print(f"触发器总数: {analysis['total_triggers']}")
        print(f"建议: {analysis['recommendation']}")
        
        if analysis['expression_patterns']:
            print("检测到的表达模式:")
            for pattern_type, stats in analysis['expression_patterns'].items():
                print(f"  - {pattern_type}: {stats['count']}次 (密度: {stats['density']:.1f})")


def demo_real_world_examples():
    """演示真实世界的例子"""
    print_section_header("真实世界应用示例")
    
    examples = [
        {
            "name": "小说对话场景",
            "text": "\"我心中涌起复杂的情感，\"他精确地表达着内心深处的想法，\"这种强烈的痛苦让我难以言喻。\"",
            "preset": "对话优化"
        },
        {
            "name": "内心独白场景",
            "text": "他意识到自己内心深处的变化，精确地感受到这种复杂的情感交织，心理层面的挣扎让他无法平静。",
            "preset": "内心独白优化"
        },
        {
            "name": "情感描述场景",
            "text": "她的心中涌起巨大的愤怒，这种强烈的情感如潮水般涌来，让她完全无法控制自己的情绪。",
            "preset": "重度去AI化"
        }
    ]
    
    for example in examples:
        print_subsection_header(example["name"])
        print(f"原文: {example['text']}")
        print(f"使用预设: {example['preset']}")
        
        result = apply_preset_downgrade(example["text"], example["preset"])
        
        print(f"降级后: {result.downgraded_text}")
        print(f"应用模式: {result.applied_modes}")
        
        # 计算改进效果
        original_analysis = analyze_text_emotional_complexity(example["text"])
        processed_analysis = analyze_text_emotional_complexity(result.downgraded_text)
        
        improvement = original_analysis['complexity_score'] - processed_analysis['complexity_score']
        print(f"复杂度改进: {improvement:.1f}分 ({improvement/original_analysis['complexity_score']*100:.1f}%)")


def main():
    """主演示函数"""
    print("🎭 情感表达降级器功能演示")
    print("=" * 60)
    print("本演示将展示如何让AI生成的过度丰富情感表达变得更加真实和不充分")
    
    try:
        # 演示各项功能
        demo_over_description_detection()
        demo_different_downgrade_modes()
        demo_intensity_effects()
        demo_preset_configurations()
        demo_emotional_complexity_analysis()
        demo_real_world_examples()
        
        print_section_header("演示总结")
        print("✅ 情感表达降级器演示完成！")
        print("\n主要功能:")
        print("1. 🔍 智能检测AI过度描述模式")
        print("2. 🎭 多种降级模式模拟人类表达局限性")
        print("3. ⚡ 可调节强度的自适应处理")
        print("4. 🎯 预设配置满足不同场景需求")
        print("5. 📊 全面的情感复杂度分析")
        print("\n通过这些功能，我们可以有效地让AI生成的内容更加人性化和真实！")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
