"""
📝 [生成] 小说生成服务模块
提供故事圣经和章节内容的AI生成功能
"""

import uuid
from typing import Dict, Any, Optional

from app.schemas.generation import StoryBibleRequest, ChapterGenerationRequest
from app.services.zhipu_client import get_zhipu_client, ChatMessage
from app.core.config import log_info, log_error, log_debug, log_success
from app.core.emotion_physical_mapper import EmotionPhysicalMapper, create_emotion_mapper
from app.core.entropy_injector import EntropyInjector, DisruptionLevel, create_entropy_injector
from app.core.deai_processor import (
    DeAIProcessor,
    ProcessingMode,
    create_deai_processor,
    quick_deai_processing
)
from app.repositories.emotional_gene_repo import EmotionalGeneRepository


def create_task_id() -> str:
    """生成任务ID"""
    return str(uuid.uuid4())


async def generate_story_bible_content(request: StoryBibleRequest) -> str:
    """
    📝 [生成] 生成故事圣经内容
    
    根据用户请求调用智谱AI生成详细的故事圣经内容
    
    Args:
        request: 故事圣经生成请求
        
    Returns:
        str: 生成的故事圣经内容
        
    Raises:
        ValueError: 当AI返回空内容时
    """
    log_debug("生成", "开始生成故事圣经内容", 标题=request.title)
    
    # 构建提示词
    prompt = f"""
你是一位资深小说编辑和创作顾问，请根据以下信息创建一个详细的故事圣经（Story Bible）。

**小说基础信息：**
- 标题：{request.title}
- 类型：{request.genre.value}
- 主题：{request.theme}
- 主角设定：{request.protagonist}
- 故事背景：{request.setting}
- 情节大纲：{request.plot_outline}
""" + (f"- 目标读者：{request.target_audience}" if request.target_audience else "") + """
""" + (f"- 写作风格：{request.writing_style}" if request.writing_style else "") + """

请创建一个全面的故事圣经，包含以下内容：

**1. 故事概述**
- 核心冲突和主题
- 故事的独特卖点
- 预期的情感基调

**2. 角色档案**
- 主要角色的详细背景、性格、动机
- 角色关系图
- 角色成长弧线

**3. 世界观设定**
- 故事发生的时间、地点
- 社会背景和规则
- 重要的设定细节

**4. 情节结构**
- 三幕式结构规划
- 关键情节点和转折
- 高潮和结局规划

**5. 写作指南**
- 叙述视角和语言风格
- 章节结构建议
- 需要注意的创作要点

请用中文撰写，内容要详细、实用，能指导后续的章节创作。
"""

    # 调用智谱AI
    client = await get_zhipu_client()
    messages = [
        ChatMessage(role="system", content="你是一位专业的小说创作顾问，擅长制作详细的故事圣经来指导小说创作。"),
        ChatMessage(role="user", content=prompt)
    ]
    
    response = await client.chat_completion(
        messages=messages,
        temperature=request.temperature,
        max_tokens=request.max_tokens
    )
    
    if response.choices and len(response.choices) > 0:
        content = response.choices[0]["message"]["content"]
        log_info("生成", "故事圣经内容生成成功", 内容长度=len(content))
        return content
    else:
        raise ValueError("智谱AI返回了空的响应内容")


async def generate_chapter_content(
    request: ChapterGenerationRequest,
    story_bible: str,
    gene_repository: Optional[EmotionalGeneRepository] = None
) -> str:
    """
    📝 [生成] 生成章节内容（集成情感增强流水线）

    根据故事圣经和章节要求生成具体的章节内容，
    支持情感增强和熵增扰动处理

    Args:
        request: 章节生成请求
        story_bible: 故事圣经内容
        gene_repository: 情感基因仓库（可选）

    Returns:
        str: 生成的章节内容

    Raises:
        ValueError: 当AI返回空内容时
    """
    log_debug("生成", "开始生成章节内容",
             章节号=request.chapter_number,
             章节标题=request.chapter_title,
             启用情感增强=request.enable_emotion_enhancement,
             启用熵增扰动=request.enable_entropy_injection)
    
    # 第一步：构建基础提示词
    base_prompt = f"""
你是一位专业的小说作家，请根据以下故事圣经和章节要求，创作一个完整的章节。

**故事圣经：**
{story_bible}

**章节要求：**
- 章节号：第{request.chapter_number}章
- 章节标题：{request.chapter_title}
- 章节大纲：{request.chapter_outline}
- 目标字数：{request.target_word_count}字
""" + (f"- 前一章节摘要：{request.previous_chapter_summary}" if request.previous_chapter_summary else "") + """
""" + (f"- 角色发展要求：{request.character_development}" if request.character_development else "") + """
""" + (f"- 情节要求：{request.plot_requirements}" if request.plot_requirements else "") + """

请创作一个完整的章节，要求：
1. 严格按照故事圣经的设定和风格
2. 情节紧凑，对话生动
3. 字数控制在目标字数左右
4. 章节结尾要有适当的悬念或转折
5. 保持角色性格的一致性

请直接输出章节内容，不需要额外的说明。
"""

    # 第二步：情感增强处理
    final_prompt = base_prompt
    emotion_context = None

    if request.enable_emotion_enhancement and gene_repository:
        try:
            log_debug("生成", "开始情感增强处理")
            emotion_mapper = await create_emotion_mapper(gene_repository)

            # 增强提示词
            enhanced_result = await emotion_mapper.enhance_prompt_with_emotions(
                original_prompt=base_prompt,
                specified_emotions=request.specified_emotions,
                emotion_intensity=request.emotion_intensity,
                auto_detect=request.auto_detect_emotions
            )

            final_prompt = enhanced_result.enhanced_prompt
            if enhanced_result.emotion_mappings:
                emotion_context = enhanced_result.emotion_mappings[0].emotion_tag

            log_info("生成", "情感增强处理完成",
                    增强指令数=len(enhanced_result.enhancement_instructions),
                    检测到的情感数=len(enhanced_result.emotion_mappings))

        except Exception as e:
            log_error("生成", "情感增强处理失败", error=e)
            # 失败时使用原始提示词
            final_prompt = base_prompt

    # 第三步：调用智谱AI生成内容
    log_debug("生成", "开始调用AI生成内容")
    client = await get_zhipu_client()
    messages = [
        ChatMessage(role="system", content="你是一位专业的小说作家，擅长根据故事圣经创作引人入胜的章节内容。"),
        ChatMessage(role="user", content=final_prompt)
    ]

    response = await client.chat_completion(
        messages=messages,
        temperature=request.temperature,
        max_tokens=request.max_tokens
    )

    if not response.choices or len(response.choices) == 0:
        raise ValueError("智谱AI返回了空的响应内容")

    raw_content = response.choices[0]["message"]["content"]
    log_info("生成", "AI内容生成成功",
            章节号=request.chapter_number,
            原始内容长度=len(raw_content))

    # 第四步：去AI化处理
    final_content = raw_content

    if request.enable_entropy_injection:
        try:
            log_debug("生成", "开始去AI化处理")

            # 使用新的去AI化处理器
            deai_processor = create_deai_processor()

            # 根据请求参数确定处理模式
            processing_mode = ProcessingMode.ADAPTIVE
            if hasattr(request, 'deai_processing_mode'):
                processing_mode = ProcessingMode(request.deai_processing_mode)

            # 执行去AI化处理
            deai_result = deai_processor.adaptive_deai_processing(
                text=raw_content,
                processing_mode=processing_mode,
                custom_intensity=request.custom_disruption_intensity,
                preserve_meaning=True
            )

            final_content = deai_result.processed_text

            log_info("生成", "去AI化处理完成",
                    AI痕迹降低=f"{deai_result.ai_reduction_percentage:.1f}%",
                    应用模块数=len(deai_result.modules_applied),
                    质量评分=f"{deai_result.quality_score:.2f}",
                    处理模式=deai_result.processing_mode.value)

        except Exception as e:
            log_error("生成", "去AI化处理失败", error=e)
            # 失败时使用原始内容
            final_content = raw_content

    log_info("生成", "章节内容生成完成",
            章节号=request.chapter_number,
            最终内容长度=len(final_content))

    return final_content


async def generate_chapter_content_with_rag(
    request: ChapterGenerationRequest,
    context_briefing,  # ContextBriefing类型，避免循环导入
    gene_repository: Optional[EmotionalGeneRepository] = None
) -> str:
    """
    🧠 [RAG] 使用RAG增强的章节内容生成

    基于动态提示词合成器构建的上下文简报，生成具有长期记忆能力的章节内容

    Args:
        request: 章节生成请求
        context_briefing: RAG上下文简报
        gene_repository: 情感基因仓库（可选）

    Returns:
        str: 生成的章节内容

    Raises:
        ValueError: 当AI返回空内容时
    """
    log_info("生成", "开始RAG增强章节生成",
            章节号=request.chapter_number,
            章节标题=request.chapter_title,
            上下文质量评分=f"{context_briefing.context_quality_score:.3f}",
            启用情感增强=request.enable_emotion_enhancement,
            启用熵增扰动=request.enable_entropy_injection)

    # 第一步：使用RAG增强的提示词作为基础
    base_prompt = context_briefing.enhanced_prompt

    # 添加具体的章节创作要求
    chapter_requirements = f"""

**具体章节创作要求：**
- 章节号：第{request.chapter_number}章
- 章节标题：{request.chapter_title}
- 章节大纲：{request.chapter_outline}
- 目标字数：{request.target_word_count}字
"""

    if request.previous_chapter_summary:
        chapter_requirements += f"- 前一章节摘要：{request.previous_chapter_summary}\n"
    if request.character_development:
        chapter_requirements += f"- 角色发展要求：{request.character_development}\n"
    if request.plot_requirements:
        chapter_requirements += f"- 情节要求：{request.plot_requirements}\n"

    chapter_requirements += """
**创作标准：**
1. 严格遵循上述上下文信息和故事设定
2. 保持与历史章节的连贯性和一致性
3. 充分利用相关记忆中的情节线索
4. 情节紧凑，对话生动自然
5. 字数控制在目标字数左右
6. 章节结尾要有适当的悬念或转折
7. 保持角色性格的一致性和发展轨迹

请直接输出章节内容，不需要额外的说明。
"""

    # 组合完整的RAG增强提示词
    rag_enhanced_prompt = base_prompt + chapter_requirements

    log_debug("生成", "RAG增强提示词构建完成",
             原始提示词长度=len(base_prompt),
             增强后长度=len(rag_enhanced_prompt),
             增强比例=f"{len(rag_enhanced_prompt)/len(base_prompt):.2f}x")

    # 第二步：情感增强处理（如果启用）
    final_prompt = rag_enhanced_prompt
    emotion_context = None

    if request.enable_emotion_enhancement and gene_repository:
        try:
            log_debug("生成", "开始情感增强处理")

            # 获取情感基因数据
            emotion_genes = await gene_repository.get_genes_by_story_id(
                int(request.story_bible_id.split('-')[-1]) if '-' in request.story_bible_id else 1
            )

            if emotion_genes:
                emotion_context = f"\n\n**情感增强上下文：**\n"
                for gene in emotion_genes[:3]:  # 取前3个最相关的情感基因
                    emotion_context += f"- {gene.emotion_type}: {gene.description}\n"

                final_prompt = rag_enhanced_prompt + emotion_context
                log_debug("生成", "情感增强处理完成", 情感基因数量=len(emotion_genes))

        except Exception as e:
            log_error("生成", "情感增强处理失败", error=e)
            # 失败时使用RAG增强提示词
            final_prompt = rag_enhanced_prompt

    # 第三步：调用智谱AI生成内容
    log_debug("生成", "开始调用AI生成RAG增强内容")
    client = await get_zhipu_client()
    messages = [
        ChatMessage(role="system", content="你是一位专业的小说作家，擅长根据丰富的上下文信息创作引人入胜的章节内容。你具有出色的长期记忆能力，能够保持故事的连贯性和一致性。"),
        ChatMessage(role="user", content=final_prompt)
    ]

    response = await client.chat_completion(
        messages=messages,
        temperature=request.temperature,
        max_tokens=request.max_tokens
    )

    if not response.get("choices") or len(response["choices"]) == 0:
        raise ValueError("智谱AI返回了空的响应内容")

    raw_content = response["choices"][0]["message"]["content"]
    log_info("生成", "RAG增强AI内容生成成功",
            章节号=request.chapter_number,
            原始内容长度=len(raw_content),
            上下文质量评分=f"{context_briefing.context_quality_score:.3f}")

    # 第四步：去AI化处理（如果启用）
    final_content = raw_content

    if request.enable_entropy_injection:
        try:
            log_debug("生成", "开始去AI化处理")

            # 使用新的去AI化处理器
            deai_processor = create_deai_processor()

            # 根据请求参数确定处理模式
            processing_mode = ProcessingMode.ADAPTIVE
            if hasattr(request, 'deai_processing_mode'):
                processing_mode = ProcessingMode(request.deai_processing_mode)

            # 执行去AI化处理
            deai_result = deai_processor.adaptive_deai_processing(
                text=raw_content,
                processing_mode=processing_mode,
                custom_intensity=request.custom_disruption_intensity,
                preserve_meaning=True
            )

            final_content = deai_result.processed_text

            log_info("生成", "去AI化处理完成",
                    AI痕迹降低=f"{deai_result.ai_reduction_percentage:.1f}%",
                    应用模块数=len(deai_result.modules_applied),
                    质量评分=f"{deai_result.quality_score:.2f}",
                    处理模式=deai_result.processing_mode.value)

        except Exception as e:
            log_error("生成", "去AI化处理失败", error=e)
            # 失败时使用原始内容
            final_content = raw_content

    log_success("生成", "RAG增强章节生成完成",
               章节号=request.chapter_number,
               最终内容长度=len(final_content),
               RAG质量评分=f"{context_briefing.context_quality_score:.3f}",
               情感增强=bool(emotion_context),
               熵增扰动=request.enable_entropy_injection)

    return final_content


def create_bible_data(task_id: str, request: StoryBibleRequest) -> Dict[str, Any]:
    """
    🏗️ [系统] 创建故事圣经数据字典
    
    Args:
        task_id: 任务ID
        request: 故事圣经请求
        
    Returns:
        Dict[str, Any]: 故事圣经数据字典
    """
    from app.schemas.generation import GenerationStatus
    
    return {
        "id": task_id,
        "title": request.title,
        "genre": request.genre,
        "theme": request.theme,
        "protagonist": request.protagonist,
        "setting": request.setting,
        "plot_outline": request.plot_outline,
        "target_audience": request.target_audience,
        "writing_style": request.writing_style,
        "ai_provider": request.ai_provider,
        "temperature": request.temperature,
        "max_tokens": request.max_tokens,
        "status": GenerationStatus.PENDING
    }


def create_chapter_data(task_id: str, request: ChapterGenerationRequest) -> Dict[str, Any]:
    """
    🏗️ [系统] 创建章节数据字典

    Args:
        task_id: 任务ID
        request: 章节请求

    Returns:
        Dict[str, Any]: 章节数据字典
    """
    from app.schemas.generation import GenerationStatus

    return {
        "id": task_id,
        "story_bible_id": request.story_bible_id,
        "chapter_number": request.chapter_number,
        "chapter_title": request.chapter_title,
        "chapter_outline": request.chapter_outline,
        "previous_chapter_summary": request.previous_chapter_summary,
        "character_development": request.character_development,
        "plot_requirements": request.plot_requirements,
        "target_word_count": request.target_word_count,

        # 情感增强流水线参数
        "enable_emotion_enhancement": request.enable_emotion_enhancement,
        "specified_emotions": request.specified_emotions,
        "emotion_intensity": request.emotion_intensity,
        "auto_detect_emotions": request.auto_detect_emotions,
        "enable_entropy_injection": request.enable_entropy_injection,
        "disruption_level": request.disruption_level,
        "custom_disruption_intensity": request.custom_disruption_intensity,

        # AI生成参数
        "ai_provider": request.ai_provider,
        "temperature": request.temperature,
        "max_tokens": request.max_tokens,
        "status": GenerationStatus.PENDING
    }
