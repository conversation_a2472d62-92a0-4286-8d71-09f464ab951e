"""
🎭 [核心模块] 非理性行为注入器

为角色添加情绪化的非逻辑行为，打破AI的完美逻辑性，
增加内容的真实感和人性化特征。

主要功能:
1. 情绪化行为库管理
2. 微行为注入
3. 情绪上下文检测
4. 与RAG系统集成的行为模式选择

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import re
import random
from enum import Enum
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

from app.core.config import log_info, log_debug, log_error


class EmotionalState(Enum):
    """情绪状态枚举"""
    ANGER = "愤怒"
    SADNESS = "悲伤"
    ANXIETY = "焦虑"
    JOY = "喜悦"
    FEAR = "恐惧"
    DISGUST = "厌恶"
    SURPRISE = "惊讶"
    NEUTRAL = "中性"


class BehaviorType(Enum):
    """行为类型枚举"""
    MICRO_ACTION = "微动作"          # 小动作：咬指甲、踢石头
    IMPULSIVE_DECISION = "冲动决定"   # 冲动决定：删聊天记录、关门
    AVOIDANCE_BEHAVIOR = "回避行为"   # 回避行为：坐在黑暗里、不接电话
    REPETITIVE_ACTION = "重复行为"    # 重复行为：反复检查、不停刷手机
    DISPLACEMENT_ACTIVITY = "替代活动" # 替代活动：翻照片、整理房间


@dataclass
class IrrationalBehavior:
    """非理性行为数据类"""
    emotion: EmotionalState
    behavior_type: BehaviorType
    action: str
    context_triggers: List[str]  # 触发上下文
    intensity_range: Tuple[float, float]  # 适用的情绪强度范围
    probability: float  # 出现概率
    description: str  # 行为描述


@dataclass
class BehaviorInjectionResult:
    """行为注入结果"""
    original_text: str
    modified_text: str
    injected_behaviors: List[Dict[str, Any]]
    injection_points: List[int]
    emotional_context: Dict[str, Any]
    processing_details: Dict[str, Any]


class IrrationalBehaviorInjector:
    """非理性行为注入器
    
    为角色添加情绪化的非逻辑行为，增加真实感
    """
    
    def __init__(self):
        """初始化非理性行为注入器"""
        self.behavior_library = self._initialize_behavior_library()
        log_debug("非理性行为", "行为库初始化完成", 行为总数=len(self.behavior_library))
        
    def _initialize_behavior_library(self) -> Dict[EmotionalState, List[IrrationalBehavior]]:
        """初始化行为库"""
        library = {emotion: [] for emotion in EmotionalState}
        
        # 愤怒行为库
        anger_behaviors = [
            IrrationalBehavior(
                emotion=EmotionalState.ANGER,
                behavior_type=BehaviorType.MICRO_ACTION,
                action="踢了一脚路边的石头",
                context_triggers=["走路", "街上", "路边", "外面"],
                intensity_range=(0.6, 1.0),
                probability=0.7,
                description="愤怒时的发泄性小动作"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.ANGER,
                behavior_type=BehaviorType.IMPULSIVE_DECISION,
                action="用力关门",
                context_triggers=["门", "房间", "离开", "出去"],
                intensity_range=(0.5, 1.0),
                probability=0.8,
                description="愤怒时的冲动行为"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.ANGER,
                behavior_type=BehaviorType.IMPULSIVE_DECISION,
                action="删掉了聊天记录",
                context_triggers=["手机", "聊天", "消息", "微信"],
                intensity_range=(0.7, 1.0),
                probability=0.6,
                description="愤怒时的冲动删除行为"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.ANGER,
                behavior_type=BehaviorType.MICRO_ACTION,
                action="握紧了拳头",
                context_triggers=["手", "愤怒", "生气"],
                intensity_range=(0.4, 1.0),
                probability=0.9,
                description="愤怒时的身体反应"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.ANGER,
                behavior_type=BehaviorType.IMPULSIVE_DECISION,
                action="把手机扔到了床上",
                context_triggers=["手机", "床", "房间"],
                intensity_range=(0.6, 0.9),
                probability=0.5,
                description="愤怒时的物品处理"
            )
        ]
        
        # 悲伤行为库
        sadness_behaviors = [
            IrrationalBehavior(
                emotion=EmotionalState.SADNESS,
                behavior_type=BehaviorType.AVOIDANCE_BEHAVIOR,
                action="一个人坐在黑暗里",
                context_triggers=["房间", "家", "独自", "一个人"],
                intensity_range=(0.5, 1.0),
                probability=0.8,
                description="悲伤时的回避行为"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.SADNESS,
                behavior_type=BehaviorType.DISPLACEMENT_ACTIVITY,
                action="翻出了很久以前的照片",
                context_triggers=["照片", "回忆", "过去", "手机"],
                intensity_range=(0.6, 1.0),
                probability=0.7,
                description="悲伤时的怀旧行为"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.SADNESS,
                behavior_type=BehaviorType.AVOIDANCE_BEHAVIOR,
                action="把头埋在枕头里",
                context_triggers=["床", "枕头", "房间", "睡觉"],
                intensity_range=(0.4, 0.9),
                probability=0.6,
                description="悲伤时的躲避行为"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.SADNESS,
                behavior_type=BehaviorType.MICRO_ACTION,
                action="无意识地抹了抹眼角",
                context_triggers=["眼泪", "哭", "难过"],
                intensity_range=(0.3, 0.8),
                probability=0.8,
                description="悲伤时的无意识动作"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.SADNESS,
                behavior_type=BehaviorType.DISPLACEMENT_ACTIVITY,
                action="开始整理房间",
                context_triggers=["房间", "家", "东西", "物品"],
                intensity_range=(0.4, 0.7),
                probability=0.5,
                description="悲伤时的转移注意力行为"
            )
        ]
        
        # 焦虑行为库
        anxiety_behaviors = [
            IrrationalBehavior(
                emotion=EmotionalState.ANXIETY,
                behavior_type=BehaviorType.REPETITIVE_ACTION,
                action="反复检查门锁",
                context_triggers=["门", "锁", "家", "出门"],
                intensity_range=(0.5, 1.0),
                probability=0.8,
                description="焦虑时的强迫性检查"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.ANXIETY,
                behavior_type=BehaviorType.REPETITIVE_ACTION,
                action="不停地刷手机",
                context_triggers=["手机", "消息", "等待", "无聊"],
                intensity_range=(0.4, 0.9),
                probability=0.9,
                description="焦虑时的重复行为"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.ANXIETY,
                behavior_type=BehaviorType.MICRO_ACTION,
                action="咬指甲",
                context_triggers=["手", "紧张", "等待", "思考"],
                intensity_range=(0.3, 0.8),
                probability=0.7,
                description="焦虑时的自我安抚行为"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.ANXIETY,
                behavior_type=BehaviorType.REPETITIVE_ACTION,
                action="来回踱步",
                context_triggers=["房间", "等待", "思考", "担心"],
                intensity_range=(0.5, 0.9),
                probability=0.6,
                description="焦虑时的运动性行为"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.ANXIETY,
                behavior_type=BehaviorType.MICRO_ACTION,
                action="不自觉地摸了摸头发",
                context_triggers=["头发", "紧张", "不安"],
                intensity_range=(0.2, 0.7),
                probability=0.5,
                description="焦虑时的自我触摸行为"
            )
        ]
        
        # 喜悦行为库
        joy_behaviors = [
            IrrationalBehavior(
                emotion=EmotionalState.JOY,
                behavior_type=BehaviorType.IMPULSIVE_DECISION,
                action="忍不住哼起了歌",
                context_triggers=["开心", "高兴", "音乐", "心情好"],
                intensity_range=(0.6, 1.0),
                probability=0.7,
                description="喜悦时的自发表达"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.JOY,
                behavior_type=BehaviorType.MICRO_ACTION,
                action="轻快地跳了一下",
                context_triggers=["走路", "开心", "兴奋"],
                intensity_range=(0.7, 1.0),
                probability=0.5,
                description="喜悦时的身体表达"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.JOY,
                behavior_type=BehaviorType.IMPULSIVE_DECISION,
                action="给朋友发了个表情包",
                context_triggers=["手机", "朋友", "分享", "消息"],
                intensity_range=(0.5, 0.9),
                probability=0.8,
                description="喜悦时的分享冲动"
            )
        ]
        
        # 恐惧行为库
        fear_behaviors = [
            IrrationalBehavior(
                emotion=EmotionalState.FEAR,
                behavior_type=BehaviorType.AVOIDANCE_BEHAVIOR,
                action="下意识地后退了一步",
                context_triggers=["害怕", "危险", "恐惧", "威胁"],
                intensity_range=(0.4, 1.0),
                probability=0.9,
                description="恐惧时的本能反应"
            ),
            IrrationalBehavior(
                emotion=EmotionalState.FEAR,
                behavior_type=BehaviorType.MICRO_ACTION,
                action="紧紧抱住了自己",
                context_triggers=["害怕", "孤独", "恐惧"],
                intensity_range=(0.6, 1.0),
                probability=0.7,
                description="恐惧时的自我保护"
            )
        ]
        
        library[EmotionalState.ANGER] = anger_behaviors
        library[EmotionalState.SADNESS] = sadness_behaviors
        library[EmotionalState.ANXIETY] = anxiety_behaviors
        library[EmotionalState.JOY] = joy_behaviors
        library[EmotionalState.FEAR] = fear_behaviors
        
        return library

    def detect_emotional_context(self, text: str) -> Dict[str, Any]:
        """检测文本中的情绪上下文

        Args:
            text: 输入文本

        Returns:
            情绪上下文分析结果
        """
        log_debug("非理性行为", "开始检测情绪上下文", 文本长度=len(text))

        # 情绪关键词映射
        emotion_keywords = {
            EmotionalState.ANGER: [
                "愤怒", "生气", "恼火", "暴怒", "气愤", "恼怒", "火大",
                "讨厌", "烦躁", "不爽", "气死了", "受不了"
            ],
            EmotionalState.SADNESS: [
                "悲伤", "难过", "伤心", "痛苦", "沮丧", "失落", "绝望",
                "哭", "眼泪", "心痛", "心碎", "忧郁", "抑郁"
            ],
            EmotionalState.ANXIETY: [
                "焦虑", "紧张", "担心", "不安", "恐慌", "忧虑", "害怕",
                "紧张", "慌张", "心慌", "不安", "烦躁", "坐立不安"
            ],
            EmotionalState.JOY: [
                "开心", "高兴", "快乐", "兴奋", "喜悦", "愉快", "欣喜",
                "激动", "满足", "幸福", "欢乐", "乐观"
            ],
            EmotionalState.FEAR: [
                "害怕", "恐惧", "惊恐", "恐慌", "畏惧", "胆怯", "惊吓",
                "可怕", "吓人", "恐怖", "威胁", "危险"
            ],
            EmotionalState.DISGUST: [
                "恶心", "厌恶", "讨厌", "反感", "嫌弃", "排斥", "憎恨"
            ],
            EmotionalState.SURPRISE: [
                "惊讶", "震惊", "意外", "吃惊", "惊奇", "诧异", "惊愕"
            ]
        }

        # 检测情绪
        detected_emotions = {}
        text_lower = text.lower()

        for emotion, keywords in emotion_keywords.items():
            matches = [kw for kw in keywords if kw in text_lower]
            if matches:
                # 计算情绪强度（基于匹配的关键词数量和位置）
                intensity = min(len(matches) * 0.3 + 0.4, 1.0)
                detected_emotions[emotion] = {
                    "intensity": intensity,
                    "keywords": matches,
                    "confidence": len(matches) / len(keywords)
                }

        # 检测上下文触发器
        context_triggers = []
        trigger_patterns = [
            "走路", "街上", "路边", "外面", "门", "房间", "离开", "出去",
            "手机", "聊天", "消息", "微信", "床", "枕头", "睡觉", "照片",
            "回忆", "过去", "锁", "家", "出门", "等待", "无聊", "朋友"
        ]

        for trigger in trigger_patterns:
            if trigger in text:
                context_triggers.append(trigger)

        # 确定主导情绪
        primary_emotion = None
        max_intensity = 0

        if detected_emotions:
            for emotion, data in detected_emotions.items():
                if data["intensity"] > max_intensity:
                    max_intensity = data["intensity"]
                    primary_emotion = emotion

        result = {
            "primary_emotion": primary_emotion,
            "emotion_intensity": max_intensity,
            "all_emotions": detected_emotions,
            "context_triggers": context_triggers,
            "has_emotional_content": len(detected_emotions) > 0,
            "text_length": len(text)
        }

        log_info("非理性行为", "情绪上下文检测完成",
                主导情绪=primary_emotion.value if primary_emotion else "无",
                情绪强度=max_intensity,
                触发器数量=len(context_triggers))

        return result

    def _select_appropriate_behaviors(
        self,
        emotion: EmotionalState,
        intensity: float,
        context_triggers: List[str],
        max_behaviors: int = 2
    ) -> List[IrrationalBehavior]:
        """选择合适的非理性行为

        Args:
            emotion: 情绪状态
            intensity: 情绪强度
            context_triggers: 上下文触发器
            max_behaviors: 最大行为数量

        Returns:
            选中的行为列表
        """
        if emotion not in self.behavior_library:
            return []

        available_behaviors = self.behavior_library[emotion]
        suitable_behaviors = []

        for behavior in available_behaviors:
            # 检查情绪强度范围
            min_intensity, max_intensity = behavior.intensity_range
            if not (min_intensity <= intensity <= max_intensity):
                continue

            # 检查上下文匹配
            context_match = False
            if not behavior.context_triggers:  # 如果没有特定上下文要求
                context_match = True
            else:
                for trigger in behavior.context_triggers:
                    if trigger in context_triggers:
                        context_match = True
                        break

            if context_match:
                suitable_behaviors.append(behavior)

        # 按概率和适配度排序
        suitable_behaviors.sort(key=lambda b: b.probability, reverse=True)

        # 随机选择，但偏向高概率行为
        selected = []
        for behavior in suitable_behaviors[:max_behaviors * 2]:  # 扩大候选池
            if random.random() < behavior.probability:
                selected.append(behavior)
                if len(selected) >= max_behaviors:
                    break

        log_debug("非理性行为", "行为选择完成",
                 可用行为数=len(available_behaviors),
                 合适行为数=len(suitable_behaviors),
                 选中行为数=len(selected))

        return selected

    def inject_micro_behavior(
        self,
        text: str,
        intensity: float = 0.7,
        max_injections: int = 2,
        preserve_meaning: bool = True
    ) -> BehaviorInjectionResult:
        """注入微行为到文本中

        Args:
            text: 原始文本
            intensity: 注入强度 (0.0-1.0)
            max_injections: 最大注入数量
            preserve_meaning: 是否保持原意

        Returns:
            行为注入结果
        """
        log_debug("非理性行为", "开始注入微行为",
                 文本长度=len(text), 强度=intensity, 最大注入数=max_injections)

        # 检测情绪上下文
        emotional_context = self.detect_emotional_context(text)

        if not emotional_context["has_emotional_content"]:
            log_info("非理性行为", "文本无情绪内容，跳过注入")
            return BehaviorInjectionResult(
                original_text=text,
                modified_text=text,
                injected_behaviors=[],
                injection_points=[],
                emotional_context=emotional_context,
                processing_details={
                    "skipped": True,
                    "reason": "无情绪内容",
                    "injection_intensity": 0.0,
                    "original_length": len(text),
                    "modified_length": len(text),
                    "behaviors_attempted": 0,
                    "behaviors_injected": 0
                }
            )

        primary_emotion = emotional_context["primary_emotion"]
        emotion_intensity = emotional_context["emotion_intensity"]
        context_triggers = emotional_context["context_triggers"]

        # 调整注入强度
        adjusted_intensity = min(intensity * emotion_intensity, 1.0)

        # 选择合适的行为
        selected_behaviors = self._select_appropriate_behaviors(
            primary_emotion,
            adjusted_intensity,
            context_triggers,
            max_injections
        )

        if not selected_behaviors:
            log_info("非理性行为", "未找到合适的行为", 情绪=primary_emotion.value)
            return BehaviorInjectionResult(
                original_text=text,
                modified_text=text,
                injected_behaviors=[],
                injection_points=[],
                emotional_context=emotional_context,
                processing_details={
                    "skipped": True,
                    "reason": "无合适行为",
                    "injection_intensity": adjusted_intensity,
                    "original_length": len(text),
                    "modified_length": len(text),
                    "behaviors_attempted": len(selected_behaviors),
                    "behaviors_injected": 0,
                    "primary_emotion": primary_emotion.value if primary_emotion else None
                }
            )

        # 执行注入
        modified_text = text
        injected_behaviors = []
        injection_points = []

        for behavior in selected_behaviors:
            injection_result = self._inject_single_behavior(
                modified_text,
                behavior,
                preserve_meaning
            )

            if injection_result["success"]:
                modified_text = injection_result["text"]
                injected_behaviors.append({
                    "behavior": behavior.action,
                    "type": behavior.behavior_type.value,
                    "emotion": behavior.emotion.value,
                    "position": injection_result["position"]
                })
                injection_points.append(injection_result["position"])

                log_debug("非理性行为", "成功注入行为",
                         行为=behavior.action, 位置=injection_result["position"])

        processing_details = {
            "original_length": len(text),
            "modified_length": len(modified_text),
            "behaviors_attempted": len(selected_behaviors),
            "behaviors_injected": len(injected_behaviors),
            "injection_intensity": adjusted_intensity,
            "primary_emotion": primary_emotion.value if primary_emotion else None
        }

        log_info("非理性行为", "微行为注入完成",
                注入行为数=len(injected_behaviors),
                文本变化=len(modified_text) - len(text))

        return BehaviorInjectionResult(
            original_text=text,
            modified_text=modified_text,
            injected_behaviors=injected_behaviors,
            injection_points=injection_points,
            emotional_context=emotional_context,
            processing_details=processing_details
        )

    def _inject_single_behavior(
        self,
        text: str,
        behavior: IrrationalBehavior,
        preserve_meaning: bool = True
    ) -> Dict[str, Any]:
        """注入单个行为到文本中

        Args:
            text: 文本
            behavior: 要注入的行为
            preserve_meaning: 是否保持原意

        Returns:
            注入结果
        """
        # 寻找合适的注入点
        injection_points = self._find_injection_points(text, behavior)

        if not injection_points:
            return {"success": False, "reason": "无合适注入点"}

        # 选择最佳注入点
        best_point = random.choice(injection_points)

        # 构造注入文本
        if preserve_meaning:
            # 保持原意的注入方式
            if best_point["type"] == "sentence_end":
                injection_text = f"，{behavior.action}"
            elif best_point["type"] == "paragraph_break":
                injection_text = f"\n\n{behavior.action}。"
            else:
                injection_text = f"，{behavior.action}"
        else:
            # 更自由的注入方式
            injection_text = f" {behavior.action}。"

        # 执行注入
        position = best_point["position"]
        new_text = text[:position] + injection_text + text[position:]

        return {
            "success": True,
            "text": new_text,
            "position": position,
            "injection_text": injection_text,
            "injection_type": best_point["type"]
        }

    def _find_injection_points(self, text: str, behavior: IrrationalBehavior) -> List[Dict[str, Any]]:
        """寻找行为注入点

        Args:
            text: 文本
            behavior: 行为

        Returns:
            可能的注入点列表
        """
        points = []

        # 句子结尾注入点
        sentence_endings = []
        for match in re.finditer(r'[。！？]', text):
            sentence_endings.append({
                "position": match.end(),
                "type": "sentence_end",
                "context": text[max(0, match.start()-10):match.end()+10]
            })

        # 段落分隔注入点
        paragraph_breaks = []
        for match in re.finditer(r'\n\s*\n', text):
            paragraph_breaks.append({
                "position": match.end(),
                "type": "paragraph_break",
                "context": text[max(0, match.start()-10):match.end()+10]
            })

        # 情绪词后注入点
        emotion_words = ["愤怒", "生气", "悲伤", "难过", "焦虑", "紧张", "开心", "高兴"]
        for word in emotion_words:
            for match in re.finditer(word, text):
                points.append({
                    "position": match.end(),
                    "type": "emotion_word",
                    "context": text[max(0, match.start()-5):match.end()+15],
                    "priority": 0.8
                })

        # 合并所有注入点
        points.extend(sentence_endings)
        points.extend(paragraph_breaks)

        # 过滤和评分
        filtered_points = []
        for point in points:
            # 检查上下文适配性
            context_score = self._evaluate_context_fit(point["context"], behavior)
            if context_score > 0.3:
                point["score"] = context_score
                filtered_points.append(point)

        # 按评分排序
        filtered_points.sort(key=lambda p: p.get("score", 0.5), reverse=True)

        return filtered_points[:5]  # 返回前5个最佳注入点

    def _evaluate_context_fit(self, context: str, behavior: IrrationalBehavior) -> float:
        """评估上下文适配性

        Args:
            context: 上下文文本
            behavior: 行为

        Returns:
            适配性评分 (0.0-1.0)
        """
        score = 0.5  # 基础分

        # 检查触发器匹配
        for trigger in behavior.context_triggers:
            if trigger in context:
                score += 0.2

        # 检查情绪词匹配
        emotion_keywords = {
            EmotionalState.ANGER: ["愤怒", "生气", "恼火"],
            EmotionalState.SADNESS: ["悲伤", "难过", "伤心"],
            EmotionalState.ANXIETY: ["焦虑", "紧张", "担心"],
            EmotionalState.JOY: ["开心", "高兴", "快乐"],
            EmotionalState.FEAR: ["害怕", "恐惧", "惊恐"]
        }

        if behavior.emotion in emotion_keywords:
            for keyword in emotion_keywords[behavior.emotion]:
                if keyword in context:
                    score += 0.15

        return min(score, 1.0)


# 工厂函数和便捷接口

def create_irrational_behavior_injector() -> IrrationalBehaviorInjector:
    """创建非理性行为注入器实例"""
    log_debug("非理性行为", "创建行为注入器实例")
    return IrrationalBehaviorInjector()


def inject_irrational_behavior(
    text: str,
    intensity: float = 0.7,
    max_injections: int = 2
) -> str:
    """快速注入非理性行为

    Args:
        text: 输入文本
        intensity: 注入强度
        max_injections: 最大注入数量

    Returns:
        处理后的文本
    """
    injector = create_irrational_behavior_injector()
    result = injector.inject_micro_behavior(text, intensity, max_injections)
    return result.modified_text


def analyze_emotional_behavior_potential(text: str) -> Dict[str, Any]:
    """分析文本的情绪行为潜力

    Args:
        text: 输入文本

    Returns:
        分析结果
    """
    injector = create_irrational_behavior_injector()
    emotional_context = injector.detect_emotional_context(text)

    # 计算行为注入潜力
    potential_score = 0.0
    if emotional_context["has_emotional_content"]:
        potential_score = emotional_context["emotion_intensity"] * 0.6
        potential_score += len(emotional_context["context_triggers"]) * 0.1
        potential_score = min(potential_score, 1.0)

    return {
        "emotional_context": emotional_context,
        "injection_potential": potential_score,
        "recommended_intensity": potential_score * 0.8,
        "suitable_for_injection": potential_score > 0.3
    }
