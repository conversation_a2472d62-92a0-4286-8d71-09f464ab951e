"""
🧪 [测试] 世界知识图谱API路由测试
测试实体和关系的CRUD操作，验证API功能正确性
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.world_graph import Entity, EntityRelationship, EntityType, RelationshipStatus
from app.models.story_bible import StoryBible
from app.core.config import log_debug


class TestEntityAPI:
    """实体API测试类"""
    
    def test_create_entity(self, async_client: TestClient, sample_story_bible_async: StoryBible):
        """测试创建实体"""
        log_debug("测试", "开始测试创建实体API")
        
        entity_data = {
            "story_id": sample_story_bible_async.id,
            "name": "张三",
            "type": "character",
            "description": "主角，年轻剑客",
            "properties": {"年龄": 20, "武功": "初级"},
            "importance_score": 8.5,
            "first_mentioned_chapter": 1
        }
        
        response = async_client.post("/api/v1/world/entities", json=entity_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "张三"
        assert data["type"] == "character"
        assert data["story_id"] == sample_story_bible_async.id
        assert data["importance_score"] == 8.5
        
        log_debug("测试", "创建实体API测试通过", entity_id=data["id"])
    
    @pytest.mark.asyncio
    async def test_create_entity_duplicate_name(self, async_client: AsyncClient, sample_entities_async: list):
        """测试创建重名实体"""
        log_debug("测试", "开始测试创建重名实体API")

        existing_entity = sample_entities_async[0]
        entity_data = {
            "story_id": existing_entity.story_id,
            "name": existing_entity.name,
            "type": existing_entity.type.value,
            "description": "重复的实体"
        }
        
        response = await async_client.post("/api/v1/world/entities", json=entity_data)
        
        assert response.status_code == 409
        assert "已存在" in response.json()["detail"]
        
        log_debug("测试", "创建重名实体API测试通过")
    
    @pytest.mark.asyncio
    async def test_get_entity(self, async_client: AsyncClient, sample_entities_async: list):
        """测试获取实体"""
        log_debug("测试", "开始测试获取实体API")

        entity = sample_entities_async[0]
        response = await async_client.get(f"/api/v1/world/entities/{entity.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == entity.id
        assert data["name"] == entity.name
        assert data["type"] == entity.type.value
        
        log_debug("测试", "获取实体API测试通过", entity_id=entity.id)
    
    @pytest.mark.asyncio
    async def test_get_entity_not_found(self, async_client: AsyncClient):
        """测试获取不存在的实体"""
        log_debug("测试", "开始测试获取不存在实体API")
        
        response = await async_client.get("/api/v1/world/entities/nonexistent")
        
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]
        
        log_debug("测试", "获取不存在实体API测试通过")
    
    @pytest.mark.asyncio
    async def test_update_entity(self, async_client: AsyncClient, sample_entities_async: list):
        """测试更新实体"""
        log_debug("测试", "开始测试更新实体API")

        entity = sample_entities_async[0]
        update_data = {
            "description": "更新后的描述",
            "importance_score": 9.0,
            "last_mentioned_chapter": 5
        }
        
        response = await async_client.put(f"/api/v1/world/entities/{entity.id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["description"] == "更新后的描述"
        assert data["importance_score"] == 9.0
        assert data["last_mentioned_chapter"] == 5
        
        log_debug("测试", "更新实体API测试通过", entity_id=entity.id)
    
    @pytest.mark.asyncio
    async def test_delete_entity(self, async_client: AsyncClient, sample_entities_async: list):
        """测试删除实体"""
        log_debug("测试", "开始测试删除实体API")

        entity = sample_entities_async[-1]  # 使用最后一个实体避免影响其他测试
        response = await async_client.delete(f"/api/v1/world/entities/{entity.id}")
        
        assert response.status_code == 204
        
        # 验证实体已被删除
        get_response = await async_client.get(f"/api/v1/world/entities/{entity.id}")
        assert get_response.status_code == 404
        
        log_debug("测试", "删除实体API测试通过", entity_id=entity.id)
    
    @pytest.mark.asyncio
    async def test_get_story_entities(self, async_client: AsyncClient, sample_story_bible_async: StoryBible):
        """测试获取故事实体列表"""
        log_debug("测试", "开始测试获取故事实体列表API")

        response = await async_client.get(f"/api/v1/world/stories/{sample_story_bible_async.id}/entities")
        
        assert response.status_code == 200
        data = response.json()
        assert "entities" in data
        assert "total" in data
        assert data["total"] >= 0
        
        log_debug("测试", "获取故事实体列表API测试通过",
            story_id=sample_story_bible_async.id,
            total_entities=data["total"]
        )
    
    @pytest.mark.asyncio
    async def test_get_story_entities_with_filters(self, async_client: AsyncClient, sample_story_bible_async: StoryBible):
        """测试带筛选条件的故事实体列表"""
        log_debug("测试", "开始测试带筛选条件的故事实体列表API")

        # 测试按类型筛选
        response = await async_client.get(
            f"/api/v1/world/stories/{sample_story_bible_async.id}/entities",
            params={"entity_type": "character", "is_active": True, "limit": 10}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "entities" in data
        
        # 验证返回的实体都是角色类型
        for entity in data["entities"]:
            assert entity["type"] == "character"
            assert entity["is_active"] == True
        
        log_debug("测试", "带筛选条件的故事实体列表API测试通过")


class TestRelationshipAPI:
    """关系API测试类"""
    
    @pytest.mark.asyncio
    async def test_create_relationship(self, async_client: AsyncClient, sample_entities_async: list):
        """测试创建关系"""
        log_debug("测试", "开始测试创建关系API")

        source_entity = sample_entities_async[0]
        target_entity = sample_entities_async[1]
        
        relationship_data = {
            "source_entity_id": source_entity.id,
            "target_entity_id": target_entity.id,
            "relationship_type": "朋友",
            "description": "青梅竹马的朋友",
            "strength": 8.0,
            "is_bidirectional": True,
            "established_chapter": 1
        }
        
        response = await async_client.post("/api/v1/world/relationships", json=relationship_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["source_entity_id"] == source_entity.id
        assert data["target_entity_id"] == target_entity.id
        assert data["relationship_type"] == "朋友"
        assert data["strength"] == 8.0
        assert data["is_bidirectional"] == True
        
        log_debug("测试", "创建关系API测试通过", relationship_id=data["id"])
    
    @pytest.mark.asyncio
    async def test_create_relationship_same_entity(self, async_client: AsyncClient, sample_entities_async: list):
        """测试创建自己与自己的关系"""
        log_debug("测试", "开始测试创建自己与自己的关系API")

        entity = sample_entities_async[0]
        relationship_data = {
            "source_entity_id": entity.id,
            "target_entity_id": entity.id,
            "relationship_type": "自恋"
        }
        
        response = await async_client.post("/api/v1/world/relationships", json=relationship_data)
        
        assert response.status_code == 422  # Validation error
        
        log_debug("测试", "创建自己与自己的关系API测试通过")
    
    @pytest.mark.asyncio
    async def test_update_relationship(self, async_client: AsyncClient, sample_relationships_async: list):
        """测试更新关系"""
        log_debug("测试", "开始测试更新关系API")

        relationship = sample_relationships_async[0]
        update_data = {
            "relationship_type": "敌人",
            "description": "因为误会变成敌人",
            "strength": 6.0,
            "status": "active"
        }
        
        response = await async_client.put(f"/api/v1/world/relationships/{relationship.id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["relationship_type"] == "敌人"
        assert data["description"] == "因为误会变成敌人"
        assert data["strength"] == 6.0
        
        log_debug("测试", "更新关系API测试通过", relationship_id=relationship.id)


class TestWorldGraphAPI:
    """世界图谱API测试类"""
    
    @pytest.mark.asyncio
    async def test_get_world_graph(self, async_client: AsyncClient, sample_story_bible_async: StoryBible):
        """测试获取完整世界图谱"""
        log_debug("测试", "开始测试获取完整世界图谱API")

        response = await async_client.get(f"/api/v1/world/stories/{sample_story_bible_async.id}/graph")
        
        assert response.status_code == 200
        data = response.json()
        assert data["story_id"] == sample_story_bible_async.id
        assert data["story_title"] == sample_story_bible_async.title
        assert "entities" in data
        assert "relationships" in data
        assert "entity_count" in data
        assert "relationship_count" in data
        assert "entity_stats" in data
        assert "relationship_stats" in data
        
        log_debug("测试", "获取完整世界图谱API测试通过",
            story_id=sample_story_bible_async.id,
            entity_count=data["entity_count"],
            relationship_count=data["relationship_count"]
        )
    
    @pytest.mark.asyncio
    async def test_get_world_graph_include_inactive(self, async_client: AsyncClient, sample_story_bible_async: StoryBible):
        """测试获取包含非活跃实体的世界图谱"""
        log_debug("测试", "开始测试获取包含非活跃实体的世界图谱API")

        response = await async_client.get(
            f"/api/v1/world/stories/{sample_story_bible_async.id}/graph",
            params={"include_inactive": True}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "entities" in data
        assert "relationships" in data
        
        log_debug("测试", "获取包含非活跃实体的世界图谱API测试通过")
    
    @pytest.mark.asyncio
    async def test_get_world_graph_nonexistent_story(self, async_client: AsyncClient):
        """测试获取不存在故事的世界图谱"""
        log_debug("测试", "开始测试获取不存在故事的世界图谱API")
        
        response = await async_client.get("/api/v1/world/stories/nonexistent/graph")
        
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]
        
        log_debug("测试", "获取不存在故事的世界图谱API测试通过")
