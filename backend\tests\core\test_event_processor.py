"""
🧪 [测试] 动态事件处理器测试
测试AI驱动的事件提取和处理功能
"""

import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.event_processor import (
    EventProcessor, 
    WorldEvent, 
    CreateEntityEventData, 
    CreateRelationshipEventData,
    get_event_processor
)
from app.models.world_graph import EntityType, RelationshipStatus
from app.models.story_bible import StoryBible, AIProvider
from app.services.zhipu_client import ChatCompletionResponse


class TestEventProcessor:
    """事件处理器测试类"""
    
    @pytest.fixture
    def event_processor(self):
        """创建事件处理器实例"""
        return EventProcessor()
    
    @pytest.fixture
    def sample_chapter_text(self):
        """示例章节文本"""
        return """
        张三走进了酒馆，看到了一个陌生的年轻人李四正在角落里独自饮酒。
        张三走过去主动搭话，两人很快成为了朋友。
        李四告诉张三，他刚刚获得了一把神秘的宝剑"龙泉剑"。
        """
    
    @pytest.fixture
    def sample_ai_response(self):
        """示例AI响应"""
        return json.dumps([
            {
                "event": "create_entity",
                "data": {
                    "name": "李四",
                    "type": "character",
                    "description": "年轻的剑客",
                    "properties": {"年龄": 25},
                    "first_mentioned_chapter": 1
                }
            },
            {
                "event": "create_entity",
                "data": {
                    "name": "龙泉剑",
                    "type": "item",
                    "description": "神秘的宝剑",
                    "properties": {"品质": "传说"},
                    "first_mentioned_chapter": 1
                }
            },
            {
                "event": "create_relationship",
                "data": {
                    "source_entity": "张三",
                    "target_entity": "李四",
                    "relationship_type": "朋友",
                    "description": "在酒馆中结识的朋友",
                    "properties": {"友谊程度": "初识"},
                    "established_chapter": 1
                }
            },
            {
                "event": "create_relationship",
                "data": {
                    "source_entity": "李四",
                    "target_entity": "龙泉剑",
                    "relationship_type": "持有",
                    "description": "李四拥有龙泉剑",
                    "properties": {"获得方式": "未知"},
                    "established_chapter": 1
                }
            }
        ])
    
    def test_event_processor_initialization(self, event_processor):
        """测试事件处理器初始化"""
        assert event_processor is not None
        assert len(event_processor.supported_events) == 4
        assert "create_entity" in event_processor.supported_events
        assert "create_relationship" in event_processor.supported_events
    
    def test_build_extraction_prompt(self, event_processor, sample_chapter_text):
        """测试提示词构建"""
        prompt = event_processor._build_extraction_prompt(
            sample_chapter_text, "test_story_001", 1
        )
        
        assert "test_story_001" in prompt
        assert "章节编号: 1" in prompt
        assert sample_chapter_text in prompt
        assert "JSON数组格式" in prompt
        assert "create_entity" in prompt
        assert "create_relationship" in prompt
    
    @pytest.mark.asyncio
    async def test_extract_events_from_chapter_success(
        self, 
        event_processor, 
        sample_chapter_text, 
        sample_ai_response
    ):
        """测试成功提取事件"""
        # Mock智谱AI响应
        mock_response = ChatCompletionResponse(
            id="test_id",
            object="chat.completion",
            created=1234567890,
            model="glm-4.5-flash",
            choices=[{
                "message": {"content": sample_ai_response},
                "finish_reason": "stop"
            }],
            usage={"total_tokens": 100}
        )
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            events = await event_processor.extract_events_from_chapter(
                sample_chapter_text, "test_story_001", 1
            )
            
            assert len(events) == 4
            assert events[0].event == "create_entity"
            assert events[0].data["name"] == "李四"
            assert events[1].event == "create_entity"
            assert events[1].data["name"] == "龙泉剑"
            assert events[2].event == "create_relationship"
            assert events[2].data["source_entity"] == "张三"
            assert events[3].event == "create_relationship"
            assert events[3].data["target_entity"] == "龙泉剑"
    
    @pytest.mark.asyncio
    async def test_extract_events_invalid_json(self, event_processor, sample_chapter_text):
        """测试AI返回无效JSON的情况"""
        # Mock智谱AI返回无效JSON
        mock_response = ChatCompletionResponse(
            id="test_id",
            object="chat.completion",
            created=1234567890,
            model="glm-4.5-flash",
            choices=[{
                "message": {"content": "这不是有效的JSON"},
                "finish_reason": "stop"
            }],
            usage={"total_tokens": 50}
        )
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            events = await event_processor.extract_events_from_chapter(
                sample_chapter_text, "test_story_001", 1
            )
            
            assert len(events) == 0
    
    @pytest.mark.asyncio
    async def test_extract_events_empty_response(self, event_processor, sample_chapter_text):
        """测试AI返回空数组的情况"""
        # Mock智谱AI返回空数组
        mock_response = ChatCompletionResponse(
            id="test_id",
            object="chat.completion",
            created=1234567890,
            model="glm-4.5-flash",
            choices=[{
                "message": {"content": "[]"},
                "finish_reason": "stop"
            }],
            usage={"total_tokens": 30}
        )
        
        with patch('app.core.event_processor.get_zhipu_client') as mock_get_client:
            mock_client = AsyncMock()
            mock_client.chat_completion.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            events = await event_processor.extract_events_from_chapter(
                sample_chapter_text, "test_story_001", 1
            )
            
            assert len(events) == 0
    
    def test_create_entity_event_data_validation(self):
        """测试创建实体事件数据验证"""
        # 有效数据
        valid_data = {
            "name": "测试角色",
            "type": "character",
            "description": "测试描述",
            "properties": {"年龄": 20},
            "first_mentioned_chapter": 1
        }
        
        event_data = CreateEntityEventData(**valid_data)
        assert event_data.name == "测试角色"
        assert event_data.type == "character"
        assert event_data.properties["年龄"] == 20
        
        # 最小有效数据
        minimal_data = {
            "name": "最小角色",
            "type": "character"
        }
        
        minimal_event_data = CreateEntityEventData(**minimal_data)
        assert minimal_event_data.name == "最小角色"
        assert minimal_event_data.description is None
        assert minimal_event_data.properties == {}
    
    def test_create_relationship_event_data_validation(self):
        """测试创建关系事件数据验证"""
        # 有效数据
        valid_data = {
            "source_entity": "张三",
            "target_entity": "李四",
            "relationship_type": "朋友",
            "description": "好朋友",
            "properties": {"友谊程度": "深厚"},
            "established_chapter": 1
        }
        
        event_data = CreateRelationshipEventData(**valid_data)
        assert event_data.source_entity == "张三"
        assert event_data.target_entity == "李四"
        assert event_data.relationship_type == "朋友"
        assert event_data.properties["友谊程度"] == "深厚"
    
    def test_world_event_validation(self):
        """测试世界事件验证"""
        # 有效事件
        valid_event_data = {
            "event": "create_entity",
            "data": {
                "name": "测试实体",
                "type": "character"
            }
        }
        
        event = WorldEvent(**valid_event_data)
        assert event.event == "create_entity"
        assert event.data["name"] == "测试实体"
    
    @pytest.mark.asyncio
    async def test_get_event_processor_singleton(self):
        """测试事件处理器单例模式"""
        processor1 = await get_event_processor()
        processor2 = await get_event_processor()
        
        assert processor1 is processor2
        assert isinstance(processor1, EventProcessor)
