"""
🌟 [性格] 性格驱动系统
基于十二星座和性格特征，为角色行为和对话注入个性化特色
"""

import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

from app.core.config import log_info, log_debug, log_error, log_success


class ZodiacSign(str, Enum):
    """十二星座枚举"""
    ARIES = "白羊座"          # 3.21-4.19
    TAURUS = "金牛座"         # 4.20-5.20
    GEMINI = "双子座"         # 5.21-6.21
    CANCER = "巨蟹座"         # 6.22-7.22
    LEO = "狮子座"           # 7.23-8.22
    VIRGO = "处女座"          # 8.23-9.22
    LIBRA = "天秤座"          # 9.23-10.23
    SCORPIO = "天蝎座"        # 10.24-11.22
    SAGITTARIUS = "射手座"     # 11.23-12.21
    CAPRICORN = "摩羯座"       # 12.22-1.19
    AQUARIUS = "水瓶座"        # 1.20-2.18
    PISCES = "双鱼座"         # 2.19-3.20


class PersonalityTrait(str, Enum):
    """性格特征枚举"""
    EXTROVERT = "外向"
    INTROVERT = "内向"
    OPTIMISTIC = "乐观"
    PESSIMISTIC = "悲观"
    RATIONAL = "理性"
    EMOTIONAL = "感性"
    AGGRESSIVE = "激进"
    CONSERVATIVE = "保守"
    CREATIVE = "创造性"
    PRACTICAL = "实用性"


@dataclass
class PersonalityProfile:
    """性格档案"""
    zodiac_sign: ZodiacSign
    primary_traits: List[PersonalityTrait]
    behavioral_patterns: List[str]
    speech_patterns: List[str]
    decision_making_style: str
    emotional_responses: Dict[str, List[str]]
    conflict_handling: List[str]
    motivation_drivers: List[str]


@dataclass
class PersonalityDrivenResult:
    """性格驱动结果"""
    success: bool
    original_text: str
    personality_driven_text: str
    applied_personality: PersonalityProfile
    modifications: List[Dict[str, Any]]
    personality_intensity: float
    processing_details: Dict[str, Any]


class PersonalityDriverSystem:
    """
    🌟 [性格驱动] 性格驱动系统
    
    基于十二星座和性格特征，为角色的行为、对话、决策等
    注入个性化特色，让角色更加立体生动。
    """
    
    def __init__(self):
        """初始化性格驱动系统"""
        # 十二星座性格档案
        self.zodiac_profiles = {
            ZodiacSign.ARIES: PersonalityProfile(
                zodiac_sign=ZodiacSign.ARIES,
                primary_traits=[PersonalityTrait.EXTROVERT, PersonalityTrait.AGGRESSIVE, PersonalityTrait.OPTIMISTIC],
                behavioral_patterns=[
                    "行动迅速，不喜欢拖延", "喜欢冒险和挑战", "容易冲动，后果考虑不周",
                    "领导欲强，喜欢主导局面", "直来直去，不喜欢绕弯子"
                ],
                speech_patterns=[
                    "语气直接有力", "经常使用感叹句", "喜欢用'马上'、'立刻'等词",
                    "说话节奏快", "容易打断别人"
                ],
                decision_making_style="快速决策，凭直觉行动",
                emotional_responses={
                    "愤怒": ["火冒三丈", "当场爆发", "直接对抗"],
                    "高兴": ["兴奋不已", "大声表达", "立即行动"],
                    "悲伤": ["短暂低落", "很快振作", "寻找新目标"]
                },
                conflict_handling=["正面对抗", "据理力争", "不轻易妥协"],
                motivation_drivers=["成就感", "竞争", "新挑战", "领导地位"]
            ),
            
            ZodiacSign.TAURUS: PersonalityProfile(
                zodiac_sign=ZodiacSign.TAURUS,
                primary_traits=[PersonalityTrait.CONSERVATIVE, PersonalityTrait.PRACTICAL, PersonalityTrait.INTROVERT],
                behavioral_patterns=[
                    "行动缓慢但坚定", "喜欢稳定和安全", "固执己见，难以改变",
                    "注重物质享受", "有很强的耐心"
                ],
                speech_patterns=[
                    "语速较慢，深思熟虑", "喜欢用'稳妥'、'安全'等词", "很少使用夸张表达",
                    "说话有条理", "不轻易表态"
                ],
                decision_making_style="深思熟虑，谨慎决策",
                emotional_responses={
                    "愤怒": ["闷在心里", "长时间记仇", "爆发时很可怕"],
                    "高兴": ["内敛表达", "默默享受", "分享给亲近的人"],
                    "悲伤": ["长期低落", "需要时间恢复", "寻求安慰"]
                },
                conflict_handling=["避免冲突", "坚持立场", "寻求妥协"],
                motivation_drivers=["安全感", "物质保障", "稳定关系", "舒适生活"]
            ),
            
            ZodiacSign.GEMINI: PersonalityProfile(
                zodiac_sign=ZodiacSign.GEMINI,
                primary_traits=[PersonalityTrait.EXTROVERT, PersonalityTrait.CREATIVE, PersonalityTrait.RATIONAL],
                behavioral_patterns=[
                    "善变多样，兴趣广泛", "好奇心强，喜欢学习", "社交能力强",
                    "注意力容易分散", "适应能力强"
                ],
                speech_patterns=[
                    "话题跳跃性强", "语言幽默风趣", "喜欢用比喻和类比",
                    "说话速度快", "经常引用各种知识"
                ],
                decision_making_style="快速分析，灵活调整",
                emotional_responses={
                    "愤怒": ["口头表达", "很快平息", "理性分析"],
                    "高兴": ["分享给所有人", "表达生动", "感染他人"],
                    "悲伤": ["寻求倾诉", "理性分析原因", "转移注意力"]
                },
                conflict_handling=["理性沟通", "寻找多种解决方案", "避免情绪化"],
                motivation_drivers=["新知识", "社交认可", "智力挑战", "变化刺激"]
            ),
            
            ZodiacSign.CANCER: PersonalityProfile(
                zodiac_sign=ZodiacSign.CANCER,
                primary_traits=[PersonalityTrait.EMOTIONAL, PersonalityTrait.INTROVERT, PersonalityTrait.CONSERVATIVE],
                behavioral_patterns=[
                    "情感丰富，敏感细腻", "重视家庭和亲情", "有很强的保护欲",
                    "容易受环境影响", "记忆力强，念旧"
                ],
                speech_patterns=[
                    "语气温和体贴", "经常关心他人", "喜欢用'家'、'温暖'等词",
                    "表达含蓄", "容易情绪化"
                ],
                decision_making_style="情感导向，考虑他人感受",
                emotional_responses={
                    "愤怒": ["默默生气", "冷战处理", "需要时间平复"],
                    "高兴": ["温暖表达", "与亲近的人分享", "创造美好回忆"],
                    "悲伤": ["深度沉浸", "需要安慰", "长时间恢复"]
                },
                conflict_handling=["回避冲突", "情感沟通", "寻求理解"],
                motivation_drivers=["情感满足", "家庭和谐", "安全感", "被需要的感觉"]
            ),
            
            ZodiacSign.LEO: PersonalityProfile(
                zodiac_sign=ZodiacSign.LEO,
                primary_traits=[PersonalityTrait.EXTROVERT, PersonalityTrait.CREATIVE, PersonalityTrait.OPTIMISTIC],
                behavioral_patterns=[
                    "自信张扬，喜欢表现", "有强烈的表演欲", "慷慨大方",
                    "喜欢被赞美和关注", "有领导才能"
                ],
                speech_patterns=[
                    "语气自信有力", "喜欢用夸张表达", "经常用'我'开头",
                    "声音洪亮", "喜欢讲故事"
                ],
                decision_making_style="自信决策，相信直觉",
                emotional_responses={
                    "愤怒": ["戏剧化表达", "声势浩大", "需要观众"],
                    "高兴": ["大张旗鼓庆祝", "与所有人分享", "成为焦点"],
                    "悲伤": ["戏剧化表现", "需要安慰和关注", "很快恢复"]
                },
                conflict_handling=["正面对抗", "展示实力", "寻求胜利"],
                motivation_drivers=["赞美认可", "成为焦点", "创造性表达", "领导地位"]
            ),
            
            ZodiacSign.VIRGO: PersonalityProfile(
                zodiac_sign=ZodiacSign.VIRGO,
                primary_traits=[PersonalityTrait.RATIONAL, PersonalityTrait.PRACTICAL, PersonalityTrait.INTROVERT],
                behavioral_patterns=[
                    "追求完美，注重细节", "有很强的分析能力", "喜欢帮助他人",
                    "有洁癖和强迫症倾向", "工作认真负责"
                ],
                speech_patterns=[
                    "逻辑清晰，条理分明", "喜欢用数据和事实", "经常纠正他人",
                    "语言精确", "避免夸张表达"
                ],
                decision_making_style="理性分析，追求最优解",
                emotional_responses={
                    "愤怒": ["内心批评", "挑剔指责", "冷静分析"],
                    "高兴": ["内敛表达", "关注细节", "与亲近的人分享"],
                    "悲伤": ["自我反省", "寻找原因", "默默承受"]
                },
                conflict_handling=["理性分析", "寻找事实", "提供建设性意见"],
                motivation_drivers=["完美主义", "帮助他人", "知识学习", "秩序感"]
            )
        }
        
        # 为了简化，这里只定义了6个星座，实际应用中应该包含全部12个
        
        log_info("性格", "性格驱动系统初始化完成",
                星座档案数=len(self.zodiac_profiles),
                性格特征数=len(PersonalityTrait))
    
    async def apply_personality_drive(
        self,
        text: str,
        zodiac_sign: Optional[ZodiacSign] = None,
        custom_traits: Optional[List[PersonalityTrait]] = None,
        intensity: float = 0.5
    ) -> PersonalityDrivenResult:
        """
        🌟 [性格驱动] 为文本应用性格驱动
        
        Args:
            text: 原始文本
            zodiac_sign: 指定星座（可选，不指定则随机选择）
            custom_traits: 自定义性格特征（可选）
            intensity: 性格驱动强度 (0.0-1.0)
            
        Returns:
            PersonalityDrivenResult: 性格驱动结果
        """
        log_info("性格", "开始应用性格驱动",
                文本长度=len(text),
                指定星座=zodiac_sign.value if zodiac_sign else "随机",
                驱动强度=intensity)
        
        try:
            # 选择性格档案
            if zodiac_sign and zodiac_sign in self.zodiac_profiles:
                personality = self.zodiac_profiles[zodiac_sign]
            else:
                # 随机选择一个星座
                personality = random.choice(list(self.zodiac_profiles.values()))
            
            # 如果有自定义特征，则修改性格档案
            if custom_traits:
                personality.primary_traits = custom_traits
            
            # 应用性格驱动
            driven_text = text
            modifications = []
            
            # 1. 语言风格调整
            driven_text, speech_mods = await self._apply_speech_patterns(
                driven_text, personality, intensity
            )
            modifications.extend(speech_mods)
            
            # 2. 情感表达调整
            driven_text, emotion_mods = await self._apply_emotional_responses(
                driven_text, personality, intensity
            )
            modifications.extend(emotion_mods)
            
            # 3. 行为模式调整
            driven_text, behavior_mods = await self._apply_behavioral_patterns(
                driven_text, personality, intensity
            )
            modifications.extend(behavior_mods)
            
            # 计算性格强度
            personality_intensity = len(modifications) * intensity / 10  # 归一化
            
            log_success("性格", "性格驱动应用完成",
                       应用星座=personality.zodiac_sign.value,
                       修改数量=len(modifications),
                       性格强度=f"{personality_intensity:.2f}")
            
            return PersonalityDrivenResult(
                success=len(modifications) > 0,
                original_text=text,
                personality_driven_text=driven_text,
                applied_personality=personality,
                modifications=modifications,
                personality_intensity=personality_intensity,
                processing_details={
                    "zodiac": personality.zodiac_sign.value,
                    "traits": [trait.value for trait in personality.primary_traits],
                    "modifications": len(modifications)
                }
            )
            
        except Exception as e:
            log_error("性格", "性格驱动应用失败", error=e)
            return PersonalityDrivenResult(
                success=False,
                original_text=text,
                personality_driven_text=text,
                applied_personality=None,
                modifications=[],
                personality_intensity=0.0,
                processing_details={"error": str(e)}
            )

    async def _apply_speech_patterns(
        self,
        text: str,
        personality: PersonalityProfile,
        intensity: float
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """应用语言模式"""
        modified_text = text
        modifications = []

        # 根据星座调整语言风格
        if personality.zodiac_sign == ZodiacSign.ARIES:
            # 白羊座：直接有力
            if random.random() < intensity:
                modified_text = modified_text.replace("可能", "肯定")
                modified_text = modified_text.replace("或许", "一定")
                modifications.append({
                    "type": "语言风格",
                    "change": "白羊座直接表达",
                    "pattern": "确定性语言"
                })

        elif personality.zodiac_sign == ZodiacSign.TAURUS:
            # 金牛座：稳重谨慎
            if random.random() < intensity:
                modified_text = modified_text.replace("马上", "慢慢")
                modified_text = modified_text.replace("立刻", "稳妥地")
                modifications.append({
                    "type": "语言风格",
                    "change": "金牛座稳重表达",
                    "pattern": "谨慎性语言"
                })

        elif personality.zodiac_sign == ZodiacSign.GEMINI:
            # 双子座：机智幽默
            if random.random() < intensity:
                # 添加比喻和类比
                sentences = text.split('。')
                for i, sentence in enumerate(sentences):
                    if sentence.strip() and random.random() < 0.3:
                        sentences[i] = sentence + "，就像..."
                modified_text = '。'.join(sentences)
                modifications.append({
                    "type": "语言风格",
                    "change": "双子座机智表达",
                    "pattern": "比喻类比"
                })

        elif personality.zodiac_sign == ZodiacSign.CANCER:
            # 巨蟹座：温和体贴
            if random.random() < intensity:
                modified_text = modified_text.replace("你", "你们")
                modified_text = modified_text.replace("我觉得", "我们觉得")
                modifications.append({
                    "type": "语言风格",
                    "change": "巨蟹座温和表达",
                    "pattern": "包容性语言"
                })

        elif personality.zodiac_sign == ZodiacSign.LEO:
            # 狮子座：自信张扬
            if random.random() < intensity:
                modified_text = modified_text.replace("我想", "我确信")
                modified_text = modified_text.replace("也许", "当然")
                modifications.append({
                    "type": "语言风格",
                    "change": "狮子座自信表达",
                    "pattern": "自信性语言"
                })

        elif personality.zodiac_sign == ZodiacSign.VIRGO:
            # 处女座：精确理性
            if random.random() < intensity:
                modified_text = modified_text.replace("很多", "大约X个")
                modified_text = modified_text.replace("经常", "频率约为Y%")
                modifications.append({
                    "type": "语言风格",
                    "change": "处女座精确表达",
                    "pattern": "数据化语言"
                })

        return modified_text, modifications

    async def _apply_emotional_responses(
        self,
        text: str,
        personality: PersonalityProfile,
        intensity: float
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """应用情感反应模式"""
        modified_text = text
        modifications = []

        # 检测情感词汇并应用性格化反应
        emotion_words = {
            "愤怒": ["生气", "愤怒", "恼火", "气愤"],
            "高兴": ["开心", "快乐", "兴奋", "愉快"],
            "悲伤": ["难过", "伤心", "痛苦", "悲伤"]
        }

        for emotion, words in emotion_words.items():
            for word in words:
                if word in text and random.random() < intensity:
                    if emotion in personality.emotional_responses:
                        responses = personality.emotional_responses[emotion]
                        new_response = random.choice(responses)
                        modified_text = modified_text.replace(word, new_response, 1)
                        modifications.append({
                            "type": "情感反应",
                            "original_emotion": word,
                            "personality_response": new_response,
                            "zodiac": personality.zodiac_sign.value
                        })

        return modified_text, modifications

    async def _apply_behavioral_patterns(
        self,
        text: str,
        personality: PersonalityProfile,
        intensity: float
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """应用行为模式"""
        modified_text = text
        modifications = []

        # 根据行为模式调整文本
        for pattern in personality.behavioral_patterns:
            if random.random() < intensity * 0.3:
                # 简单的行为模式注入
                if "迅速" in pattern and "快" not in modified_text:
                    modified_text = modified_text.replace("然后", "然后迅速")
                    modifications.append({
                        "type": "行为模式",
                        "pattern": pattern,
                        "modification": "添加迅速行动"
                    })
                elif "谨慎" in pattern and "小心" not in modified_text:
                    modified_text = modified_text.replace("决定", "谨慎决定")
                    modifications.append({
                        "type": "行为模式",
                        "pattern": pattern,
                        "modification": "添加谨慎态度"
                    })

        return modified_text, modifications

    def get_zodiac_by_date(self, birth_date: datetime) -> ZodiacSign:
        """
        🗓️ [星座计算] 根据出生日期计算星座

        Args:
            birth_date: 出生日期

        Returns:
            ZodiacSign: 对应的星座
        """
        month = birth_date.month
        day = birth_date.day

        if (month == 3 and day >= 21) or (month == 4 and day <= 19):
            return ZodiacSign.ARIES
        elif (month == 4 and day >= 20) or (month == 5 and day <= 20):
            return ZodiacSign.TAURUS
        elif (month == 5 and day >= 21) or (month == 6 and day <= 21):
            return ZodiacSign.GEMINI
        elif (month == 6 and day >= 22) or (month == 7 and day <= 22):
            return ZodiacSign.CANCER
        elif (month == 7 and day >= 23) or (month == 8 and day <= 22):
            return ZodiacSign.LEO
        elif (month == 8 and day >= 23) or (month == 9 and day <= 22):
            return ZodiacSign.VIRGO
        elif (month == 9 and day >= 23) or (month == 10 and day <= 23):
            return ZodiacSign.LIBRA
        elif (month == 10 and day >= 24) or (month == 11 and day <= 22):
            return ZodiacSign.SCORPIO
        elif (month == 11 and day >= 23) or (month == 12 and day <= 21):
            return ZodiacSign.SAGITTARIUS
        elif (month == 12 and day >= 22) or (month == 1 and day <= 19):
            return ZodiacSign.CAPRICORN
        elif (month == 1 and day >= 20) or (month == 2 and day <= 18):
            return ZodiacSign.AQUARIUS
        else:  # (month == 2 and day >= 19) or (month == 3 and day <= 20)
            return ZodiacSign.PISCES


# ==================== 工厂函数 ====================

def create_personality_driver() -> PersonalityDriverSystem:
    """
    🏭 [工厂] 创建性格驱动系统实例

    Returns:
        PersonalityDriverSystem: 性格驱动系统实例
    """
    return PersonalityDriverSystem()


async def quick_personality_drive(
    text: str,
    zodiac_sign: Optional[ZodiacSign] = None,
    intensity: float = 0.5
) -> PersonalityDrivenResult:
    """
    ⚡ [快速接口] 快速性格驱动

    Args:
        text: 原始文本
        zodiac_sign: 星座（可选）
        intensity: 驱动强度

    Returns:
        PersonalityDrivenResult: 驱动结果
    """
    driver = create_personality_driver()
    return await driver.apply_personality_drive(text, zodiac_sign, None, intensity)
