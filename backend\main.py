"""
🏗️ [系统] 文心小说后端服务系统主应用入口
FastAPI应用的主入口文件，包含应用初始化、路由注册和中间件配置
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from contextlib import asynccontextmanager
import uvicorn
from typing import AsyncGenerator

from app.core.config import settings, setup_logging, log_info, log_error
from app.core.database import init_database, close_database
from app.routers import generation


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    🏗️ [系统] 应用生命周期管理器
    在应用启动和关闭时执行相应的初始化和清理操作
    """
    # 启动时执行
    log_info("系统", "应用正在启动", 版本="0.1.0", 环境="开发")
    try:
        # 设置日志系统
        setup_logging()
        log_info("系统", "日志系统初始化完成")

        # 初始化数据库
        await init_database()
        log_info("系统", "数据库初始化完成")
        
        log_info("系统", "应用启动完成", 
                主机=settings.api_host, 
                端口=settings.api_port,
                默认AI提供商=settings.default_ai_provider)
        
        yield  # 应用运行期间
        
    except Exception as e:
        log_error("系统", "应用启动失败", error=e)
        raise
    finally:
        # 关闭时执行
        log_info("系统", "应用正在关闭")
        await close_database()
        log_info("系统", "数据库连接已关闭")


def create_app() -> FastAPI:
    """
    🏗️ [系统] 创建并配置FastAPI应用实例
    """
    app = FastAPI(
        title="文心小说后端服务系统",
        description="基于FastAPI的AI小说生成后端服务，支持智谱AI和Kimi AI多AI服务架构",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan
    )
    
    # 🌐 [API] 配置CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins_list,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )
    
    # 📝 [生成] 注册业务路由
    app.include_router(generation.router)

    # 🧬 [情感基因] 注册情感基因库路由
    from app.routers import emotion_genes
    app.include_router(emotion_genes.router)

    # 🧠 [记忆] 注册记忆嵌入路由
    from app.routers import memory
    app.include_router(memory.router)

    # 🌐 [世界图谱] 注册世界知识图谱路由
    from app.routers import world_graph
    app.include_router(world_graph.router)

    # 🎭 [剧情分析] 注册剧情分析和推荐路由
    from app.routers import plot_analysis
    app.include_router(plot_analysis.router)

    # ⚔️ [冲突检测] 注册冲突检测路由
    from app.routers import conflict_detection, plot_planning, relationship_analysis
    app.include_router(conflict_detection.router)
    app.include_router(plot_planning.router)
    app.include_router(relationship_analysis.router)

    # 🎬 [编排] 注册AI智能体编排路由
    from app.routers import agent_orchestration
    app.include_router(agent_orchestration.router)

    # 🎨 [文学风格] 注册文学风格增强路由
    from app.routers import literary_style
    app.include_router(literary_style.router)

    # 📚 [语料库管理] 注册语料库管理路由
    from app.routers import corpus_management
    app.include_router(corpus_management.router)

    return app


# 创建应用实例
app = create_app()


@app.get("/health", summary="健康检查", tags=["系统"])
async def health_check():
    """
    🔧 [调试] 健康检查端点
    用于验证服务是否正常运行，返回系统状态信息
    """
    log_info("系统", "收到健康检查请求")
    
    return JSONResponse(
        status_code=200,
        content={
            "status": "healthy",
            "message": "文心小说后端服务运行正常",
            "version": "0.1.0",
            "service": "wenxin-novel-backend",
            "ai_providers": {
                "default": settings.default_ai_provider,
                "available": ["zhipu", "kimi"]
            },
            "timestamp": None  # 这里可以添加当前时间戳
        }
    )


@app.get("/", summary="根路径", tags=["系统"])
async def root():
    """
    🎨 [UI] 根路径端点
    提供API服务的基本信息和欢迎消息
    """
    log_info("系统", "收到根路径访问请求")
    
    return JSONResponse(
        content={
            "message": "欢迎使用文心小说后端服务系统",
            "service": "wenxin-novel-backend",
            "version": "0.1.0",
            "docs": "/docs",
            "health": "/health",
            "description": "基于FastAPI的AI小说生成后端服务"
        }
    )


if __name__ == "__main__":
    """
    🚀 [启动] 开发环境直接运行入口
    """
    log_info("系统", "使用开发模式启动应用")
    
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_reload,
        log_level=settings.log_level.lower()
    )