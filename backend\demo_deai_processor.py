"""
🎭 [演示] 去AI化处理器演示脚本

展示去AI化处理器的完整功能，包括：
1. AI痕迹检测和分析
2. 自适应处理强度调节
3. 多模块协同去AI化处理
4. 处理前后效果对比

运行方式: python demo_deai_processor.py

作者: 文心小说后端服务系统
创建时间: 2025-08-04
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.deai_processor import (
    create_deai_processor,
    analyze_ai_traces,
    ProcessingMode,
    AITraceLevel
)
from app.core.config import setup_logging


def print_separator(title: str):
    """打印分隔线"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)


def print_analysis_result(title: str, analysis: dict):
    """打印分析结果"""
    print(f"\n📊 {title}")
    print(f"   AI痕迹百分比: {analysis['ai_percentage']:.1f}%")
    print(f"   AI痕迹等级: {analysis['ai_level'].value}")
    print(f"   AI特征匹配数: {analysis['total_ai_matches']}")
    print(f"   人性化特征数: {analysis['total_human_matches']}")
    print(f"   评估结果: {analysis['assessment']}")
    
    if analysis['ai_matches_by_category']:
        print("   AI特征分布:")
        for category, count in analysis['ai_matches_by_category'].items():
            if count > 0:
                print(f"     - {category}: {count}")


def print_processing_result(result):
    """打印处理结果"""
    print(f"\n✨ 去AI化处理结果:")
    print(f"   AI痕迹降低: {result.ai_reduction_percentage:.1f}%")
    print(f"   处理前AI痕迹: {result.ai_trace_before:.1f}%")
    print(f"   处理后AI痕迹: {result.ai_trace_after:.1f}%")
    print(f"   应用模块数: {len(result.modules_applied)}")
    print(f"   应用的模块: {', '.join(result.modules_applied)}")
    print(f"   质量评分: {result.quality_score:.2f}")
    print(f"   处理模式: {result.processing_mode.value}")


def demo_ai_trace_detection():
    """演示AI痕迹检测功能"""
    print_separator("AI痕迹检测演示")
    
    # 测试样本
    test_samples = [
        {
            "name": "典型AI生成文本",
            "text": """
            不仅如此，而且更重要的是，这个完美的解决方案毫无疑问地展现了绝对的优势。
            首先，它具有非常出色的性能；其次，它拥有十分完善的功能；最后，它提供了极其便利的操作体验。
            总的来说，这是一个无瑕的产品，完全满足了用户的所有需求。
            因此，我们可以毫不犹豫地说，这是一个完美的选择。
            """
        },
        {
            "name": "自然人类文本",
            "text": """
            嗯，这个东西好像还行吧。用起来感觉稍微有点别扭，不过大概能满足需求。
            或许是我还没习惯，总觉得有些地方怎么说呢...不太顺手。
            但整体来看，应该算是个不错的选择。
            """
        },
        {
            "name": "情感过度表达文本",
            "text": """
            他的心中涌起了无比复杂的情感，眼中闪烁着激动的光芒，脸上浮现出难以言喻的表情。
            内心充满了矛盾和挣扎，心情变得极其复杂，仿佛有千万种情绪在胸中翻腾。
            """
        }
    ]
    
    for sample in test_samples:
        analysis = analyze_ai_traces(sample["text"])
        print_analysis_result(sample["name"], analysis)


def demo_adaptive_processing():
    """演示自适应去AI化处理"""
    print_separator("自适应去AI化处理演示")
    
    # 创建处理器
    processor = create_deai_processor()
    
    # 高AI痕迹文本
    ai_text = """
    不仅如此，而且更重要的是，这个完美的解决方案毫无疑问地展现了绝对的优势。
    首先，它具有非常出色的性能，能够完全满足用户的所有需求；
    其次，它拥有十分完善的功能，提供了极其便利的操作体验；
    最后，它的设计非常精美，展现了无瑕的工艺水准。
    总的来说，这是一个绝对完美的产品，毫无疑问地超越了所有竞争对手。
    因此，我们可以毫不犹豫地说，这是市场上最优秀的选择。
    """
    
    print("📝 原始文本:")
    print(ai_text.strip())
    
    # 分析原始文本
    original_analysis = analyze_ai_traces(ai_text)
    print_analysis_result("原始文本分析", original_analysis)
    
    # 测试不同处理模式
    processing_modes = [
        ProcessingMode.CONSERVATIVE,
        ProcessingMode.BALANCED,
        ProcessingMode.AGGRESSIVE,
        ProcessingMode.ADAPTIVE
    ]
    
    for mode in processing_modes:
        print(f"\n🔧 使用{mode.value}模式处理:")
        
        result = processor.adaptive_deai_processing(
            text=ai_text,
            processing_mode=mode,
            preserve_meaning=True
        )
        
        print_processing_result(result)
        
        print(f"\n📝 处理后文本:")
        print(result.processed_text.strip())


def demo_emotion_based_processing():
    """演示基于情绪的去AI化处理"""
    print_separator("基于情绪的去AI化处理演示")
    
    processor = create_deai_processor()
    
    # 不同情绪的AI文本样本
    emotion_samples = [
        {
            "emotion": "愤怒",
            "text": """
            他非常愤怒地说道："这个完美的计划毫无疑问地展现了绝对的优势！"
            不仅如此，而且更重要的是，他的内心充满了极其复杂的情感。
            因此，他毫不犹豫地做出了这个完全正确的决定。
            """
        },
        {
            "emotion": "悲伤",
            "text": """
            她感到非常悲伤，心中涌起了无比复杂的情感，眼中闪烁着痛苦的光芒。
            不仅如此，而且她的内心充满了深深的失落感。
            总的来说，这是一个令人心碎的时刻，完全改变了她的人生轨迹。
            """
        },
        {
            "emotion": "焦虑",
            "text": """
            他感到极其焦虑和不安，心情变得非常复杂，内心充满了担忧。
            不仅如此，而且时间似乎过得非常缓慢，每一分钟都显得格外漫长。
            因此，他毫不犹豫地开始了这个完美的应对计划。
            """
        }
    ]
    
    for sample in emotion_samples:
        print(f"\n🎭 {sample['emotion']}情绪文本处理:")
        print(f"📝 原始文本: {sample['text'].strip()}")
        
        # 分析原始文本
        original_analysis = analyze_ai_traces(sample["text"])
        print(f"   原始AI痕迹: {original_analysis['ai_percentage']:.1f}%")
        
        # 自适应处理
        result = processor.adaptive_deai_processing(
            text=sample["text"],
            processing_mode=ProcessingMode.ADAPTIVE,
            preserve_meaning=True
        )
        
        print(f"   处理后AI痕迹: {result.ai_trace_after:.1f}%")
        print(f"   AI痕迹降低: {result.ai_reduction_percentage:.1f}%")
        print(f"   应用模块: {', '.join(result.modules_applied)}")
        print(f"📝 处理后文本: {result.processed_text.strip()}")


def demo_processing_intensity():
    """演示不同处理强度的效果"""
    print_separator("处理强度对比演示")
    
    processor = create_deai_processor()
    
    test_text = """
    不仅如此，而且更重要的是，这个完美的解决方案毫无疑问地展现了绝对的优势。
    首先，它具有非常出色的性能；其次，它拥有十分完善的功能。
    总的来说，这是一个无瑕的产品，完全满足了所有需求。
    """
    
    print("📝 原始文本:")
    print(test_text.strip())
    
    # 分析原始文本
    original_analysis = analyze_ai_traces(test_text)
    print(f"   原始AI痕迹: {original_analysis['ai_percentage']:.1f}%")
    
    # 测试不同强度
    intensities = [0.3, 0.5, 0.7, 0.9]
    
    for intensity in intensities:
        print(f"\n🎚️ 处理强度 {intensity}:")
        
        result = processor.adaptive_deai_processing(
            text=test_text,
            processing_mode=ProcessingMode.BALANCED,
            custom_intensity=intensity,
            preserve_meaning=True
        )
        
        print(f"   AI痕迹降低: {result.ai_reduction_percentage:.1f}%")
        print(f"   应用模块数: {len(result.modules_applied)}")
        print(f"   质量评分: {result.quality_score:.2f}")
        print(f"   文本长度变化: {len(result.processed_text) - len(test_text):+d}")


def main():
    """主函数"""
    print("🤖 去AI化处理器演示程序")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    
    try:
        # 演示AI痕迹检测
        demo_ai_trace_detection()
        
        # 演示自适应处理
        demo_adaptive_processing()
        
        # 演示基于情绪的处理
        demo_emotion_based_processing()
        
        # 演示处理强度对比
        demo_processing_intensity()
        
        print_separator("演示完成")
        print("✅ 所有演示已完成！去AI化处理器运行正常。")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
