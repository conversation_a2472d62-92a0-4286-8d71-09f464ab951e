"""
🧪 [测试] 剧情推荐引擎测试
测试智能剧情推荐引擎的核心功能
"""

import pytest
from datetime import datetime
from typing import List

from app.core.plot_recommendation import (
    PlotRecommendationEngine,
    PlotPattern,
    PlotPatternType,
    RecommendationPriority,
    StoryAnalysis,
    create_plot_recommendation_engine
)
from app.schemas.world_graph import EntityResponse, RelationshipResponse, WorldGraphResponse
from app.models.world_graph import EntityType, RelationshipStatus, RelationshipTypes


@pytest.fixture
def sample_world_graph() -> WorldGraphResponse:
    """创建示例世界知识图谱数据"""
    entities = [
        EntityResponse(
            id="char-001",
            name="张三",
            type=EntityType.CHARACTER,
            description="主角，年轻的剑客",
            properties={"age": 25, "weapon": "长剑"},
            importance_score=0.9,
            first_mentioned_chapter=1,
            last_mentioned_chapter=5,
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        EntityResponse(
            id="char-002",
            name="李四",
            type=EntityType.CHARACTER,
            description="反派，邪恶的法师",
            properties={"age": 40, "magic": "黑魔法"},
            importance_score=0.8,
            first_mentioned_chapter=2,
            last_mentioned_chapter=5,
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        EntityResponse(
            id="char-003",
            name="王五",
            type=EntityType.CHARACTER,
            description="配角，张三的朋友",
            properties={"age": 23, "skill": "弓箭"},
            importance_score=0.5,
            first_mentioned_chapter=1,
            last_mentioned_chapter=3,
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        EntityResponse(
            id="item-001",
            name="神秘宝剑",
            type=EntityType.ITEM,
            description="传说中的神器",
            properties={"power": "极高", "origin": "古代"},
            importance_score=0.7,
            first_mentioned_chapter=3,
            last_mentioned_chapter=5,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    ]
    
    relationships = [
        RelationshipResponse(
            id="rel-001",
            source_entity_id="char-001",
            target_entity_id="char-002",
            relationship_type=RelationshipTypes.ENEMY,
            strength=0.8,
            status=RelationshipStatus.ACTIVE,
            description="张三与李四是宿敌",
            established_chapter=2,
            last_updated_chapter=5,
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        RelationshipResponse(
            id="rel-002",
            source_entity_id="char-001",
            target_entity_id="char-003",
            relationship_type=RelationshipTypes.FRIEND,
            strength=0.7,
            status=RelationshipStatus.ACTIVE,
            description="张三与王五是好友",
            established_chapter=1,
            last_updated_chapter=4,
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        RelationshipResponse(
            id="rel-003",
            source_entity_id="char-001",
            target_entity_id="item-001",
            relationship_type=RelationshipTypes.OWNS,
            strength=0.9,
            status=RelationshipStatus.ACTIVE,
            description="张三拥有神秘宝剑",
            established_chapter=3,
            last_updated_chapter=5,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    ]
    
    return WorldGraphResponse(
        story_id="test-story-001",
        entities=entities,
        relationships=relationships,
        total_entities=len(entities),
        total_relationships=len(relationships),
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def plot_engine() -> PlotRecommendationEngine:
    """创建剧情推荐引擎实例"""
    return create_plot_recommendation_engine()


class TestPlotRecommendationEngine:
    """剧情推荐引擎测试类"""
    
    def test_engine_initialization(self, plot_engine: PlotRecommendationEngine):
        """测试引擎初始化"""
        assert plot_engine is not None
        assert len(plot_engine.plot_patterns) > 0
        
        # 验证包含基本的剧情模式
        pattern_types = [p.pattern_type for p in plot_engine.plot_patterns]
        assert PlotPatternType.CONFLICT_ESCALATION in pattern_types
        assert PlotPatternType.RELATIONSHIP_CHANGE in pattern_types
        assert PlotPatternType.CHARACTER_GROWTH in pattern_types
    
    @pytest.mark.asyncio
    async def test_story_analysis(
        self, 
        plot_engine: PlotRecommendationEngine,
        sample_world_graph: WorldGraphResponse
    ):
        """测试故事状态分析"""
        analysis = await plot_engine.analyze_story_state(
            world_graph=sample_world_graph,
            current_chapter=5
        )
        
        assert analysis is not None
        assert analysis.story_id == "test-story-001"
        assert analysis.current_chapter == 5
        
        # 验证角色分类
        assert len(analysis.main_characters) == 2  # 张三和李四
        assert len(analysis.supporting_characters) == 1  # 王五
        
        # 验证关系分类
        assert len(analysis.conflict_relationships) == 1  # 张三vs李四
        assert len(analysis.alliance_relationships) == 1  # 张三和王五
        
        # 验证评分范围
        assert 0.0 <= analysis.tension_level <= 1.0
        assert 0.0 <= analysis.plot_complexity <= 1.0
        assert 0.0 <= analysis.pacing_score <= 1.0
    
    @pytest.mark.asyncio
    async def test_recommendation_generation(
        self,
        plot_engine: PlotRecommendationEngine,
        sample_world_graph: WorldGraphResponse
    ):
        """测试推荐生成"""
        # 先分析故事状态
        analysis = await plot_engine.analyze_story_state(
            world_graph=sample_world_graph,
            current_chapter=5
        )
        
        # 生成推荐
        recommendations = await plot_engine.generate_recommendations(
            story_analysis=analysis,
            max_recommendations=3
        )
        
        assert recommendations is not None
        assert len(recommendations) <= 3
        
        if recommendations:
            # 验证推荐结构
            rec = recommendations[0]
            assert rec.id is not None
            assert rec.title is not None
            assert rec.description is not None
            assert rec.pattern_type in [p.value for p in PlotPatternType]
            assert rec.priority in [p.value for p in RecommendationPriority]
            
            # 验证评分范围
            assert 0.0 <= rec.feasibility_score <= 1.0
            assert 0.0 <= rec.dramatic_impact <= 1.0
            assert 0.0 <= rec.character_development <= 1.0
            assert 0.0 <= rec.plot_advancement <= 1.0
            assert 0.0 <= rec.overall_score <= 1.0
            
            # 验证推荐按评分排序
            for i in range(1, len(recommendations)):
                assert recommendations[i-1].overall_score >= recommendations[i].overall_score
    
    def test_tension_level_calculation(self, plot_engine: PlotRecommendationEngine):
        """测试张力水平计算"""
        # 创建测试关系
        conflict_rels = [
            type('MockRel', (), {'strength': 0.8})(),
            type('MockRel', (), {'strength': 0.6})()
        ]
        alliance_rels = [
            type('MockRel', (), {'strength': 0.5})()
        ]
        
        tension = plot_engine._calculate_tension_level(conflict_rels, alliance_rels)
        
        assert 0.0 <= tension <= 1.0
        assert tension > 0  # 有冲突应该有张力
    
    def test_pattern_applicability(
        self,
        plot_engine: PlotRecommendationEngine,
        sample_world_graph: WorldGraphResponse
    ):
        """测试剧情模式适用性检查"""
        # 创建简单的分析对象
        analysis = type('MockAnalysis', (), {
            'conflict_relationships': [type('MockRel', (), {})()],
            'active_relationships': [type('MockRel', (), {})()],
            'main_characters': [type('MockChar', (), {})(), type('MockChar', (), {})()],
            'alliance_relationships': [type('MockRel', (), {})()],
            'character_development_stage': {'char1': 0.5, 'char2': 0.3}
        })()
        
        # 测试冲突升级模式
        conflict_pattern = next(
            p for p in plot_engine.plot_patterns 
            if p.pattern_type == PlotPatternType.CONFLICT_ESCALATION
        )
        assert plot_engine._is_pattern_applicable(conflict_pattern, analysis)
        
        # 测试角色成长模式
        growth_pattern = next(
            p for p in plot_engine.plot_patterns 
            if p.pattern_type == PlotPatternType.CHARACTER_GROWTH
        )
        assert plot_engine._is_pattern_applicable(growth_pattern, analysis)
    
    @pytest.mark.asyncio
    async def test_conflict_escalation_recommendation(
        self,
        plot_engine: PlotRecommendationEngine,
        sample_world_graph: WorldGraphResponse
    ):
        """测试冲突升级推荐生成"""
        analysis = await plot_engine.analyze_story_state(
            world_graph=sample_world_graph,
            current_chapter=5
        )
        
        # 找到冲突升级模式
        conflict_pattern = next(
            p for p in plot_engine.plot_patterns 
            if p.pattern_type == PlotPatternType.CONFLICT_ESCALATION
        )
        
        # 生成推荐
        recommendation = await plot_engine._generate_conflict_escalation_recommendation(
            "test-rec-001",
            conflict_pattern,
            analysis
        )
        
        assert recommendation is not None
        assert "张三" in recommendation.title
        assert "李四" in recommendation.title
        assert "冲突" in recommendation.title
        assert len(recommendation.involved_entities) == 2
        assert len(recommendation.execution_steps) > 0


if __name__ == "__main__":
    """运行测试"""
    pytest.main([__file__, "-v"])
