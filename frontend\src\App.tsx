import { Routes, Route, Navigate } from 'react-router-dom';
import { useEffect } from 'react';
import { ROUTES } from './constants/routes';
import { useAuthStore, useStoryStore, useUIStore } from './stores';
import { ApiAdapter } from './services/apiAdapter';

// 导入页面组件
import { HomePage } from './pages/home/<USER>';
import { PromptStep } from './pages/prompt/page';
import { GeneratingPage } from './pages/generating/page';
import { BibleStep } from './pages/bible/page';
import { CreationCockpit } from './pages/cockpit/page';
import { ChapterReadingPage } from './pages/reading/page';
import ApiTestPage from './pages/api-test/page';

// 导入布局组件
import { AppLayout } from './app/layout';

console.log('🏗️ [App组件] 路由系统初始化...');

// 首页适配器组件
function HomePageAdapter() {
  const { isLoggedIn, login, logout, setLoading } = useAuthStore();
  const { isConnected, connectionLatency, setConnectionStatus } = useUIStore();

  const handleStartCreating = () => {
    console.log('📝 [首页] 开始创作');
    window.location.href = '/prompt';
  };

  const handleViewProjects = () => {
    console.log('📁 [首页] 查看项目');
    alert('项目管理功能正在开发中，敬请期待！');
  };

  const handleLogin = async () => {
    console.log('🔐 [首页] 开始登录');
    setLoading(true);
    try {
      const result = await ApiAdapter.loginWithFixedToken();
      if (result.success && result.token) {
        login(result.token);
        alert(`登录成功！\n\n${result.message}`);
      } else {
        alert(`登录失败：${result.message}`);
      }
    } catch (error) {
      alert('登录过程中发生错误，请稍后重试。');
      console.error('🔐 [首页] 登录异常:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    console.log('🔐 [首页] 登出');
    logout();
    alert('已成功登出！');
  };

  // 初始化连接状态检查
  useEffect(() => {
    const checkConnection = async () => {
      const result = await ApiAdapter.healthCheck();
      setConnectionStatus(result.connected, result.latency);
    };
    
    checkConnection();
    const interval = setInterval(checkConnection, 30000);
    return () => clearInterval(interval);
  }, [setConnectionStatus]);

  return (
    <HomePage
      onStartCreating={handleStartCreating}
      onViewProjects={handleViewProjects}
      isConnected={isConnected}
      connectionLatency={connectionLatency || undefined}
      isLoggedIn={isLoggedIn}
      onLogin={handleLogin}
      onLogout={handleLogout}
      isLoading={false}
    />
  );
}

// 创意输入页面适配器
function PromptStepAdapter() {
  return <PromptStep />;
}

// 生成页面适配器
function GeneratingPageAdapter() {
  return <GeneratingPage />;
}

// 故事圣经页面适配器
function BibleStepAdapter() {
  return <BibleStep />;
}

// 创作驾驶舱适配器
function CreationCockpitAdapter() {
  const { storyBible } = useStoryStore();

  if (!storyBible) {
    return <Navigate to="/" replace />;
  }

  return (
    <CreationCockpit />
  );
}

// 章节阅读适配器
function ChapterReadingPageAdapter() {
  const { storyBible, currentChapter, chapters } = useStoryStore();

  if (!storyBible || !currentChapter) {
    return <Navigate to="/" replace />;
  }

  return (
    <ChapterReadingPage
      storyBible={storyBible}
      currentChapter={currentChapter}
      allChapters={chapters}
      selectedAIProvider={undefined}
      onGenerateNextChapter={() => {}}
      onBackToWriting={() => window.history.back()}
      isGenerating={false}
    />
  );
}

function App() {
  return (
    <AppLayout>
      <Routes>
        {/* 首页路由 */}
        <Route path={ROUTES.HOME} element={<HomePageAdapter />} />
        
        {/* 创意输入页面 */}
        <Route path={ROUTES.PROMPT} element={<PromptStepAdapter />} />
        
        {/* AI生成页面 */}
        <Route path={ROUTES.GENERATING} element={<GeneratingPageAdapter />} />
        
        {/* 故事圣经审核页面 */}
        <Route path="/bible" element={<BibleStepAdapter />} />
        <Route path="/bible/:id" element={<BibleStepAdapter />} />
        
        {/* 创作驾驶舱 */}
        <Route path="/cockpit/:storyId" element={<CreationCockpitAdapter />} />
        
        {/* 章节阅读页面 */}
        <Route path="/reading/:storyId/:chapterId" element={<ChapterReadingPageAdapter />} />

        {/* API测试页面 */}
        <Route path="/api-test" element={<ApiTestPage />} />

        {/* 404页面 - 重定向到首页 */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </AppLayout>
  );
}

export default App;