"""
⚔️ [测试] 冲突检测系统测试
测试实时逻辑冲突检测系统的核心功能
"""

import pytest
from datetime import datetime
from typing import List

from app.core.conflict_detection import (
    ConflictDetectionEngine,
    ConflictType,
    ConflictSeverity,
    ConflictDetection,
    ConflictAnalysisResult,
    create_conflict_detection_engine
)
from app.schemas.world_graph import EntityResponse, RelationshipResponse, WorldGraphResponse
from app.models.world_graph import EntityType, RelationshipStatus, RelationshipTypes


@pytest.fixture
def conflict_world_graph() -> WorldGraphResponse:
    """创建包含冲突的示例世界知识图谱数据"""
    entities = [
        EntityResponse(
            id="char-001",
            story_id="test-conflict-story",
            name="张三",
            type=EntityType.CHARACTER,
            description="主角，年轻的剑客",
            properties={"age": 25, "weapon": "长剑", "personality": "善良勇敢"},
            importance_score=0.9,
            first_mentioned_chapter=1,
            last_mentioned_chapter=5,
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        EntityResponse(
            id="char-002",
            story_id="test-conflict-story",
            name="李四",
            type=EntityType.CHARACTER,
            description="反派，邪恶的法师",
            properties={"age": 40, "magic": "黑魔法", "personality": "邪恶残忍"},
            importance_score=0.8,
            first_mentioned_chapter=3,  # 注意：关系在第2章建立，但角色在第3章才出现
            last_mentioned_chapter=5,
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        EntityResponse(
            id="char-003",
            story_id="test-conflict-story",
            name="王五",
            type=EntityType.CHARACTER,
            description="配角，张三的朋友",
            properties={"age": 23, "skill": "弓箭", "personality": "善良友善"},
            importance_score=0.5,
            first_mentioned_chapter=1,
            last_mentioned_chapter=3,
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        EntityResponse(
            id="item-001",
            story_id="test-conflict-story",
            name="神秘宝剑",
            type=EntityType.ITEM,
            description="传说中的神器",
            properties={"power": "极高", "origin": "古代"},
            importance_score=0.7,
            first_mentioned_chapter=3,
            last_mentioned_chapter=5,
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        EntityResponse(
            id="scene-001",
            story_id="test-conflict-story",
            name="神秘森林",
            type=EntityType.SCENE,
            description="充满魔法的森林",
            properties={"location": "北方", "position": "南方"},  # 位置冲突
            importance_score=0.6,
            first_mentioned_chapter=2,
            last_mentioned_chapter=4,
            is_active=True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    ]
    
    relationships = [
        # 时间线冲突：关系在角色出现前建立
        RelationshipResponse(
            id="rel-001",
            source_entity_id="char-001",
            target_entity_id="char-002",
            relationship_type=RelationshipTypes.ENEMY,
            strength=0.8,
            status=RelationshipStatus.ACTIVE,
            description="张三与李四是宿敌",
            established_chapter=2,  # 李四在第3章才出现
            last_updated_chapter=5,
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        # 关系突然转变
        RelationshipResponse(
            id="rel-002",
            source_entity_id="char-001",
            target_entity_id="char-002",
            relationship_type=RelationshipTypes.FRIEND,
            strength=0.7,
            status=RelationshipStatus.ACTIVE,
            description="张三与李四成为朋友",
            established_chapter=3,  # 从敌人突然变朋友
            last_updated_chapter=5,
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        # 物品多重拥有冲突
        RelationshipResponse(
            id="rel-003",
            source_entity_id="char-001",
            target_entity_id="item-001",
            relationship_type=RelationshipTypes.OWNS,
            strength=0.9,
            status=RelationshipStatus.ACTIVE,
            description="张三拥有神秘宝剑",
            established_chapter=3,
            last_updated_chapter=5,
            created_at=datetime.now(),
            updated_at=datetime.now()
        ),
        RelationshipResponse(
            id="rel-004",
            source_entity_id="char-003",
            target_entity_id="item-001",
            relationship_type=RelationshipTypes.OWNS,
            strength=0.8,
            status=RelationshipStatus.ACTIVE,
            description="王五也拥有神秘宝剑",  # 同一物品被多人拥有
            established_chapter=4,
            last_updated_chapter=5,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    ]
    
    return WorldGraphResponse(
        story_id="test-conflict-story",
        story_title="测试冲突故事",
        entities=entities,
        relationships=relationships,
        entity_count=len(entities),
        relationship_count=len(relationships),
        entity_stats={
            "CHARACTER": 3,
            "ITEM": 1,
            "SCENE": 1
        },
        relationship_stats={
            "敌人": 1,
            "朋友": 1,
            "拥有": 2
        }
    )


@pytest.fixture
def conflict_engine() -> ConflictDetectionEngine:
    """创建冲突检测引擎实例"""
    return create_conflict_detection_engine()


class TestConflictDetectionEngine:
    """冲突检测引擎测试类"""
    
    def test_engine_initialization(self, conflict_engine: ConflictDetectionEngine):
        """测试引擎初始化"""
        assert conflict_engine is not None
        assert len(conflict_engine.conflict_rules) > 0
        
        # 验证包含基本的冲突检测规则
        rule_types = list(conflict_engine.conflict_rules.keys())
        assert ConflictType.RELATIONSHIP_CONTRADICTION in rule_types
        assert ConflictType.TIMELINE_INCONSISTENCY in rule_types
        assert ConflictType.ITEM_OWNERSHIP_CONFLICT in rule_types
    
    @pytest.mark.asyncio
    async def test_comprehensive_conflict_detection(
        self,
        conflict_engine: ConflictDetectionEngine,
        conflict_world_graph: WorldGraphResponse
    ):
        """测试全面的冲突检测"""
        analysis_result = await conflict_engine.detect_conflicts(
            world_graph=conflict_world_graph,
            current_chapter=5
        )
        
        assert analysis_result is not None
        assert analysis_result.story_id == "test-conflict-story"
        assert analysis_result.analysis_chapter == 5
        assert analysis_result.total_conflicts > 0
        
        # 验证检测到了预期的冲突类型
        all_conflicts = (
            analysis_result.critical_conflicts +
            analysis_result.high_conflicts +
            analysis_result.medium_conflicts +
            analysis_result.low_conflicts
        )
        
        conflict_types = [c.conflict_type for c in all_conflicts]
        
        # 应该检测到关系矛盾（敌人突然变朋友）
        assert ConflictType.RELATIONSHIP_CONTRADICTION in conflict_types
        
        # 应该检测到时间线不一致（关系在角色出现前建立）
        assert ConflictType.TIMELINE_INCONSISTENCY in conflict_types
        
        # 应该检测到物品归属冲突（同一物品被多人拥有）
        assert ConflictType.ITEM_OWNERSHIP_CONFLICT in conflict_types
        
        # 验证评分范围
        assert 0.0 <= analysis_result.story_consistency_score <= 1.0
        assert 0.0 <= analysis_result.logical_integrity_score <= 1.0
        assert 0.0 <= analysis_result.overall_health_score <= 1.0
    
    @pytest.mark.asyncio
    async def test_relationship_contradiction_detection(
        self,
        conflict_engine: ConflictDetectionEngine,
        conflict_world_graph: WorldGraphResponse
    ):
        """测试关系矛盾检测"""
        conflicts = await conflict_engine._check_relationship_contradictions(
            conflict_world_graph, 5
        )
        
        assert len(conflicts) > 0
        
        # 验证检测到张三和李四的关系突然转变
        relationship_conflict = next(
            (c for c in conflicts if "张三" in c.title and "李四" in c.title),
            None
        )
        
        assert relationship_conflict is not None
        assert relationship_conflict.conflict_type == ConflictType.RELATIONSHIP_CONTRADICTION
        assert "char-001" in relationship_conflict.involved_entities
        assert "char-002" in relationship_conflict.involved_entities
        assert len(relationship_conflict.repair_suggestions) > 0
    
    @pytest.mark.asyncio
    async def test_timeline_inconsistency_detection(
        self,
        conflict_engine: ConflictDetectionEngine,
        conflict_world_graph: WorldGraphResponse
    ):
        """测试时间线不一致检测"""
        conflicts = await conflict_engine._check_timeline_inconsistencies(
            conflict_world_graph, 5
        )
        
        assert len(conflicts) > 0
        
        # 验证检测到李四在出现前就参与关系的冲突
        timeline_conflict = next(
            (c for c in conflicts if "李四" in c.title and "首次出现前" in c.title),
            None
        )
        
        assert timeline_conflict is not None
        assert timeline_conflict.conflict_type == ConflictType.TIMELINE_INCONSISTENCY
        assert "char-002" in timeline_conflict.involved_entities
    
    @pytest.mark.asyncio
    async def test_item_ownership_conflict_detection(
        self,
        conflict_engine: ConflictDetectionEngine,
        conflict_world_graph: WorldGraphResponse
    ):
        """测试物品归属冲突检测"""
        conflicts = await conflict_engine._check_item_ownership_conflicts(
            conflict_world_graph, 5
        )
        
        assert len(conflicts) > 0
        
        # 验证检测到神秘宝剑的多重拥有冲突
        ownership_conflict = next(
            (c for c in conflicts if "神秘宝剑" in c.title and "多个角色" in c.title),
            None
        )
        
        assert ownership_conflict is not None
        assert ownership_conflict.conflict_type == ConflictType.ITEM_OWNERSHIP_CONFLICT
        assert "item-001" in ownership_conflict.involved_entities
    
    @pytest.mark.asyncio
    async def test_location_conflict_detection(
        self,
        conflict_engine: ConflictDetectionEngine,
        conflict_world_graph: WorldGraphResponse
    ):
        """测试位置冲突检测"""
        conflicts = await conflict_engine._check_world_setting_conflicts(
            conflict_world_graph, 5
        )
        
        # 验证检测到神秘森林的位置信息不一致
        location_conflicts = [
            c for c in conflicts 
            if c.conflict_type == ConflictType.LOCATION_CONFLICT and "神秘森林" in c.title
        ]
        
        if location_conflicts:  # 可能检测到位置冲突
            location_conflict = location_conflicts[0]
            assert "scene-001" in location_conflict.involved_entities
            assert "位置" in location_conflict.description
    
    def test_severity_determination(self, conflict_engine: ConflictDetectionEngine):
        """测试严重程度判定"""
        severity_thresholds = {
            "critical": 0.9,
            "high": 0.7,
            "medium": 0.5,
            "low": 0.3
        }
        
        # 测试不同置信度的严重程度判定
        assert conflict_engine._determine_severity(0.95, severity_thresholds) == ConflictSeverity.CRITICAL
        assert conflict_engine._determine_severity(0.75, severity_thresholds) == ConflictSeverity.HIGH
        assert conflict_engine._determine_severity(0.55, severity_thresholds) == ConflictSeverity.MEDIUM
        assert conflict_engine._determine_severity(0.25, severity_thresholds) == ConflictSeverity.LOW
    
    def test_consistency_score_calculation(self, conflict_engine: ConflictDetectionEngine):
        """测试一致性评分计算"""
        # 创建测试冲突
        conflicts = [
            type('MockConflict', (), {'severity': ConflictSeverity.CRITICAL})(),
            type('MockConflict', (), {'severity': ConflictSeverity.HIGH})(),
            type('MockConflict', (), {'severity': ConflictSeverity.MEDIUM})()
        ]
        
        score = conflict_engine._calculate_consistency_score(conflicts)
        assert 0.0 <= score <= 1.0
        
        # 无冲突时应该返回1.0
        no_conflicts_score = conflict_engine._calculate_consistency_score([])
        assert no_conflicts_score == 1.0
    
    def test_conflict_trend_analysis(self, conflict_engine: ConflictDetectionEngine):
        """测试冲突趋势分析"""
        current_conflicts = [
            type('MockConflict', (), {})() for _ in range(5)
        ]
        
        # 创建模拟的历史分析结果
        previous_analysis = type('MockAnalysis', (), {'total_conflicts': 3})()
        
        trend = conflict_engine._analyze_conflict_trend(current_conflicts, previous_analysis)
        assert trend in ["improving", "stable", "worsening"]
        
        # 当前冲突增加，应该是恶化趋势
        assert trend == "worsening"
        
        # 无历史数据时应该返回稳定
        no_history_trend = conflict_engine._analyze_conflict_trend(current_conflicts, None)
        assert no_history_trend == "stable"
    
    def test_risk_areas_identification(self, conflict_engine: ConflictDetectionEngine):
        """测试风险区域识别"""
        # 创建集中在某个类型的冲突
        conflicts = [
            type('MockConflict', (), {
                'conflict_type': ConflictType.RELATIONSHIP_CONTRADICTION,
                'conflicting_chapters': [3],
                'severity': ConflictSeverity.CRITICAL
            })() for _ in range(4)
        ]
        
        risk_areas = conflict_engine._identify_risk_areas(conflicts)
        assert len(risk_areas) > 0
        
        # 应该识别出关系矛盾频发的风险
        relationship_risk = any("relationship_contradiction" in area for area in risk_areas)
        assert relationship_risk


if __name__ == "__main__":
    """运行测试"""
    pytest.main([__file__, "-v"])
